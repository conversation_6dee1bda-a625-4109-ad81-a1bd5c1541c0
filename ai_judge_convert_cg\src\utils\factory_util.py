import importlib


class Factory:
    @staticmethod
    def create_model(model_name, model_arch, device, conf):
        module = importlib.import_module(f'model_interface.{model_name.lower()}')
        class_ = getattr(module, model_name)
        return class_(model_arch, device, conf)

    @staticmethod
    def create_module(module_name, act_name, conf, param, model_dict):
        module = importlib.import_module(f'modules.{module_name.lower()}')
        class_ = getattr(module, module_name)
        return class_(act_name, conf, param, model_dict)