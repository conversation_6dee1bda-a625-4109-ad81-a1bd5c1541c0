from __future__ import annotations
from abc import ABCMeta, abstractmethod
from typing import Union, List, Tuple, Dict, Optional

from libs.yolov7.utils.datasets import letterbox
from libs.yolov7.utils.general import non_max_suppression, non_max_suppression_plate, non_max_suppression_seg, \
    scale_coords
from libs.yolov7.utils import add_model_process as add_p
from libs.plate_recognition.plate_rec import decodePlate2, plateName
from conf.config import FuncModel_SET, BaseConf, ModelBaseConf
import numpy as np
from numpy import ndarray
import torch
import torchvision.transforms as transforms
import torch.nn.functional as F
from PIL import Image
import onnxruntime
import json
from tqdm import tqdm


class ModelArch(metaclass=ABCMeta):
    @abstractmethod
    def load(self, weight: str, device: int):
        pass

    @abstractmethod
    def infer(self, model: Union[onnxruntime.InferenceSession, "AclLiteModel"], input_data: ndarray) -> Union[
        Tuple[ndarray, ndarray], ndarray]:
        pass


# 英伟达显卡或CPU版本模型加载和推理模块
class NvidiaArch(ModelArch):
    def load(self, weight: str, device: int) -> onnxruntime.InferenceSession:
        """
        模型加载
        :param weight: 模型权重文件路径
        :param device: 指定用哪张显卡运行
        :return:onnx格式实例化的模型
        """
        weight += ".onnx"
        options = [{'device_id': device}, {}]  # GPU设备ID
        providers = ['CUDAExecutionProvider', 'CPUExecutionProvider']
        model = onnxruntime.InferenceSession(weight, providers=providers, provider_options=options)
        return model

    def infer(self, model: onnxruntime.InferenceSession, input_data: Union[List[ndarray], ndarray]) -> Union[
        Tuple[ndarray, ndarray], ndarray]:
        """
        模型推理
        :param model: 加载好的onnx模型
        :param input_data: 经过前处理后的图片数据
        :return: 模型的推理结果
        """
        # 获取输入和输出名称
        inputs = model.get_inputs()
        outputs = model.get_outputs()
        input_names = [input.name for input in inputs]
        output_names = [output.name for output in outputs]

        if len(input_data) == 2:
            input_feed = {
                input_names[0]: input_data[0],
                input_names[1]: input_data[1]
            }
        else:
            input_feed = {input_names[0]: input_data}
        # 运行推理
        result = model.run(output_names, input_feed)
        if len(result) == 2:
            # 实例分割模型
            return result[0], result[1]
        else:
            return result[0]


# 华为Atlas服务器版本模型加载和推理模块
class AscendArch(ModelArch):
    def load(self, weight: str, device: int) -> "AclLiteModel":
        """
        模型加载
        :param weight: 模型权重文件路径
        :param device: 指定用哪张显卡运行
        :return:AclLiteModel实例化的模型
        """
        from libs.ascend.acllite import AclLiteModel
        from libs.ascend.acllite import AclLiteResource
        weight += ".om"
        acl_resource = AclLiteResource(device)
        acl_resource.init()
        model = AclLiteModel(weight)
        return model

    def infer(self, model: "AclLiteModel", input_data: ndarray) -> Union[Tuple[ndarray, ndarray], ndarray]:
        """
        模型推理
        :param model: 加载好的模型
        :param input_data: 经过前处理后的图片数据
        :return: 模型的推理结果
        """
        result = model.execute(input_data)
        if len(result) == 2:
            return result[0], result[1]
        else:
            return result[0]


# 保存模型单个目标结果
class ModelResult:
    def __init__(self, task: str, superclass: List[str], subclass: str, score: Union[float, List[float]],
                 img: ndarray, box: List[int] = None,
                 mask: ndarray = None, text: str = None):
        self.task = task  # class:分类, det:检测, seg：分割, ocr：识别
        self.superclass = superclass  # 包含多个子类的超类
        self.subclass = subclass  # 子类别
        self.score = score  # 置信度
        self.img = img  # 图片numpy array TODO:未使用
        self.box = box  # 坐标框[x1,y1,x2,y2]
        self.mask = mask  # 分割掩码
        self.text = text  # ocr识别的文本信息
        self.extends = {}  # 扩展的输出信息

    # 格式化输出
    def __str__(self) -> str:
        s = {
            'task': self.task,
            'superclass': self.superclass,
            'subclass': self.subclass,
            'score': self.score,
            'img': self.img.shape,
            'box': self.box,
            'mask': self.mask.shape if self.mask is not None else None,
            'text': self.text,
            # 'extends': str(self.extends)
        }
        return json.dumps(s, ensure_ascii=False)

    def get_extends(self):
        return self.extends

    def get_ext_value(self, key):
        return self.extends.get(key, None)

    def add_ext_items(self, items) -> None:
        if isinstance(items, list):
            for item in items:
                key, value = item
                self.extends[key] = value
        elif isinstance(items, tuple):
            key, value = items
            self.extends[key] = value


# 按超类保存模型多个目标结果
class ModelResults:
    def __init__(self):
        self.results: Dict[str, List[ModelResult]] = {}

    def __str__(self) -> str:
        results = [str(r) for rs in self.results.values() for r in rs]
        return "\n".join(results)

    def add_result(self, result: ModelResult) -> None:  # 新增subclass
        superclass_list = result.superclass
        for superclass in superclass_list:
            if superclass not in self.results:
                self.results[superclass] = []
            self.results[superclass].append(result)

    def merge_results(self, model_results: ModelResults) -> None:
        for superclass, results in model_results.results.items():
            if superclass not in self.results:
                self.results[superclass] = []
            self.results[superclass] += results

    def get_results(self, superclass: str = None, attr: str = None) -> List:
        if superclass:
            results = self.results.get(superclass, [])
        else:
            results = [r for rs in self.results.values() for r in rs]
        if superclass and attr:  # TODO:应保证各attr（box、cls）排序后索引对应
            try:
                results.sort(key=lambda x: x.box[3], reverse=True)
            except:
                pass
        if attr:
            results = [getattr(r, attr) for r in results]

        return results


# 通用图像模型，包括目标检测、语义分割、图像分类、OCR识别等
class ImageModel(metaclass=ABCMeta):
    def __init__(self, model_arch: ModelArch, device: int, conf: ModelBaseConf):
        self._model_arch = model_arch  # 模型架构选择
        self._device = device  # 指定运行的GPU编号
        self._conf = conf  # 模型配置类
        self._model_thresh = conf.model_thresh  # 置信度筛选阈值
        self._iou_thresh = conf.iou_thresh  # NMS去重中IOU的阈值
        self._model_type = conf.model_type  # 同模型结果中的task
        self._weight = conf.weight  # 模型权重文件路径
        self._label_dict = conf.label_dict  # 索引和类别的对应关系
        self._det_class = conf.det_class  # 模型检测的类别
        self._super_class = conf.superclass  # 包含多个子类的超类
        self._prepare_shape = conf.prepare_shape  # 前处理需缩放的尺寸
        self._model = self._model_arch.load(self._weight, self._device)  # 加载模型
        self.transform = transforms.Compose([
            transforms.Resize(self._prepare_shape),
            transforms.ToTensor(),
        ])  # 分类/相似度模型对图片数据的前处理

    # 模型运行主流程
    def run(self, image: ndarray, det_cls: List = None, thresh: float = None) -> ModelResults:
        model_input = self.preprocess(image)
        model_output = self._model_arch.infer(self._model, model_input)  # 模型推理
        result = self.postprocess(image, model_output, det_cls, thresh)
        return result

    # 前处理
    @abstractmethod
    def preprocess(self, image: ndarray) -> ndarray:
        pass

    # 后处理
    @abstractmethod
    def postprocess(self, image: ndarray, model_output: Union[Tuple[ndarray, ndarray], ndarray],
                    det_cls: List, thresh: float) -> ModelResults:
        pass

    def ensure_tensor(self, model_output: ndarray) -> Optional[torch.Tensor]:
        if isinstance(model_output, ndarray):
            return torch.from_numpy(model_output)
        elif isinstance(model_output, torch.Tensor):
            return model_output
        else:
            assert False, print(f"Unsupported data type: {type(model_output)}")

    # 通过类别名称得到类别索引，用于NMS筛选需要的类别
    def subclass_to_idx(self, cls_list: Optional[List[str]]) -> List[int]:
        value_list = list(self._label_dict.values())
        cls_list = cls_list if cls_list else value_list
        idx_list = [value_list.index(cls) for cls in cls_list]
        return idx_list

    # 通过类别索引得到类别名称
    def idx_to_subclass(self, cls_idx: int) -> Optional[str]:
        return self._label_dict.get(cls_idx, None)

    # 找出子类对应的所有超类（包含子类本身）
    def get_superclass(self, subclass: str) -> List[str]:
        superclasses = [subclass]  # 添加subclass
        for superclass, subclasses in self._super_class.items():
            if subclass in subclasses:
                superclasses.append(superclass)
        return superclasses

    @property
    def conf(self) -> ModelBaseConf:
        return self._conf


# 目标检测模型
class DetModel(ImageModel):
    def __init__(self, model_arch, device, conf):
        super().__init__(model_arch, device, conf)

    def preprocess(self, image):
        img = letterbox(image, self._prepare_shape, stride=self._conf.stride)[0]
        img = img[:, :, ::-1].transpose(2, 0, 1).astype(np.float32)  # BGR to RGB
        img /= 255.0  # 0 - 255 to 0.0 - 1.0
        img_npy = np.ascontiguousarray(img)  # 确保数组的连续性
        if img_npy.ndim == 3:
            img_npy = np.expand_dims(img_npy, 0)
        return img_npy

    def postprocess(self, image, model_output, det_cls, thresh):
        model_output = self.ensure_tensor(model_output)  # 统一数据格式
        assert model_output.ndim == 3, print("model_output数据维度异常")
        det_cls = det_cls if det_cls else self._det_class
        thresh = thresh if thresh else self._model_thresh
        idx_list = self.subclass_to_idx(det_cls)
        dets = non_max_suppression(model_output, thresh,
                                   self._conf.iou_thresh,
                                   classes=idx_list,
                                   agnostic=True)
        model_results = ModelResults()
        for det in dets:  # detections per image
            if len(det):
                # Rescale boxes from img_size to im0 size
                det[:, :4] = scale_coords(self._prepare_shape, det[:, :4], image.shape).round()
                for per_obj in det:
                    box = per_obj[:4].int().tolist()
                    cls_idx = int(per_obj[-1])
                    score = round(float(per_obj[-2]), 3)
                    subclass = self.idx_to_subclass(cls_idx)
                    superclass = self.get_superclass(subclass)
                    obj = ModelResult(self._model_type, superclass, subclass, score, image, box=box)
                    model_results.add_result(obj)
        return model_results


# 分类模型
class ClassModel(ImageModel):
    def __init__(self, model_arch, device, conf):
        super().__init__(model_arch, device, conf)

    def preprocess(self, image):
        img = image[..., ::-1]  # BGR to RGB
        img = np.asarray(self.transform(Image.fromarray(img)))
        img_npy = np.ascontiguousarray(img)  # 确保数组的连续性
        if img_npy.ndim == 3:
            img_npy = np.expand_dims(img_npy, 0)
        return img_npy

    # 修改输出逻辑为：只要违法行为大于等于阈值就算违法
    def postprocess(self, image, model_output, det_cls, thresh):
        model_output = self.ensure_tensor(model_output)  # 统一数据格式
        thresh = thresh if thresh else self._model_thresh
        waste_idx = self.get_waste_idx()
        prediction = np.asarray(F.softmax(model_output, dim=1)[0])
        new_pred = np.delete(prediction, waste_idx)  # 测试：不考虑废片的概率
        idx = np.where(new_pred >= thresh)[0]
        score = 0
        if len(idx):
            score = round(float(np.average(new_pred[idx])), 3)
        if len(idx) > 1:
            max_idx = -1
        elif len(idx):
            max_idx = int(idx)
        else:
            max_idx = int(waste_idx)
            score = 1 - thresh
        # max_idx = int(prediction.argmax())
        # # 未达到阈值修改类别
        # if score < thresh:
        #     max_idx = waste_idx if waste_idx else max_idx
        results = ModelResults()
        subclass = self.idx_to_subclass(max_idx)
        superclass = self.get_superclass(subclass)
        obj = ModelResult(self._model_type, superclass, subclass, score, image)
        results.add_result(obj)
        return results

    def get_waste_idx(self):
        match_key = None
        for key, values in self._label_dict.items():
            if values == "waste":
                match_key = key
        return match_key


# 车牌检测模型
class PlateDetModel(DetModel):
    def __init__(self, model_arch, device, conf):
        super().__init__(model_arch, device, conf)

    def postprocess(self, image, model_output, det_cls, thresh):
        model_output = self.ensure_tensor(model_output)  # 统一数据格式
        assert model_output.ndim == 3, print("model_output数据维度异常")
        det_cls = det_cls if det_cls else self._det_class
        thresh = thresh if thresh else self._model_thresh
        idx_list = self.subclass_to_idx(det_cls)
        dets = non_max_suppression_plate(model_output, conf_thres=thresh,
                                         iou_thres=self._iou_thresh, classes=idx_list, kpt_label=4)
        model_results = ModelResults()
        for det in dets:  # detections per image
            if len(det):
                add_p.scale_coords_plate(self._prepare_shape, det[:, :4], image.shape, kpt_label=False)
                add_p.scale_coords_plate(self._prepare_shape, det[:, 6:], image.shape, kpt_label=4, step=3)
                for per_obj in det:
                    box = per_obj[:4].int().tolist()
                    score = round(float(per_obj[4]), 3)
                    cls_idx = int(per_obj[5])
                    subclass = self.idx_to_subclass(cls_idx)
                    superclass = self.get_superclass(subclass)
                    landmarks = per_obj[6:].int().tolist()
                    key_points = [landmarks[i] for i in [0, 1, 3, 4, 6, 7, 9, 10]]
                    obj = ModelResult(self._model_type, superclass, subclass, score, image, box=box)
                    obj.add_ext_items(('key_points', key_points))
                    model_results.add_result(obj)
        return model_results


# 车牌识别模型
class PlateRecModel(ImageModel):
    def __init__(self, model_arch, device, conf):
        super().__init__(model_arch, device, conf)

    def preprocess(self, image):
        img = letterbox(image, self._prepare_shape, stride=self._conf.stride)[0]
        img = img.transpose([2, 0, 1]).astype(np.float32)
        mean_value, std_value = (0.588, 0.193)
        img = (img / 255. - mean_value) / std_value
        img_npy = np.ascontiguousarray(img)  # 确保数组的连续性
        if img_npy.ndim == 3:
            img_npy = np.expand_dims(img_npy, 0)
        return img_npy

    def postprocess(self, image, model_output, det_cls, thresh):
        model_output = self.ensure_tensor(model_output)
        model_output = torch.softmax(model_output, dim=-1)
        prob, index = model_output.max(dim=-1)
        index = index.view(-1).numpy()
        prob = prob.view(-1).numpy()
        newPreds, new_index = decodePlate2(index)
        score_list = [round(float(score), 3) for score in prob[new_index]]
        plate = ""
        model_results = ModelResults()
        for i in newPreds:
            plate += plateName[i]
        obj = ModelResult(self._model_type, ["plate_rec"], "plate_rec", score_list, image, text=plate)
        model_results.add_result(obj)

        return model_results


class SegModel(DetModel):
    def __init__(self, model_arch, device, conf):
        super().__init__(model_arch, device, conf)

    def postprocess(self, image, model_output, det_cls, thresh):
        pred, proto = model_output

        pred = self.ensure_tensor(pred)
        proto = self.ensure_tensor(proto)
        det_cls = det_cls if det_cls else self._det_class
        thresh = thresh if thresh else self._model_thresh
        idx_list = self.subclass_to_idx(det_cls)
        pred = non_max_suppression_seg(pred, thresh, self._iou_thresh, classes=idx_list, agnostic=True,
                                       max_det=1000, nm=32)
        model_results = ModelResults()
        for i, det in enumerate(pred):  # per image
            if len(det):
                masks = add_p.process_mask(proto[i], det[:, 6:], det[:, :4], self._prepare_shape, upsample=True)  # HWC
                masks = add_p.scale_masks(self._prepare_shape, masks, image.shape)
                # kp_mask
                kp_mask = [add_p.GenericMask(x, image.shape[0], image.shape[1]) for x in masks]
                det[:, :4] = scale_coords(self._prepare_shape, det[:, :4], image.shape).round()
                for idx, per_obj in enumerate(det):
                    box = per_obj[:4].int().tolist()
                    cls_idx = int(per_obj[5])
                    score = round(float(per_obj[4]), 3)
                    subclass = self.idx_to_subclass(cls_idx)
                    superclass = self.get_superclass(subclass)
                    obj = ModelResult(self._model_type, superclass, subclass, score, image, box=box, mask=masks[idx])
                    obj.add_ext_items(("kp_mask", kp_mask))
                    model_results.add_result(obj)
        return model_results


class SimilarModel(ImageModel):

    def __init__(self, model_arch, device, conf):
        super().__init__(model_arch, device, conf)

    def preprocess(self, image):
        assert isinstance(image, list) and isinstance(image[0], ndarray), print("输入数据格式异常！")
        target_img, unknown_img = image
        t_img = target_img[..., ::-1]  # BGR to RGB
        u_img = unknown_img[..., ::-1]  # BGR to RGB

        t_img = np.asarray(self.transform(Image.fromarray(t_img)))
        u_img = np.asarray(self.transform(Image.fromarray(u_img)))

        t_img = np.ascontiguousarray(t_img)  # 确保数组的连续性
        u_img = np.ascontiguousarray(u_img)  # 确保数组的连续性

        if t_img.ndim == 3:
            t_img = np.expand_dims(t_img, 0)
        if u_img.ndim == 3:
            u_img = np.expand_dims(u_img, 0)
        return [t_img, u_img]

    def postprocess(self, image, model_output, det_cls, thresh):
        sim_dist = round(model_output.item(), 3)
        results = ModelResults()
        obj = ModelResult(self._model_type, ["sim_match"], "sim_match", sim_dist, image[0])
        results.add_result(obj)
        return results


class ModelFactory:
    def __init__(self):
        self._model_arch = BaseConf.arch
        self._model_list = FuncModel_SET

    @staticmethod
    def create(model_name, model_arch, device, conf):
        return eval(model_name)(model_arch, device, conf)

    def init_models(self, device: int = 0, choose_model: list = None):
        arch_list = [AscendArch(), NvidiaArch()]
        try:
            model_arch = arch_list[self._model_arch]
        except:
            model_arch = NvidiaArch()  # 默认使用英伟达架构
        model_dict = {}
        for model_name, values in tqdm(self._model_list.items()):
            if choose_model and model_name not in choose_model:
                continue
            name, model_conf = values
            model = self.create(name, model_arch, device, model_conf)
            model_dict[model_name] = model
        return model_dict
