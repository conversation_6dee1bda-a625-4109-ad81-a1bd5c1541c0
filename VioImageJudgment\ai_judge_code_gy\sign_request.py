import requests
from datetime import datetime
import urllib3
from main_config.config import IP, <PERSON><PERSON><PERSON><PERSON>, AppSecret, TPC_IP, Sign_Mode

no_header_sign_key = [
    "X-Ca-Signature", "X-Ca-Signature-Headers", "Accept", "Content-MD5", "Content-Type", "Date", "Content-Length",
    "Server", "Connection", "Host", "Transfer-Encoding", "X-Application-Context", "Content-Encoding"
]


def headers_values(headers: dict):
    # 排序
    headers = dict(sorted(headers.items(), key=lambda x: x[0]))
    k = headers.keys()
    sign_headers = ""
    str_headers = ""
    for i in k:
        if i not in no_header_sign_key:
            sign_headers = sign_headers + "," + i.lower() if sign_headers else i.lower()
            str_headers += i.lower() + ":" + str(headers[i]).strip() + "\n"
    return sign_headers, str_headers


def get_sha256(data, key):
    from hashlib import sha256
    import hmac, base64
    key = key.encode('utf-8')  # sha256加密的key
    message = data.encode('utf-8')  # 待sha256加密的内容
    sign = base64.b64encode(hmac.new(key, message, digestmod=sha256).digest()).decode()
    return sign


def send_request():
    if isinstance(Sign_Mode, str) and Sign_Mode.lower() == "iiea":
        try:
            ret = requests.get(f"https://{TPC_IP}/iiea-web/service/rs/heartbeat/v1/auth", verify=False, timeout=5)
            if ret.status_code == 404:
                return {"code":-1,"data":False,"msg":"请求失败，status_code=404"}
        except Exception as e:
            print('=========Error!===============', e,
                  e.__traceback__.tb_frame.f_globals["__file__"], e.__traceback__.tb_lineno,
                  "请检查config.py中TPC_IP是否配置正确！")
            return {}
    else:
        urllib3.disable_warnings()
        GMT_FORMAT = '%a, %d %b %Y %H:%M:%S GMT'

        sk = AppSecret
        ip = IP
        modelID = "m_jtwftxznshmx_C236D4_933"
        url = f"https://{ip}/artemis/api/v1/model/queryModelAuthorization?modelIdentification={modelID}"
        # params = {"modelIdentification": "m_jtsgspzntqmx_C236D4_494"}
        Path = "/artemis/api/v1/model/queryModelAuthorization"
        Query = f"modelIdentification={modelID}"
        Url = Path + "?" + Query
        src_headers = {
            'Accept': "*/*",
            'Content-Type': "application/json",
            'Date': datetime.now().strftime(GMT_FORMAT),
            'X-Ca-Key': AppKey
        }
        init_sign = "GET" + "\n" + src_headers['Accept'] + "\n" + src_headers['Content-Type'] + "\n" + src_headers[
            "Date"] + "\n"

        sign_headers, str_headers = headers_values(src_headers)
        src_headers['X-Ca-Signature-Headers'] = sign_headers
        sign_str = init_sign + str_headers + Url
        sign = get_sha256(sign_str, sk)
        src_headers['X-Ca-Signature'] = sign
        # print("headers:", src_headers, "\n", "url:", url, "\n", sign_str, "\n")
        # ret = requests.get(url, headers=src_headers, verify=False, timeout=5)

        try:
            ret = requests.get(url, headers=src_headers, verify=False, timeout=5)
        except Exception as e:
            print('=========Error!===============', e,
                  e.__traceback__.tb_frame.f_globals["__file__"], e.__traceback__.tb_lineno,
                  "请检查config.py中IP、AppKey、AppSecret是否配置正确！")
            return {}
    try:
        return ret.json()
    except:
        return ret
