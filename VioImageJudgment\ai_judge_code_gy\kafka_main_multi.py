#!/usr/bin/python
# -*- coding: UTF-8 -*-
# import os
# #
# os.environ["CUDA_VISIBLE_DEVICES"] = "1"
import os.path
import uuid as uid

import numpy as np
import torch.cuda

from init_models import *

if Is_Allowed_Type["1625"]:
    from ai_judge_code_gy.vio_judge_16251 import judge_vio as judge_16251
if Is_Allowed_Type["1208"]:
    from ai_judge_code_gy.vio_judge_12080 import judge_vio as judge_12080
if Is_Allowed_Type["1345"]:
    from ai_judge_code_gy.vio_judge_13450 import judge_vio as judge_13450
if Is_Allowed_Type["1301"]:
    if NX_VERSION == 2:
        from ai_judge_code_gy.vio_judge_13010v2 import judge_vio as judge_13010
    else:
        from ai_judge_code_gy.vio_judge_13010 import judge_vio as judge_13010
if Is_Allowed_Type["1352"]:
    from ai_judge_code_gy.vio_judge_13522 import judge_vio as judge_13522
if Is_Allowed_Type["1357"]:
    from ai_judge_code_gy.vio_judge_13570v2 import judge_vio as judge_13570
if Is_Allowed_Type["1223"] or Is_Allowed_Type["1101"]:
    from ai_judge_code_gy.vio_judge_12230_11010 import judge_vio as judge_12230_11010
if Is_Allowed_Type["7102"]:
    from ai_judge_code_gy.vio_judge_71020 import judge_vio as judge_71020
if Is_Allowed_Type["8013"]:
    if Stop_Drv_Version == 2:
        from ai_judge_code_gy.vio_judge_80130_jz import judge_vio as judge_80130
    else:
        from ai_judge_code_gy.vio_judge_80130 import judge_vio as judge_80130
if Is_Allowed_Type["1039"]:
    from ai_judge_code_gy.vio_judge_10390 import judge_vio as judge_10390
if Is_Allowed_Type["1018"]:
    from ai_judge_code_gy.vio_judge_10180 import judge_vio as judge_10180
if Is_Allowed_Type["1000"]:
    from ai_judge_code_gy.vio_filter_vehicle import judge_vio as judge_10000
import urllib.request
import socket
import traceback
import json
from main_config.log_config import get_logging
import logging
import logging.config
import time
import datetime as dt
from kafka import KafkaConsumer, KafkaProducer, TopicPartition, OffsetAndMetadata
import multiprocessing
from multiprocessing import Process


def judge_act_conf(act, match_code, vio_data):
    judge_res = False
    try:
        conf_list = Code_Cross_Direction_Conf.get(act, [])
        # 卡口名称
        cross = vio_data["vehicleAlarmResult"][0]["targetAttrs"]["crossingName"]
        # 方向
        all_mark = f"{match_code}_{cross}_all"
        if all_mark in conf_list:
            judge_res = True
        else:
            direction_dict = {"eastwest": "东西", "westeast": "西东", "northsouth": "北南", "southnorth": "南北"}
            tmp_direction = vio_data["vehicleAlarmResult"][0]["targetAttrs"]["directionIndex"].lower()
            direction = direction_dict.get(tmp_direction, tmp_direction)
            mark = f"{match_code}_{cross}_{direction}"
            if mark in conf_list:
                judge_res = True
    except:
        error_msg = catch_error()
        print(f"电警杆数据特殊配置异常!{error_msg}")
    return judge_res


def base2img(img_path, data):
    file = open(img_path, 'wb')
    file.write(data)
    file.close()


def load_image_from_url(url):
    img = None
    for i in range(Url_Retry_Num):
        try:
            # 发送HTTP请求获取图片数据
            response = requests.get(url, timeout=3)
            # 将图片数据转换为numpy数组
            img_array = np.array(bytearray(response.content), dtype=np.uint8)
            # 使用OpenCV解码图片
            img = cv2.imdecode(img_array, cv2.IMREAD_COLOR)
        except Exception as e:
            error_msg = catch_error()
            print(error_msg)
        if img is not None:
            break
        print(f"图片读取失败！重试第{i + 1}次")
        time.sleep(Url_Retry_Interval)
    return img


def convert_iiea_rect(iieaMergeRect):
    try:
        x, y, w, h = map(float, iieaMergeRect.split(','))
        y2 = y + h
        y_dist = [y, abs(1 - y2)]
        black_pos = ["up", "down"]
        idx = np.argmax(y_dist)
        return y_dist[idx], black_pos[idx]
    except Exception as e:
        error_msg = catch_error()
        print(error_msg)
        return None, None


def get_images(request_data, vio_code, logger):
    socket.setdefaulttimeout(5)
    s_time = dt.datetime.now()
    main_data = request_data["vehicleAlarmResult"]
    # raw_img_dir = '../temp/raw_imgs_%s' % str(vio_code)
    # if not os.path.exists(raw_img_dir):
    #     os.makedirs(raw_img_dir)
    # if len(os.listdir(raw_img_dir)) > 100:
    #     os.system('find {} -mmin +5 -name "*.*" -delete'.format(raw_img_dir))
    try:
        iieaMergeType = request_data['iieaMergeType']
    except:
        iieaMergeType = None
    try:
        iieaMergeRect = request_data['iieaMergeRect']
    except:
        iieaMergeRect = None
    logger.info(f"{s_time} iieaMergeType:{iieaMergeType} iieaMergeRect:{iieaMergeRect}")

    file_list = []
    src_list = []
    split_mode = 111
    black_height_rate = 0
    black_pos = None
    if len(main_data) == 1:
        try:
            try:
                if Judge_Url and vio_code not in No_Judge_Url_Code:
                    url_num = str(main_data[0]).lower().count("picurl")
                    url_list = [main_data[0]["targetPicUrl"]]
                    # 去除2个url：targetPicUrl、platePicUrl
                    for i in range(url_num - 2):
                        url_list.append(main_data[0]["targetAttrs"][f"vehiclePicUrl{i + 1}"])
                    # 读取图片
                    file_list = []
                    error_urls = []
                    for url in url_list:
                        img = load_image_from_url(url)
                        file_list.append(img)
                        if img is None:
                            error_urls.append(url)
                    file_list = [load_image_from_url(url) for url in url_list]
                    src_list = [img.copy() for img in file_list if img is not None]
                    if any(img is None for img in file_list):
                        logger.debug(f"图片获取不完整,读取情况：url_list:{url_list} error_urls:{error_urls}")
                        file_list = []
                else:
                    img_url = main_data[0]["targetPicUrl"]
                    # img_path = raw_img_dir + '/%s.jpg' % plate_num
                    # urllib.request.urlretrieve(img_url, filename=img_path)
                    # img = cv2.imread(img_path)

                    img = load_image_from_url(img_url)
                    if img is None:
                        return file_list, src_list, split_mode, black_height_rate, black_pos
                    src_list.append(img.copy())

                    # iiea配置缺失
                    if not iieaMergeType or not iieaMergeRect:
                        # 自动切分
                        if SPLIT_DICT[vio_code] is None or (
                                vio_code in ["1625", "1208",
                                             "1357"] and BLACK_HEIGHT is None and SPLIT_DICT[vio_code] in [411, 611,
                                                                                                           621]):
                            t1 = time.time()
                            split_mode, black_height_rate, black_pos = Tools().cut(img, vio_code)

                            auto_split_time = round(time.time() - t1, 3)
                            logger.info(str(dt.datetime.now())[
                                        :-3] + f" 自动切分模块耗时：{auto_split_time} {SPLIT_MODE_DESC[split_mode]} 黑框高度比例：{black_height_rate}")
                            if BLACK_HEIGHT is not None:
                                black_height_rate = BLACK_HEIGHT
                            if vio_code in BLACK_POSITION_DICT.keys():
                                black_pos = BLACK_POSITION_DICT[vio_code]
                            if SPLIT_DICT[vio_code] is not None:
                                split_mode = SPLIT_DICT[vio_code]
                        else:
                            split_mode = SPLIT_DICT[str(vio_code)]
                            black_height_rate = BLACK_HEIGHT
                            if black_height_rate is None:
                                black_height_rate = 0
                            if vio_code in BLACK_POSITION_DICT.keys():
                                black_pos = BLACK_POSITION_DICT[vio_code]
                        # 配置的拼接方式
                        if vio_code in Split_Set_Type:
                            try:
                                cameraIndexCode = main_data[0]["targetAttrs"]["cameraIndexCode"]
                                crossIndexCode = main_data[0]["targetAttrs"]["crossingIndexCode"]
                            except:
                                cameraIndexCode = "camera"
                                crossIndexCode = "cross"
                            if crossIndexCode in Cross_Split_Set.keys():
                                split_mode = Cross_Split_Set[crossIndexCode]
                                logger.info(str(dt.datetime.now())[
                                            :-3] + f" 根据卡口配置修正拼接格式：{split_mode}")
                            elif cameraIndexCode in Camera_Split_Set.keys():
                                split_mode = Camera_Split_Set[cameraIndexCode]
                                logger.info(str(dt.datetime.now())[
                                            :-3] + f" 根据相机配置修正拼接格式：{split_mode}")
                    # iiea配置导入
                    if iieaMergeType:
                        split_mode = int(iieaMergeType)
                    if iieaMergeRect:
                        black_height_rate, black_pos = convert_iiea_rect(iieaMergeRect)
                        logger.info(
                            f"{s_time} iiea配置转换:black_height_rate:{black_height_rate} black_pos:{black_pos}")

                    file_list = img_split(img, n=split_mode, black_height=black_height_rate, black_pos=black_pos)
            except Exception as e:
                error_msg = catch_error()
                traceback.print_exc()
                logger.error(str(dt.datetime.now())[:-3] + f" {e} error_msg:{error_msg}")
            urllib.request.urlcleanup()
        except socket.timeout:
            error_msg = catch_error()
            traceback.print_exc()
            logger.error(str(dt.datetime.now())[:-3] + f" error_msg:{error_msg}")

    return file_list, src_list, split_mode, black_height_rate, black_pos


def calculate(request_data, weights_list, logger, save=False):
    # h = time.localtime()[3]
    # d = time.localtime()[2]
    # UTC时区转换为东八区
    # pro_time = time.strftime(f'%Y-%m-{d + 1 if h + 8 >= 24 else d} {(h + 8) % 24}:%M:%S', time.localtime())
    s_time = dt.datetime.now()
    try:
        uuid = request_data["iieaUUID"]
    except:
        uuid = str(uid.uuid4())
    try:
        main_data = request_data["vehicleAlarmResult"]
        violation_type = str(main_data[0]["targetAttrs"]["alarmType"])
        plate = main_data[0]["target"][0]["vehicle"]["plateNo"]["value"]
    except Exception as e:
        error_msg = catch_error()
        logger.error(str(dt.datetime.now())[:-3] + f" {e}\n{error_msg}")
        request_data["modelJudgeResult"] = {"judgeStatus": 0, "judgeConf": 1, "resultCode": "no_judge_101",
                                            "resultDesc": f"kafka字段解析异常 {error_msg}"}
        return request_data, -1
    file_name = f"{uuid}_{plate}_{violation_type}"

    match_code = None
    judge_type = violation_type[:4] if len(violation_type) > 4 else violation_type
    if judge_type in Vio_Type_Match["1000"]:
        match_code = "1000"
    else:
        for k, v in Vio_Type_Match.items():
            if str(judge_type) in v:
                match_code = k
                break
    if match_code is None:
        request_data["modelJudgeResult"] = {"judgeStatus": 0, "judgeConf": 1, "resultCode": "no_judge_001",
                                            "resultDesc": "不支持的违法类型"}

        return request_data, 0

    save_path = f"{SAVE_RES_DIR}/{match_code}/vis"
    place_type = ""

    logger.info(
        f"{s_time} UUID:{uuid} violation_type:{violation_type} match_code:{match_code} plate_num:{plate}")

    vio_judge = "None"
    judge_infos = {}
    try:
        t1 = time.time()
        file_list, src_list, split_mode, black_height_rate, black_pos = get_images(request_data, match_code, logger)
        t2 = time.time()
        use_time = round(t2 - t1, 2)
        logger.info(f"进程id：{os.getpid()} 获取图片耗时：{use_time}")
        # iiea审核模式配置
        try:
            iieaAuditMode = bool(int(request_data["iieaAuditMode"]))
        except:
            iieaAuditMode = None

        # iiea特殊车辆配置
        try:
            iieaFilterVehicle = [bool(int(i)) for i in request_data["iieaFilterVehicle"].split("#")]
        except:
            iieaFilterVehicle = None

        logger.info(
            f"{s_time} iiea审核模式配置：{iieaAuditMode} iiea白名单车辆类型配置：{iieaFilterVehicle}")
        extra_msg = [split_mode, black_height_rate, black_pos, iieaAuditMode, iieaFilterVehicle]
        # img = cv2.imread("./test.jpg")
        # file_list = img_split(img, 2)
        # src_list = [img.copy()]
        request_data["downloadPicTime"] = use_time
        if file_list == []:
            if No_Pic_To_Waste:
                request_data["modelJudgeResult"] = {"judgeStatus": 2, "judgeConf": 1, "resultCode": "no_pic_waste",
                                                    "resultDesc": f"无图片作废，耗时：{use_time}"}
            else:
                request_data["modelJudgeResult"] = {"judgeStatus": 0, "judgeConf": 1, "resultCode": "no_judge_101",
                                                    "resultDesc": f"违法图片获取失败,耗时：{use_time}"}
            return request_data, -1
        scale = 1

        # 修正图片处理顺序
        try:
            judge_index = PIC_JUDGE_INDEX[match_code]
            if judge_index:
                file_list = [file_list[idx] for idx in judge_index]
        except:
            pass
        try:
            cameraIndexCode = request_data["vehicleAlarmResult"][0]["targetAttrs"]["cameraIndexCode"]
        except:
            cameraIndexCode = ""
        try:
            crossIndexCode = request_data["vehicleAlarmResult"][0]["targetAttrs"]["crossingIndexCode"]
        except:
            crossIndexCode = ""
        if Is_Allowed_Type[str(match_code)] and int(match_code) == 1208:
            if crossIndexCode in Light_Static_Cross or cameraIndexCode in Light_Static_Camera or Precision_Judge_Mode[
                "1208"]:
                high_recall = False
            else:
                high_recall = True
            # iiea强过滤配置
            if iieaAuditMode:
                high_recall = False

            recall = judge_act_conf("recall_mode", match_code, request_data)
            precision = judge_act_conf("precision_mode", match_code, request_data)
            if recall:
                high_recall = True
            elif precision:
                high_recall = False
            vio_judge, judge_infos = \
                judge_12080(plate, file_list, weights_list, resize_scale=scale, merge=False, save=save,
                            save_path=save_path, high_recall=high_recall, extra_msg=extra_msg)

        # 2
        if Is_Allowed_Type[str(match_code)] and int(match_code) == 1625:
            if cameraIndexCode in Set_Light_Direct.keys():
                extra_config = Set_Light_Direct[cameraIndexCode]
            else:
                extra_config = None
            if crossIndexCode in Light_Static_Cross or cameraIndexCode in Light_Static_Camera or Precision_Judge_Mode[
                "1625"]:
                high_recall = False
            else:
                high_recall = True
            # iiea强过滤配置
            if iieaAuditMode:
                high_recall = False
            recall = judge_act_conf("recall_mode", match_code, request_data)
            precision = judge_act_conf("precision_mode", match_code, request_data)
            if recall:
                high_recall = True
            elif precision:
                high_recall = False
            print("high_recall:", high_recall)

            vio_judge, judge_infos = \
                judge_16251(plate, file_list, weights_list, resize_scale=scale, merge=False, save=save,
                            save_path=save_path, extra_config=extra_config, high_recall=high_recall,
                            extra_msg=extra_msg)
        # 3
        if Is_Allowed_Type[str(match_code)] and int(match_code) == 1345:
            vio_judge, judge_infos = \
                judge_13450(plate, file_list, weights_list, resize_scale=scale, save=save,
                            save_path=save_path, extra_msg=extra_msg)

        # 4
        if Is_Allowed_Type[str(match_code)] and int(match_code) == 1301:
            is_highway = False
            if place_type in ["高速", "匝道"]:
                is_highway = True
            vio_judge, judge_infos = \
                judge_13010(plate, file_list, weights_list, resize_scale=scale, merge=True, save=save,
                            save_path=save_path, highway=is_highway, extra_msg=extra_msg)
        # 5
        if Is_Allowed_Type[str(match_code)] and int(match_code) == 1352:
            vio_judge, judge_infos = \
                judge_13522(plate, file_list, weights_list, resize_scale=scale, save=save,
                            save_path=save_path, extra_msg=extra_msg)
        # 6
        if Is_Allowed_Type[str(match_code)] and int(match_code) == 1357:
            vio_judge, judge_infos = \
                judge_13570(plate, file_list, weights_list, resize_scale=scale, save=save,
                            save_path=save_path, extra_msg=extra_msg)
        # 7
        if Is_Allowed_Type[str(match_code)] and int(match_code) == 7102:
            vio_judge, judge_infos = \
                judge_71020(plate, file_list, weights_list, resize_scale=scale, save=save,
                            save_path=save_path, extra_msg=extra_msg)
        # 8
        if Is_Allowed_Type[str(match_code)] and int(match_code) == 8013:
            vio_judge, judge_infos = \
                judge_80130(plate, file_list, weights_list, resize_scale=scale, save=save,
                            save_path=save_path, extra_msg=extra_msg)
        # 9,10
        if Is_Allowed_Type[str(match_code)] and int(match_code) in [1223, 1101]:
            judge_type = violation_type[:4] if len(violation_type) > 4 else violation_type
            vio_judge, judge_infos = \
                judge_12230_11010([match_code, judge_type], plate, file_list, weights_list, resize_scale=scale,
                                  save=save,
                                  save_path=save_path, extra_msg=extra_msg)
        # 11
        if Is_Allowed_Type[str(match_code)] and int(match_code) == 1039:
            vio_judge, judge_infos, score_list = \
                judge_10390(plate, file_list, weights_list, save=save, save_path=save_path, extra_msg=extra_msg)
            judge_infos["extra"] = str(score_list)
        # 12
        if Is_Allowed_Type[str(match_code)] and int(match_code) == 1018:
            vio_judge, judge_infos = \
                judge_10180(plate, file_list, weights_list, resize_scale=scale, save=IS_SAVE_VIS_PIC,
                            save_path=f"{SAVE_RES_DIR}/{match_code}/vis", extra_msg=extra_msg)
        # 13
        if Is_Allowed_Type[str(match_code)] and int(match_code) == 1000:
            vio_judge, judge_infos = \
                judge_10000(plate, file_list, weights_list, resize_scale=scale, save=IS_SAVE_VIS_PIC,
                            save_path=f"{SAVE_RES_DIR}/{match_code}/vis", extra_msg=extra_msg)
        if vio_judge.startswith('vio'):
            request_data["modelJudgeResult"]["judgeStatus"] = 1
        elif vio_judge == "None":
            request_data["modelJudgeResult"] = {"judgeStatus": 0, "judgeConf": 1, "resultCode": "no_judge_001",
                                                "resultDesc": "未配置使用的违法类型"}
        else:
            request_data["modelJudgeResult"]["judgeStatus"] = 2
        if judge_infos:
            request_data["modelDetectResults"] = judge_infos["modelDetectResults"]

        tmp_status = request_data["modelJudgeResult"]["judgeStatus"]
        try:
            if tmp_status != 0:
                if vio_judge in JudgeResult[match_code].keys():
                    request_data["modelJudgeResult"] = {"judgeStatus": tmp_status,
                                                        "judgeConf": float(judge_infos["judgeConf"]),
                                                        "resultCode": vio_judge,
                                                        "resultDesc": JudgeResult[match_code][vio_judge]}
                elif match_code == "1039" and request_data["modelJudgeResult"]["judgeStatus"] == 2:
                    request_data["modelJudgeResult"] = {"judgeStatus": tmp_status,
                                                        "judgeConf": float(judge_infos["judgeConf"]),
                                                        "resultCode": vio_judge,
                                                        "resultDesc": "废片，每张图车牌匹配结果(0:未匹配,1:模糊匹配,2:精确匹配):" +
                                                                      vio_judge.split("-")[-1]}
                else:
                    request_data["modelJudgeResult"] = {"judgeStatus": tmp_status,
                                                        "judgeConf": float(judge_infos["judgeConf"]),
                                                        "resultCode": vio_judge,
                                                        "resultDesc": vio_judge}
            if not Judge_Url or match_code in No_Judge_Url_Code:
                request_data["modelJudgeResult"]["resultDesc"] += f"(图片切分方式：{SPLIT_MODE_DESC[split_mode]})"
        except Exception as e:
            error_msg = catch_error()
            logger.error(time.strftime("%Y-%m-%d %H:%M:%S", time.localtime()) +
                         f" 返回模型审核结果错误\n{error_msg}")
    except Exception as e:
        # print('=========Error!====={}=={}=========='.format(violation_type, plate_num), e,
        #       e.__traceback__.tb_frame.f_globals["__file__"], e.__traceback__.tb_lineno)
        error_msg = catch_error()
        logger.error(time.strftime("%Y-%m-%d %H:%M:%S", time.localtime()) +
                     f" UUID:{uuid} violation_type:{violation_type} plate_num:{plate} {e}\n{error_msg}")
        traceback.print_exc()
        request_data["modelJudgeResult"] = {"judgeStatus": 0, "judgeConf": 1, "resultCode": "no_judge_101",
                                            "resultDesc": f"模型处理异常 {error_msg}"}

        return request_data, -1
    e_time = dt.datetime.now()
    time_delta = round((e_time - s_time).seconds, 2)
    # tt_time += time_delta
    # print(time.strftime("%Y-%m-%d %H:%M:%S", time.localtime()), f' {session_id} use time', time_delta, '\n', res_infos,
    #       "\n\n", "-" * 50, "\n")
    logger.info(time.strftime("%Y-%m-%d %H:%M:%S",
                              time.localtime()) + f' UUID:{uuid} use time:{time_delta}\njudge_infos:{judge_infos}')
    # res_infos = json.dumps(res_infos)

    # 保存判罚结果图片
    try:
        pred_result = f"{SAVE_RES_DIR}/{match_code}/vio" if vio_judge.startswith(
            "vio") else f"{SAVE_RES_DIR}/{match_code}/no_vio"
        os.makedirs(pred_result, exist_ok=True)
        try:
            cameraIndexCode = request_data["vehicleAlarmResult"][0]["targetAttrs"]["cameraIndexCode"]
            cross_index = request_data["vehicleAlarmResult"][0]["targetAttrs"]["crossingIndexCode"]
        except:
            cameraIndexCode = "camera"
            cross_index = "cross"

        if len(src_list) == 1:
            cv2.imwrite(f"{pred_result}/{cross_index}_{cameraIndexCode}_{uuid}_{plate}_{violation_type}.jpg",
                        src_list[0])
        elif len(src_list) > 1:
            for idx, img in enumerate(src_list):
                cv2.imwrite(
                    f"{pred_result}/{cross_index}_{cameraIndexCode}_{uuid}_{plate}_{violation_type}_{idx + 1}.jpg", img)
        if len(file_list) == 0:
            print(time.strftime("%Y-%m-%d %H:%M:%S", time.localtime()), f" {uuid} 图片为空！")

        try:
            src_vio_num = len(os.listdir(f"{SAVE_RES_DIR}/{match_code}/vio"))
        except:
            src_vio_num = 0
        try:
            src_no_vio_num = len(os.listdir(f"{SAVE_RES_DIR}/{match_code}/no_vio"))
        except:
            src_no_vio_num = 0
        try:
            if (src_vio_num + src_no_vio_num) > Max_Save_Num:
                # 清理保存的图片
                shutil.rmtree(f"{SAVE_RES_DIR}/{match_code}")
        except Exception as e:
            error_msg = catch_error()
            logger.error(time.strftime("%Y-%m-%d %H:%M:%S", time.localtime()) +
                         f" UUID:{uuid} match_code:{match_code} plate_num:{plate} {e}\n{error_msg} {match_code}图片清理失败！")
    except Exception as e:
        error_msg = catch_error()
        logger.error(time.strftime("%Y-%m-%d %H:%M:%S", time.localtime()) +
                     f" UUID:{uuid} match_code:{match_code} plate_num:{plate} {e}\n{error_msg} 保存图片失败！")
    return request_data, file_name


def to_timestamp(time_t):
    str_time = time_t.replace("T", " ").split("+")[0]
    date_time = dt.datetime.strptime(str_time, "%Y-%m-%d %H:%M:%S.%f")
    return date_time.timestamp()


def main(i, partitions, num_partitions):
    logging.config.dictConfig(get_logging("kafka_main"))
    logger = logging.getLogger("console_plain_file_logger")
    torch.cuda.set_device(i)
    set_partition = [i.partition for i in partitions]
    PID = os.getpid()
    # 初始化所有模型
    if Is_Allowed_Type["1223"]:
        phone = True
    else:
        phone = False
    weights_list = init_models(traffic=True, lp=True, yolov7=True, trace=False, phone=phone, vio_model=True, cnn=True,
                               init_device=i)
    # 心跳
    try:
        if Multi_Num[0] > 1 and torch.cuda.device_count() > 1:
            if i in ["cuda:1", "cpu"]:
                hp = Process(target=heartbeat)
                hp.daemon = True
                hp.start()
                torch.cuda.is_available()
        else:
            hp = Process(target=heartbeat)
            hp.daemon = True
            hp.start()
    except:
        hp = Process(target=heartbeat)
        hp.daemon = True
        hp.start()
    # 初始化保存路径
    save = IS_SAVE_VIS_PIC
    # 请求消费者
    while True:
        try:
            session_timeout_ms: int = Max_Poll_Interval_Ms  # int(Max_Poll_Interval_Ms * 1.5) #部分项目kafka客户端配置不允许
            consumer = KafkaConsumer(
                bootstrap_servers=SERVER,
                group_id=GROUP_ID,
                auto_offset_reset=AUTO_OFFSET_RESET, client_id=CLIENT_ID,
                max_poll_interval_ms=Max_Poll_Interval_Ms,
                request_timeout_ms=int(Max_Poll_Interval_Ms * 1.6) + 1000,
                session_timeout_ms=session_timeout_ms,
                connections_max_idle_ms=int(Max_Poll_Interval_Ms * 1.6) + 5000,
                enable_auto_commit=False, max_poll_records=Max_Poll_Records,
                metadata_max_age_ms=Max_Poll_Interval_Ms,
                heartbeat_interval_ms=session_timeout_ms // 3,
                max_partition_fetch_bytes=5242880)
            break
        except Exception as e:
            error_msg = catch_error()
            logger.error(time.strftime("%Y-%m-%d %H:%M:%S\n", time.localtime()) +
                         error_msg)
            time.sleep(Heart_Interval)

    start_time = time.time()
    park_num = 0
    # 模型结果生产者
    producer = KafkaProducer(bootstrap_servers=SERVER, value_serializer=lambda m: json.dumps(m).encode('utf-8'),
                             client_id=CLIENT_ID, retries=5, retry_backoff_ms=500)

    # 手动分配分区
    consumer.assign(partitions)
    logger.info(time.strftime("%Y-%m-%d %H:%M:%S\n",
                              time.localtime()) + f" PID:{PID} 消费已就绪！总分区数：{num_partitions}，消费分区：{set_partition} GPU:{i[-1]}")

    while True:
        try:
            # 拉取消息
            records = consumer.poll(timeout_ms=1000)
        except Exception as e:
            error_msg = catch_error()
            logger.error(time.strftime("%Y-%m-%d %H:%M:%S", time.localtime()) +
                         f"{e}\n{error_msg}拉取消息异常!")
            records = {}
        poll_st = time.time()
        for tp, messages in records.items():
            for msg in messages:
                is_online = True  # 是否上线
                try:
                    topic_partition = TopicPartition(msg.topic, msg.partition)
                    offset = OffsetAndMetadata(msg.offset + 1, metadata="")
                except Exception as e:
                    error_msg = catch_error()
                    logger.error(time.strftime("%Y-%m-%d %H:%M:%S", time.localtime()) +
                                 f"{e}\n{error_msg}Topic分区和偏移量初始化异常!")
                    continue

                try:
                    request_dict = json.loads(msg.value)
                except Exception as e:
                    error_msg = catch_error()
                    logger.error(time.strftime("%Y-%m-%d %H:%M:%S", time.localtime()) +
                                 f"{e}\n{error_msg}数据格式错误!")
                    continue
                try:
                    s_id = request_dict["iieaUUID"]
                except:
                    s_id = str(uid.uuid4())
                try:
                    cross_index = request_dict["vehicleAlarmResult"][0]["targetAttrs"]["crossingIndexCode"]
                    cameraIndexCode = request_dict["vehicleAlarmResult"][0]["targetAttrs"]["cameraIndexCode"]
                except:
                    cross_index = ""
                    cameraIndexCode = ""
                try:
                    plate_color = request_dict["vehicleAlarmResult"][0]["target"][0]["vehicle"]["plateColor"]["value"]
                except:
                    plate_color = ""

                save_name_list = []
                # try:
                #     save_name_list = os.listdir(f"{SAVE_RES_DIR}/1625/vio") if os.path.exists(
                #         f"{SAVE_RES_DIR}/1625/vio") else [] + os.listdir(f"{SAVE_RES_DIR}/1625/no_vio") if os.path.exists(
                #         f"{SAVE_RES_DIR}/1625/no_vio") else []
                #     save_name_list = [i.split("_")[0] for i in save_name_list]
                # except:
                #     pass
                try:
                    send_time = request_dict["sendTime"]
                except Exception as e:
                    logger.info(time.strftime("%Y-%m-%d %H:%M:%S", time.localtime()) +
                                "缺少sendTime字段或结构特殊!")
                    send_time = dt.datetime.now().strftime("%Y-%m-%dT%H:%M:%S.%f")[
                                :-3] + "+08:00"
                try:
                    logger.info(
                        f"\nUUID:{s_id} PID:{PID} partition:{msg.partition} 违法原始数据：{request_dict}")
                    request_dict["modelRecvTime"] = dt.datetime.now().strftime("%Y-%m-%dT%H:%M:%S.%f")[:-3] + "+08:00"
                    consumer_delay = to_timestamp(request_dict["modelRecvTime"]) - to_timestamp(send_time)
                    plate = request_dict["vehicleAlarmResult"][0]["target"][0]["vehicle"]["plateNo"]["value"]
                    violation_type = str(request_dict["vehicleAlarmResult"][0]["targetAttrs"]["alarmType"])
                    file_name = f"{s_id}_{plate}_{violation_type}"
                    t1 = time.time()
                    # 去重
                    # if img_url in url_list:
                    #    logger.info(f"UUID:{s_id} 重复数据:{request_dict}")
                    #    request_dict["modelJudgeStatus"] = 3  # 重复
                    # else:
                    # url_list.append(img_url)

                    match_code = None
                    judge_type = violation_type[:4] if len(violation_type) > 4 else violation_type
                    if judge_type in Vio_Type_Match["1000"]:
                        match_code = "1000"
                    else:
                        for k, v in Vio_Type_Match.items():
                            if str(judge_type) in v:
                                match_code = k
                                break
                    request_dict["modelVioCode"] = match_code
                    request_dict["modelDetectResults"] = []
                    request_dict["modelJudgeResult"] = {"judgeStatus": 0, "judgeConf": 1, "resultCode": "",
                                                        "resultDesc": ""}
                    # iiea警车配置
                    try:
                        police_filter = bool(int(request_dict["iieaFilterVehicle"].split("#")[0]))
                    except:
                        police_filter = None
                    # iiea消防车配置
                    try:
                        fire_filter = bool(int(request_dict["iieaFilterVehicle"].split("#")[2]))
                    except:
                        fire_filter = None
                    try:
                        dataSource = str(request_dict["dataSource"])
                    except:
                        dataSource = None
                    # 修改号牌类型
                    try:
                        plate_type = request_dict["vehicleAlarmResult"][0]["target"][0]["vehicle"]["plateType"]
                        if Convert_Plate_Type and len(plate) == 8 and (plate_type == "" or plate_type == "unknown"):
                            request_dict["vehicleAlarmResult"][0]["target"][0]["vehicle"]["plateType"] = "newEnergy"
                            logger.info("已修改号牌类型为新能源'newEnergy'")
                    except:
                        pass
                    if match_code is None:
                        request_dict["modelJudgeResult"] = {"judgeStatus": 0, "judgeConf": 1,
                                                            "resultCode": "no_judge_001",
                                                            "resultDesc": "不支持的违法类型"}
                    elif match_code == "8013" and Filter_Itrc and dataSource != "vps_manual":
                        request_dict["modelJudgeResult"] = {"judgeStatus": 0, "judgeConf": 1,
                                                            "resultCode": "no_judge_001",
                                                            "resultDesc": "货车闯禁行场景不处理非itrc数据"}
                    elif plate and plate[-1] == "警" and (
                            (Police_Plate_Thresh >= 0 and police_filter is None) or police_filter):
                        request_dict["modelJudgeResult"]["judgeStatus"] = 2
                        request_dict["modelJudgeResult"]["resultCode"] = "no_vio_010"
                        request_dict["modelJudgeResult"]["resultDesc"] = "警车过滤，抓拍设备识别的车牌结尾字符为'警'"
                    elif plate and plate.endswith("应急") and len(plate) >= 2 and plate[1] in ["X", "S"] and (
                            ("fire_engine_b" in Special_Type or "fire_engine_h" in Special_Type) or fire_filter):
                        request_dict["modelJudgeResult"]["judgeStatus"] = 2
                        request_dict["modelJudgeResult"]["resultCode"] = "no_vio_010"
                        request_dict["modelJudgeResult"][
                            "resultDesc"] = f"消防车过滤，抓拍设备识别的车牌结尾字符为'应急'且第二位为{plate[1]}"
                    # 电警杆数据特殊动作配置
                    elif judge_act_conf("no_judge", match_code, request_dict):
                        request_dict["modelJudgeResult"] = {"judgeStatus": 0, "judgeConf": 1.0,
                                                            "resultCode": "alarm_pole",
                                                            "resultDesc": f"配置数据不做处理（{Code_Cross_Direction_Conf['no_judge']}）"}
                    elif judge_act_conf("judge_vio", match_code, request_dict):
                        request_dict["modelJudgeResult"] = {"judgeStatus": 1, "judgeConf": 1.0,
                                                            "resultCode": "alarm_pole",
                                                            "resultDesc": f"配置数据统一违法（{Code_Cross_Direction_Conf['judge_vio']}）"}
                    elif judge_act_conf("judge_waste", match_code, request_dict):
                        request_dict["modelJudgeResult"] = {"judgeStatus": 2, "judgeConf": 1.0,
                                                            "resultCode": "alarm_pole",
                                                            "resultDesc": f"配置数据统一作废（{Code_Cross_Direction_Conf['judge_waste']}）"}

                    # 过滤废片率高的相机数据
                    elif match_code in Filter_Waste_Camera.keys() and cameraIndexCode in Filter_Waste_Camera[
                        match_code]:
                        request_dict["modelJudgeResult"] = {"judgeStatus": 2, "judgeConf": 0.9,
                                                            "resultCode": "no_vio_011",
                                                            "resultDesc": "根据配置参数Filter_Waste_Camera过滤废片率高的相机数据"}
                    elif consumer_delay > Max_Delay_Time > 0:
                        logger.info(f"{s_id}_{plate}_{violation_type}——超时数据不做处理！")
                        request_dict["modelJudgeResult"] = {"judgeStatus": 0, "judgeConf": 1,
                                                            "resultCode": "no_judge_003",
                                                            "resultDesc": "超时未处理的数据"}

                        file_name = f"{s_id}_{plate}_{violation_type}"
                        if match_code == "1039":
                            park_num += 1
                    elif match_code == "1625":
                        if cross_index in Light_Filter_Cross or cameraIndexCode in Light_Filter_Camera:
                            request_dict["modelJudgeResult"] = {"judgeStatus": 0, "judgeConf": 1,
                                                                "resultCode": "no_judge_004",
                                                                "resultDesc": "特殊数据不分析透传"}

                        elif (isinstance(Online_Cross_1625,
                                         list) and cross_index in Online_Cross_1625) or Online_Cross_1625 == "all":
                            logger.info(f"{s_id}_{plate}_{violation_type}——正在调用模型，等待判罚结果...")
                            request_dict, file_name = calculate(request_dict, weights_list, logger, save=save)
                        elif (isinstance(Test_Cross_1625,
                                         list) and cross_index in Test_Cross_1625) or Test_Cross_1625 == "all":
                            logger.info(f"{s_id}_{plate}_{violation_type}——正在调用模型，等待判罚结果...")
                            request_dict, file_name = calculate(request_dict, weights_list, logger, save=save)
                            is_online = False
                        elif isinstance(Max_Camera_Num, int) and save_name_list.count(
                                cameraIndexCode) > Max_Camera_Num > 0:
                            request_dict["modelJudgeResult"] = {"judgeStatus": 0, "judgeConf": 1,
                                                                "resultCode": "no_judge_002",
                                                                "resultDesc": "闯红灯场景相机编号超限不处理的数据"}
                        else:
                            request_dict["modelJudgeResult"] = {"judgeStatus": 0, "judgeConf": 1,
                                                                "resultCode": "no_judge_002",
                                                                "resultDesc": "闯红灯场景卡口配置外不处理的数据"}
                    else:
                        if match_code == "1039":
                            park_num += 1
                            if park_num >= Vio_Park_Rate:
                                logger.info(f"{s_id}_{plate}_{violation_type}——正在调用模型，等待判罚结果...")
                                request_dict, file_name = calculate(request_dict, weights_list, logger, save=save)
                                park_num = 0
                            else:
                                request_dict["modelJudgeResult"] = {"judgeStatus": 0, "judgeConf": 1,
                                                                    "resultCode": "no_judge_002",
                                                                    "resultDesc": "违停场景按比例过滤不处理的数据"}
                        elif match_code == "8013":
                            if Filter_Itrc:
                                logger.info(f"{s_id}_{plate}_{violation_type}——正在调用模型，等待判罚结果...")
                                request_dict, file_name = calculate(request_dict, weights_list, logger, save=save)
                            elif plate_color in ["yellow", "黄", "黄色"] and len(plate) < 8:
                                logger.info(f"{s_id}_{plate}_{violation_type}——正在调用模型，等待判罚结果...")
                                request_dict, file_name = calculate(request_dict, weights_list, logger, save=save)
                            else:

                                request_dict["modelJudgeResult"] = {"judgeStatus": 0, "judgeConf": 1,
                                                                    "resultCode": "no_judge_002",
                                                                    "resultDesc": "闯禁行场景过滤不处理的数据(非黄牌或车牌长度大于7)"}
                        else:
                            logger.info(f"{s_id}_{plate}_{violation_type}——正在调用模型，等待判罚结果...")
                            request_dict, file_name = calculate(request_dict, weights_list, logger, save=save)
                    t2 = time.time()
                    model_use_time = t2 - t1
                    request_dict["modelSendTime"] = dt.datetime.now().strftime("%Y-%m-%dT%H:%M:%S.%f")[:-3] + "+08:00"
                    Delay_Time = to_timestamp(request_dict["modelSendTime"]) - to_timestamp(send_time)
                    modelRecvTime = request_dict["modelRecvTime"]
                    modelSendTime = request_dict["modelSendTime"]
                    if request_dict["modelJudgeResult"]["judgeStatus"] in [1, 2]:
                        logger.debug(
                            f"UUID:{s_id} PID:{PID} 违法类型：{violation_type} sendTime:{send_time} modelRecvTime:{modelRecvTime} modelSendTime:{modelSendTime} model_use_time:{model_use_time} delay_time:{int(Delay_Time)}")

                    if request_dict["modelJudgeResult"]["judgeStatus"] in FILTER_STATUS or is_online:
                        show_data = json.dumps(request_dict, ensure_ascii=False, indent=2)
                        logger.info(f"\nUUID:{s_id} PID:{PID} 模型推送数据：{show_data}")
                        try:
                            producer.send(MODEL_RES_TOPIC, request_dict)
                            logger.info(f"{file_name}的判罚结果已推送至缓冲区!")
                        except:
                            logger.error(f"{file_name}消息推送至缓冲区失败！")
                    elif request_dict["modelJudgeResult"]["resultCode"] != "no_judge_001":
                        logger.info(f"UUID:{s_id} 模型未审核的数据：{request_dict}")
                except Exception as e:
                    error_msg = catch_error()
                    logger.error(time.strftime("%Y-%m-%d %H:%M:%S", time.localtime()) +
                                 f"{e}\n{error_msg}模型调用过程出错!")
                    request_dict["modelJudgeResult"] = {"judgeStatus": 0, "judgeConf": 1, "resultCode": "no_judge_101",
                                                        "resultDesc": f"模型处理异常  {error_msg}"}
                    logger.info(f"UUID:{s_id} 模型未审核的数据：{request_dict}")
                    file_name = ""
                    try:
                        if request_dict["modelJudgeResult"]["judgeStatus"] in FILTER_STATUS or is_online:
                            producer.send(MODEL_RES_TOPIC, request_dict)
                    except Exception as e:
                        error_msg = catch_error()
                        logger.error(time.strftime("%Y-%m-%d %H:%M:%S", time.localtime()) +
                                     f"{e}\n{error_msg}模型推送异常消息出错!")

                try:
                    if request_dict["modelJudgeResult"]["judgeStatus"] in [1, 2]:
                        producer.flush()
                        logger.info(f"PID:{PID} {file_name}结果已推送至Topic！")
                except Exception as e:
                    error_msg = catch_error()
                    logger.error(time.strftime("%Y-%m-%d %H:%M:%S", time.localtime()) +
                                 f"{e}\n{error_msg}缓冲区消息推送至Topic出错!")
                try:
                    # 手动提交偏移量
                    consumer.commit({topic_partition: offset})
                except Exception as e:
                    error_msg = catch_error()
                    logger.error(time.strftime("%Y-%m-%d %H:%M:%S", time.localtime()) +
                                 f"{e}\n{error_msg}topic offset update error!")
                    retry_commit(consumer, {topic_partition: offset}, logger)
                try:
                    if os.path.exists("./logs/log.txt") and (
                            os.path.getsize("./logs/log.txt") / 1024 / 1024) > LOG_MAX_SIZE:
                        with open("./logs/log.txt", "w", encoding="utf-8") as f:
                            f.close()
                except Exception as e:
                    logger.error(time.strftime("%Y-%m-%d %H:%M:%S", time.localtime()) +
                                 f"{e} {e.__traceback__.tb_lineno}\nout日志初始化失败！")
                if time.time() - start_time > SET_RUN_TIME > 0:
                    consumer.close()
                # torch.cuda.empty_cache()
        poll_et = time.time()
        if records != {}:
            logger.info(time.strftime("%Y-%m-%d %H:%M:%S", time.localtime()) +
                        f"处理完拉取消息总耗时：{poll_et - poll_st:.2f}S max_poll_interval_ms：{Max_Poll_Interval_Ms / 1000}S\n")


# 重试提交偏移量
def retry_commit(consumer, offsets, logger, max_retries=3):
    retries = 0
    while retries < max_retries:
        try:
            consumer.commit(offsets)
            break
        except Exception as e:
            logger.info(f"偏移量提交第{retries + 1}次重试失败! {e}")
            retries += 1
        time.sleep(0.1)
    if retries == max_retries:
        logger.error(f"重试{max_retries}次提交偏移量失败！")


def heartbeat():
    while True:
        try:
            req = requests.get(f"https://{TPC_IP}/iiea-web/service/rs/heartbeat/v1/beat", verify=False, timeout=3)
            now = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())
            mode = "w" if os.path.exists("./logs/heart.txt") and (
                    os.path.getsize("./logs/heart.txt") / 1024 / 1024) > 10 else "a"
            try:
                with open("./logs/heart.txt", mode, encoding="utf-8") as heart_log:
                    heart_log.write(f"{now} 状态码：{req.status_code} 返回数据：{req.text}\n")
            except:
                pass
        except Exception as e:
            error_msg = catch_error()
            now = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())
            mode = "w" if os.path.exists("./logs/heart.txt") and (
                    os.path.getsize("./logs/heart.txt") / 1024 / 1024) > 10 else "a"
            with open("./logs/heart.txt", mode, encoding="utf-8") as heart_log:
                heart_log.write(f"{now} {error_msg} 心跳机制异常!\n")
        time.sleep(Heart_Interval)


def get_topic_partitions():
    while True:
        try:
            consumer = KafkaConsumer(bootstrap_servers=SERVER, client_id=CLIENT_ID)
            partitions = consumer.partitions_for_topic(VIO_DATA_TOPIC)
            consumer.close()
            break
        except Exception as e:
            error_msg = catch_error()
            print(error_msg)
            time.sleep(Heart_Interval)
    if partitions is None:
        print(f"TOPIC不存在，请先创建{VIO_DATA_TOPIC}")
        time.sleep(Heart_Interval)
        get_topic_partitions()
    return [TopicPartition(VIO_DATA_TOPIC, p) for p in partitions]


if __name__ == '__main__':
    partitions = get_topic_partitions()
    num_partitions = len(partitions)
    processes = []
    if torch.cuda.is_available():
        multiprocessing.set_start_method("spawn", force=True)
        if isinstance(Multi_Num, list) and len(Multi_Num) == 2:
            GPU_NUM = Multi_Num[0] if torch.cuda.device_count() >= Multi_Num[0] else torch.cuda.device_count()
            Per_Multi = Multi_Num[1]
            num_consumers = GPU_NUM * Per_Multi
            partitions_per_consumer = num_partitions // num_consumers
            extra_partitions = num_partitions % num_consumers
            for i in range(GPU_NUM):
                for j in range(Per_Multi):
                    idx = i * Per_Multi + j
                    if idx >= num_partitions:
                        print(f"总分区数：{num_partitions} 多余的进程数：{idx + 1}")
                        continue
                    start = idx * partitions_per_consumer
                    end = (idx + 1) * partitions_per_consumer
                    process_partitions = partitions[start:end] + partitions[
                                                                 num_consumers * partitions_per_consumer + idx:num_consumers * partitions_per_consumer + idx + 1] if idx < extra_partitions else partitions[
                                                                                                                                                                                                 start:end]
                    p = Process(target=main, args=(f"cuda:{i}", process_partitions, num_partitions,))
                    p.start()
                    processes.append(p)
        else:
            GPU_NUM = torch.cuda.device_count()
            num_consumers = GPU_NUM
            partitions_per_consumer = num_partitions // num_consumers
            extra_partitions = num_partitions % num_consumers
            for i in range(GPU_NUM):
                start = i * partitions_per_consumer
                end = (i + 1) * partitions_per_consumer
                process_partitions = partitions[start:end] + partitions[
                                                             num_consumers + i:num_consumers + i + 1] if i < extra_partitions else partitions[
                                                                                                                                   start:end]
                p = Process(target=main, args=(f"cuda:{i}", process_partitions, num_partitions,))
                p.start()
                processes.append(p)

    elif isinstance(Multi_Num, list) and len(Multi_Num) == 2:
        num_consumers = Multi_Num[1]
        partitions_per_consumer = num_partitions // num_consumers
        extra_partitions = num_partitions % num_consumers
        for i in range(Multi_Num[1]):
            start = i * partitions_per_consumer
            end = (i + 1) * partitions_per_consumer
            process_partitions = partitions[start:end] + partitions[
                                                         num_consumers + i:num_consumers + i + 1] if i < extra_partitions else partitions[
                                                                                                                               start:end]
            p = Process(target=main, args=("cpu", process_partitions, num_partitions,))
            p.start()
            processes.append(p)
    # 监控子进程状态
    while True:
        try:
            is_alive = [f"{p.pid}:{p.is_alive()}" for p in processes]
            save_status = " ".join(is_alive)
            now = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())
            mode = "w" if os.path.exists("./logs/status.txt") and (
                    os.path.getsize("./logs/status.txt") / 1024 / 1024) > 10 else "a"
            with open("./logs/status.txt", mode, encoding="utf-8") as status_log:
                status_log.write(f"{now} 进程状态：{save_status}\n")
        except:
            pass
        time.sleep(Heart_Interval)
