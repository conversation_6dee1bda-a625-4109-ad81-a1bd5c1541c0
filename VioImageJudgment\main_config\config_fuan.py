import pandas as pd

# **********************************************************
# 授权模式 iiea/MR
Sign_Mode = "iiea"
# 模型仓库授权
IP = ""
AppKey = ""
AppSecret = ""
# 授权配置TPC的IP和端口
TPC_IP = "**********:443"
# **********************************************************
# 违法图片拼接方式
BLACK_HEIGHT = 0  # 黑框高度的占比,为None时通过自动切分计算高度
# up:黑框在图片顶部 down:黑框在图片底部
BLACK_POSITION_DICT = {
    # 格式示例："1625":"up",

}
SPLIT_MODE_DESC = {111: "单图无拼接", 211: "2图横向拼接", 221: "2图竖向拼接", 311: "3图横向拼接", 312: "倒品字型",
                   313: "品字型", 321: "3图竖向拼接", 322: "品字型顺时针旋转90度", 323: "品字型逆时针旋转90度",
                   411: "4图横向拼接", 611: "6图横向拼接", 621: "6图竖向拼接", None: "自动切分"}
# 以下为审片模型内部违法代码，无需修改
SPLIT_DICT = {
    "1625": 411,  # 闯红灯
    "1208": 411,  # 不按导向行驶
    "1345": 411,  # 违反禁止标示线
    "1301": 411,  # 逆行
    "1357": 411,  # 不礼让行人
    "1352": None,  # 超速
    "1039": 411,  # 违法停车
    "1101": 411,  # 开车未系安全带
    "1223": 411,  # 开车打电话
    "8013": None,  # 闯限行
    "7102": None,  # 占用公交车道
    "1018": None,  # 占用非机动车道
    "1000": 411,  # 过滤两轮三轮车
}
# 根据违法数据中url的数量判断是否为合成图(默认不开启)
Judge_Url = False
# 不适用Judge_Url判定的违法代码
No_Judge_Url_Code = []
# 设定卡口的拼接方式："卡口内码":611,
Cross_Split_Set = {

}
# 设定相机的拼接方式："相机内码":611,
Camera_Split_Set = {

}
# 设定拼接方式生效的违法代码
Split_Set_Type = ["1625", "1208"]

# 图片处理序号，主要关注闯红灯1625、不按导向1208、不礼让行人1357三个场景
PIC_JUDGE_INDEX = {"1625": [], "1208": [], "1357": [],
                   "1301": [], "1345": [], "1352": [],
                   "1039": [], "1101": [], "1223": [],
                   "8013": [], "7102": [], "1018": [],
                   "1000": []}
# 各违法类型审核模式 (0:普通模式（少遗漏正片）   1：强过滤模式（多过滤废片）)
Precision_Judge_Mode = {
    "1625": 0,  # 闯红灯
    "1208": 0,  # 不按导向行驶
    "1345": 1,  # 违反禁止标示线
    "1301": 0,  # 逆行
    "1357": 0,  # 不礼让行人
    "1352": 0,  # 超速
    "1039": 0,  # 违法停车
    "1101": 0,  # 开车未系安全带
    "1223": 0,  # 开车打电话
    "8013": 0,  # 闯限行
    "7102": 0,  # 占用公交车道
    "1018": 0,  # 占用非机动车道
    "1000": 0,  # 过滤两轮三轮车
}
# 各违法类型两种模式的判罚置信度阈值 [高检出模式阈值,强过滤模式阈值]
Judge_Thresh = {
    "1625": [None, None],  # 闯红灯
    "1208": [None, None],  # 不按导向行驶
    "1345": [[0.25, 15], [0.55, 5]],  # 违反禁止标示线([[交通标志模型阈值,车辆与地标线的像素距离],])
    "1301": [None, None],  # 逆行
    "1357": [2, 1.5],  # 不礼让行人(车辆与行人横向距离小于车宽的n倍)
    "1352": [[3, 2], [4, 1]],  # 超速(车牌模糊匹配阈值)
    "1039": [[3, 2], [4, 1]],  # 违法停车(车牌模糊匹配阈值)
    "1101": [0.25, 0.92],  # 开车未系安全带(模型阈值)
    "1223": [0.3, 0.75],  # 开车打电话(模型阈值)
    "8013": [[0.2, 0.2], [0.35, 0.5]],  # 闯限行([[yolov7置信度阈值,blendMask置信度阈值],])
    "7102": [0.2, 0.45],  # 占用公交车道(yolov7置信度阈值)
    "1018": [0.2, 0.5],  # 占用非机动车道(blendMask置信度阈值)
    '1000': [[0.2, 0.2], [0.35, 0.45]],  # 过滤两轮三轮车([[yolov7置信度阈值,blendMask置信度阈值],])
}

# 使用基础版本的相机编号(闯红灯和不按导向)
Light_Static_Camera = [

]
# 使用基础版本的卡口编号(闯红灯和不按导向)
Light_Static_Cross = [

]
# 闯红灯按相机编号(cameraIndexCode)预设红绿灯方向
Set_Light_Direct = {

}
# 闯红灯按相机编号(cameraIndexCode)过滤不处理的特殊数据
Light_Filter_Camera = [

]
# 闯红灯按卡口编号(crossingIndexCode)过滤不处理的特殊数据
Light_Filter_Cross = [

]
# 按相机编号过滤废片率高的路口数据: "违法代码":[相机编号,]
Filter_Waste_Camera = {

}
# 逆行场景版本切换
NX_VERSION = 1
# 闯红灯需测试的卡口
Test_Cross_1625 = []
# 闯红灯上线的卡口
Online_Cross_1625 = "all"
# kafka 集群多个ip逗号隔开
SERVER = "**********:29092"
VIO_DATA_TOPIC = 'BAYONET_VEHICLEALARM_TO_JUDGE_JSON_TOPIC'
MODEL_RES_TOPIC = 'BAYONET_VEHICLEALARM_JUDGE_JSON_TOPIC'
CLIENT_ID = 'admin'
GROUP_ID = "ai_judge_1113"
AUTO_OFFSET_RESET = "latest"
Max_Poll_Interval_Ms = 60000  # 消费最大拉取时间，单位毫秒
Max_Poll_Records = 4  # 一次拉取的数据量
# 配置模型处理延时的最大值（单位秒）
Max_Delay_Time = 60 * 30
# 控制模型运行时间
SET_RUN_TIME = -1  # 值为负数则一直运行
# 闯红灯各相机最大处理数量
Max_Camera_Num = -1
# modelJudgeStatus推送结果的值
FILTER_STATUS = [0, 1, 2]
# 配置kafka心跳
Heart_Interval = 10  # 心跳机制间隔时间
Heart_Error_Num = -1  # 心跳机制连续异常次数达到该值模型停止运行
# **********************************************************
# 模型运行方式
DEVICE = "gpu"
# 多进程数
Multi_Num = [2, 2]  # [使用显卡数,每张卡进程数]
SAVE_RES_DIR = "../test_results"  # 审核图片结果分类保存路径
IS_SAVE_VIS_PIC = True  # 是否保存可视化结果图片
# 各违法场景保存图片的最大值
Max_Save_Num = 500
# 去重列表初始化的时间
Init_Url_List_Time = -1
# 收集图片最大数量
Max_Get_Camera_Num = 5
# 日志最大值MB
LOG_MAX_SIZE = 150
# 交辅警废片判定
Police_Judge = False
# 特殊车辆类型（不违法）
Special_Type = ['ambulance_h', 'ambulance_b']
# 特殊车辆判定废片阈值
Special_Car_Thresh = 0.5  # 0.56
Ambulance_Head_Thresh = 0.587
Special_Car_Vis_Thresh = 0.2
# 识别车牌结尾为警字的置信度大于等于此值报废片
Police_Plate_Thresh = 0.85  # 为-1时不作废警车数据
# 停止线判定
Stop_Line_Judge = [1]
Car1_Stop_Line_Rate = 0.64  # 一图停止线判定高度倍数
Stop_Line_1208 = False  # 不按导向是否启用停止线判定
# 不按导向压线模块左右箭头的距离比例
Cover_Line_Rate = 1.25
# 开车副驾未系安全带是否违法(强判定) 为None时根据违法代码进行判定
Copilot_Belt_Is_Vio = False
# 未识别到人是否判定废片(安全带/打电话场景)
No_Person_Is_Waste = True
# *************↓限行场景↓******************
# 实例分割模型禁行类别
stop_car_type_list = ['truck_b', 'truck_h']
# 实例分割模型过滤类别
Filter_Vehicle_Type = ['motorbike_b', 'motorbike_h']  # 荆州低速车
# yolov7模型禁行类别
yolov7_stop_car_type_list = ['truck']
# 货车限行违法高检出率
Truck_Vio_Recall = False
# 配置限行违法阈值，提升检准率
Yolo_Conf = 0.1  # 无效
Blend_Conf = 0.3  # 无效
# 限行场景模型结果去重的IOU阈值
IOU_THRESH = 0.75
# 过滤闯禁行非itrc数据
Filter_Itrc = False
# *************↑限行场景↑******************
First_Plate_Thresh = 0.8  # 机占非车牌首字符置信度阈值
Plate_Fuzzy_Thresh = [None, None]  # 机占非车牌模糊匹配阈值
# 闯红灯三图未匹配到目标车辆是否判定废片
No_Car3_Is_Waste = True
# 红绿灯模型阈值
LIGHT_THRESH = 0.05
# 不按导向未检测到导向箭头是否过滤
Filter_No_Arrow = True
# 违停数据处理比例
Vio_Park_Rate = 1  # 每多少条数据处理一次
# 违停违法高检出率模式(不匹配车牌)
Vio_Park_High_Recall = False
# 过滤车辆类型场景
Filter_Vehicle_Type_Yolo = ["motorcycle"]
Filter_Vehicle_Type_Blend = ['motorbike_b', 'motorbike_h']

# **********************↓文件夹测试模块配置↓****************************
# imgs_dir_test.py
# 图片命名格式示例：同组违法图片编号_车牌号码_时间序号.jpg
SPLIT_STR = "_"
ID_INDEX = 0
PLATE_INDEX = 0
SINGLE_PIC_NUM = {
    "1625": 4,  # 闯红灯
    "1208": 4,  # 不按导向行驶
    "1345": 2,  # 违反禁止标示线
    "1301": 3,  # 逆行
    "1357": 4,  # 不礼让行人
    "1352": 1,  # 超速
    "1039": 4,  # 违法停车
    "1101": 1,  # 开车未系安全带
    "1223": 1,  # 开车打电话
    "8013": 1,  # 闯限行
    "7102": 3,  # 占用公交车道
    "1018": 3,  # 占用非机动车道
    '1000': 4,  # 过滤两轮三轮车
}
# **********************↑文件夹测试模块配置↑****************************

# 车牌开头列表
chars = [u"京", u"沪", u"津", u"渝", u"冀", u"晋", u"蒙", u"辽", u"吉", u"黑", u"苏", u"浙", u"皖", u"闽", u"赣",
         u"鲁", u"豫", u"鄂", u"湘", u"粤", u"桂", u"琼", u"川", u"贵", u"云", u"藏", u"陕", u"甘", u"青", u"宁",
         u"新", u"港", u"学", u"使", u"警", u"澳", u"挂", u"军", u"北", u"南", u"广", u"沈", u"兰", u"成", u"济",
         u"海", u"民", u"航", u"空"]

# 特殊车辆（白名单车辆列表）
try:
    try:
        spec_cars_list = pd.read_csv('../data/spec_cars.csv')['plate'].to_list()
    except:
        spec_cars_list = pd.read_csv('../data/spec_cars.csv')['plate'].tolist()
except:
    spec_cars_list = []
# 实例分割模型类别
WINDOW_NAME = "COCO detections"
_LABEL = ['_background_', 'lane1', 'lane2', 'stop_area', 'car_h', 'car_b', 'bus_h', 'bus_b', 'truck_h', 'truck_b',
          'ambulance_h', 'ambulance_b', 'fire_engine_h', 'fire_engine_b', 'police_car_h', 'police_car_b',
          'engineering_car_h',
          'engineering_car_b', 'motorbike_h', 'motorbike_b', 'arrow_l', 'arrow_s', 'arrow_r', 'arrow_t', 'stop_line',
          'zebra_line', 'no_entry', 'arrow_s_t', 'arrow_l_t', 'arrow_r_t', 'aux_police', 'no_parking_area']

# 实例分割模型需要检测车辆种类列表
car_list = ["car_h", "car_b", "bus_h", "bus_b", "truck_h", "truck_b", "engineering_car_h", "engineering_car_b"]
Blend_Judge_Vehicle = ["car_h", "car_b", "bus_h", "bus_b", "truck_h", "truck_b", "engineering_car_h",
                       "engineering_car_b", 'motorbike_h', 'motorbike_b', 'ambulance_h', 'ambulance_b', 'fire_engine_h',
                       'fire_engine_b', "police_car_h", "police_car_b"]
# 车辆相似度过滤车头类
Filter_Vehicle_Head = ["car_h", "bus_h", "truck_h", "engineering_car_h", 'ambulance_h', 'fire_engine_h',
                       "police_car_h"]
Det_Head_Thresh = 0.42
Head_IOU = 0.65
# 交辅警置信度
police_threshold = 0.7

# yolov7类别列表
coco_names = ['person', 'bicycle', 'car', 'motorcycle', 'airplane', 'bus', 'train', 'truck', 'boat', 'traffic light',
              'fire hydrant', 'stop sign', 'parking meter', 'bench', 'bird', 'cat', 'dog', 'horse', 'sheep', 'cow',
              'elephant', 'bear', 'zebra', 'giraffe', 'backpack', 'umbrella', 'handbag', 'tie', 'suitcase', 'frisbee',
              'skis', 'snowboard', 'sports ball', 'kite', 'baseball bat', 'baseball glove', 'skateboard', 'surfboard',
              'tennis racket', 'bottle', 'wine glass', 'cup', 'fork', 'knife', 'spoon', 'bowl', 'banana', 'apple',
              'sandwich', 'orange', 'broccoli', 'carrot', 'hot dog', 'pizza', 'donut', 'cake', 'chair', 'couch',
              'potted plant', 'bed', 'dining table', 'toilet', 'tv', 'laptop', 'mouse', 'remote', 'keyboard',
              'cell phone',
              'microwave', 'oven', 'toaster', 'sink', 'refrigerator', 'book', 'clock', 'vase', 'scissors', 'teddy bear',
              'hair drier', 'toothbrush']
# 违法代码匹配
Vio_Type_Match = {
    '1625': ['1302', '1625'],  # 闯红灯
    '1208': ['1208', '6095'],  # 不按导向行驶
    '1345': ['1117', '1230', '1345'],  # 违反禁止标示线
    '1301': ['1301', '1373', '1734', '4702'],  # 逆行
    '1357': ['1357', '1358'],  # 不礼让行人
    '1352': ['1130', '1349', '1350', '1351', '1352', '1369', '1628', '1629', '1630', '1631', '1632', '1633', '1634',
             '1635', '1636', '1650', '1721', '1722', '1723', '1724', '1725', '1726', '1727', '1728', '1729', '4305',
             '4609', '4610', '4611', '4612', '4706', '4707', '4708', '4709', '4710', '4711', '4712', '4713', '6048',
             '6050', '6093', '7626'],  # 超速
    '1039': ['1039'],  # 违法停车
    '1101': ['1101', '1120', '1240', '1244', '3019', '4101', '6011'],  # 开车未系安全带
    '1223': ['1223', '1362'],  # 开车打电话
    '8013': ['1116', '1229', '1344'],  # 闯限行
    '7102': ['1019'],  # 占用公交车道
    '1018': ['1018'],  # 占用非机动车道
    '1000': ["1116", '1229', '1344'],  # 过滤两轮三轮车
}
# 可用的违法场景
Is_Allowed_Type = {
    "1625": True,  # 闯红灯
    "1208": True,  # 不按导向行驶
    "1345": True,  # 违反禁止标示线
    "1301": True,  # 逆行
    "1357": True,  # 不礼让行人
    "1352": True,  # 超速
    "1039": True,  # 违法停车
    "1101": True,  # 开车未系安全带
    "1223": True,  # 开车打电话
    "8013": True,  # 闯限行
    "7102": True,  # 占用公交车道
    "1018": True,  # 占用非机动车道
    '1000': True,  # 过滤两轮三轮车
}
# 审片结果代码及描述
JudgeResult = {
    # 闯红灯
    "1625": {
        "vio_straight": "目标车辆直行且直行交通信号灯为红灯",
        "vio_left": "目标车辆左转且左转交通信号灯为红灯",
        "vio_right": "目标车辆右转且右转交通信号灯为红灯",
        "vio_004": "第三图未找到目标车，根据特殊规则判定违法",
        "vio": "正片高检出模式判定为正片",
        "vio_002": "行驶方向结果不可信置为正片",
        "no_vio": "目标车行驶方向与之对应的交通信号灯一三图交集不存在红灯",
        "no_vio_001": "一图与三图的交通信号灯识别结果无交集或无红灯",
        "no_vio_002": "抓拍相机识别的车牌首字符异常",
        "no_vio_003": "一图和二图都不能通过识别车牌匹配到目标车辆",
        "no_vio_004": "第三图未找到目标车",
        "no_vio_005": "一图到二图或二图到三图，目标车未产生明显位移",
        "no_vio_006": "停止线判定",
        "no_vio_007": "同一灯盘的红灯和绿灯或黄灯同亮",
        "no_vio_008": "红灯偏黄或抓拍为黄灯",
        "no_vio_009": "抓拍设备识别的车牌开头字符为：'无'、'未'或车牌为空",
        "no_vio_010": "特殊车辆（警车、应急车、救护车）过滤",
        "no_vio_011": "图片经切分后传入模型不足四张图",
        "no_vio_012": "前三张图片缩放错误",
    },
    # 不按导向行驶
    "1208": {
        "vio_straight": "目标车辆直行且所在车道导向箭头的方向不存在直行",
        "vio_left": "目标车辆左转且所在车道导向箭头的方向不存在左转",
        "vio_right": "目标车辆右转且所在车道导向箭头的方向不存在右转",
        "vio_004": "第三图未找到目标车，根据特殊规则判定违法",
        "vio_002": "行驶方向结果不可信置为正片",
        "vio": "正片高检出模式判定为正片",
        "no_vio": "目标车所在车道识别的箭头线方向包含目标车行驶方向",
        "no_vio_001": "目标车辆所在车道未检测到导向箭头",
        "no_vio_002": "抓拍相机识别的车牌首字符异常",
        "no_vio_003": "一图和二图都不能通过识别车牌匹配到目标车辆",
        "no_vio_004": "第一图或第三图未找到目标车",
        "no_vio_005": "一图到二图或二图到三图，目标车未产生明显位移",
        "no_vio_006": "停止线判定",
        "no_vio_007": "压线判定",
        "no_vio_009": "抓拍设备识别的车牌开头字符为：'无'、'未'或车牌为空",
        "no_vio_010": "特殊车辆（警车、应急车、救护车）过滤",
        "no_vio_011": "图片经切分后传入模型不足四张图",
        "no_vio_012": "前三张图片缩放错误",
    },
    # 违反禁止标示线
    "1345": {
        "vio": "压线违法",
        "vio_002": "压线违法2",
        "vio_003": "压线违法3",
        "vio_004": "图片经切分后数量不足，无法分析透传违法",
        "no_vio": "无压线违法",
        "no_vio_002": "抓拍相机识别的车牌首字符异常",
        "no_vio_005": "未检测到有效地标信息",
        "no_vio_006": "无法通过车牌识别匹配目标车辆",
        "no_vio_007": "模型识别的车牌首字符异常",
        "no_vio_009": "抓拍设备识别的车牌开头字符为：'无'、'未'或车牌为空",
        "no_vio_010": "特殊车辆（警车、应急车、救护车）过滤",
    },
    # 逆行
    "1301": {
        "vio": "逆行违法",
        "vio_003": "图片经切分后数量不足，无法分析透传违法",
        "no_vio": "无逆行违法",
        "no_vio_001": "模型未识别到辅助判定的交通标志",
        "no_vio_002": "抓拍相机识别的车牌首字符异常",
        "no_vio_003": "目标车辆行驶方向与导向箭头一致",
        "no_vio_004": "目标车辆行驶方向与双黄线相对位置判定为废片",
        "no_vio_005": "目标车辆行驶方向与双黄线中心点位置判定为废片",
        "no_vio_006": "无法通过车牌识别匹配目标车辆",
        "no_vio_007": "模型识别的车牌首字符异常",
        "no_vio_009": "抓拍设备识别的车牌开头字符为：'无'、'未'或车牌为空",
        "no_vio_010": "特殊车辆（警车、应急车、救护车）过滤",
    },
    # 不礼让行人
    "1357": {
        "vio": "不礼让行人违法",
        "no_vio": "行人和目标车辆的距离不在判定阈值范围内",
        "no_vio_001": "没有一张图片检测到行人",
        "no_vio_002": "抓拍相机识别的车牌首字符异常",
        "no_vio_006": "无法通过车牌识别匹配目标车辆",
        "no_vio_007": "第三图未能匹配到车辆",
        "no_vio_009": "抓拍设备识别的车牌开头字符为：'无'、'未'或车牌为空",
        "no_vio_010": "特殊车辆（警车、应急车、救护车）过滤",
    },
    # 超速
    "1352": {
        "vio": "超速违法",
        "no_vio": "未能通过车牌匹配到目标车辆",
        "no_vio_002": "抓拍相机识别的车牌首字符异常",
        "no_vio_009": "抓拍设备识别的车牌开头字符为：'无'、'未'或车牌为空",
        "no_vio_010": "特殊车辆（警车、应急车、救护车）过滤",
    },
    # 违法停车
    "1039": {
        "vio_001": "每张图车牌均匹配成功且至少有一张图完成精确匹配",
        "vio_002": "每张图车牌均匹配成功",
        "vio_003": "第一张图/第二张图和最后一张图车牌能匹配成功",
        "vio_004": "前三张图至少有一张图车牌能匹配成功",
        "vio_005": "高检出率模式，不进行车牌匹配判定",
        "no_vio": "无违法停车",
        "no_vio_002": "抓拍相机识别的车牌首字符异常",
        "no_vio_009": "抓拍设备识别的车牌开头字符为：'无'、'未'或车牌为空",
        "no_vio_010": "特殊车辆（警车、应急车、救护车）过滤",
    },
    # 开车未系安全带
    "1101": {
        "vio": "主驾和副驾都未系安全带",
        "no_vio": "无未系安全带违法行为",
        "vio_001": "未检测到人根据配置判定违法",
        "vio_003": "主驾未系安全带",
        "vio_004": "副驾未系安全带",
        "vio_005": "副驾未系安全带（副驾违法代码过滤）",
        "no_vio_001": "未检测到人",
        "no_vio_002": "抓拍相机识别的车牌首字符异常",
        "no_vio_006": "无法通过车牌识别匹配目标车辆",
        "no_vio_009": "抓拍设备识别的车牌开头字符为：'无'、'未'或车牌为空",
        "no_vio_010": "特殊车辆（警车、应急车、救护车）过滤",
    },
    # 开车打电话
    "1223": {
        "vio": "主驾开车打电话",
        "vio_001": "未检测到人根据配置判定违法",
        "no_vio": "未识别到开车打电话行为",
        "no_vio_001": "未检测到人",
        "no_vio_002": "抓拍相机识别的车牌首字符异常",
        "no_vio_006": "无法通过车牌识别匹配目标车辆",
        "no_vio_009": "抓拍设备识别的车牌开头字符为：'无'、'未'或车牌为空",
        "no_vio_010": "特殊车辆（警车、应急车、救护车）过滤",
    },
    # 闯限行
    "8013": {
        "vio": "货车闯限行违法",
        "no_vio": "目标车辆非货车",
        "no_vio_001": "目标车牌未匹配成功",
        "no_vio_002": "抓拍相机识别的车牌首字符异常",
        "no_vio_003": "抓拍相机识别车牌有误",
        "no_vio_004": "未检测到车辆",
        "no_vio_009": "抓拍设备识别的车牌开头字符为：'无'、'未'或车牌为空",
        "no_vio_010": "特殊车辆（警车、应急车、救护车）过滤",
    },
    # 占用公交车道
    "7102": {
        "vio": "占用公交车道违法",
        "no_vio": "目标车辆为公交车",
        "no_vio_002": "抓拍相机识别的车牌首字符异常",
        "no_vio_006": "无法通过车牌识别匹配目标车辆",
        "no_vio_009": "抓拍设备识别的车牌开头字符为：'无'、'未'或车牌为空",
        "no_vio_010": "特殊车辆（警车、应急车、救护车）过滤",
    },
    # 占用非机动车道
    "1018": {
        "vio": "非法占用非机动车道",
        "no_vio": "目标车辆为非机动车",
        "no_vio_001": "目标车牌未匹配成功",
        "no_vio_002": "抓拍相机识别的车牌首字符异常",
        "no_vio_003": "抓拍相机识别车牌有误",
        "no_vio_004": "模型识别的车牌首字符与抓拍相机不一致",
        "no_vio_005": "模型识别的车牌首字符异常",
        "no_vio_009": "抓拍设备识别的车牌开头字符为：'无'、'未'或车牌为空",
        "no_vio_010": "特殊车辆（警车、应急车、救护车）过滤",
    },
    # 过滤车辆类型场景
    "1000": {
        "vio": "非过滤车型",
        "no_vio": f"目标车辆为过滤车型",
        "no_vio_001": "目标车牌未匹配成功",
        "no_vio_002": "抓拍相机识别的车牌首字符异常",
        "no_vio_003": "抓拍相机识别车牌有误",
        "no_vio_004": "未检测到车辆",
        "no_vio_005": "模型识别的车牌首字符异常",
        "no_vio_006": "模型识别的车牌首字符与抓拍相机不一致",
        "no_vio_009": "抓拍设备识别的车牌开头字符为：'无'、'未'或车牌为空",
        "no_vio_010": "特殊车辆（警车、应急车、救护车）过滤",
    }
}
