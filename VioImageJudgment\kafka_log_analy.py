import os
import datetime as dt
from glob import glob
from main_config.config import *

model_vio_code_desc = {
    "1625": "闯红灯",
    "1208": "不按导向行驶",
    "1345": "违反禁止标示线",
    "1301": "逆行",
    "1357": "不礼让行人",
    "1352": "超速",
    "1344": "违法停车",
    "1101": "开车未系安全带",
    "1223": "开车打电话",
    "8013": "闯限行",
    "7102": "占用公交车道",
    "1018": "占用非机动车道"
}


def to_timestamp(time_t):
    str_time = time_t.replace("T", " ").split("+")[0]
    date_time = dt.datetime.strptime(str_time, "%Y-%m-%d %H:%M:%S.%f")
    return date_time.timestamp()


def timestamp_to_str(time_t):
    date_time = dt.datetime.fromtimestamp(time_t)
    str_time = date_time.strftime("%Y-%m-%d %H:%M:%S.%f")[:-3]
    str_t = str_time.replace(" ", "T") + "+08:00"
    return str_t


if __name__ == '__main__':
    # 日志路径
    root = "./ai_judge_code_gy/logs"
    logs_list = glob(root + "/kafka_main.*")
    task_num = 0
    total_time = 0
    dict_time = {"0-10s": 0, "11-20s": 0, "21-30s": 0, "31-40s": 0, "41-50s": 0, "51-60s": 0, ">60s": 0}
    vio_type_dict = {}
    model_waste_dict = {
        "1625": [],
        "1208": [],
        "1345": [],
        "1301": [],
        "1357": [],
        "1352": [],
        "1344": [],
        "1101": [],
        "1223": [],
        "8013": [],
        "7102": [],
        "1018": []
    }
    last_time = 0
    last_send_time = 0
    error_num = 0

    waste_num = 0
    # earliest_time = None
    # latest_time = 0
    for path in logs_list:
        with open(path, "r", encoding="utf-8") as f:
            for line in f:
                if "模型推送数据：" in line:
                    idx = line.find("模型推送数据：")
                    msg_dict = eval(line[idx + 7:])
                    if msg_dict["modelJudgeResult"]["judgeStatus"] in [1, 2]:
                        send_time = to_timestamp(msg_dict["sendTime"])
                        s_id = msg_dict["vehicleAlarmResult"][0]["taskID"]
                        model_time = msg_dict["modelRecvTime"]
                        url = msg_dict["vehicleAlarmResult"][0]["targetPicUrl"]
                        # if earliest_time is None or earliest_time>send_time:
                        #     earliest_time = send_time
                        # if send_time>latest_time:
                        #     latest_time = send_time
                        violation_type = msg_dict["vehicleAlarmResult"][0]["targetAttrs"]["alarmType"]
                        t1 = msg_dict["modelRecvTime"]
                        t2 = msg_dict["modelSendTime"]
                        use_time = to_timestamp(t2) - to_timestamp(t1)
                        if violation_type not in vio_type_dict.keys():
                            vio_type_dict[violation_type] = [1, use_time]
                        else:
                            vio_type_dict[violation_type][0] += 1
                            vio_type_dict[violation_type][1] += use_time
                        # 统计废片原因
                        if msg_dict["modelJudgeResult"]["judgeStatus"] == 2:
                            # waste_num += 1
                            match_code = msg_dict["modelVioCode"]
                            resultDesc = msg_dict["modelJudgeResult"]['resultDesc']
                            if match_code in model_waste_dict.keys():
                                model_waste_dict[match_code].append(resultDesc)

                        task_num += 1
                        total_time += use_time
                        if 0 <= use_time <= 10:
                            dict_time["0-10s"] += 1
                        elif 11 <= use_time <= 20:
                            dict_time["11-20s"] += 1
                        elif 21 <= use_time <= 30:
                            dict_time["21-30s"] += 1
                        elif 31 <= use_time <= 40:
                            dict_time["31-40s"] += 1
                        elif 41 <= use_time <= 50:
                            dict_time["41-50s"] += 1
                        elif 51 <= use_time <= 60:
                            dict_time["51-60s"] += 1
                        else:
                            dict_time[">60s"] += 1
    if task_num>0:
        for k, v in model_waste_dict.items():
            desc_list = list(set(v))
            tmp_dict = {}
            for d in desc_list:
                tmp_dict[d] = v.count(d)
            type_desc = model_vio_code_desc[k]
            print(type_desc)
            for k2, v2 in tmp_dict.items():
                print("废片原因：", k2, "\t数量：", v2)
            print("\n")
        print("\n")
        print(f"完成分析的任务数:{task_num} 平均耗时:{total_time / task_num:.2f}秒 ")
        print("耗时范围\t数量\t\t比例")
        for k, v in dict_time.items():
            rate = round(v / task_num, 4)
            print(k, "\t", v, "\t", rate)
        print("\n")
        print("违法代码\t数量\t\t比例\t\t平均耗时")
        for k, v in vio_type_dict.items():
            avg_time = round(v[1] / v[0], 2)
            rate = round(v[0] / task_num, 4)
            print(k, "\t", v[0], "\t", rate, "\t", avg_time)
    else:
        print("暂无分析完成的数据！")

