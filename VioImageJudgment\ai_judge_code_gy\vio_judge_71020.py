# 占用公交车道场景
import os

# os.environ["CUDA_VISIBLE_DEVICES"] = "1"
from init_models import *


# 1 获取condinst的目标车辆
# class BlendMaskCarInfo(object):
#     def __init__(self, img_np, car_num, weights_list, device):
#         self.img_np = img_np
#         self.car_num = car_num
#         self.weights_list = weights_list
#         self.device = device
#
#     def get_goal_car_info(self):
#         return get_car_box(self.img_np, self.car_num, self.weights_list, self.device)


# 占用公交车道主函数
def judge_vio(src_plate_num, file_list, weights_list, resize_scale=0.7, save=False, save_path=".", extra_msg=None):
    error_path = f"{save_path}/no_vio"
    vio_path = f"{save_path}/vio"
    if save:
        os.makedirs(error_path, exist_ok=True)
        os.makedirs(vio_path, exist_ok=True)
    # 读取模型
    plate_det_model = weights_list[0][0]
    plate_rec_model = weights_list[0][1]
    demo = weights_list[1]
    lp_model = weights_list[2][0]
    lp_meta = weights_list[2][1]
    yolov7_model = weights_list[3][0]
    yolov7_meta = weights_list[3][1]
    model_cnn = weights_list[-2]
    vio_model = weights_list[-1]
    try:
        split_mode = extra_msg[0]
        black_height = extra_msg[1]
        black_pos = extra_msg[2]
        iieaAuditMode = extra_msg[3]
        iieaFilterVehicle = extra_msg[4]
    except:
        split_mode = 111
        black_height = 0
        black_pos = None
        iieaAuditMode = None
        iieaFilterVehicle = None
    judge_infos = {'plate_num': src_plate_num,
                   'vio_code': 7102,
                   'vio_judge': "no_vio",
                   'zebra_line': {},
                   'police': 0,
                   'has_crossline': 0,
                   'vehicle_location': 0,
                   'drv_direction': 0,
                   'drive_direction_light': [],
                   'lamp': [],
                   'stop_line': 0,
                   'lane': [],
                   'target_car': [],
                   'judgeConf': 1.0,
                   'modelDetectResults': [], 'split_mode': split_mode, "black_height": black_height,
                   "black_pos": black_pos, "iieaAuditMode": iieaAuditMode, "iieaFilterVehicle": iieaFilterVehicle}

    # 特殊车牌判定
    if not src_plate_num:
        vio_judge = "no_vio_009"
        judge_infos['vio_judge'] = vio_judge
        return vio_judge, judge_infos
    if src_plate_num[0] not in chars:
        vio_judge = 'no_vio_002'
        judge_infos['vio_judge'] = vio_judge
        return vio_judge, judge_infos
    vio_judge = VIO_JUDGE_TOOL().spe_no_vio_judge(src_plate_num, iieaFilterVehicle)
    if vio_judge.startswith('no_vio'):
        judge_infos['vio_judge'] = vio_judge
        return vio_judge, judge_infos
    vio_judge = 'no_vio'

    judge_infos['target_car'] = []
    plate_list = []
    all_car_list = []
    target_car_list = []
    # iiea审核模式
    if iieaAuditMode is None:
        judge_mode = Precision_Judge_Mode[str(judge_infos['vio_code'])]
    else:
        judge_mode = iieaAuditMode
    judge_thresh = Judge_Thresh[str(judge_infos['vio_code'])][int(judge_mode)]
    if judge_thresh is None:
        judge_thresh = 0.5 if judge_mode else 0.2
    for idx, img in enumerate(file_list):
        temp_car = {}
        if (img.shape[0] > 4000 or img.shape[1] > 4000) and float(resize_scale) < 1:
            img = cv2.resize(img, None, fx=resize_scale, fy=resize_scale)
        # yolov7检测非公交车
        crop_img, car_bbox_ps, _, score_list = yolov7_detect(img, yolov7_model, yolov7_meta[0], yolov7_meta[1],
                                                             yolov7_meta[2],
                                                             classes=["car", "truck", "motorcycle"], conf_thres=judge_thresh,need_conf=True)
        # 车牌识别匹配模块
        img_car_ps, plate_num, img_plate_dict, new_car_bbox_ps = CarJudge(
            iiea_cfg=judge_infos["iieaFilterVehicle"]).confirm_img1_car_ps(img, src_plate_num,
                                                                           plate_det_model,
                                                                           plate_rec_model,
                                                                           car_bbox_ps,
                                                                           yolov7_meta[2], label=1,
                                                                           show=save,
                                                                           save_path=save_path)
        plate_list.append(plate_num)
        all_car_list.append(car_bbox_ps)
        target_car_list.append(img_car_ps)
        temp_car['plate_num'] = plate_num
        temp_car['car_ps'] = img_car_ps
        judge_infos['target_car'].append(temp_car)

        # 返回检测框和置信度信息
        if img_car_ps:
            target_idx = car_bbox_ps.index(img_car_ps)
            img_id = idx + 1
            percent_coord = convert_src_coord(img_car_ps, img, img_id,
                                              judge_infos["split_mode"], judge_infos["black_height"],
                                              judge_infos["black_pos"])
            tmp_res = {
                "objId": img_id,
                "objType": "vehicle",
                "objConf": float(score_list[target_idx]),
                "objCoord": percent_coord
            }
            judge_infos["modelDetectResults"].append(tmp_res)
        # 车牌位置信息
        plate_cor = img_plate_dict[str(img_car_ps)] if img_car_ps else []
        if plate_cor:
            target_idx = str(img_car_ps) + "conf"
            img_id = idx + 1
            percent_coord = convert_src_coord(plate_cor, img, img_id,
                                              judge_infos["split_mode"], judge_infos["black_height"],
                                              judge_infos["black_pos"])
            tmp_res = {
                "objId": img_id,
                "objType": "plate",
                "objConf": float(img_plate_dict[target_idx]),
                "objCoord": percent_coord
            }
            judge_infos["modelDetectResults"].append(tmp_res)

        try:
            x_ratio = ((img_car_ps[0] + img_car_ps[2]) / 2) / img.shape[1]
        except:
            x_ratio = 0
        # 0 代表左边， 1代表右边
        if x_ratio <= 0.5:
            judge_infos['vehicle_location'] = 0
        else:
            judge_infos['vehicle_location'] = 1
        # iiea白名单车辆
        if filter_special_vehicle(demo, img, img_car_ps, vio_judge, save, judge_infos) == 'no_vio_010':
            vio_judge = 'no_vio_010'
            break
    # 废片场景没有一张图片通过车牌匹配到目标车辆
    if vio_judge != "no_vio_010":
        res_list = []
        for plate in plate_list:
            if plate == "NO_DETECT":
                res_list.append(0)
            elif plate.endswith("警"):
                res_list.append(-1)
                break
            else:
                res_list.append(1)
        if np.sum(res_list) == 0:
            vio_judge = 'no_vio_006'
            judge_infos["judgeConf"] = 0.9
        elif np.sum(res_list) == -1:
            vio_judge = 'no_vio_010'
            judge_infos["judgeConf"] = 0.8
        # 至少有一张图片找到目标车辆
        elif np.sum([len(i) for i in target_car_list]):
            vio_judge = 'vio'

    judge_infos['vio_judge'] = vio_judge
    judge_infos["judgeConf"] = 0.85
    try:
        if save:
            show_cars2(file_list, all_car_list)
            show_plate2(file_list, target_car_list)

            result_path = vio_path if vio_judge.startswith("vio") else error_path
            for idx, i in enumerate(file_list):
                cv2.imwrite(result_path + f'/{vio_judge}_{src_plate_num}_img{idx + 1}.jpg', file_list[idx])
    except:
        print("Error!占用公交车道可视化模块异常!")
    return vio_judge, judge_infos
