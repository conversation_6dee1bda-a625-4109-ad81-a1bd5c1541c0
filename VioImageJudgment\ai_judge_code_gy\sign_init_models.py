import sys

sys.path.append('..')
sys.path.append('../yolov7')
from main_config.config import *
import warnings

warnings.filterwarnings('ignore')
import os

if DEVICE in ["cpu", "CPU"]:
    os.environ["CUDA_VISIBLE_DEVICES"] = "-1"
import shutil
import numpy as np
import cv2
import time
import math
from tqdm import tqdm
import torch
import traceback
from shapely import geometry
from PIL import Image
import torch.nn.functional as F
import torchvision.transforms as transforms
from src.EfficientNet import efficientnet
from ai_judge_code_gy.blendmask_detect import BlendMaskModel, BlendMaskDetect, mask_gen
from yolov7.yolo_interface import load_lp, detect_lp, yolov7_detect, load_plate_model, detect_Recognition_plate, \
    draw_result2
from yolov7.plate_recognition.plate_rec import get_plate_result
from ai_judge_code_gy.car_detect import CarJudge, get_car_type, get_car_box
from ai_judge_code_gy.retrograde_pro import Road
from ai_judge_code_gy.judge_tools import Tools, VIO_JUDGE_TOOL
from ai_judge_code_gy.car_direction import CarDriveDirectionJudge
from ai_judge_code_gy.light_detect import judge_light_status
from ai_judge_code_gy.vio_classify_13450 import Vio_pred, Net
from ai_judge_code_gy.visualization import *


# 初始化所有模型
def init_models(traffic=True, lp=True, yolov7=True, trace=False, vio_model=False, cnn=False):
    # 模型授权
    from sign_request import send_request
    res = send_request()
    print(res)
    f = open("logs/sign_msg.txt", "w", encoding="utf-8")
    f.write(str(res) + "\n")
    if "data" in res.keys() and res["data"]:
        print("模型授权成功！")
        f.write("模型授权成功！")
        f.flush()
        f.close()
    else:
        print("模型授权失败，请检查配置文件中IP、AppKey、AppSecret是否正确！")
        f.write("模型授权失败！")
        f.flush()
        f.close()
        exit()

    init_device = "cuda:0" if torch.cuda.is_available() else "cpu"
    yolov7_module = lp_module = [None, None]
    demo = vio_judge_model = model_cnn = None
    # 车牌检测识别模型
    plate_det_model, plate_rec_model = load_plate_model("../models/plate_detect.pt",
                                                        "../models/plate_rec.pth", device=init_device)
    plate_module = [plate_det_model, plate_rec_model]
    if traffic:
        # 交通标志识别模型
        ##demo = BlendMaskModel().load_model()
        demo = BlendMaskModel().load_specific_model("../models/model_0041299.pth", device=init_device)
        # 仅仅是作为测试看看效果   --- 对黑夜场景的识别能力不好
        # demo = BlendMaskModel().load_specific_model("../models/old_blendmask_model_best.pth")
    if yolov7:
        # 车辆行人检测模型
        yolov7_model, imgsz, stride, device = load_lp("../models/yolov7-e6e.pt", trace=trace, device=init_device)
        yolov7_meta = (imgsz, stride, device)
        yolov7_module = [yolov7_model, yolov7_meta]
    if lp:
        # 红绿灯识别模型
        lp_net, imgsz, stride, device = load_lp("../models/best.pt", trace=trace, device=init_device)
        lp_meta = (imgsz, stride, device)
        lp_module = [lp_net, lp_meta]

    if vio_model:
        # 违法判定模型
        vio_judge_model = VioJudgeModel("../models/EfficientNet_B7_n_hc.pth", device=init_device).load_model()
    if cnn:
        # 压线判定模型
        model_cnn = torch.load('../models/cnn_class_model.pth', map_location=init_device)

    return [plate_module, demo, lp_module, yolov7_module, model_cnn, vio_judge_model]


# 判别12230,12440类别违法信息
class VioJudgeModel(object):
    def __init__(self, model_path, device=None):
        if device is None:
            self.device = torch.device("cuda:0" if torch.cuda.is_available() else "cpu")
        else:
            self.device = torch.device(device)
        self.model_path = model_path
        self.model = efficientnet(net="B7")

    def load_model(self):
        model_dict = self.model.state_dict()
        state_dict = torch.load(self.model_path, map_location=self.device)
        filtered_state_dict = {k: v for k, v in state_dict.items() if k in model_dict}
        self.model.load_state_dict(filtered_state_dict, strict=False)
        self.model.to(self.device)
        return self.model
