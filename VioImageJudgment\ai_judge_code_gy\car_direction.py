#!/usr/bin/python
# -*- coding: UTF-8 -*-
import warnings

warnings.filterwarnings('ignore')
import cv2
import numpy as np


# 车辆行驶方向判定
class CarDriveDirectionJudge(object):
    def __init__(self, img_np_list, img_car_ps_list, plate_list, lane_list, car_body_direct_judge=True,
                 lane_pos_judge=True, need_three_car_body_data=True):
        self.img_np_list = img_np_list
        self.img_car_ps_list = img_car_ps_list
        self.plate_list = plate_list
        self.lane_list = lane_list
        self.car_body_direct_judge = car_body_direct_judge
        self.lane_pos_judge = lane_pos_judge
        self.need_three_car_body_data = need_three_car_body_data
        # car_body_direct_judge 车身方向是否加入判定
        # lane_pos_judge 车道线的位置因素是否加入判定
        # need_three_car_body_data 是否需要三个车身数据进行判别

    def car_mid_pos_driv_dire_judge(self):  # 三张图片以车身中点的相对位置判断车辆运动方向

        img1_car_ps = self.img_car_ps_list[0]
        img2_car_ps = self.img_car_ps_list[1]
        img3_car_ps = self.img_car_ps_list[2]
        ###############################################
        img = self.img_np_list[0].copy()
        img1 = self.img_np_list[0]
        img2 = self.img_np_list[1]
        img3 = self.img_np_list[2]
        img_W = img1.shape[1]
        img_H = img1.shape[0]
        try:
            img1_car_cor = [int((img1_car_ps[0] + img1_car_ps[2]) / 2), int((img1_car_ps[1] + img1_car_ps[3]) / 2)]
            img1_lx = round((img1_car_cor[0] / img_W), 4)
            img1_ly = round((img1_car_cor[1] / img_H), 4)
        except:
            img1_car_cor = []
            img1_lx = 0
            img1_ly = 0
        try:
            img2_car_cor = [int((img2_car_ps[0] + img2_car_ps[2]) / 2), int((img2_car_ps[1] + img2_car_ps[3]) / 2)]
            img2_lx = round((img2_car_cor[0] / img_W), 4)
            img2_ly = round((img2_car_cor[1] / img_H), 4)
        except:
            img2_car_cor = []
            img2_lx = 0
            img2_ly = 0
        try:
            img3_car_cor = [int((img3_car_ps[0] + img3_car_ps[2]) / 2), int((img3_car_ps[1] + img3_car_ps[3]) / 2)]
            img3_lx = round((img3_car_cor[0] / img_W), 4)
            img3_ly = round((img3_car_cor[1] / img_H), 4)
        except:
            img3_car_cor = []
            img3_lx = 0
            img3_ly = 0
        angle1 = None
        angle2 = None
        angle3 = None
        # iou12 = 0
        # iou23 = 0

        if len(img1_car_ps) and len(img2_car_ps):
            if abs(img2_car_cor[1] - img1_car_cor[1]) < 5:
                angle1 = 1000
            else:
                h1 = img2_car_cor[1] - img1_car_cor[1]
                w1 = img2_car_cor[0] - img1_car_cor[0]
                if h1 != 0:
                    tan_theta = w1 / h1
                    angle1 = round(float(np.degrees(np.arctan(tan_theta))), 3)

            # x11, y11, x12, y12 = img1_car_ps[0], img1_car_ps[1], img1_car_ps[2], img1_car_ps[3]
            # x21, y21, x22, y22 = img2_car_ps[0], img2_car_ps[1], img2_car_ps[2], img2_car_ps[3]
            # iou12 = Tools().IOU(np.array([x11, y11]), np.array([x12, y12]), np.array([x21, y21]), np.array([x22, y22]))

        if len(img2_car_ps) and len(img3_car_ps):
            if abs(img3_car_cor[1] - img2_car_cor[1]) < 5:
                angle2 = 1000
            else:
                h2 = img3_car_cor[1] - img2_car_cor[1]
                w2 = img3_car_cor[0] - img2_car_cor[0]
                if h2 != 0:
                    tan_theta2 = w2 / h2
                    angle2 = round(float(np.degrees(np.arctan(tan_theta2))), 3)

            # x21, y21, x22, y22 = img2_car_ps[0], img2_car_ps[1], img2_car_ps[2], img2_car_ps[3]
            # x31, y31, x32, y32 = img3_car_ps[0], img3_car_ps[1], img3_car_ps[2], img3_car_ps[3]
            # iou23 = Tools().IOU(np.array([x21, y21]), np.array([x22, y22]), np.array([x31, y31]), np.array([x32, y32]))

        if len(img1_car_ps) and len(img3_car_ps):
            if abs(img3_car_cor[1] - img1_car_cor[1]) < 5:
                angle3 = 1000
            else:
                h3 = img3_car_cor[1] - img1_car_cor[1]
                w3 = img3_car_cor[0] - img1_car_cor[0]
                if h3 != 0:
                    tan_theta = w3 / h3
                    angle3 = round(float(np.degrees(np.arctan(tan_theta))), 3)

        # print(angle1, angle2, angle3)
        direction = 'straight'
        # 三张均存在车
        if angle2 and angle1 and angle3:
            delta1 = angle2 - angle1
            delta2 = angle3 - angle2
            if angle2 < 0 and (abs(delta1) > 35 or abs(delta2) > 50 or angle2 < -50):
                if img3_car_cor[0] < img1_car_cor[0] and img3_car_cor[0] < img2_car_cor[0]:
                    if angle1 < -30 and angle1 > -80:
                        direction = 'right'
                    elif angle1 > 30 and angle1 < 80:
                        direction = 'left'
                    elif angle1 > -30 and angle1 < 30:
                        direction = 'straight'
                else:
                    direction = 'right'
            elif angle2 > 0 and (abs(delta1) > 35 or abs(delta2) > 40 or angle2 > 50):
                if img3_car_cor[0] > img1_car_cor[0] and img3_car_cor[0] > img2_car_cor[0]:
                    if angle1 < -30 and angle1 > -80:
                        direction = 'right'
                    elif angle1 > 30 and angle1 < 80:
                        direction = 'left'
                    elif angle1 > -30 and angle1 < 30:
                        direction = 'straight'
                else:
                    direction = 'left'
            elif angle2 > 0 and (img2_lx < 0.3 or img3_lx < 0.2):
                direction = 'left'
            elif angle2 < 0 and (img2_lx > 0.7 or img3_lx > 0.8):
                direction = 'right'
            else:
                direction = 'straight'
        # 二三张均存在车
        elif angle2:
            if angle2 < -40:
                direction = 'right'
            elif angle2 > 40:
                direction = 'left'
            else:
                direction = 'straight'
        # 一三张均存在车
        elif angle3:
            if angle3 < -35:
                direction = 'right'
            elif angle3 > 35:
                direction = 'left'
            else:
                direction = 'straight'
        # 一二张均存在车
        elif angle1:
            if angle1 < -30:
                direction = 'right'
            elif angle1 > 30:
                direction = 'left'
            elif angle1 > -30 and angle1 < 30:
                direction = 'straight'

        # if iou12 > 0.75 or iou23 > 0.9:
        #     direction = 'stop'

        return direction

    # 弃用使用car_body_pos_dire_judge2
    def car_body_pos_dire_judge(self):  # 三张图片以车身自身的方向作为车辆运动方向判别。该判别需要识别车牌号，前置因素多，作为前置判别
        img1_car_ps = self.img_car_ps_list[0]
        img2_car_ps = self.img_car_ps_list[1]
        img3_car_ps = self.img_car_ps_list[2]
        plate1 = self.plate_list[0]
        plate2 = self.plate_list[1]
        plate3 = self.plate_list[2]
        # 车头中点
        if img1_car_ps != []:
            img1_car_head_cor = [int((img1_car_ps[0] + img1_car_ps[2]) / 2), int(img1_car_ps[1])]
        else:
            img1_car_head_cor = []

        if img2_car_ps != []:
            img2_car_head_cor = [int((img2_car_ps[0] + img2_car_ps[2]) / 2), int(img2_car_ps[1])]
        else:
            img2_car_head_cor = []

        if img3_car_ps != []:
            img3_car_head_cor = [int((img3_car_ps[0] + img3_car_ps[2]) / 2), int(img3_car_ps[1])]

        else:
            img3_car_head_cor = []
        # 车尾车牌中点
        if plate1:
            plate1_cor = [img1_car_ps[0] + plate1[0] + int((plate1[2]) / 2),
                          img1_car_ps[1] + plate1[1] + int((plate1[3]) / 2)]
        else:
            plate1_cor = []

        if plate2:
            plate2_cor = [img2_car_ps[0] + plate2[0] + int((plate2[2]) / 2),
                          img2_car_ps[1] + plate2[1] + int((plate2[3]) / 2)]
        else:
            plate2_cor = []

        if plate3:
            plate3_cor = [img3_car_ps[0] + plate3[0] + int((plate3[2]) / 2),
                          img3_car_ps[1] + plate3[1] + int((plate3[3]) / 2)]

        else:
            plate3_cor = []
        # 车身方向判别
        car_angle1 = None
        car_angle2 = None
        car_angle3 = None
        w1 = w2 = h1 = h2 = 0
        if len(img1_car_head_cor) and len(plate1_cor):
            h1 = img1_car_head_cor[1] - plate1_cor[1]
            w1 = img1_car_head_cor[0] - plate1_cor[0]
            if w1 != 0:
                tan_theta = h1 / w1
                car_angle1 = round(float(np.degrees(np.arctan(-tan_theta))), 3)
            # else:
            #     car_angle1 = 90

        if len(img2_car_head_cor) and len(plate2_cor):
            h2 = img2_car_head_cor[1] - plate2_cor[1]
            w2 = img2_car_head_cor[0] - plate2_cor[0]
            if w2 != 0:
                tan_theta2 = h2 / w2
                car_angle2 = round(float(np.degrees(np.arctan(-tan_theta2))), 3)
            # else:
            #     car_angle2 = -89

        if len(img3_car_head_cor) and len(plate3_cor):
            h3 = img3_car_head_cor[1] - plate3_cor[1]
            w3 = img3_car_head_cor[0] - plate3_cor[0]
            if w3 != 0:
                tan_theta3 = h3 / w3
                car_angle3 = round(float(np.degrees(np.arctan(-tan_theta3))), 3)
            else:
                car_angle3 = 90
                direction = 'straight'
                return direction, car_angle1, car_angle3

        # 三张均存在车牌
        print("car_body_pos_dire_judge", car_angle1, car_angle2, car_angle3)
        direction = None
        if car_angle1 and car_angle2 and car_angle3:
            delta = car_angle3 - car_angle1
            if car_angle3 > 15 and (delta < -12):
                direction = 'right'
            elif (abs(car_angle1) > 60 and abs(car_angle3) > 60) and 160 > delta > 120:
                direction = 'right'
            elif (abs(car_angle2) < 60 and 60 > car_angle2 > 0) or (
                    abs(car_angle3) < 60 and 60 > car_angle3 > 0):
                direction = 'right'
            elif car_angle3 < -15 and (delta > 12):
                direction = 'left'
            elif (abs(car_angle1) > 60 and abs(car_angle3) > 60) and -160 < delta < -120:
                direction = 'left'
            elif (abs(car_angle2) < 60 and -60 < car_angle2 < 0) or (
                    abs(car_angle3) < 60 and -60 < car_angle3 < 0):
                direction = 'left'
            else:
                direction = 'straight'
        if not self.need_three_car_body_data:
            # 二三张均存在车牌
            if car_angle2 and car_angle3 and car_angle1 == None:
                delta = car_angle3 - car_angle2
                if car_angle3 > 15 and (delta < -10):
                    direction = 'right'
                elif (abs(car_angle2) > 60 and abs(car_angle3) > 60) and 160 > delta > 120:
                    direction = 'right'
                elif (abs(car_angle2) < 60 and 60 > car_angle2 > 0) or (
                        abs(car_angle3) < 60 and 60 > car_angle3 > 0):
                    direction = 'right'
                elif car_angle3 < -15 and (delta > 10):
                    direction = 'left'
                elif (abs(car_angle2) > 60 and abs(car_angle3) > 60) and -160 < delta < -120:
                    direction = 'left'
                elif (abs(car_angle2) < 60 and -60 < car_angle2 < 0) or (
                        abs(car_angle3) < 60 and -60 < car_angle3 < 0):
                    direction = 'left'
                else:
                    direction = 'straight'
                # 一三张均存在车牌
            elif car_angle1 and car_angle3 and car_angle2 is None:
                delta = car_angle3 - car_angle1
                if car_angle3 > 15 and (delta < -12):
                    direction = 'right'
                elif (abs(car_angle1) > 60 and abs(car_angle3) > 60) and 160 > delta > 120:
                    direction = 'right'
                elif (abs(car_angle3) < 60 and car_angle3 < 60 and car_angle3 > 0):
                    direction = 'right'
                elif car_angle3 < -15 and (delta > 12):
                    direction = 'left'
                elif (abs(car_angle1) > 60 and abs(car_angle3) > 60) and delta > -160 and delta < -120:
                    direction = 'left'
                elif (abs(car_angle3) < 60 and car_angle3 > -60 and car_angle3 < 0):
                    direction = 'left'
                else:
                    direction = 'straight'
            # 一二张均存在车牌
            elif car_angle1 and car_angle2 and car_angle3 == None:
                delta = car_angle2 - car_angle1
                if car_angle2 > 15 and (delta < -10):
                    direction = 'right'
                elif (abs(car_angle1) > 60 and abs(car_angle2) > 60) and delta < 160 and delta > 120:
                    direction = 'right'
                elif (abs(car_angle2) < 60 and car_angle2 < 60 and car_angle2 > 0):
                    direction = 'right'
                elif car_angle2 < -15 and (delta > 10):
                    direction = 'left'
                elif (abs(car_angle1) > 60 and abs(car_angle2) > 60) and delta > -160 and delta < -120:
                    direction = 'left'
                elif (abs(car_angle2) < 60 and car_angle2 > -60 and car_angle2 < 0):
                    direction = 'left'
                else:
                    direction = 'straight'

            elif car_angle1 == None and car_angle3 and car_angle2 == None:

                if abs(car_angle3) < 60 and car_angle3 < 60 and car_angle3 > 0:
                    direction = 'right'
                elif (abs(car_angle3) < 60 and car_angle3 > -60 and car_angle3 < 0):
                    direction = 'left'
                else:
                    direction = None
            elif car_angle1 == None and car_angle3 == None and car_angle2:

                if (abs(car_angle2) < 60 and car_angle2 < 60 and car_angle2 > 0):
                    direction = 'right'
                elif (abs(car_angle2) < 60 and car_angle2 > -60 and car_angle2 < 0):
                    direction = 'left'
                else:
                    direction = None
        return direction, car_angle1, car_angle3

    def car_body_pos_dire_judge2(self):  # 三张图片以车身自身的方向作为车辆运动方向判别。该判别需要检测车辆车牌位置
        img1_car_ps = self.img_car_ps_list[0]
        img2_car_ps = self.img_car_ps_list[1]
        img3_car_ps = self.img_car_ps_list[2]
        plate1 = self.plate_list[0]
        plate2 = self.plate_list[1]
        plate3 = self.plate_list[2]
        # 车头中点
        if img1_car_ps:
            img1_car_head_cor = [int((img1_car_ps[0] + img1_car_ps[2]) / 2), int(img1_car_ps[1])]
        else:
            img1_car_head_cor = []

        if img2_car_ps:
            img2_car_head_cor = [int((img2_car_ps[0] + img2_car_ps[2]) / 2), int(img2_car_ps[1])]
        else:
            img2_car_head_cor = []

        if img3_car_ps:
            img3_car_head_cor = [int((img3_car_ps[0] + img3_car_ps[2]) / 2), int(img3_car_ps[1])]

        else:
            img3_car_head_cor = []
        # 车尾车牌中点
        if plate1:
            plate1_cor = [(plate1[0] + plate1[2]) // 2, (plate1[1] + plate1[3]) // 2]
        else:
            plate1_cor = []

        if plate2:
            plate2_cor = [(plate2[0] + plate2[2]) // 2, (plate2[1] + plate2[3]) // 2]
        else:
            plate2_cor = []

        if plate3:
            plate3_cor = [(plate3[0] + plate3[2]) // 2, (plate3[1] + plate3[3]) // 2]

        else:
            plate3_cor = []
        # 车身方向判别
        car_angle1 = None
        car_angle2 = None
        car_angle3 = None
        # 计算一二三图目标车辆夹角（车头中点与车牌中点）
        if len(img1_car_head_cor) and len(plate1_cor):
            h1 = img1_car_head_cor[1] - plate1_cor[1]
            w1 = img1_car_head_cor[0] - plate1_cor[0]
            if w1 != 0:
                tan_theta = h1 / w1
                car_angle1 = round(float(np.degrees(np.arctan(-tan_theta))), 3)
            else:
                car_angle1 = 90

        if len(img2_car_head_cor) and len(plate2_cor):
            h2 = img2_car_head_cor[1] - plate2_cor[1]
            w2 = img2_car_head_cor[0] - plate2_cor[0]
            if w2 != 0:
                tan_theta2 = h2 / w2
                car_angle2 = round(float(np.degrees(np.arctan(-tan_theta2))), 3)
            # else:
            #     car_angle2 = -89
        x_judge = False
        if len(img3_car_head_cor) and len(plate3_cor):
            h3 = img3_car_head_cor[1] - plate3_cor[1]
            w3 = img3_car_head_cor[0] - plate3_cor[0]
            if w3 != 0:
                tan_theta3 = h3 / w3
                car_angle3 = round(float(np.degrees(np.arctan(-tan_theta3))), 3)
            # elif w1 != 0 and w2 != 0 and h1 / w1 < h2 / w2:
            #     # 救护车等车牌在左下的情况
            #     direction = 'left'
            #     print("***test judge***")
            #     return direction, car_angle1, car_angle3, x_judge
            else:
                car_angle3 = 90
                direction = 'straight'
                print("***test judge2***")
                return direction, car_angle1, car_angle3, x_judge

        # 三张均存在车牌
        print("car_body_pos_dire_judge", car_angle1, car_angle2, car_angle3)
        cv2.putText(self.img_np_list[2], f"car_angle:{car_angle1} {car_angle2} {car_angle3}",
                    (int(self.img_np_list[2].shape[1] * 0.6), 60), cv2.FONT_HERSHEY_COMPLEX, 1,
                    (0, 0, 255), 2)
        direction = None
        if car_angle3 and car_angle1:
            print("***No.1***")
            cx1 = (img1_car_ps[0] + img1_car_ps[2]) / 2
            cx3 = (img3_car_ps[0] + img3_car_ps[2]) / 2
            cx2 = (img2_car_ps[0] + img2_car_ps[2]) / 2 if img2_car_ps else np.average((cx1, cx3))

            cy3 = (img3_car_ps[1] + img3_car_ps[3]) / 2

            # wh_rate2 = (img2_car_ps[2] - img2_car_ps[0]) / (img2_car_ps[3] - img2_car_ps[1]) if img2_car_ps \
            #     else (img1_car_ps[2] - img1_car_ps[0]) / (img1_car_ps[3] - img1_car_ps[1])
            # wh_rate3 = (img2_car_ps[2] - img2_car_ps[0]) / (img2_car_ps[3] - img2_car_ps[1]) if img2_car_ps \
            #     else (img1_car_ps[2] - img1_car_ps[0]) / (img1_car_ps[3] - img1_car_ps[1])
            # print("cy3:", cy3, "dist_y:", dist_y, self.img_np_list[2].shape)
            # if cx1 > cx2 > cx3 or cx1 < cx2 < cx3:
            #     dist_judge = cx3 - cx1
            # else:
            dist_judge = cx3 - cx2
            dist_judge_y = img3_car_ps[3] - img2_car_ps[3] if img2_car_ps else (img1_car_ps[3] + img3_car_ps[3]) / 3 * 2
            if (car_angle1 > 0 and car_angle3 < 0) or (car_angle1 < 0 and car_angle3 > 0):
                sup_angle = 180 - abs(car_angle1) - abs(car_angle3)
            else:
                sup_angle = abs(car_angle1 - car_angle3)
            car_w = img2_car_ps[2] - img2_car_ps[0] if img2_car_ps else img1_car_ps[2] - img1_car_ps[0]

            # 当一图角度偏转过大时，三图角度变化与车辆位移存在异常关系时判定为直行
            turn_judge = True
            if car_angle2:
                if (car_angle1 > car_angle2 > car_angle3 > 0 and dist_judge < -0.5 * car_w) or (
                        0 < car_angle1 < car_angle2 < car_angle3 and 0 < dist_judge < 0.5 * car_w and car_angle3 - car_angle1 > 18) or (
                        car_angle1 < car_angle2 < car_angle3 < 0 and dist_judge > 0.5 * car_w):
                    turn_judge = False
            yx_rate = dist_judge_y / dist_judge if dist_judge != 0 else 0
            print(dist_judge, car_w, sup_angle, yx_rate, turn_judge, "###")
            if car_angle1 < -75:  # 达到偏移角度的最大值
                # 近似抓拍设备与道路的倾斜角度，修正三图转向角度，降低干扰
                bias_angle = -car_angle1 - 90
                angle3 = car_angle3 + bias_angle
                angle3 = angle3 if angle3 > -90 else 180 - abs(angle3)
                if car_angle2:
                    min_angle = max(car_angle2, car_angle3)
                else:
                    min_angle = car_angle3
                if abs(yx_rate) <0.5 and sup_angle <12:
                    direction = "vio_002"
                elif cy3 > self.img_np_list[2].shape[
                    0] * 0.27 and (((yx_rate > -2.2 and dist_judge > 0) or sup_angle > 23) and (
                        (0 < angle3 < 68 and dist_judge > 0) or (
                        angle3 > 0 and sup_angle > 21.5 and dist_judge > 0) or (
                                angle3 > 0 and dist_judge > 0 and yx_rate > -1.4 and sup_angle > 8))):
                    direction = 'right'
                elif (-58 < angle3 < 0 or cy3 > self.img_np_list[2].shape[
                    0] * 0.27) and (
                        (0 > angle3 > -68 and dist_judge < 0) or (-60 > angle3 > -70 and dist_judge < -0.5 * car_w) or (
                        0 > car_angle3 > car_angle1 and dist_judge < 0 and yx_rate < 1.4 and sup_angle > 8) or (
                                -60 < min_angle < 0 and dist_judge < 0)):
                    direction = 'left'
                else:
                    direction = 'straight'
            elif car_angle1 > 75:
                bias_angle = 90 - car_angle1  # 12
                angle3 = car_angle3 + bias_angle  # 76
                # 转换角度最大值
                angle3 = angle3 if angle3 < 90 else abs(angle3) - 180
                if car_angle2:
                    min_angle = min(car_angle2, car_angle3)
                else:
                    min_angle = car_angle3
                print(dist_judge - 0.8 * car_w, cy3 - self.img_np_list[2].shape[
                    0] * 0.27, yx_rate, "AAAAAAAAAAAAA")
                if abs(yx_rate) <0.5 and sup_angle <12:
                    direction = "vio_002"
                elif yx_rate > 10 or yx_rate < -10:
                    direction = 'straight'
                elif cy3 > self.img_np_list[2].shape[
                    0] * 0.27 and (
                        (0 < angle3 < 65 and dist_judge > 0) or (65 <= angle3 < 70 and dist_judge > 0.5 * car_w) or (
                        0 < car_angle3 < car_angle1 and dist_judge > 0.8 * car_w and 0 > yx_rate > -1.4 and sup_angle >= 10) or (
                                0 < min_angle < 60 and 0 > yx_rate > -1.7) or (
                                dist_judge > 0 and self.img_np_list[2].shape[1] - img3_car_ps[2] < 20)):
                    direction = 'right'
                elif (0 > angle3 > -45 or cy3 > self.img_np_list[2].shape[
                    0] * 0.27) and (((yx_rate < 2.2 and dist_judge < 0) or 0>angle3>-59) and (
                        0 > angle3 > -65 or (0 > angle3 > -73 and dist_judge < 0) or (
                        0 > angle3 > -79 and dist_judge < 0 and sup_angle > 12) or (
                                angle3 < 0 and sup_angle > 19.5 and dist_judge < 0) or (
                                dist_judge < 0 and img3_car_ps[0] < 20))):
                    direction = 'left'
                else:
                    direction = 'straight'
            else:
                if car_angle3 < 0 and (
                        (
                                car_angle3 > -80 and dist_judge < -car_w and yx_rate < 1.4 and turn_judge and sup_angle > 8) or (
                                car_angle3 > -60 and dist_judge < 0) or (
                                car_angle1 > 0 and sup_angle > 19.5 and cx3 < cx1) or (
                                sup_angle > 30 and dist_judge < 0)):
                    direction = 'left'
                elif car_angle3 > 0 and sup_angle > 16 and yx_rate < 1.4:
                    direction = 'left'
                elif car_angle3 > 0 and turn_judge and (
                        (car_angle3 < 80 and dist_judge > car_w and cy3 > self.img_np_list[2].shape[0] * 0.3) or (
                        car_angle3 < 60 and dist_judge > 0) or (
                                car_angle3 < 65 and dist_judge > 0.5 * car_w) or (
                                car_angle3 < 72 and dist_judge > 0.7 * car_w and car_angle1 > 0) or (
                                car_angle1 < 0 and sup_angle > 19.5 and cx3 > cx1) or sup_angle > 40):
                    direction = 'right'
                else:
                    direction = 'straight'
        elif img1_car_ps and img3_car_ps:
            print("***NO.2***")
            cx1 = (img1_car_ps[0] + img1_car_ps[2]) / 2
            cx3 = (img3_car_ps[0] + img3_car_ps[2]) / 2

            car_w = img1_car_ps[2] - img1_car_ps[0]
            car_h = img1_car_ps[3] - img1_car_ps[1]

            car3_w = img3_car_ps[2] - img3_car_ps[0]
            car3_h = img3_car_ps[3] - img3_car_ps[1]

            # 车辆长宽比
            car_hw = round(car_h / car_w, 3)
            car3_hw = round(car3_h / car3_w, 3)

            dist_judge = cx3 - cx1
            dist_judge_y = img3_car_ps[3] - img1_car_ps[3]
            yx_rate = abs(dist_judge_y) / dist_judge if dist_judge != 0 else 0

            car3_ch = (img3_car_ps[3] + img3_car_ps[1]) / 2
            if img2_car_ps:
                cx2 = (img2_car_ps[0] + img2_car_ps[2]) / 2
                car2_w = img2_car_ps[2] - img2_car_ps[0]
                car2_h = img2_car_ps[3] - img2_car_ps[1]

                dist_judge12 = cx2 - cx1
                dist_judge23 = cx3 - cx2
                dist_judge_y = img3_car_ps[3] - img2_car_ps[3]
                yx_rate = abs(dist_judge_y) / dist_judge23 if dist_judge23 != 0 else 0

                # min_w = min((car_w, car2_w, car3_w))
                # 大概率有转向
                if car_hw - car3_hw > 0.258:
                    if ((abs(yx_rate) > 2.2 or yx_rate == 0) and abs(dist_judge_y) > car3_h and car3_hw > 0.55) or (
                            car3_ch < 0.27 * self.img_np_list[2].shape[
                        0] and -dist_judge_y > 2 * car3_h and car3_hw > 0.65):
                        direction = 'straight'
                    elif dist_judge23 < 0:
                        direction = 'left'
                    elif dist_judge23 > 0:
                        direction = 'right'
                    else:
                        direction = 'straight'
                # 大概率直行
                else:
                    if car3_ch < 0.3 * self.img_np_list[2].shape[0] and -dist_judge_y > 2 * car3_h and car3_hw > 0.65:
                        direction = 'straight'
                    elif dist_judge12 * dist_judge23 < 0 and dist_judge23 / dist_judge12 < -1.8:
                        if dist_judge23 < 0:
                            direction = 'left'
                        elif dist_judge23 > 0:
                            direction = 'right'
                    elif -1.4 < yx_rate <= 2 and yx_rate != 0:
                        if dist_judge23 < 0:
                            direction = 'left'
                        elif dist_judge23 > 0:
                            direction = 'right'
                    else:
                        direction = 'straight'

                print("##########NO.2 DEBUG:", car_hw, car3_hw, dist_judge12,
                      dist_judge23, yx_rate, abs(dist_judge_y), car3_h, car3_ch)

                cv2.putText(self.img_np_list[2],
                            f"{car_hw:.3f} {car3_hw:.3f} {dist_judge12} {dist_judge23} {yx_rate:.3f} {abs(dist_judge_y)} {car3_h} {car3_ch}",
                            (int(self.img_np_list[2].shape[1] * 0.3), 120), cv2.FONT_HERSHEY_COMPLEX, 1,
                            (0, 0, 255), 2)
            else:
                # 大概率有转向
                if car3_ch < 0.3 * self.img_np_list[2].shape[0] and -dist_judge_y > 3 * car3_h and car3_hw > 0.65:
                    direction = 'straight'
                elif car_hw - car3_hw > 0.285:
                    if (abs(yx_rate) > 2.2 or yx_rate == 0) and abs(dist_judge_y) > car3_h:
                        direction = 'straight'
                    elif dist_judge < 0:
                        direction = 'left'
                    elif dist_judge > 0:
                        direction = 'right'
                # 大概率直行
                else:
                    if -1.4 < yx_rate <= 2 and yx_rate != 0:
                        if dist_judge < 0:
                            direction = 'left'
                        elif dist_judge > 0:
                            direction = 'right'
                    else:
                        direction = 'straight'

        elif car_angle3 and car_angle2:
            print("***No.3***")
            cx3 = (img3_car_ps[0] + img3_car_ps[2]) / 2
            cx2 = (img2_car_ps[0] + img2_car_ps[2]) / 2
            if (car_angle2 > 0 and car_angle3 < 0) or (car_angle2 < 0 and car_angle3 > 0):
                sup_angle = 180 - abs(car_angle2) - abs(car_angle3)
            else:
                sup_angle = abs(car_angle2 - car_angle3)
            if (car_angle3 < 0 and car_angle2 < 0) or (car_angle3 > 0 and car_angle2 > 0):
                dist_23 = car_angle3 - car_angle2
                if (0 > car_angle3 > -60 and cx3 < cx2) or (
                        0 > car_angle3 > -75 and (dist_23 > 12 or dist_23 < -20) and cx3 < cx2):
                    direction = 'left'
                elif (0 < car_angle3 < 60 and cx3 > cx2) or (
                        0 < car_angle3 < 75 and (dist_23 < -12 or dist_23 > 20) and cx3 > cx2):
                    direction = 'right'
                else:
                    direction = 'straight'
            elif sup_angle > 25:
                if 0 > car_angle3 > -80 and cx3 < cx2:
                    direction = 'left'
                elif 0 < car_angle3 < 80 and cx3 > cx2:
                    direction = 'right'
                else:
                    direction = 'straight'
        elif car_angle1 and car_angle2 and img3_car_head_cor:
            print("***No.4***")
            cx1 = img1_car_head_cor[0]
            cx2 = img2_car_head_cor[0]
            cx3 = img3_car_head_cor[0]
            cy3 = (img3_car_ps[1] + img3_car_ps[3]) / 2

            dist_judge = cx2 - cx1
            dist_judge32 = cx3 - cx2
            dist_judge31 = cx3 - cx1

            # print(dist_judge, dist_judge32, round(dist_judge32 / dist_judge, 3))
            # print("@@@",(img3_car_ps[3]-img1_car_ps[3])/dist_judge31)
            if (car_angle1 > 0 and car_angle2 < 0) or (car_angle1 < 0 and car_angle2 > 0):
                sup_angle = 180 - abs(car_angle1) - abs(car_angle2)
            else:
                sup_angle = abs(car_angle1 - car_angle2)
            car_w = img1_car_ps[2] - img1_car_ps[0]
            car_h = img1_car_ps[3] - img1_car_ps[1]

            car2_w = img2_car_ps[2] - img2_car_ps[0]

            car3_w = img3_car_ps[2] - img3_car_ps[0]
            car3_h = img3_car_ps[3] - img3_car_ps[1]

            # 3图车辆长宽比
            car3_hw = round(car3_h / car3_w, 3)
            if abs(dist_judge31 / (dist_judge + 0.1)) > 8 and abs(dist_judge31) > car_w and car3_hw < 0.6:
                x_judge = True
            if car_angle1 < -75:  # 达到偏移角度的最大值
                bias_angle = -car_angle1 - 90
                angle2 = car_angle2 + bias_angle
                angle2 = angle2 if angle2 > -90 else 180 - abs(angle2)
                if (0 < angle2 < 75 and dist_judge31 > 0) or (
                        angle2 > 0 and sup_angle > 15 and dist_judge31 > 0.4 * min((car_w, car2_w, car3_w))) or (
                        (car_angle1 > car_angle2 >= -90 or car_angle2 > 0) and min(
                    (car_w, car2_w, car3_w)) * 0.4 < 1.05 * abs(
                    dist_judge) < dist_judge32 and cy3 > self.img_np_list[2].shape[0] * 0.3):
                    direction = 'right'
                elif (0 > angle2 > -75 and dist_judge31 < 0) or (
                        car_angle1 < car_angle2 < 0 and dist_judge32 < 2 * -abs(dist_judge) < min(
                    (car_w, car2_w, car3_w)) * -0.4 and cy3 >
                        self.img_np_list[2].shape[0] * 0.3):
                    direction = 'left'
                else:
                    direction = 'straight'
            elif car_angle1 > 75:
                bias_angle = 90 - car_angle1
                angle2 = car_angle2 + bias_angle
                angle2 = angle2 if angle2 < 90 else abs(angle2) - 180
                if (0 < angle2 < 75 and dist_judge31 > 0) or (
                        car_angle1 > car_angle2 > 0 and dist_judge32 > 2 * abs(dist_judge) > min(
                    (car_w, car2_w, car3_w)) * 0.4 and cy3 >
                        self.img_np_list[2].shape[0] * 0.25):
                    direction = 'right'
                elif (0 > angle2 > -75 and dist_judge31 < 0) or (
                        angle2 < 0 and sup_angle > 15 and dist_judge31 < -0.4 * min((car_w, car2_w, car3_w))) or (
                        (car_angle1 < car_angle2 <= 90 or car_angle2 < 0) and dist_judge32 < -1.05 * abs(
                    dist_judge) < min((car_w, car2_w, car3_w)) * -0.4 and cy3 > self.img_np_list[2].shape[0] * 0.3):
                    direction = 'left'
                else:
                    direction = 'straight'
            else:
                print("***待调整阈值***")
                if car_angle2 < 0 and (car_angle2 > -80 and dist_judge < -car_w * 0.3) or (
                        car_angle2 > -70 and dist_judge < 0) or (
                        dist_judge32 < 4 * -abs(dist_judge) and cy3 > self.img_np_list[2].shape[0] * 0.3):
                    direction = 'left'
                elif car_angle2 > 0 and (car_angle2 < 80 and dist_judge > car_w * 0.3) or (
                        car_angle2 < 70 and dist_judge > 0) or (
                        dist_judge32 > 4 * abs(dist_judge) and cy3 > self.img_np_list[2].shape[0] * 0.3):
                    direction = 'right'
                else:
                    direction = 'straight'
            if x_judge:
                print("x_judge:", x_judge, direction)
                if dist_judge31 < 0:
                    direction = 'left'
                elif dist_judge31 > 0:
                    direction = 'right'
                else:
                    direction = 'stop'
        return direction, car_angle1, car_angle3, x_judge

    def car_Lane_pos_judge(self):
        img1 = self.img_np_list[0]
        img1_car_ps = self.img_car_ps_list[0]
        img2_car_ps = self.img_car_ps_list[1]
        img3_car_ps = self.img_car_ps_list[2]
        img_W = img1.shape[1]
        img_H = img1.shape[0]
        if img1_car_ps != []:
            img1_car_cor = [int((img1_car_ps[0] + img1_car_ps[2]) / 2), int((img1_car_ps[1] + img1_car_ps[3]) / 2)]
            img1_lx = round((img1_car_cor[0] / img_W), 4)
            img1_ly = round((img1_car_cor[1] / img_H), 4)
        else:
            img1_car_cor = []
            img1_lx = 0
            img1_ly = 0
        if img2_car_ps != []:
            img2_car_cor = [int((img2_car_ps[0] + img2_car_ps[2]) / 2), int((img2_car_ps[1] + img2_car_ps[3]) / 2)]
            img2_lx = round((img2_car_cor[0] / img_W), 4)
            img2_ly = round((img2_car_cor[1] / img_H), 4)
        else:
            img2_car_cor = []
            img2_lx = 0
            img2_ly = 0
        if img3_car_ps != []:
            img3_car_cor = [int((img3_car_ps[0] + img3_car_ps[2]) / 2), int((img3_car_ps[1] + img3_car_ps[3]) / 2)]
            img3_lx = round((img3_car_cor[0] / img_W), 4)
            img3_ly = round((img3_car_cor[1] / img_H), 4)
        else:
            img3_car_cor = []
            img3_lx = 0
            img3_ly = 0
        angle1 = None
        angle2 = None
        if len(img1_car_ps) and len(img3_car_ps):
            h2 = (img3_car_cor[1] - img1_car_cor[1])
            w2 = (img3_car_cor[0] - img1_car_cor[0])
            if w2 != 0:
                tan_theta2 = h2 / w2
                angle1 = round(float(np.degrees(np.arctan(-tan_theta2))), 3)

        if len(img2_car_ps) and len(img3_car_ps):
            h2 = img3_car_cor[1] - img2_car_cor[1]
            w2 = img3_car_cor[0] - img2_car_cor[0]
            if w2 != 0:
                tan_theta2 = h2 / w2
                angle2 = round(float(np.degrees(np.arctan(-tan_theta2))), 3)

        direction = None
        if angle1:
            if angle1 > 5 and (img1_lx > 0.5 or img3_lx > 0.65) and img3_lx != 0 and img1_lx != 0 and self.lane_list[
                2] != 0:
                direction = 'right'
            elif angle1 < -5 and (img1_lx < 0.5 or img3_lx < 0.35) and self.lane_list[0] != 0:
                direction = 'left'
            elif self.lane_list[1] != 0:
                direction = 'straight'
        if angle2:
            if angle2 > 5 and (img2_lx > 0.55 or img3_lx > 0.65) and img3_lx != 0 and img2_lx != 0 and self.lane_list[
                2] != 0:
                direction = 'right'
            elif angle2 < -5 and (img2_lx < 0.45 or img3_lx < 0.35) and self.lane_list[0] != 0:
                direction = 'left'
            elif self.lane_list[1] != 0:
                direction = 'straight'

        if not angle1 and not angle2:
            direction = 'straight'
        return direction

    # 弃用
    def driv_dire_judge(self):  # 三个判断函数联合判断
        car_angle1 = None
        dir_tmp = None
        if self.lane_pos_judge:
            direction = self.car_Lane_pos_judge()
            print("11111111111111111111111111", direction)
        direction = self.car_mid_pos_driv_dire_judge()
        print("222222222222222222222222222", direction)
        if self.car_body_direct_judge:
            dir_tmp, car_angle1, _ = self.car_body_pos_dire_judge()
            print("3333333333333333333333333333333", dir_tmp)
        if dir_tmp is not None and dir_tmp != 'straight':
            direction = dir_tmp
        if direction is None:
            direction = 'straight'
        print("4444444444444444444444444444444444444444", direction)
        return direction, self.img_car_ps_list[0], car_angle1

    # 三个方向判定方法联合判断
    def driv_dire_judge_16250(self):
        # 两张图片车辆中心点角度逻辑判定（一三图、二三图）
        direction1 = self.car_Lane_pos_judge()
        print("方法一判定方向：", direction1)
        # 两张图片相对位置判断车辆运动方向（一二图、二三图、一三图）
        direction2 = self.car_mid_pos_driv_dire_judge()
        print("方法二判定方向：", direction2)
        # 计算单图片夹角与位移关系判定
        direction3, _, angel3, x_judge = self.car_body_pos_dire_judge2()
        print("方法三判定方向：", direction3)
        sure = False
        if direction3 is not None:
            direction = direction3
        elif direction2 is not None:
            direction = direction2
        else:
            direction = direction1

        print("direction:", direction)
        return direction, self.img_car_ps_list[0], sure
