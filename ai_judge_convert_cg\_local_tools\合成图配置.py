import os

from src.utils.tools import cv_imread
import cv2

# 全局变量用于存储鼠标点击位置
click_point = (-1, -1)


def mouse_callback(event, x, y, flags, param):
    global click_point
    if event == cv2.EVENT_LBUTTONDOWN:
        # 存储点击位置
        click_point = (x, y)
        print(f"Clicked at: ({x}, {y})")


def draw_division_lines(image, crop_top=None, crop_bottom=None):
    height, width = image.shape[:2]
    x = 0
    y = 0
    w = 1
    h = 1
    if crop_top is not None:
        remaining_height = height - crop_top
        split_height = crop_top
        y = crop_top / height
        h = 1 - y
    elif crop_bottom is not None:
        remaining_height = crop_bottom
        split_height = crop_bottom
        h = crop_bottom / height
    else:
        remaining_height = height
        split_height = height

    half_width = width // 2
    half_height = remaining_height // 2

    # Draw lines for four divisions
    if crop_top is not None:
        start_y = crop_top
    else:
        start_y = 0

    cv2.line(image, (0, start_y + half_height), (width, start_y + half_height), (255, 255, 255),
             1)  # First horizontal line
    cv2.line(image, (half_width, start_y), (half_width, start_y + 2 * half_height), (255, 255, 255),
             1)  # Second horizontal line
    cv2.line(image, (0, split_height), (width, split_height), (0, 255, 0),
             1)

    return f"{x},{y},{w},{h}"


def resize_to_fit_screen(image, screen_width=1778, screen_height=1000):
    """根据屏幕尺寸调整图像大小"""
    height, width = image.shape[:2]
    scale_ratio = min(screen_width / width, screen_height / height)
    new_size = (int(width * scale_ratio), int(height * scale_ratio))
    return cv2.resize(image, new_size)


if __name__ == "__main__":
    root = r'E:\AI审片项目和资料汇总\合成图配置'
    name_list = os.listdir(root)
    split_char = "_"
    for name in name_list:
        if len(name.split(",")) == 4:
            continue
        img_path = f"{root}/{name}"
        if os.path.isdir(img_path) or not name.endswith("jpg"):
            continue
        src_img = cv_imread(img_path)
        original_img = src_img.copy()  # 保留原始图片副本以备后续操作使用

        cv2.namedWindow('image', cv2.WINDOW_AUTOSIZE)  # 使用WINDOW_NORMAL允许手动调整窗口大小
        cv2.setMouseCallback('image', mouse_callback)
        resized_img = resize_to_fit_screen(original_img)  # 调整图像大小以适应屏幕
        while True:
            res_rect = None
            img = resized_img.copy()

            if click_point[1] != -1:  # 如果用户进行了点击
                clicked_y = click_point[1]
                print("已点击，y值：",clicked_y)

                if clicked_y < img.shape[0] / 2:
                    # 如果点击在上半部，画出从点击点到图像底部的四等分线
                    res_rect = draw_division_lines(img, crop_top=clicked_y)
                else:
                    # 如果点击在下半部，画出从图像顶部到点击点的四等分线
                    res_rect = draw_division_lines(img, crop_bottom=clicked_y)

                cv2.imshow('image', img)  # 显示带有等分线的图像

                # 等待用户按键确认
                key = cv2.waitKey(0) & 0xFF  # 持续等待按键输入

                if key == ord('q'):  # 按 q 键退出
                    break
                click_point = (-1, -1)  # 重置点击点
            else:
                # print("未点击")
                cv2.imshow('image', img)  # 正常情况下显示图像

            key = cv2.waitKey(30) & 0xFF  # 等待一段时间获取按键输入

            if key == ord('q'):  # 按 q 键退出
                break

        cv2.destroyAllWindows()
        click_point = (-1, -1)  # 重置点击点
        print("res_rect:", res_rect)
        name_split = name.split(".")
        save_name = f"{name_split[0]}{split_char}{res_rect}.{name_split[1]}" if res_rect else name
        save_path = f"{root}/{save_name}"
        print(save_path)
        os.rename(img_path, save_path)
