from lpips import lpips
import torchvision.transforms as transforms
from PIL import Image
import cv2
import torch

transform = transforms.Compose([
    transforms.Resize([224, 224]),
    transforms.ToTensor(),
])
import time


class SIMILAR_JUDGE():
    def __init__(self):
        pass

    # 模型计算目标相似度
    def similar_judge(self, source_img, img_np_list,device=None):
        if device is None:
            device = "cuda:0" if torch.cuda.is_available() else "cpu"

        loss_fn_vgg = lpips.LPIPS(net='squeeze', device=device)

        s_img = source_img[1]
        s_img_tmp = transform(Image.fromarray(cv2.cvtColor(s_img, cv2.COLOR_BGR2RGB))).unsqueeze(0).to(device)
        img_dis_list = []
        img_list = []
        cor_list = []
        for i, img_np_info in enumerate(img_np_list):
            import datetime as dt
            s_time = dt.datetime.now()
            img_cor = img_np_info[0]
            img_np = img_np_info[1]
            img_np_tmp = transform(Image.fromarray(cv2.cvtColor(img_np, cv2.COLOR_BGR2RGB))).unsqueeze(0).to(device)
            with torch.no_grad():
                dis = loss_fn_vgg(s_img_tmp, img_np_tmp).detach().cpu().data.numpy()[0][0][0][0]

            # print('88888888888888', dis)
            # t_time = dt.datetime.now()
            # print('======', (t_time-s_time).seconds, dis)
            img_dis_list.append(dis)
            cor_list.append(img_cor)
            img_list.append(img_np)
        try:
            min_dis_index = img_dis_list.index(min(img_dis_list))
            img_img = img_list[min_dis_index]
            img_cor = cor_list[min_dis_index]
            dis = round(min(img_dis_list), 4)
        except:
            dis = 10000
            img_cor = []
            img_img = []
        return round(dis, 3), img_cor, img_img
