2023-05-18 14:07:00,098 INFO    StreamThr :200290 [internal.py:wandb_internal():89] W&B internal server running at pid: 200290, started at: 2023-05-18 14:07:00.097846
2023-05-18 14:07:00,102 DEBUG   HandlerThread:200290 [handler.py:handle_request():144] handle_request: status
2023-05-18 14:07:00,108 INFO    WriterThread:200290 [datastore.py:open_for_write():85] open: /home/<USER>/projects/ai_judge_simple/yolov7/wandb/run-20230518_140700-g8e8ru0v/run-g8e8ru0v.wandb
2023-05-18 14:07:00,110 DEBUG   SenderThread:200290 [sender.py:send():375] send: header
2023-05-18 14:07:00,110 DEBUG   SenderThread:200290 [sender.py:send():375] send: run
2023-05-18 14:07:00,124 INFO    SenderThread:200290 [sender.py:_maybe_setup_resume():762] checking resume status for None/YOLOR/g8e8ru0v
2023-05-18 14:07:05,108 DEBUG   HandlerThread:200290 [handler.py:handle_request():144] handle_request: keepalive
2023-05-18 14:07:10,110 DEBUG   HandlerThread:200290 [handler.py:handle_request():144] handle_request: keepalive
2023-05-18 14:07:15,114 DEBUG   HandlerThread:200290 [handler.py:handle_request():144] handle_request: keepalive
2023-05-18 14:07:20,117 DEBUG   HandlerThread:200290 [handler.py:handle_request():144] handle_request: keepalive
2023-05-18 14:07:25,121 DEBUG   HandlerThread:200290 [handler.py:handle_request():144] handle_request: keepalive
2023-05-18 14:07:30,125 DEBUG   HandlerThread:200290 [handler.py:handle_request():144] handle_request: keepalive
2023-05-18 14:07:33,882 INFO    SenderThread:200290 [retry.py:__call__():172] Retry attempt failed:
Traceback (most recent call last):
  File "/opt/anaconda3/envs/adet_train/lib/python3.7/site-packages/urllib3/connection.py", line 175, in _new_conn
    (self._dns_host, self.port), self.timeout, **extra_kw
  File "/opt/anaconda3/envs/adet_train/lib/python3.7/site-packages/urllib3/util/connection.py", line 95, in create_connection
    raise err
  File "/opt/anaconda3/envs/adet_train/lib/python3.7/site-packages/urllib3/util/connection.py", line 85, in create_connection
    sock.connect(sa)
socket.timeout: timed out

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/opt/anaconda3/envs/adet_train/lib/python3.7/site-packages/urllib3/connectionpool.py", line 710, in urlopen
    chunked=chunked,
  File "/opt/anaconda3/envs/adet_train/lib/python3.7/site-packages/urllib3/connectionpool.py", line 386, in _make_request
    self._validate_conn(conn)
  File "/opt/anaconda3/envs/adet_train/lib/python3.7/site-packages/urllib3/connectionpool.py", line 1042, in _validate_conn
    conn.connect()
  File "/opt/anaconda3/envs/adet_train/lib/python3.7/site-packages/urllib3/connection.py", line 363, in connect
    self.sock = conn = self._new_conn()
  File "/opt/anaconda3/envs/adet_train/lib/python3.7/site-packages/urllib3/connection.py", line 182, in _new_conn
    % (self.host, self.timeout),
urllib3.exceptions.ConnectTimeoutError: (<urllib3.connection.HTTPSConnection object at 0x7f63cc829590>, 'Connection to api.wandb.ai timed out. (connect timeout=10)')

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/opt/anaconda3/envs/adet_train/lib/python3.7/site-packages/requests/adapters.py", line 497, in send
    chunked=chunked,
  File "/opt/anaconda3/envs/adet_train/lib/python3.7/site-packages/urllib3/connectionpool.py", line 788, in urlopen
    method, url, error=e, _pool=self, _stacktrace=sys.exc_info()[2]
  File "/opt/anaconda3/envs/adet_train/lib/python3.7/site-packages/urllib3/util/retry.py", line 592, in increment
    raise MaxRetryError(_pool, url, error or ResponseError(cause))
urllib3.exceptions.MaxRetryError: HTTPSConnectionPool(host='api.wandb.ai', port=443): Max retries exceeded with url: /graphql (Caused by ConnectTimeoutError(<urllib3.connection.HTTPSConnection object at 0x7f63cc829590>, 'Connection to api.wandb.ai timed out. (connect timeout=10)'))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/opt/anaconda3/envs/adet_train/lib/python3.7/site-packages/wandb/sdk/lib/retry.py", line 131, in __call__
    result = self._call_fn(*args, **kwargs)
  File "/opt/anaconda3/envs/adet_train/lib/python3.7/site-packages/wandb/sdk/internal/internal_api.py", line 285, in execute
    return self.client.execute(*args, **kwargs)  # type: ignore
  File "/opt/anaconda3/envs/adet_train/lib/python3.7/site-packages/wandb/vendor/gql-0.2.0/wandb_gql/client.py", line 52, in execute
    result = self._get_result(document, *args, **kwargs)
  File "/opt/anaconda3/envs/adet_train/lib/python3.7/site-packages/wandb/vendor/gql-0.2.0/wandb_gql/client.py", line 60, in _get_result
    return self.transport.execute(document, *args, **kwargs)
  File "/opt/anaconda3/envs/adet_train/lib/python3.7/site-packages/wandb/sdk/lib/gql_request.py", line 55, in execute
    request = self.session.post(self.url, **post_args)
  File "/opt/anaconda3/envs/adet_train/lib/python3.7/site-packages/requests/sessions.py", line 635, in post
    return self.request("POST", url, data=data, json=json, **kwargs)
  File "/opt/anaconda3/envs/adet_train/lib/python3.7/site-packages/requests/sessions.py", line 587, in request
    resp = self.send(prep, **send_kwargs)
  File "/opt/anaconda3/envs/adet_train/lib/python3.7/site-packages/requests/sessions.py", line 701, in send
    r = adapter.send(request, **kwargs)
  File "/opt/anaconda3/envs/adet_train/lib/python3.7/site-packages/requests/adapters.py", line 507, in send
    raise ConnectTimeout(e, request=request)
requests.exceptions.ConnectTimeout: HTTPSConnectionPool(host='api.wandb.ai', port=443): Max retries exceeded with url: /graphql (Caused by ConnectTimeoutError(<urllib3.connection.HTTPSConnection object at 0x7f63cc829590>, 'Connection to api.wandb.ai timed out. (connect timeout=10)'))
2023-05-18 14:07:35,129 DEBUG   HandlerThread:200290 [handler.py:handle_request():144] handle_request: keepalive
2023-05-18 14:07:40,134 DEBUG   HandlerThread:200290 [handler.py:handle_request():144] handle_request: keepalive
2023-05-18 14:07:45,138 DEBUG   HandlerThread:200290 [handler.py:handle_request():144] handle_request: keepalive
2023-05-18 14:07:48,737 DEBUG   HandlerThread:200290 [handler.py:handle_request():144] handle_request: shutdown
2023-05-18 14:07:48,738 INFO    HandlerThread:200290 [handler.py:finish():842] shutting down handler
2023-05-18 14:07:49,125 INFO    WriterThread:200290 [datastore.py:close():298] close: /home/<USER>/projects/ai_judge_simple/yolov7/wandb/run-20230518_140700-g8e8ru0v/run-g8e8ru0v.wandb
2023-05-25 14:10:04,992 ERROR   StreamThr :200290 [internal.py:wandb_internal():174] Thread SenderThread:
Traceback (most recent call last):
  File "/opt/anaconda3/envs/adet_train/lib/python3.7/site-packages/urllib3/connection.py", line 175, in _new_conn
    (self._dns_host, self.port), self.timeout, **extra_kw
  File "/opt/anaconda3/envs/adet_train/lib/python3.7/site-packages/urllib3/util/connection.py", line 95, in create_connection
    raise err
  File "/opt/anaconda3/envs/adet_train/lib/python3.7/site-packages/urllib3/util/connection.py", line 85, in create_connection
    sock.connect(sa)
socket.timeout: timed out

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/opt/anaconda3/envs/adet_train/lib/python3.7/site-packages/urllib3/connectionpool.py", line 710, in urlopen
    chunked=chunked,
  File "/opt/anaconda3/envs/adet_train/lib/python3.7/site-packages/urllib3/connectionpool.py", line 386, in _make_request
    self._validate_conn(conn)
  File "/opt/anaconda3/envs/adet_train/lib/python3.7/site-packages/urllib3/connectionpool.py", line 1042, in _validate_conn
    conn.connect()
  File "/opt/anaconda3/envs/adet_train/lib/python3.7/site-packages/urllib3/connection.py", line 363, in connect
    self.sock = conn = self._new_conn()
  File "/opt/anaconda3/envs/adet_train/lib/python3.7/site-packages/urllib3/connection.py", line 182, in _new_conn
    % (self.host, self.timeout),
urllib3.exceptions.ConnectTimeoutError: (<urllib3.connection.HTTPSConnection object at 0x7f63cc33ddd0>, 'Connection to api.wandb.ai timed out. (connect timeout=10)')

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/opt/anaconda3/envs/adet_train/lib/python3.7/site-packages/requests/adapters.py", line 497, in send
    chunked=chunked,
  File "/opt/anaconda3/envs/adet_train/lib/python3.7/site-packages/urllib3/connectionpool.py", line 788, in urlopen
    method, url, error=e, _pool=self, _stacktrace=sys.exc_info()[2]
  File "/opt/anaconda3/envs/adet_train/lib/python3.7/site-packages/urllib3/util/retry.py", line 592, in increment
    raise MaxRetryError(_pool, url, error or ResponseError(cause))
urllib3.exceptions.MaxRetryError: HTTPSConnectionPool(host='api.wandb.ai', port=443): Max retries exceeded with url: /graphql (Caused by ConnectTimeoutError(<urllib3.connection.HTTPSConnection object at 0x7f63cc33ddd0>, 'Connection to api.wandb.ai timed out. (connect timeout=10)'))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/opt/anaconda3/envs/adet_train/lib/python3.7/site-packages/wandb/apis/normalize.py", line 41, in wrapper
  File "/opt/anaconda3/envs/adet_train/lib/python3.7/site-packages/wandb/sdk/internal/internal_api.py", line 980, in run_resume_status
  File "/opt/anaconda3/envs/adet_train/lib/python3.7/site-packages/wandb/sdk/internal/internal_api.py", line 260, in gql
  File "/opt/anaconda3/envs/adet_train/lib/python3.7/site-packages/wandb/sdk/lib/retry.py", line 131, in __call__
  File "/opt/anaconda3/envs/adet_train/lib/python3.7/site-packages/wandb/sdk/internal/internal_api.py", line 285, in execute
  File "/opt/anaconda3/envs/adet_train/lib/python3.7/site-packages/wandb/vendor/gql-0.2.0/wandb_gql/client.py", line 52, in execute
  File "/opt/anaconda3/envs/adet_train/lib/python3.7/site-packages/wandb/vendor/gql-0.2.0/wandb_gql/client.py", line 60, in _get_result
  File "/opt/anaconda3/envs/adet_train/lib/python3.7/site-packages/wandb/sdk/lib/gql_request.py", line 55, in execute
  File "/opt/anaconda3/envs/adet_train/lib/python3.7/site-packages/requests/sessions.py", line 635, in post
    return self.request("POST", url, data=data, json=json, **kwargs)
  File "/opt/anaconda3/envs/adet_train/lib/python3.7/site-packages/requests/sessions.py", line 587, in request
    resp = self.send(prep, **send_kwargs)
  File "/opt/anaconda3/envs/adet_train/lib/python3.7/site-packages/requests/sessions.py", line 701, in send
    r = adapter.send(request, **kwargs)
  File "/opt/anaconda3/envs/adet_train/lib/python3.7/site-packages/requests/adapters.py", line 507, in send
    raise ConnectTimeout(e, request=request)
requests.exceptions.ConnectTimeout: HTTPSConnectionPool(host='api.wandb.ai', port=443): Max retries exceeded with url: /graphql (Caused by ConnectTimeoutError(<urllib3.connection.HTTPSConnection object at 0x7f63cc33ddd0>, 'Connection to api.wandb.ai timed out. (connect timeout=10)'))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/opt/anaconda3/envs/adet_train/lib/python3.7/site-packages/wandb/sdk/internal/internal_util.py", line 49, in run
  File "/opt/anaconda3/envs/adet_train/lib/python3.7/site-packages/wandb/sdk/internal/internal_util.py", line 100, in _run
  File "/opt/anaconda3/envs/adet_train/lib/python3.7/site-packages/wandb/sdk/internal/internal.py", line 328, in _process
  File "/opt/anaconda3/envs/adet_train/lib/python3.7/site-packages/wandb/sdk/internal/sender.py", line 382, in send
  File "/opt/anaconda3/envs/adet_train/lib/python3.7/site-packages/wandb/sdk/internal/sender.py", line 946, in send_run
  File "/opt/anaconda3/envs/adet_train/lib/python3.7/site-packages/wandb/sdk/internal/sender.py", line 765, in _maybe_setup_resume
  File "/opt/anaconda3/envs/adet_train/lib/python3.7/site-packages/wandb/apis/normalize.py", line 87, in wrapper
  File "/opt/anaconda3/envs/adet_train/lib/python3.7/site-packages/wandb/apis/normalize.py", line 41, in wrapper
  File "/opt/anaconda3/envs/adet_train/lib/python3.7/site-packages/wandb/sdk/internal/internal_api.py", line 980, in run_resume_status
  File "/opt/anaconda3/envs/adet_train/lib/python3.7/site-packages/wandb/sdk/internal/internal_api.py", line 260, in gql
  File "/opt/anaconda3/envs/adet_train/lib/python3.7/site-packages/wandb/sdk/lib/retry.py", line 131, in __call__
  File "/opt/anaconda3/envs/adet_train/lib/python3.7/site-packages/wandb/sdk/internal/internal_api.py", line 285, in execute
  File "/opt/anaconda3/envs/adet_train/lib/python3.7/site-packages/wandb/vendor/gql-0.2.0/wandb_gql/client.py", line 52, in execute
  File "/opt/anaconda3/envs/adet_train/lib/python3.7/site-packages/wandb/vendor/gql-0.2.0/wandb_gql/client.py", line 60, in _get_result
  File "/opt/anaconda3/envs/adet_train/lib/python3.7/site-packages/wandb/sdk/lib/gql_request.py", line 55, in execute
  File "/opt/anaconda3/envs/adet_train/lib/python3.7/site-packages/requests/sessions.py", line 635, in post
    return self.request("POST", url, data=data, json=json, **kwargs)
  File "/opt/anaconda3/envs/adet_train/lib/python3.7/site-packages/requests/sessions.py", line 587, in request
    resp = self.send(prep, **send_kwargs)
  File "/opt/anaconda3/envs/adet_train/lib/python3.7/site-packages/requests/sessions.py", line 701, in send
    r = adapter.send(request, **kwargs)
  File "/opt/anaconda3/envs/adet_train/lib/python3.7/site-packages/requests/adapters.py", line 507, in send
    raise ConnectTimeout(e, request=request)
wandb.errors.CommError: HTTPSConnectionPool(host='api.wandb.ai', port=443): Max retries exceeded with url: /graphql (Caused by ConnectTimeoutError(<urllib3.connection.HTTPSConnection object at 0x7f63cc33ddd0>, 'Connection to api.wandb.ai timed out. (connect timeout=10)'))
