# 请求生产者
import sys

sys.path.append("..")
import base64
import os
from kafka import KafkaProducer
from main_config.config import *
from tqdm import tqdm
import json

def file_input():
    img_dir = "../test_dir/nj_data"
    name_list = os.listdir(img_dir)
    producer = KafkaProducer(bootstrap_servers=SERVER, value_serializer=lambda m: json.dumps(m).encode(),
                             max_request_size=20000000)

    for idx, name in enumerate(tqdm(name_list)):
        img_path = f"{img_dir}/{name}"
        plate = name.split("_")[-2]
        with open(img_path, "rb") as f:
            image = f.read()
            base64data = base64.b64encode(image)
            input_data = [{"file": base64data.decode()}]
            data = {"session_id": f"{idx}", "mode": 1, "violation_type": "1625", "plate_number": plate,
                    "images": input_data}
            producer.send(REQ_TOPIC, data)
            # producer.send(IMG_TOPIC, {"test": name})
            print(f"{idx + 1}/{len(name_list)}", name)
            producer.flush(3)

    producer.close()

if __name__ == '__main__':
    file_input()
