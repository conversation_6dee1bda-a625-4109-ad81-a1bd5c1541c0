# parameters
nc: 80  # number of classes
depth_multiple: 1.0  # model depth multiple
width_multiple: 1.0  # layer channel multiple

# anchors
anchors:
  - [12,16, 19,36, 40,28]  # P3/8
  - [36,75, 76,55, 72,146]  # P4/16
  - [142,110, 192,243, 459,401]  # P5/32

# CSP-ResNeXt backbone
backbone:
  # [from, number, module, args]
  [[-1, 1, <PERSON><PERSON>, [128]],  # 0-P1/2
   [-1, 3, Res<PERSON><PERSON><PERSON>, [128]],
   [-1, 1, Conv, [256, 3, 2]],  # 2-P3/8
   [-1, 4, Res<PERSON><PERSON><PERSON>, [256]],
   [-1, 1, Conv, [512, 3, 2]],  # 4-P3/8
   [-1, 6, Res<PERSON><PERSON><PERSON>, [512]],
   [-1, 1, Conv, [1024, 3, 2]],  # 6-P3/8
   [-1, 3, Res<PERSON><PERSON><PERSON>, [1024]],  # 7
  ]

# CSP-ResX-PAN head
head:
  [[-1, 1, <PERSON><PERSON><PERSON><PERSON>, [512]], # 8
   [-1, 1, Conv, [256, 1, 1]],
   [-1, 1, nn.Upsample, [None, 2, 'nearest']],
   [5, 1, Conv, [256, 1, 1]], # route backbone P4
   [[-1, -2], 1, Concat, [1]],
   [-1, 2, ResXCSPB, [256]], # 13
   [-1, 1, Conv, [128, 1, 1]],
   [-1, 1, nn.Upsample, [None, 2, 'nearest']],
   [3, 1, Conv, [128, 1, 1]], # route backbone P3
   [[-1, -2], 1, Concat, [1]],
   [-1, 2, ResXCSPB, [128]], # 18
   [-1, 1, Conv, [256, 3, 1]],
   [-2, 1, Conv, [256, 3, 2]],
   [[-1, 13], 1, Concat, [1]],  # cat
   [-1, 2, ResXCSPB, [256]], # 22
   [-1, 1, Conv, [512, 3, 1]],
   [-2, 1, Conv, [512, 3, 2]],
   [[-1, 8], 1, Concat, [1]],  # cat
   [-1, 2, ResXCSPB, [512]], # 26
   [-1, 1, Conv, [1024, 3, 1]],

   [[19,23,27], 1, IDetect, [nc, anchors]],   # Detect(P3, P4, P5)
  ]
