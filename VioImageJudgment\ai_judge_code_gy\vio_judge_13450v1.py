# 违反禁止标示线场景
import cv2
import numpy as np

from init_models import *


# 融合所有图片识别到的车辆、停止线、单双实线信息
def merge_img(car_mask, stop_mask, lane_mask, src_plate_num, save=False, save_path="."):
    car = np.empty((car_mask.shape[0], car_mask.shape[1], 3), dtype=int)
    #    car = np.expand_dims(car_mask, 2).repeat(3, axis=2)
    car_mask_arr = np.where(car_mask == 0, 0, 255) if len(car_mask) > 0 else np.full(car[:, :, 2].shape, 0)
    stop_mask_arr = np.where(stop_mask == 0, 0, 255) if len(stop_mask) > 0 else np.full(car[:, :, 1].shape, 0)
    lane_mask_arr = np.where(lane_mask == 0, 0, 255) if len(lane_mask) > 0 else np.full(car[:, :, 0].shape, 0)

    car[:, :, 2] = np.where(car_mask == 0, 0, 255) if len(car_mask) > 0 else np.full(car[:, :, 2].shape, 0)
    car[:, :, 1] = np.where(stop_mask == 0, 0, 255) if len(stop_mask) > 0 else np.full(car[:, :, 1].shape, 0)
    car[:, :, 0] = np.where(lane_mask == 0, 0, 255) if len(lane_mask) > 0 else np.full(car[:, :, 0].shape, 0)

    if save:
        n = 1
        # save_dir = '../test_13450/{}_{}'.format(src_plate_num, n)
        save_dir = f"{save_path}/{src_plate_num}_{n}"
        while os.path.exists(save_dir):
            n += 1
            save_dir = f"{save_path}/{src_plate_num}_{n}"

        os.makedirs(save_dir, exist_ok=True)

        car_save_name = save_dir + '/{}_car_{}.jpg'.format(src_plate_num, n)
        lane_save_name = save_dir + '/{}_lane_{}.jpg'.format(src_plate_num, n)
        stop_save_name = save_dir + '/{}_stop_{}.jpg'.format(src_plate_num, n)
        merge_save_name = save_dir + '/{}_merge_{}.jpg'.format(src_plate_num, n)
        cv2.imwrite(car_save_name, 0 + car_mask_arr)
        cv2.imwrite(stop_save_name, 0 + stop_mask_arr)
        cv2.imwrite(lane_save_name, 0 + lane_mask_arr)
        cv2.imwrite(merge_save_name, car.astype(int))
    return car


# 压线变道场景主框架函数
def judge_vio(src_plate_num, file_list, weights_list, resize_scale=0.7, save=False, save_path=".", extra_msg=None):
    # 可视化结果保存路径
    error_path = f"{save_path}/no_vio"
    vio_path = f"{save_path}/vio"
    blend_vis_path = f"{save_path}/blendmask"
    if save:
        os.makedirs(error_path, exist_ok=True)
        os.makedirs(vio_path, exist_ok=True)
        os.makedirs(blend_vis_path, exist_ok=True)
    # 读取模型
    plate_det_model = weights_list[0][0]
    plate_rec_model = weights_list[0][1]
    demo = weights_list[1]
    lp_model = weights_list[2][0]
    lp_meta = weights_list[2][1]
    yolov7_model = weights_list[3][0]
    yolov7_meta = weights_list[3][1]
    model_cnn = weights_list[-2]
    vio_model = weights_list[-1]

    vio_judge = 'no_judge'
    try:
        split_mode = extra_msg[0]
        black_height = extra_msg[1]
        black_pos = extra_msg[2]
        iieaAuditMode = extra_msg[3]
        iieaFilterVehicle = extra_msg[4]
    except:
        split_mode = 111
        black_height = 0
        black_pos = None
        iieaAuditMode = None
        iieaFilterVehicle = None
    judge_infos = {'plate_num': src_plate_num,
                   'vio_code': 1345,
                   'vio_judge': vio_judge,
                   'drv_direction': 0,
                   'drive_direction_light': [],
                   'lamp': [],
                   'target_car': [],
                   'stop_line': 0,
                   'police': 0,
                   'has_crossline': 0,
                   'vehicle_location': 0,
                   'lane': [],
                   'zebra_line': [],
                   'judgeConf': 1.0,
                   'modelDetectResults': [], 'split_mode': split_mode, "black_height": black_height, "black_pos":black_pos,"iieaAuditMode":iieaAuditMode,"iieaFilterVehicle":iieaFilterVehicle}
    # 特殊车牌废片判定
    if not src_plate_num:
        vio_judge = "no_vio_009"
        judge_infos['vio_judge'] = vio_judge
        return vio_judge, judge_infos
    if src_plate_num[0] not in chars:
        vio_judge = 'no_vio_002'
        judge_infos['vio_judge'] = vio_judge
        return vio_judge, judge_infos
    # 判定车牌开头结尾有无特殊字符
    vio_judge = VIO_JUDGE_TOOL().spe_no_vio_judge(src_plate_num,iieaFilterVehicle)
    if vio_judge.startswith('no_vio'):
        judge_infos['vio_judge'] = vio_judge
        return vio_judge, judge_infos
    vio_judge = 'no_vio'
    img3_car_ps = None
    try:
        if len(file_list) >= 3:
            img1, img2, img3 = file_list[:3]
            db_img_label = 0
        elif len(file_list) == 2:
            img1, img2 = file_list
            db_img_label = 1
            img3 = None
        else:
            vio_judge = 'vio_004'
            judge_infos['vio_judge'] = vio_judge
            judge_infos['judgeConf'] = 0.5
            return vio_judge, judge_infos
    except:
        error_msg = catch_error()
        print(error_msg)
        vio_judge = 'vio_004'
        judge_infos['vio_judge'] = vio_judge
        judge_infos['judgeConf'] = 0.1

        return vio_judge, judge_infos
    # try:
    #     img1, img2, img3 = Tools().resize_imgs(img1_np, img2_np, img3_np)
    # except Exception as e:
    #     import traceback
    #     traceback.print_exc()
    #     vio_judge = 'no_vio_012'
    #     judge_infos['vio_judge'] = vio_judge
    #     return vio_judge, judge_infos
    # try:
    #     img1, img2, img3 = Tools().resize_imgs(img1_np, img2_np, img3_np)
    # except:
    #     import traceback
    #     traceback.print_exc()
    #     vio_judge = 'no_vio_012'
    #     judge_infos['vio_judge'] = vio_judge
    #     return vio_judge, judge_infos

    # if img1.shape[0] > 4000 or img1.shape[1] > 4000:
    #     resize_scale = resize_scale
    #     if float(resize_scale) < 1:
    #         img1 = cv2.resize(img1, None, fx=resize_scale, fy=resize_scale)
    #         img2 = cv2.resize(img2, None, fx=resize_scale, fy=resize_scale)
    #         if not db_img_label:
    #             img3 = cv2.resize(img3, None, fx=resize_scale, fy=resize_scale)

    if db_img_label:
        file_list = [img1, img2]
    else:
        file_list = [img1, img2, img3]

    # 判定是否存在交警与辅警指挥
    if Police_Judge:
        p_res = []
        p_num = 0
        judge_p_num = np.min((np.ceil(len(file_list) / 3 * 2), len(file_list)))
        for i in file_list:
            _, p = detect_lp(i, lp_model, lp_meta[0], lp_meta[1], lp_meta[2], conf_thres=0.2, iou_thres=0.45)
            if p:
                p_num += 1
            p_res.append(p)

        print("p_num:", p_num)
        if p_num >= judge_p_num:
            vio_judge = 'no_vio_004'
            judge_infos['police'] = 1
            judge_infos['vio_judge'] = vio_judge
            code = vio_judge.split("_")[-1]
            if save:
                img_list = [img1, img2, img3]
                show_police(img_list, p_res)
                cv2.imwrite(f"{error_path}/{code}_{src_plate_num}_img1.jpg", img1)
                cv2.imwrite(f"{error_path}/{code}_{src_plate_num}_img2.jpg", img2)
                if not db_img_label:
                    cv2.imwrite(f"{error_path}/{code}_{src_plate_num}_img3.jpg", img3)
            return vio_judge, judge_infos
        else:
            judge_infos['police'] = 0
    # 获得目标车辆位置信息，车道线停止线mask信息
    predictions, cars_ps, all_car_ps, plate_list, cars_masks_list, kp_mask_list, lane_mask, stop_mask, rejudge_label = \
        mask_gen(src_plate_num, file_list, plate_det_model, plate_rec_model, demo, device=yolov7_meta[2], merge=True,
                 save=save, save_path=f"{save_path}/mask_gen", judge_infos=judge_infos)

    print("pred plate:", plate_list)
    for i, value in enumerate(plate_list):
        temp_tar_car = {'plate_num': '', 'car_ps': ''}
        try:
            temp_tar_car['plate_num'] = plate_list[i]
        except:
            pass
        try:
            temp_tar_car['car_ps'] = cars_ps[i]
        except:
            pass
        judge_infos['target_car'].append(temp_tar_car)
    # 白名单车辆
    if judge_infos['vio_judge'] == "no_vio_010":
        vio_judge = 'no_vio_010'
        return vio_judge, judge_infos
    # 信息缺失报废片
    if db_img_label:
        img1_car_mask, img2_car_mask = cars_masks_list
        if len(img1_car_mask) == len(img2_car_mask) == len(lane_mask) == len(stop_mask) == 0:
            vio_judge = 'no_vio_005'
            judge_infos['vio_judge'] = vio_judge
            judge_infos['judgeConf'] = 0.7
            return vio_judge, judge_infos
    else:
        img1_car_mask, img2_car_mask, img3_car_mask = cars_masks_list
        if len(img1_car_mask) == len(img2_car_mask) == len(img3_car_mask) == len(lane_mask) == len(stop_mask) == 0:
            vio_judge = 'no_vio_005'
            judge_infos['vio_judge'] = vio_judge
            judge_infos['judgeConf'] = 0.7
            return vio_judge, judge_infos
    # 车牌识别无法匹配目标车辆报废片
    res_list = []
    for plate in plate_list:
        if plate == "NO_DETECT":
            res_list.append(0)
        else:
            res_list.append(1)
    if np.sum(res_list) == 0:
        vio_judge = 'no_vio_006'
        judge_infos['vio_judge'] = vio_judge
        judge_infos['judgeConf'] = 0.9
        return vio_judge, judge_infos
    # print(img1_car_mask.shape, stop_mask.shape, lane_mask.shape)
    car1 = merge_img(img1_car_mask, stop_mask, lane_mask, src_plate_num, save=save, save_path=blend_vis_path)
    # cv2.imwrite("1345_1.jpg",car1)
    # iiea审核模式
    if iieaAuditMode is None:
        judge_mode = Precision_Judge_Mode[str(judge_infos['vio_code'])]
    else:
        judge_mode = iieaAuditMode
    judge_thresh = Judge_Thresh[str(judge_infos['vio_code'])][int(judge_mode)]
    # 模型违法判定（一二三图存在任意一张压线违法）
    vio_or_no1 = Vio_pred(car1, model_cnn, yolov7_meta[2])
    pred_label1 = vio_or_no1.pred(judge_mode,judge_thresh)
    if pred_label1 == 1:
        pred_label2 = 0
    else:
        car2 = merge_img(img2_car_mask, stop_mask, lane_mask, src_plate_num, save=save, save_path=blend_vis_path)
        # cv2.imwrite("1345_2.jpg", car2)
        vio_or_no2 = Vio_pred(car2, model_cnn, yolov7_meta[2])
        pred_label2 = vio_or_no2.pred(judge_mode,judge_thresh)
    if not db_img_label:
        if pred_label1 == 1 or pred_label2 == 1:
            pred_label3 = 0
        else:
            car3 = merge_img(img3_car_mask, stop_mask, lane_mask, src_plate_num, save=save, save_path=blend_vis_path)
            # cv2.imwrite("1345_3.jpg", car3)

            vio_or_no3 = Vio_pred(car3, model_cnn, yolov7_meta[2])
            pred_label3 = vio_or_no3.pred(judge_mode,judge_thresh)
        if np.array([pred_label1, pred_label2, pred_label3]).any() == 1:
            vio_judge = 'vio'
        else:
            vio_judge = 'no_vio'
        img1_car_ps, img2_car_ps, img3_car_ps = cars_ps
    else:
        if np.array([pred_label1, pred_label2]).any() == 1:
            vio_judge = 'vio'
        else:
            vio_judge = 'no_vio'
        img1_car_ps, img2_car_ps = cars_ps

    try:
        x_ratio = ((img1_car_ps[0] + img1_car_ps[2]) / 2) / img1.shape[1]
    except:
        x_ratio = 0
    if x_ratio <= 0.5:
        judge_infos['vehicle_location'] = 0
    else:
        judge_infos['vehicle_location'] = 1

    if not db_img_label:
        judge_infos['img3_car_ps'] = img3_car_ps

    if vio_judge.startswith('vio') and rejudge_label == 0:
        vio_judge = 'vio'
    if vio_judge.startswith('vio') and rejudge_label == 1:
        vio_judge = 'vio_002'
    if vio_judge.startswith('vio') and rejudge_label == 2:
        vio_judge = 'vio_003'

    if vio_judge.startswith('vio'):
        judge_infos['has_crossline'] = 1
    judge_infos['vio_judge'] = vio_judge
    if vio_judge.startswith('vio'):
        judge_infos['judgeConf'] = 0.85
    else:
        judge_infos['judgeConf'] = 0.7

    # 可视化结果保存
    try:
        if save:
            if not db_img_label:
                img_list = [img1, img2, img3]
            else:
                img_list = [img1, img2]
            if db_img_label and all_car_ps:
                car1_bbox_ps, car2_bbox_ps = all_car_ps
                show_cars(img1, img2, None, car1_bbox_ps, car2_bbox_ps, None)

            elif all_car_ps:
                car1_bbox_ps, car2_bbox_ps, car3_bbox_ps = all_car_ps
                show_cars(img1, img2, img3, car1_bbox_ps, car2_bbox_ps, car3_bbox_ps)
            if Police_Judge:
                show_police(img_list, p_res)
            show_plate(img1, img2, img3, img1_car_ps, img2_car_ps, img3_car_ps)
            if vio_judge.startswith("no_vio"):
                cv2.imwrite(error_path + f'/{vio_judge}_{src_plate_num}_img1.jpg', img1)
                cv2.imwrite(error_path + f'/{vio_judge}_{src_plate_num}_img2.jpg', img2)
                if not db_img_label:
                    cv2.imwrite(error_path + f'/{vio_judge}_{src_plate_num}_img3.jpg', img3)
            else:
                cv2.imwrite(vio_path + f'/{vio_judge}_{src_plate_num}_img1.jpg', img1)
                cv2.imwrite(vio_path + f'/{vio_judge}_{src_plate_num}_img2.jpg', img2)
                if not db_img_label:
                    cv2.imwrite(vio_path + f'/{vio_judge}_{src_plate_num}_img3.jpg', img3)
    except Exception as e:
        print("压线图片可视化异常", e)
    return vio_judge, judge_infos
