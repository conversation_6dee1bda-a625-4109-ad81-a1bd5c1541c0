import os
import shutil
from tqdm import tqdm
from src.utils.tools import catch_error
vis_root = r"E:\AI审片项目和资料汇总\已审核数据\闯红灯\高检出率测试\待测试"
src_root = r"E:\AI审片项目和资料汇总\已审核数据\闯红灯\高检出率测试\正片"
src_root2 = r"E:\AI审片项目和资料汇总\已审核数据\闯红灯\高检出率测试\废片"

save_root = f"{vis_root}/src"#f"E:\AI审片项目和资料汇总\已审核数据\闯红灯\高检出率测试\废片"
is_move = False
os.makedirs(save_root,exist_ok=True)
vis_plate_list =[name.split("_")[-2] for name in os.listdir(vis_root) if name.endswith(".jpg")]
for name in tqdm(os.listdir(src_root)):
    plate = name.split("_")[-2]
    if plate in vis_plate_list:
        try:
            if is_move:
                shutil.move(f"{src_root}/{name}",f"{save_root}/{name}")
            else:
                shutil.copy(f"{src_root}/{name}",f"{save_root}/{name}")
        except:
            error_msg= catch_error()
            print(error_msg)
for name in tqdm(os.listdir(src_root2)):
    plate = name.split("_")[-2]
    if plate in vis_plate_list:
        try:
            if is_move:
                shutil.move(f"{src_root2}/{name}",f"{save_root}/{name}")
            else:
                shutil.copy(f"{src_root2}/{name}",f"{save_root}/{name}")
        except:
            error_msg= catch_error()
            print(error_msg)