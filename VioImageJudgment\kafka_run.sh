#!/bin/bash
export LANG=C.UTF-8
export LD_LIBRARY_PATH=$LD_LIBRARY_PATH:/usr/local/cuda/lib64
cp -f /home/<USER>/ai_judge/Shanghai /etc/localtime
echo 'Aisa/Shanghai' > /etc/timezone
cd /home/<USER>/ai_judge/ai_judge_code_gy/
if [ -d "./logs" ]; then
    rm -rf ./logs
    mkdir "logs"
else
    mkdir "logs"
fi
nohup /opt/anaconda3/bin/python kafka_main_multi.py >> logs/log.txt 2>&1 &
#cd /home/<USER>/ai_judge/
#sleep 600
#counter=1
#while [ $counter -le 10 ]
#do
#   /opt/anaconda3/bin/python kafka_log_analy.py > ./log_analy.txt 2>&1 &
#   sleep 600
#done
/bin/bash
