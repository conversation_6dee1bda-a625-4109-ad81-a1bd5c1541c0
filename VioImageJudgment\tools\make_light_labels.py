# 1.生成json标签（labelme）2.json2xml 3.train_eval.py 4.yolo_label.py

import os

import sys

sys.path.append('..')
sys.path.append('../yolov7')
sys.path.append('../ai_judge_code_gy')

import json
import cv2
import numpy as np
from yolov7.yolo_interface import load_lp, detect_lp
from main_config.config import *
from tqdm import tqdm
import random
import shutil
from ai_judge_code_gy.judge_tools import Tools
from ai_judge_code_gy.init_models import img_split

def make_labelme_json(R, img_name, h, w, save_path):
    base_json = {
        "version": "4.5.13",
        "flags": {},
        "shapes": [],
        "imagePath": f"{img_name}.jpg",
        "imageData": None,
        "imageHeight": h,
        "imageWidth": w
    }
    for res in R:
        box = eval(res[0])
        label = res[1].split("-")[0]
        tmp_shapes = {"label": label,
                      "points": box,
                      "group_id": None,
                      "shape_type": "rectangle",
                      "flags": {}}
        base_json["shapes"].append(tmp_shapes)

    with open(f"{save_path}/{img_name}.json", "w", encoding="utf-8") as f:
        json.dump(base_json, f, indent=2)
        print(f"{img_name}.json is saved!")




if __name__ == '__main__':
    # 特殊格式数据
    up_down_3 = ["3ed7d27c0cc346c884fe271e346bd891", "6bc850d52ce44673acd23b2a814c984d"]
    # 13                                # 03
    index_error = ["008f7aa6fbdb474095e74e0651c5e630", "249f9f92f6294819b816349ba582d17f"]
    split_6 = "fe3eb21fa3cd42cf9e2198a8997bc548"  # 0 2
    img_dir = "./2"
    img_list = os.listdir(img_dir)
    print(len(img_list))
    img_list = sorted(img_list, key=lambda x: x.split("_"))
    lp_model, imgsz, stride, device = load_lp("../models/best0820.pt")

    save_path = "../add_light_labels"
    os.makedirs(save_path, exist_ok=True)
    # for n, img_name in enumerate(tqdm(img_list)):
    #     image = cv2.imread(f"{img_dir}/{img_name}")
    #     name_split = img_name.split("_")
    #     new_name = f"JZ2_{name_split[1]}_{name_split[0]}_{n}" if len(name_split) > 4 else f"JZ_NoIndex_{n}"
    #     if name_split[1] in up_down_3:
    #         split_mode = 7
    #         choose_idx = [0, 2]
    #     elif name_split[1] == split_6:
    #         split_mode = 6
    #         choose_idx = [0, 1]
    #     elif name_split[1] == index_error[0]:
    #         split_mode = 2
    #         choose_idx = [1, 3]
    #     elif name_split[1] == index_error[1]:
    #         split_mode = 2
    #         choose_idx = [0, 3]
    #     else:
    #         split_mode = 2
    #         choose_idx = [0, 2]
    #     for idx, img in enumerate(split_img(image, split_mode)):
    #         if idx not in choose_idx:
    #             continue
    #         h, w = img.shape[:2]
    #         l, p = detect_lp(img, lp_model, imgsz, stride, device, conf_thres=0.15, iou_thres=0.45)
    #         make_labelme_json(l, new_name + f"_{idx + 1}", h, w, save_path)
    #         cv2.imwrite(f"{save_path}/{new_name}_{idx + 1}.jpg", img)

    # 已切分的图片
    # for n, img_name in enumerate(tqdm(img_list)):
    #     image = cv2.imread(f"{img_dir}/{img_name}")
    #     h, w = image.shape[:2]
    #     l, p = detect_lp(image, lp_model, imgsz, stride, device, conf_thres=0.15, iou_thres=0.45)
    #     json_name = img_name.split(".")[0]
    #     make_labelme_json(l, json_name, h, w, save_path)
    #     shutil.copy(f"{img_dir}/{img_name}", save_path)

    # 未切分图片使用自动切分
    name_idx = "QC_Light_Data"
    num = 1
    for n, img_name in enumerate(tqdm(img_list)):
        image = cv2.imread(f"{img_dir}/{img_name}")
        split_mode, black_height_rate,black_pos = Tools().cut(image,"1625")
        print(SPLIT_MODE_DESC[split_mode],black_height_rate)
        file_list = img_split(image,split_mode,black_height_rate,black_pos=black_pos)
        for idx,img in enumerate(file_list):
            if idx not in [0,2]:
                continue
            h, w = img.shape[:2]
            l, p = detect_lp(img, lp_model, imgsz, stride, device, conf_thres=0.15, iou_thres=0.45)
            json_name = f"{name_idx}_{num}"
            make_labelme_json(l, json_name, h, w, save_path)
            cv2.imwrite(f"{save_path}/{json_name}.jpg",img)
            num+=1