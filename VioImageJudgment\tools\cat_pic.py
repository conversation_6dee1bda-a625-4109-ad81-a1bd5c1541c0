import cv2
import numpy as np
import os
import shutil
from tqdm import tqdm
print("开始拼接车牌识别结果...")
save_path = "../cat_result"
if os.path.exists(save_path):
    shutil.rmtree(save_path)
os.makedirs(save_path, exist_ok=True)
dir_list = [i for i in os.listdir(".") if not os.path.isfile(i)]
if len(dir_list):
    file_name = os.listdir(dir_list[0])
    for i in tqdm(file_name):
        print(i)
        img_list = []
        for d in dir_list:
            path = f"{d}/{i}"
            img_list.append(cv2.imread(path))
        h, w, _ = img_list[0].shape
        title = np.ones((50, w * len(dir_list), 3), dtype=np.uint8) * 177
        sz = min((round(min((h, w)) / 180, 1), 1.5))
        for idx, j in enumerate(dir_list):
            pt = (w // 3 + idx * w, 30)
            cv2.putText(title, j, pt, cv2.FONT_HERSHEY_COMPLEX, sz,
                        (0, 0, 255), 2)
        # cat
        img_cat = np.concatenate([i for i in img_list], 1)
        new_img = np.concatenate((title, img_cat), 0)
        cv2.imwrite(f"{save_path}/{i}", new_img)
