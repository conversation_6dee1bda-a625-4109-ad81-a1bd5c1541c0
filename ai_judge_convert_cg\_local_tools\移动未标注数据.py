import os
import shutil

from tqdm import tqdm
root = r"D:\phone_data\test"
save_path = r"D:\phone_data\unsure"
os.makedirs(save_path,exist_ok=True)
name_list = list(set(i.split(".")[0] for i in os.listdir(root)))
num = 0
for name in tqdm(name_list):
    if not os.path.exists(f"{root}/{name}.json"):
        shutil.move(f"{root}/{name}.jpg",save_path)
        num+=1
print(f"总数量：{len(name_list)} 未标注数量：{num}")