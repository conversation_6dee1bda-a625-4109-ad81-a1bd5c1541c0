"""EfficientNet architecture.

See:
- https://arxiv.org/abs/1905.11946 - EfficientNet
- https://arxiv.org/abs/1801.04381 - MobileNet V2
- https://arxiv.org/abs/1905.02244 - MobileNet V3
- https://arxiv.org/abs/1709.01507 - Squeeze-and-Excitation
- https://arxiv.org/abs/1803.02579 - Concurrent spatial and channel squeeze-and-excitation
"""

import os
import torch
import torch.nn as nn
import torch.nn.functional as F

import math
import numpy as np
import collections

#from layers import (DropConnect, SamePadConv2d, Attention, 
#                    Swish, conv_bn_act, Flattener)

batch_norm_momentum=0.010000000000000009
batch_norm_epsilon=1e-3


class CBAMLayer(nn.Module):
    def __init__(self, channel, reduction=16, spatial_kernel=7, activation=torch.nn.ReLU6):
        super(CBAMLayer, self).__init__()
        # channel attention
        self.avg_pool = nn.AdaptiveAvgPool2d(1)
        self.max_pool = nn.AdaptiveMaxPool2d(1)
        self.mlp = nn.Sequential(
            nn.Conv2d(channel, channel // reduction, 1, bias=False),
            nn.ReLU(inplace=True),
            nn.Conv2d(channel // reduction, channel, 1, bias=False),
        )
        # spatial attention
        self.conv = nn.Conv2d(2, 1, kernel_size=spatial_kernel, padding=spatial_kernel//2, bias=False)
        self.sigmoid = nn.Sigmoid()

    def forward(self, x):
        # channel attention
        max_out = self.mlp(self.max_pool(x))
        avg_out = self.mlp(self.avg_pool(x))
        channel_out = self.sigmoid(max_out + avg_out)
        x = channel_out * x

        # spatial attention
        max_out, _ = torch.max(x, dim=1, keepdim=True)
        avg_out = torch.mean(x, dim=1, keepdim=True)
        spatial_out = self.sigmoid(self.conv(torch.cat([max_out, avg_out], dim=1)))
        x = spatial_out * x
        return x


class SELayer(nn.Module):
    def __init__(self, channel, reduction=16, activation=torch.nn.ReLU6):
        """Channel attention."""
        super().__init__()
        self.avg_pool = nn.AdaptiveAvgPool2d(1)
        self.max_pool = nn.AdaptiveMaxPool2d(1)
        self.mlp = nn.Sequential(
            nn.Conv2d(channel, channel//reduction, kernel_size=1, stride=1, padding=0, bias=True),
            activation(),
            nn.Conv2d(channel//reduction, channel, kernel_size=1, stride=1, padding=0, bias=True),
        )
        self.sigmoid = nn.Sigmoid()
      
    def forward(self, x):
        max_out = self.mlp(self.max_pool(x))
        avg_out = self.mlp(self.avg_pool(x))
        channel_out = self.sigmoid(max_out + avg_out)
        x = channel_out * x
        return x
    
    
class Attention(nn.Module):
    def __init__(self, channels, reduction=16):
        super().__init__()
        self.attention = nn.Sequential(
            nn.AdaptiveAvgPool2d(1),
            nn.Conv2d(channels, channels//reduction, kernel_size=1, stride=1, padding=0, bias=True),
            Swish(),
            nn.Conv2d(channels//reduction, channels, kernel_size=1, stride=1, padding=0, bias=True),
        )

    def forward(self, x):
        return x * torch.sigmoid(self.attention(x))
        
    
class Flatten(nn.Module):      
    def forward(self, inp): return inp.view(inp.size(0), -1)

    
class Swish(nn.Module):
    def forward(self, x):
        return x * torch.sigmoid(x)
    
    
class DropConnect(nn.Module):
    def __init__(self, ratio):
        super().__init__()
        self.ratio = 1.0 - ratio

    def forward(self, x):
        return x
#         if not self.training:
#             return x

#         random_tensor = self.ratio
#         random_tensor += torch.rand([x.shape[0], 1, 1, 1], dtype=torch.float, device=x.device)
#         random_tensor.requires_grad_(False)
#         return x / self.ratio * random_tensor.floor()
    

class AdaptiveConcatPool2d(nn.Module):
    "Layer that concats `AdaptiveAvgPool2d` and `AdaptiveMaxPool2d`."
    def __init__(self, output_size):
        super().__init__()
        self.ap = nn.AdaptiveAvgPool2d(output_size)
        self.mp = nn.AdaptiveMaxPool2d(output_size)
        
    def forward(self, x): 
        return torch.cat([self.mp(x), self.ap(x)], 1)
        
            
def custom_head(num_classes=1000, num_feat=1024, ps=0.5):
    """Head leveraged from fast.ai library. Dropout assigned in params.json.
    ps assigned to last fc layer. The layer before has ps/2. The same as in 
    fast.ai."""
    return nn.Sequential(
        Flatten(),
        nn.BatchNorm1d(num_features=num_feat),
        nn.Dropout(p=ps/2),
        nn.Linear(in_features=num_feat, out_features=num_feat // 2, bias=True),
        nn.ReLU(inplace=True),
        nn.BatchNorm1d(num_features=num_feat // 2),
        nn.Dropout(p=ps),
        nn.Linear(in_features=num_feat // 2, out_features=num_classes, bias=True),
    )


class Upsample(nn.Module):
    def __init__(self, scale):
        super(Upsample, self).__init__()
        self.scale = scale

    def forward(self, x):
        return F.interpolate(x, scale_factor=self.scale, mode='bilinear', align_corners=False)
    

def conv_bn_act(inp, oup, kernel_size, stride=1, groups=1, bias=True, eps=1e-3, momentum=0.01):
    return nn.Sequential(
        SamePadConv2d(inp, oup, kernel_size, stride, groups=groups, bias=bias),
        nn.BatchNorm2d(oup, eps, momentum),
        Swish()
    )


class SamePadConv2d(nn.Conv2d):
    """
    Conv with TF padding='same'
    https://github.com/pytorch/pytorch/issues/3867#issuecomment-349279036
    It means if we have kernel_size=5 and we want to leave conv output
    be the same size as input we need to define how many pads should we have.
    In TF we have option padding='same' but in Pytorch we need to provide
    number of paddings. So we class is analog of TF padding='same'.
    As you can see we don't have `padding` as input to this class, since 
    it we be calc automaticaly.
    """

    def __init__(self, inp, oup, kernel_size, stride=1, dilation=1, groups=1, bias=True, padding_mode="zeros"):
        super().__init__(inp, oup, kernel_size, stride, 0, dilation, groups, bias, padding_mode)

    def get_pad_odd(self, in_, weight, stride, dilation):
        effective_filter_size_rows = (weight - 1) * dilation + 1
        out_rows = (in_ + stride - 1) // stride
        padding_needed = max(0, (out_rows - 1) * stride + effective_filter_size_rows - in_)
        padding_rows = max(0, (out_rows - 1) * stride + (weight - 1) * dilation + 1 - in_)
        rows_odd = (padding_rows % 2 != 0)
        return padding_rows, rows_odd

    def forward(self, x):
        padding_rows, rows_odd = self.get_pad_odd(x.shape[2], self.weight.shape[2], self.stride[0], self.dilation[0])
        padding_cols, cols_odd = self.get_pad_odd(x.shape[3], self.weight.shape[3], self.stride[1], self.dilation[1])

        if rows_odd or cols_odd:
            x = F.pad(x, [0, int(cols_odd), 0, int(rows_odd)])
            
        result=F.conv2d(x, self.weight, self.bias, self.stride,
                        padding=(padding_rows // 2, padding_cols // 2),dilation=self.dilation, groups=self.groups)

        return result


class Flattener:
    """Flatten modules in a list. Even with modules in modules.
    Works recursively.
    
    Example:
    
    flatener = Flattener()
    flatener(model.features[15])
    
    Returns a list of all layers in a module.
    """
    def __init__(self):
        self.flattened_module = []
    
    def flat(self, module):
        flattened_module = []
        childrens = list(module.children())
        for children in childrens:
            flattened_module.append(children)
        return flattened_module
    
    def __call__(self, module):
        """Recursive function."""
        childrens = list(module.children())
        for children in childrens:
            if len(self.flat(children))==0:
                self.flattened_module.append(children)
            else:
                self.__call__(children) 
        return self.flattened_module

EfficientNetParam = collections.namedtuple(
    "EfficientNetParam", ["width", "depth", "resolution", "dropout"])

EfficientNetParams = {
  "B0": EfficientNetParam(1.0, 1.0, 224, 0.2),
  "B1": EfficientNetParam(1.0, 1.1, 240, 0.2),
  "B2": EfficientNetParam(1.1, 1.2, 260, 0.3),
  "B3": EfficientNetParam(1.2, 1.4, 300, 0.3),
  "B4": EfficientNetParam(1.4, 1.8, 380, 0.4),
  "B5": EfficientNetParam(1.6, 2.2, 456, 0.4),
  "B6": EfficientNetParam(1.8, 2.6, 528, 0.5),
  "B7": EfficientNetParam(2.0, 3.1, 600, 0.5),
   }

EfficientNetUrls = {
    'B0': 'https://drive.google.com/uc?export=download&id=1rNU-wCPT_ebdc7qG1NzwSoqXLJxIL9Qz', 
    'B1': 'https://drive.google.com/uc?export=download&id=1rNU-wCPT_ebdc7qG1NzwSoqXLJxIL9Qz',
    'B2': 'https://drive.google.com/uc?export=download&id=1SeA-VuRiuWI9f8PVuhTYYdu8NjKFl_dL',
    'B3': 'https://drive.google.com/uc?export=download&id=1fhi6xlrJl1iKl2b2knxPchjCsI9f9-Fr',
    'B4': 'https://drive.google.com/uc?export=download&id=1iroAuwlUssk3mzcbHdYDGhnXLm37ZfN2',
    'B5': 'https://drive.google.com/uc?export=download&id=188XFvL4JqH8SX0Pb3EtTENdaGzolN4-s',
    'B6': 'https://drive.google.com/uc?export=download&id=1PGLFWp3xF8LVUjVGJ_h9DYYhyOAGX2gG',
    'B7': 'https://drive.google.com/uc?export=download&id=18BHuBD2HpjTj2r9ffHo_Y6dgdprmcGUc',
    }
    

class InvertedResidual(nn.Module):
    def __init__(self, inp, oup, expand_ratio, kernel_size, stride, se_reduction, drop_connect_ratio=0.2):
        """Basic building block - Inverted Residual Convolution from MobileNet V2 
        architecture.

        Arguments:
            expand_ratio (int): ratio to expand convolution in width inside convolution.
                It's not the same as width_mult in MobileNet which is used to increase
                persistent input and output number of channels in layer. Which is not a
                projection of channels inside the conv. 
        """
        super().__init__()
        
        hidden_dim = int(inp * expand_ratio)
        self.use_res_connect = stride == 1 and inp == oup

        if self.use_res_connect:
            self.dropconnect = DropConnect(drop_connect_ratio)
            
        if expand_ratio == 1:
            self.conv = nn.Sequential(
                # depth-wise 
                SamePadConv2d(inp=hidden_dim, oup=hidden_dim, kernel_size=kernel_size, stride=stride, groups=hidden_dim, 
                              bias=False),
                nn.BatchNorm2d(hidden_dim, eps=batch_norm_epsilon, momentum=batch_norm_momentum),
                Swish(), 
                Attention(channels=hidden_dim, reduction=4),  # somehow here reduction should be always 4
                
                # point-wise-linear
                SamePadConv2d(inp=hidden_dim, oup=oup, kernel_size=1, stride=1, bias=False),
                nn.BatchNorm2d(oup, eps=batch_norm_epsilon, momentum=batch_norm_momentum),
            )
        else:
            self.conv = nn.Sequential(
                # point-wise
                SamePadConv2d(inp, hidden_dim, kernel_size=1, stride=1, bias=False),
                nn.BatchNorm2d(hidden_dim, eps=batch_norm_epsilon, momentum=batch_norm_momentum),
                Swish(), 
                                
                # depth-wise
                SamePadConv2d(hidden_dim, hidden_dim, kernel_size, stride, groups=hidden_dim, bias=False),
                nn.BatchNorm2d(hidden_dim, eps=batch_norm_epsilon, momentum=batch_norm_momentum),
                Swish(),
                Attention(channels=hidden_dim, reduction=se_reduction),  
                
                # point-wise-linear
                SamePadConv2d(hidden_dim, oup, kernel_size=1, stride=1, bias=False),
                nn.BatchNorm2d(oup, eps=batch_norm_epsilon, momentum=batch_norm_momentum),
            )

    def forward(self, inputs):
        if self.use_res_connect: 
            return self.dropconnect(inputs) + self.conv(inputs)
        else: 
            return self.conv(inputs)

        
def round_filters(filters, width_coef, depth_divisor=8, min_depth=None):
    """Calculate and round number of filters based on depth multiplier. """
    if not width_coef:
        return filters
    filters *= width_coef
    min_depth = min_depth or depth_divisor
    new_filters = max(min_depth, int(filters + depth_divisor / 2) // depth_divisor * depth_divisor)
    if new_filters < 0.9 * filters:  # prevent rounding by more than 10%
        new_filters += depth_divisor
    return int(new_filters)


def round_repeats(repeats, depth_coef):
    """Round number of filters based on depth multiplier."""
    if not depth_coef:
        return repeats
    return int(math.ceil(depth_coef * repeats)) 
    
        
class EfficientNet(nn.Module):
    def __init__(self, num_classes=3, width_coef=1., depth_coef=1., scale=1., dropout_ratio=0.2, 
                 se_reduction=24, drop_connect_ratio=0.5):
        super(EfficientNet, self).__init__()
        
        block = InvertedResidual
        input_channel     = round_filters(32, width_coef)   # input_channel = round(32*width_coef) 
        self.last_channel = round_filters(1280, width_coef)     # self.last_channel  = round(1280*width_coef)
        config = np.array([
            # stride only for first layer in group, all other always with stride 1
            # channel,expand,repeat,stride,kernel_size
            [16,  1, 1, 1, 3],
            [24,  6, 2, 2, 3],
            [40,  6, 2, 2, 5],
            [80,  6, 3, 2, 3],
            [112, 6, 3, 1, 5],
            [192, 6, 4, 2, 5],
            [320, 6, 1, 1, 3],
            ])

        # first steam layer - ordinar conv
        self.features = [conv_bn_act(3, input_channel, kernel_size=3, stride=2, bias=False)]

        # main 7 group of layers
        for c, t, n, s, k in config:
            output_channel = round_filters(c, width_coef)
            for i in range(round_repeats(n, depth_coef)):
                if i == 0:
                    self.features.append(block(inp=input_channel, oup=output_channel, expand_ratio=t, kernel_size=k, 
                                               stride=s, se_reduction=se_reduction, drop_connect_ratio=drop_connect_ratio))
                else:
                    # here stride is equal 1 because only first layer in group could have stride 2, 
                    self.features.append(block(inp=input_channel, oup=output_channel, expand_ratio=t, kernel_size=k, 
                                               stride=1, se_reduction=se_reduction, drop_connect_ratio=drop_connect_ratio))
                input_channel = output_channel
        
        # building last several layers
        self.features.append(conv_bn_act(input_channel, self.last_channel, kernel_size=1, bias=False))
        self.features = nn.Sequential(*self.features)
            
        self.classifier = nn.Sequential(
            nn.Dropout(0.2),
            nn.Linear(self.last_channel, num_classes),
            )
     
        self._initialize_weights()

                      
    def _initialize_weights(self):
        flattener = Flattener()
        flattened = flattener(self)
        for m in flattened:
            if isinstance(m, nn.Conv2d):
                n = m.kernel_size[0] * m.kernel_size[1] * m.out_channels
                m.weight.data.normal_(0, math.sqrt(2. / n))
                if m.bias is not None:
                    m.bias.data.zero_()
            elif isinstance(m, nn.BatchNorm2d):
                m.weight.data.fill_(1)
                m.bias.data.zero_()
            elif isinstance(m, nn.Linear):
                n = m.weight.size(1)
                m.weight.data.normal_(0, 0.01)
                m.bias.data.zero_()

    def forward(self, x):
        x = self.features(x)
        x = x.mean(3).mean(2)
        x = self.classifier(x)
        return x
    
    
def efficientnet(net="B0", pretrained=False):
    """Weights for B5-B7 models should be loaded manually since after 100Mb 
    Google Drive checks virusis. I'm lazzy to fix it sice I never user models
    higher that B4 :)
    """
    net = net.upper()
    model = EfficientNet(
        width_coef    = EfficientNetParams[net].width, 
        depth_coef    = EfficientNetParams[net].depth, 
        scale         = EfficientNetParams[net].resolution, 
        se_reduction  = 24, 
        dropout_ratio = EfficientNetParams[net].dropout, 
    )
    
    if pretrained:
        weights_path = "/home/<USER>/jupyter_test/checkpoint/EfficientNet_B7_hc.pth"
#         if not os.path.isfile(weights_path):
#             wget.download(url=EfficientNetUrls[net], out=weights_path)
        model_dict = model.state_dict()
        state_dict = torch.load(weights_path)
        new_state_dict = {k: v for k, v in state_dict.items() if k in model_dict}
        model_dict.update(new_state_dict)
        model.load_state_dict(model_dict)
                    
#         checkpoint = torch.load(weights_path)
#         filtered_state_dict = {k:v for k, v in checkpoint.items() if 'features' in k}
#         model.load_state_dict(filtered_state_dict, strict=False)
        
    return model