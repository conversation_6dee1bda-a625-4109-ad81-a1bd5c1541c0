#!/usr/bin/python
# -*- coding: UTF-8 -*-
import sys
sys.path.append('..')

from keras.layers import *
import cv2
import darknet_yolov4.darknet as dn


class Police_Judge():
    def __init__(self):
        pass

    def IOU(self, tl1, br1, tl2, br2):
        wh1, wh2 = br1 - tl1, br2 - tl2
        assert ((wh1 >= .0).all() and (wh2 >= .0).all())
        intersection_wh = np.maximum(np.minimum(br1, br2) - np.maximum(tl1, tl2), 0.)
        intersection_area = np.prod(intersection_wh)
        area1, area2 = (np.prod(wh1), np.prod(wh2))
        union_area = area1 + area2 - intersection_area
        return intersection_area / union_area

    def police_detect(self, img_np, police_net, police_meta, threshold, show):
        # img_path = img_path.encode('utf-8')
        R = dn.detect_image(police_net, police_meta, img_np, thresh=threshold)
        R = [r for r in R if r[0] in [b'police_1', b'police_2']]
        if show:
            self.draw_show_img(R, img_path.decode('utf-8'))
        return R

    def draw_show_img(self, R, img_path):
        if len(R):
            Iorig = cv2.imread(img_path)
            WH = np.array(Iorig.shape[1::-1], dtype=float)
            Lcars = []
            for i, r in enumerate(R):
                cx, cy, w, h = (np.array(r[2]) / np.concatenate((WH, WH))).tolist()
                tl = [cx - w / 2., cy - h / 2.]
                br = [cx + w / 2., cy + h / 2.]
                class_name = r[0].decode('utf-8')
                class_con = round(r[1], 3)
                x1 = int(max(Iorig.shape[1] * tl[0], 0))
                y1 = int(max(Iorig.shape[0] * tl[1], 0))
                x2 = int(max(Iorig.shape[1] * br[0], 0))
                y2 = int(max(Iorig.shape[0] * br[1], 0))
                cv2.rectangle(Iorig, (x1, y1), (x2, y2), color=(0, 0, 255), thickness=2)
                cv2.putText(Iorig, class_name + " " + str(class_con), (x1, y1), cv2.FONT_HERSHEY_COMPLEX, 1.5, (0, 255, 0),
                            2)
                Lcars.append((tl, br))
            if WH[0] > 1000:
                Iorig = cv2.resize(Iorig, None, fx=0.3, fy=0.3)
            cv2.imshow('Display', Iorig)
            cv2.moveWindow('Display', 0, 0)
            key = cv2.waitKey(0) & 0xEFFFFF
            cv2.destroyWindow('Display')
            if key == 27:
                sys.exit()
            else:
                return key

    def judge_police_ps(self, police_dict):
        ps_dict = {}
        for k, v in police_dict.items():
            if len(v) == 1:
                ps_dict[str(v[0])] = k
            if len(v) > 1:
                for i in v:
                    ps_dict[str(i)] = k
        ps_list = list(ps_dict.keys())
        ps_label = list(ps_dict.values())
        ps_list = [eval(i) for i in ps_list]
        drop_list = []
        final_dict = {}
        for i in range(len(ps_list)):
            ps = ps_list[i]
            ps_xvalue = [ps[0], ps[1]]
            ps_yvalue = [ps[2], ps[3]]
            final_dict[str([ps_xvalue, ps_yvalue])] = [ps_label[i]]
            if ps in drop_list:
                continue
            for psx in ps_list[i + 1:]:
                psx_xvalue = [psx[0], psx[1]]
                psx_yvalue = [psx[2], psx[3]]
                # print(psx)
                iou = self.IOU(np.array(ps[:2]), np.array(ps[2:]), np.array(psx[:2]), np.array(psx[2:]))
                # print(iou)
                if iou <= 0.2:
                    pass
                else:
                    drop_list.append([psx_xvalue, psx_yvalue])
                    if not ps_label[ps_list.index(psx)] in final_dict[str([ps_xvalue, ps_yvalue])]:
                        final_dict[str([ps_xvalue, ps_yvalue])].append(ps_label[ps_list.index(psx)])

        for i in drop_list:
            try:
                del final_dict[str(i)]
            except:
                pass
        res = sorted(final_dict.items(), key=lambda x: x[0], reverse=False)

        res_dict = {}
        for i in res:
            type_name = i[1][0]
            cor = eval(i[0])
            x1 = cor[0][0]
            y1 = cor[0][1]
            x2 = cor[1][0]
            y2 = cor[1][1]
            if type_name in list(res_dict.keys()):
                res_dict[type_name].append([x1, y1, x2, y2])
            else:
                res_dict[type_name] = [[x1, y1, x2, y2]]

        return res_dict

    def police_judge(self, imgs_list, police_net, police_meta, threshold, show=False):
        police_dict = {}
        for img_np in enumerate(imgs_list):
            # print('POLICE DETECT !!')
            # Iorig = cv2.imread(img_path)
            Iorig = img_np
            # print(Iorig.shape)
            try:
                WH = np.array(Iorig.shape[1::-1], dtype=float)
                R = self.police_detect(img_np, police_net, police_meta, threshold, show)
                if len(R):
                    for i, r in enumerate(R):
                        class_name = r[0].decode('utf-8')
                        cx, cy, w, h = (np.array(r[2]) / np.concatenate((WH, WH))).tolist()
                        tl = [cx - w / 2., cy - h / 2.]
                        br = [cx + w / 2., cy + h / 2.]
                        x1 = int(max(Iorig.shape[1] * tl[0], 0))
                        y1 = int(max(Iorig.shape[0] * tl[1], 0))
                        x2 = int(max(Iorig.shape[1] * br[0], 0))
                        y2 = int(max(Iorig.shape[0] * br[1], 0))
                        if class_name in police_dict.keys():
                            police_dict[class_name].append([x1, y1, x2, y2])
                        else:
                            police_dict[class_name] = [[x1, y1, x2, y2]]
            except:
                pass
        # print(police_dict)
        res = self.judge_police_ps(police_dict)
        # info_dict = {}
        # for i in range(len(res)):
        #     info_dict['%d' % i] = {'type': res[i][0], 'score': res[i][1], 'cor': res[i][2]}
        return res


if __name__ == '__main__':
    police_weights = '../models/light_police/light_police_best.weights'.encode('utf-8')
    police_netcfg = '../models/light_police/light_police.cfg'.encode('utf-8')
    police_dataset = '../models/light_police/light_police.data'.encode('utf-8')
    police_net = dn.load_net(police_netcfg, police_weights, 0, 1)
    police_meta = dn.load_meta(police_dataset)
    threshold = 0.5
    # img1_path = '/home/<USER>/Documents/train_dataset/police_dataset/data_imgs_0623/贵JUC796_1.jpg'
    # img2_path = '/home/<USER>/Documents/train_dataset/police_dataset/data_imgs_0623/贵JUC796_2.jpg'
    # img3_path = '/home/<USER>/Documents/train_dataset/police_dataset/data_imgs_0623/贵JUC796_3.jpg'
    # img_list = [img1_path, img2_path, img3_path]
    # Res = Police_Judge().police_judge(img_list, police_net, police_meta, threshold, show=True)
    # print(Res)

    # img_dir = '/home/<USER>/Documents/train_dataset/police_dataset/data_imgs_0623'
    # file_list = os.listdir(img_dir)
    # for i in file_list:
    #     img_path = os.path.join(img_dir, i)
    #     print(img_path)
    #     Police_Judge().police_detect(img_path, police_net, police_meta, threshold, show=True)

    img_path = '/home/<USER>/IMGS_ALL/兴义/判罚通过图片/0101/01日海康违法/12080HK违法/无效/贵AH730S_1.jpg'
    img = cv2.imread(img_path)
    from ai_judge_code_gy.judge_tools import CUTIMG, SIMPLE_CUT
    file_list, split_x, split_y = CUTIMG().cut(img)
    if len(file_list) != 4:
        file_list, split_x, split_y = SIMPLE_CUT().break_img_4(img)
    for img in file_list:
        Res = Police_Judge().police_detect(img, police_net, police_meta, threshold, show=False)
        print(Res)

    # info_dict = {}
    # for i in range(len(Res)):
    #     info_dict['%d'%i] = {'type': Res[i][0], 'score':Res[i][1], 'cor':Res[i][2]}

    # R = [r[0] for r in R if r[0] in [b'police_1', b'police_2']]
