import os
import time

from kafka import KafkaProducer
import sys

sys.path.append("..")
from main_config.config import *
import json
import datetime as dt
import urllib.parse

# 模型结果生产者
producer = KafkaProducer(bootstrap_servers=SERVER, value_serializer=lambda m: json.dumps(m).encode())
data = {"sendTime": "2024-09-03T10:55:54.503+08:00", "picUploadInterval": "47", "channelID": 34,
        "channelName": "东鹤路与凤翔路南向北", "dataType": "vehicleAlarm", "dateTime": "2024-09-03T10:55:54.651+08:00",
        "eventDescription": "vehicleAlarmResult", "eventType": "vehicleAlarmResult", "ipAddress": "*************",
        "portNo": 8000, "recvTime": "2024-09-03T10:55:54.452+08:00", "vehicleAlarmResult": [{"target": [
        {"rect": {"height": 0.24, "width": 0.117, "x": 0.462, "y": 0.558},
         "vehicle": {"dangmark": {"value": "unknown"}, "envprosign": {"value": "unknown"}, "isMainVehicle": "true",
                     "pendant": {"value": "unknown"}, "pilotSafebelt": {"value": "unknown"},
                     "pilotSunvisor": {"value": "unknown"}, "plateColor": {"value": "yellow"},
                     "plateNo": {"confidence": 0.0, "value": "豫C23K20"},
                     "plateRect": {"height": 0.0, "width": 0.0, "x": 0.0, "y": 0.0}, "plateType": {"value": "unknown"},
                     "uphone": {"value": "unknown"}, "vehicleColor": {"value": "unknown"},
                     "vehicleHead": {"value": "back"}, "vehicleLogo": {"value": "1677"},
                     "vehicleModel": {"value": "129"}, "vehicleSubLogo": {"value": "2"},
                     "vehicleType": {"value": "truck"}, "vicePilotSafebelt": {"value": "unknown"},
                     "vicePilotSunvisor": {"value": "unknown"}}}], "targetAttrs": {"alarmId": "", "alarmType": "1625",
                                                                                   "areaCode": "20bd3921b61b4a5a9f7819ff33df4f08",
                                                                                   "cameraAddress": "东鹤路与凤翔路南向北",
                                                                                   "cameraIndexCode": "805c3549bea2489a89be33cc0c87df56",
                                                                                   "cameraName": "东鹤路与凤翔路南向北",
                                                                                   "cascade": "0", "crossingId": 54,
                                                                                   "crossingIndexCode": "a3b82285b4b348e5acf03034b4d5c7f4",
                                                                                   "crossingName": "东鹤路与凤翔路",
                                                                                   "deviceIndexCode": "ec2739967e914888ac37c4e6bdaa0406",
                                                                                   "deviceName": "东鹤路与凤翔路",
                                                                                   "deviceType": 107001,
                                                                                   "directionIndex": "southNorth",
                                                                                   "imageServerCode": "83d1d258-49bd-4f4c-af19-3744831a4f0a",
                                                                                   "laneNo": 2, "latitude": "25.951918",
                                                                                   "longitude": "119.530129",
                                                                                   "multiVehicle": 0,
                                                                                   "passID": "AD0DACBE-DFAD-B747-A8FC-3D506326812A",
                                                                                   "passTime": "2024-09-03T10:55:54.651+08:00",
                                                                                   "platePicUrl": "http://35.26.210.25:6120/pic?5E0FB0D80AA0AB01C7B7*hcs87e8cc71bdd24f34b2023/cljj/27600;1725332044790429225254?pic*1052775206*7348*2058*5E0FB0D80AA0AB01C7B7-2*1725332154",
                                                                                   "recognitionSign": 1,
                                                                                   "regionIndexCode": "20bd3921b61b4a5a9f7819ff33df4f08",
                                                                                   "uuid": "1ab69ef2-1dd2-11b2-801f-bfd4478fb7d0",
                                                                                   "vehicleColorDepth": "0",
                                                                                   "vehicleLen": 0, "vehiclePicNum": 2,
                                                                                   "vehicleSpeed": 0, "xmlBuf": ""},
        "taskID": "385679362"}]}
print("ddd")
root = "/data"  # tomcat挂载的目录
pic_root = f"{root}/ai_judge_depoly/ai_judge_new/debug_dir"

vio_code = "1345"
finished_plate = []
num = 0
for idx, type in enumerate(os.listdir(pic_root)):
    # if type != "1625":
    #     continue
    dir1 = f"{pic_root}/{type}"
    if os.path.isdir(dir1):
        for i, name in enumerate(os.listdir(dir1)):
            print(type, name)
            tmp_url = f"{dir1}/{name}"
            data["vehicleAlarmResult"][0]["taskID"] = f"{type}_{num + 1}"
            num += 1
            url = "http://10.184.48.200:8080" + tmp_url
            data["vehicleAlarmResult"][0]["targetPicUrl"] = urllib.parse.quote(url, safe=":/=?#")
            # data["sendTime"] = "2024-10-10T10:16:30.594+08:00"
            data["sendTime"] = dt.datetime.now().strftime("%Y-%m-%dT%H:%M:%S.%f")[:-3] + "+08:00"
            data["recvTime"] = dt.datetime.now().strftime("%Y-%m-%dT%H:%M:%S.%f")[:-3] + "+08:00"
            data["vehicleAlarmResult"][0]["targetAttrs"]["alarmType"] = type
            data["vehicleAlarmResult"][0]["target"][0]["vehicle"]["plateNo"]["value"] = name.split("_")[0]
            # data["iieaMergeType"] = 411
            # data["iieaMergeRect"] = None
            # data["iieaSendTime"] =dt.datetime.now().strftime("%Y-%m-%dT%H:%M:%S.%f")[:-3] + "+08:00"
            # data["dataSource"] = "vps_manual"
            # if type == "6095":
            #     data["vehicleAlarmResult"][0]["targetAttrs"]["cameraIndexCode"] = "test"
            producer.send(VIO_DATA_TOPIC, data)
            producer.flush(3)
            # time.sleep(3)
    else:
        print(type)
        tmp_url = dir1
        plate = type.split(SPLIT_STR)[PLATE_INDEX]
        if plate in finished_plate:
            continue
        name_list = [name for name in sorted(os.listdir(pic_root)) if name.split(SPLIT_STR)[PLATE_INDEX] == plate]

        data["vehicleAlarmResult"][0]["taskID"] = f"{vio_code}_{idx + 1}"
        for i2, name in enumerate(name_list):
            tmp_url = f"{pic_root}/{name}"
            url = "http://10.184.48.200:8080" + tmp_url
            if i2 == 0:
                data["vehicleAlarmResult"][0]["targetPicUrl"] = urllib.parse.quote(url, safe=":/=?#")
            else:
                data["vehicleAlarmResult"][0]["targetAttrs"][f"vehiclePicUrl{i2}"] = urllib.parse.quote(url,
                                                                                                        safe=":/=?#")

        # data["sendTime"] = "2024-10-10T10:16:30.594+08:00"
        data["sendTime"] = dt.datetime.now().strftime("%Y-%m-%dT%H:%M:%S.%f")[:-3] + "+08:00"
        data["recvTime"] = dt.datetime.now().strftime("%Y-%m-%dT%H:%M:%S.%f")[:-3] + "+08:00"
        data["vehicleAlarmResult"][0]["targetAttrs"]["alarmType"] = vio_code
        data["vehicleAlarmResult"][0]["target"][0]["vehicle"]["plateNo"]["value"] = name.split("_")[-2]
        # if type == "6095":
        #     data["vehicleAlarmResult"][0]["targetAttrs"]["cameraIndexCode"] = "test"
        producer.send(VIO_DATA_TOPIC, data)
        producer.flush(3)
        finished_plate.append(plate)
producer.close()
