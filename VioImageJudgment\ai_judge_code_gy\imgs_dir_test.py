import os

os.environ["CUDA_VISIBLE_DEVICES"] = "1"
import sys

sys.path.append("..")
from ai_judge_code_gy.kafka_main_multi import convert_iiea_rect
from init_models import *

if Is_Allowed_Type["1625"]:
    from ai_judge_code_gy.vio_judge_16251 import judge_vio as judge_16251
if Is_Allowed_Type["1208"]:
    from ai_judge_code_gy.vio_judge_12080 import judge_vio as judge_12080
if Is_Allowed_Type["1345"]:
    from ai_judge_code_gy.vio_judge_13450 import judge_vio as judge_13450
if Is_Allowed_Type["1301"]:
    from ai_judge_code_gy.vio_judge_13010 import judge_vio as judge_13010
if Is_Allowed_Type["1352"]:
    from ai_judge_code_gy.vio_judge_13522 import judge_vio as judge_13522
if Is_Allowed_Type["1357"]:
    from ai_judge_code_gy.vio_judge_13570v2 import judge_vio as judge_13570
if Is_Allowed_Type["1223"] or Is_Allowed_Type["1101"]:
    from ai_judge_code_gy.vio_judge_12230_11010 import judge_vio as judge_12230_11010
if Is_Allowed_Type["7102"]:
    from ai_judge_code_gy.vio_judge_71020 import judge_vio as judge_71020
if Is_Allowed_Type["8013"]:
    if Stop_Drv_Version == 2:
        from ai_judge_code_gy.vio_judge_80130_jz import judge_vio as judge_80130
    else:
        from ai_judge_code_gy.vio_judge_80130 import judge_vio as judge_80130
if Is_Allowed_Type["1039"]:
    from ai_judge_code_gy.vio_judge_10390 import judge_vio as judge_10390
if Is_Allowed_Type["1018"]:
    from ai_judge_code_gy.vio_judge_10180 import judge_vio as judge_10180
if Is_Allowed_Type["1000"]:
    from ai_judge_code_gy.vio_filter_vehicle import judge_vio as judge_10000
import datetime


def select_vio_def(violation_type, plate_num, file_list, weights_list, extra_msg=None):
    # 修正图片处理顺序
    try:
        judge_index = PIC_JUDGE_INDEX[violation_type]
        if judge_index:
            file_list = [file_list[idx] for idx in judge_index]
    except:
        pass

    if violation_type in Vio_Type_Match.keys():
        match_code = violation_type
    else:
        print("不支持的违法类型：", violation_type)
        return "no_judge", {}

    scale = 0.7
    vio_judge = judge_infos = None
    if Is_Allowed_Type[str(match_code)] and int(match_code) == 1208:
        vio_judge, judge_infos = \
            judge_12080(plate_num, file_list, weights_list, resize_scale=scale, merge=False, save=IS_SAVE_VIS_PIC,
                        save_path=f"{SAVE_RES_DIR}/{violation_type}/vis", extra_msg=extra_msg)
    # 2
    if Is_Allowed_Type[str(match_code)] and int(match_code) == 1625:
        vio_judge, judge_infos = \
            judge_16251(plate_num, file_list, weights_list, resize_scale=scale, merge=False, save=IS_SAVE_VIS_PIC,
                        save_path=f"{SAVE_RES_DIR}/{violation_type}/vis", extra_msg=extra_msg)
    # 3
    if Is_Allowed_Type[str(match_code)] and int(match_code) == 1345:
        vio_judge, judge_infos = \
            judge_13450(plate_num, file_list, weights_list, resize_scale=scale, save=IS_SAVE_VIS_PIC,
                        save_path=f"{SAVE_RES_DIR}/{violation_type}/vis", extra_msg=extra_msg)
    # 4
    if Is_Allowed_Type[str(violation_type)] and int(violation_type) == 1301:
        is_highway = False
        vio_judge, judge_infos = \
            judge_13010(plate_num, file_list, weights_list, resize_scale=scale, merge=True, highway=is_highway,
                        save=IS_SAVE_VIS_PIC, save_path=f"{SAVE_RES_DIR}/{violation_type}/vis", extra_msg=extra_msg)
    # 5
    if Is_Allowed_Type[str(violation_type)] and int(violation_type) == 1352:
        vio_judge, judge_infos = \
            judge_13522(plate_num, file_list, weights_list, resize_scale=scale, save=IS_SAVE_VIS_PIC,
                        save_path=f"{SAVE_RES_DIR}/{violation_type}/vis", extra_msg=extra_msg)
    # 6
    if Is_Allowed_Type[str(violation_type)] and int(violation_type) == 1357:
        vio_judge, judge_infos = \
            judge_13570(plate_num, file_list, weights_list, resize_scale=scale, save=IS_SAVE_VIS_PIC,
                        save_path=f"{SAVE_RES_DIR}/{violation_type}/vis", extra_msg=extra_msg)
    # 7
    if Is_Allowed_Type[str(violation_type)] and int(violation_type) == 7102:
        vio_judge, judge_infos = \
            judge_71020(plate_num, file_list, weights_list, resize_scale=scale, save=IS_SAVE_VIS_PIC,
                        save_path=f"{SAVE_RES_DIR}/{violation_type}/vis", extra_msg=extra_msg)
    # 8
    if Is_Allowed_Type[str(violation_type)] and int(violation_type) == 8013:
        vio_judge, judge_infos = \
            judge_80130(plate_num, file_list, weights_list, resize_scale=scale, save=IS_SAVE_VIS_PIC,
                        save_path=f"{SAVE_RES_DIR}/{violation_type}/vis", extra_msg=extra_msg)
    # 9,10
    if Is_Allowed_Type[str(violation_type)] and int(violation_type) in [1223, 1101]:
        vio_judge, judge_infos = \
            judge_12230_11010([violation_type, violation_type], plate_num, file_list, weights_list, resize_scale=scale,
                              save=IS_SAVE_VIS_PIC, save_path=f"{SAVE_RES_DIR}/{violation_type}/vis",
                              extra_msg=extra_msg)
    # 11
    if Is_Allowed_Type[str(violation_type)] and int(violation_type) == 1039:
        vio_judge, judge_infos, score_list = \
            judge_10390(plate_num, file_list, weights_list, save=IS_SAVE_VIS_PIC,
                        save_path=f"{SAVE_RES_DIR}/{violation_type}/vis", extra_msg=extra_msg)
    # 12
    if Is_Allowed_Type[str(match_code)] and int(match_code) == 1018:
        vio_judge, judge_infos = \
            judge_10180(plate_num, file_list, weights_list, resize_scale=scale, save=IS_SAVE_VIS_PIC,
                        save_path=f"{SAVE_RES_DIR}/{match_code}/vis", extra_msg=extra_msg)
    # 13
    if Is_Allowed_Type[str(match_code)] and int(match_code) == 1000:
        vio_judge, judge_infos = \
            judge_10000(plate_num, file_list, weights_list, resize_scale=scale, save=IS_SAVE_VIS_PIC,
                        save_path=f"{SAVE_RES_DIR}/{match_code}/vis", extra_msg=extra_msg)
    return vio_judge, judge_infos


def split_images(img, vio_code, iieaMergeRect):
    file_list = []
    src_list = []
    black_pos = None
    split_mode = SPLIT_DICT[str(vio_code)]
    black_height_rate = BLACK_HEIGHT
    try:
        src_list.append(img.copy())
        if not iieaMergeRect:
            # 自动切分
            if SPLIT_DICT[vio_code] is None or (
                    vio_code in ["1625", "1208",
                                 "1357"] and BLACK_HEIGHT is None and vio_code not in BLACK_POSITION_DICT.keys() and
                    SPLIT_DICT[vio_code] in [411, 611, 621]):
                t1 = time.time()
                split_mode, black_height_rate, black_pos = Tools().cut(img, vio_code)
                auto_split_time = round(time.time() - t1, 3)
                print("自动切分耗时：", auto_split_time, SPLIT_MODE_DESC[split_mode], "黑框高度比例:", black_height_rate)
                if BLACK_HEIGHT is not None:
                    black_height_rate = BLACK_HEIGHT
                if SPLIT_DICT[vio_code] is not None:
                    split_mode = SPLIT_DICT[vio_code]
            else:
                if black_height_rate is None:
                    black_height_rate = 0
                if vio_code in BLACK_POSITION_DICT.keys():
                    black_pos = BLACK_POSITION_DICT[vio_code]
        if iieaMergeRect:
            black_height_rate, black_pos = convert_iiea_rect(iieaMergeRect)
            print(
                f"切分方式：{SPLIT_MODE_DESC[split_mode]} iiea配置转换:black_height_rate:{black_height_rate} black_pos:{black_pos}")
        file_list = img_split(img, n=split_mode, black_height=black_height_rate, black_pos=black_pos)
    except Exception as e:
        error_msg = catch_error()
        print("切分错误:", error_msg)

    return file_list, src_list, split_mode, black_height_rate, black_pos


if __name__ == '__main__':
    # 初始化所有模型
    weights_list = init_models(traffic=True, lp=True, yolov7=True, trace=False, phone=True, vio_model=True, cnn=True)
    test_root = "../dev_pics"
    os.makedirs(test_root, exist_ok=True)
    for vio_type in os.listdir(test_root):
        if vio_type not in Vio_Type_Match.keys():
            continue
        miss_dir = f"{SAVE_RES_DIR}/{vio_type}/miss_data"
        if os.path.exists(f"{SAVE_RES_DIR}/{vio_type}"):
            shutil.rmtree(f"{SAVE_RES_DIR}/{vio_type}")
        os.makedirs(f"{SAVE_RES_DIR}/{vio_type}/vio", exist_ok=True)
        os.makedirs(f"{SAVE_RES_DIR}/{vio_type}/no_vio", exist_ok=True)

        test_sources = f"{test_root}/{vio_type}"
        num = len(os.listdir(test_sources))
        split_mode = SPLIT_DICT[vio_type]
        print(f"测试违法类型：{vio_type} 图片总数：{num} 拼接方式：{SPLIT_MODE_DESC[split_mode]}")
        img_name_list = sorted(os.listdir(test_sources))
        if split_mode == 111:
            n = 0
            tt_time = 0
            img_dict = {}
            path_list = []
            need_num = SINGLE_PIC_NUM[vio_type]
            for name in tqdm(img_name_list):
                img_path = f"{test_sources}/{name}"
                key_id = name.split(SPLIT_STR)[ID_INDEX]
                try:
                    plate_num = name.split(SPLIT_STR)[PLATE_INDEX]
                except:
                    plate_num = "None"
                img = cv2.imread(img_path)
                if img is None:
                    continue
                tmp_key = f"{key_id}_{plate_num}"
                if need_num == 1:
                    img_dict = {tmp_key: [img]}
                    path_list = [img_path]
                    file_list = img_dict[tmp_key]
                    s_time = datetime.datetime.now()
                    print(f"{tmp_key} {vio_type}违法图片开始审核...{need_num}")
                    vio_judge, judge_infos = select_vio_def(vio_type, plate_num, file_list, weights_list)

                    e_time = datetime.datetime.now()
                    time_delta = round((e_time - s_time).seconds, 2)
                    tt_time += time_delta
                    print(tmp_key, vio_judge, judge_infos, 'use time:', time_delta)
                    print("\n")
                    pred_result = f"{SAVE_RES_DIR}/{vio_type}/vio" if vio_judge.startswith(
                        "vio") else f"{SAVE_RES_DIR}/{vio_type}/no_vio"
                    for idx, i in enumerate(path_list):
                        try:
                            shutil.copy(i, pred_result)
                        except Exception as e:
                            print(e)
                elif n % need_num == 0:
                    img_dict = {tmp_key: [img]}
                    path_list = [img_path]
                    n += 1
                elif tmp_key in img_dict.keys():
                    img_dict[tmp_key].append(img)
                    path_list.append(img_path)
                    n += 1
                    file_list = img_dict[tmp_key]
                    if len(file_list) == need_num:
                        s_time = datetime.datetime.now()
                        print(f"{tmp_key} {vio_type}违法图片开始审核...{need_num}")
                        vio_judge, judge_infos = select_vio_def(vio_type, plate_num, file_list, weights_list)

                        e_time = datetime.datetime.now()
                        time_delta = round((e_time - s_time).seconds, 2)
                        tt_time += time_delta
                        print(tmp_key, vio_judge, judge_infos, 'use time:', time_delta)
                        print("\n")
                        pred_result = f"{SAVE_RES_DIR}/{vio_type}/vio" if vio_judge.startswith(
                            "vio") else f"{SAVE_RES_DIR}/{vio_type}/no_vio"
                        for idx, i in enumerate(path_list):
                            try:
                                shutil.copy(i, pred_result)
                            except Exception as e:
                                print(e)
                            # print(f"{src_plate_num}判罚成功--{idx + 1}/3")
                else:
                    print(f"发现缺失数据,缺失 id_车牌:{list(img_dict.keys())[0]}--当前图片 id_车牌：{tmp_key}————{name}")
                    img_dict = {tmp_key: [img]}
                    n = 1
                    os.makedirs(miss_dir, exist_ok=True)
                    for i in path_list:
                        try:
                            shutil.move(i, miss_dir)
                        except Exception as e:
                            print(e)
                    path_list = [img_path]
            if len(path_list) < need_num:
                os.makedirs(miss_dir, exist_ok=True)
                for i in path_list:
                    try:
                        shutil.move(i, miss_dir)
                    except Exception as e:
                        print(e)
            print(f"违法代码：{vio_type} 图片总数：{num} 总耗时：{tt_time}")
        else:
            tt_time = 0
            iiea_rect = None
            for name in tqdm(img_name_list):
                print(f"{name} 分析中...")
                if len(name.split(",")) == 4:
                    iiea_rect = name.split("_")[-1][:-4]

                img_path = f"{test_sources}/{name}"
                try:
                    plate_num = name.split(SPLIT_STR)[PLATE_INDEX]
                except:
                    plate_num = "None"
                img = cv2.imread(img_path)
                img_tmp = img.copy()
                if img is None:
                    continue
                # 切分图片
                file_list, src_list, split_mode, black_height_rate, black_pos = split_images(img_tmp, vio_type,
                                                                                             iiea_rect)
                extra_msg = [split_mode, black_height_rate, black_pos, None, None]
                s_time = datetime.datetime.now()
                vio_judge, judge_infos = select_vio_def(vio_type, plate_num, file_list, weights_list, extra_msg)
                # 验证框
                # save_path = f"{SAVE_RES_DIR}/{vio_type}/coord"
                # os.makedirs(save_path,exist_ok=True)
                # for D in judge_infos["modelDetectResults"]:
                #     coord = D["objCoord"]
                #     h,w,_ = img.shape
                #     pt1 = (int(coord[0]*w),int(coord[1]*h))
                #     pt2 = (int(coord[2]*w),int(coord[3]*h))
                #     cv2.rectangle(img,pt1,pt2,(0,0,255),2)
                # cv2.imwrite(f"{save_path}/{name}",img)

                e_time = datetime.datetime.now()
                time_delta = round((e_time - s_time).seconds, 2)
                tt_time += time_delta
                print(plate_num, vio_judge, judge_infos, 'use time:', time_delta)
                print("\n")
                if vio_judge is not None:
                    pred_result = f"{SAVE_RES_DIR}/{vio_type}/vio" if vio_judge.startswith(
                        "vio") else f"{SAVE_RES_DIR}/{vio_type}/no_vio"
                    shutil.copy(img_path, pred_result)
            print(f"违法代码：{vio_type} 图片总数：{num} 总耗时：{tt_time}")
