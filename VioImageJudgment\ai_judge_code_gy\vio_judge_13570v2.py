# 不礼让行人场景
import numpy as np
from init_models import *
from judge_tools import Tools
from car_detect import get_box_full
import cv2


# 调用函数匹配目标车辆
# 每张图只用blendmask识别一次，避免毫无意义的资源损耗

class BlendMaskCarInfo(object):
    def __init__(self, img_np, car_num, weights_list, device):
        self.img_np = img_np
        self.car_num = car_num
        self.weights_list = weights_list
        self.device = device

    def get_goal_car_info(self, judge_infos):
        return get_box_full(self.img_np, self.car_num, self.weights_list, self.device, judge_infos)

    # def get_zebra_line_box(self):
    #     return get_zebra_line_info(self.img_np)


class ComityPedestrianRull(object):
    def __init__(self, carbox_list, zebra_line_box_list, person_box_list, run_state, lane2_point,judge_mode,judge_thresh):
        self.carbox_list = carbox_list
        self.zebra_line_box_list = zebra_line_box_list
        self.person_box_list = person_box_list
        self.run_state = run_state
        self.lane2_point = lane2_point
        self.judge_mode = judge_mode
        self.judge_thresh = judge_thresh
    # 此场景未使用
    # def if_point_inpoly(self, polygon, point):
    #     line = LineString(polygon)
    #     pt = Point(point)
    #     pg = Polygon(line)
    #     return pg.contains(pt)

    # 此场景未使用
    def generate_poly_point(self, box):
        point1 = (box[0], box[1])
        point2 = (box[2], box[1])
        point3 = (box[2], box[3])
        point4 = (box[0], box[3])
        return [point1, point2, point3, point4]

    # 车辆与行人位置关系计算判定
    def get_shortest_car_person(self, in_zeabra_person_box):
        if self.judge_thresh is None:
            judge_thresh = 1.5 if self.judge_mode else 2
        else:
            judge_thresh = self.judge_thresh
        car_x_mid = (self.carbox_list[0] + self.carbox_list[2]) / 2
        car_y_mid = (self.carbox_list[1] + self.carbox_list[3]) / 2
        car_x_wid = abs(self.carbox_list[2] - self.carbox_list[0])
        car_y_wid = abs(self.carbox_list[3] - self.carbox_list[1])
        for person in in_zeabra_person_box:
            person_x_mid = (person[0] + person[2]) / 2
            # person_y_mid = (person[1] + person[3]) / 2
            person_y_mid = person[3]

            if self.lane2_point:
                ### 计算车中心点 人中心点到直线的垂足
                person_judge = Tools().get_foot_point([person_x_mid, person_y_mid],
                                                      [self.lane2_point[0][:2], self.lane2_point[0][2:]])
                car_judge = Tools().get_foot_point([car_x_mid, car_y_mid],
                                                   [self.lane2_point[0][:2], self.lane2_point[0][2:]])
                if abs(car_x_mid - person_x_mid) < car_x_wid * judge_thresh and abs(car_y_mid - person_y_mid) < 0.9 * car_y_wid \
                        and (car_x_mid - car_judge[0]) * (person_x_mid - person_judge[0]) > 0:
                    return True
            else:
                if abs(car_x_mid - person_x_mid) < car_x_wid * judge_thresh and abs(car_y_mid - person_y_mid) < 0.9 * car_y_wid:
                    return True
        return False

    # 判断车辆和最近行人的位置关系：通过目标车辆的box值和行人的多个box做x方向的距离判断，若小于一个box宽度，则认为违法
    def if_car_vio(self):
        return self.get_shortest_car_person(self.person_box_list)


# 计算IOU
def IOU(tl1, br1, tl2, br2):
    wh1, wh2 = br1 - tl1, br2 - tl2
    assert ((wh1 >= .0).all() and (wh2 >= .0).all())
    intersection_wh = np.maximum(np.minimum(br1, br2) - np.maximum(tl1, tl2), 0.)
    intersection_area = np.prod(intersection_wh)
    area1, area2 = (np.prod(wh1), np.prod(wh2))
    union_area = area1 + area2 - intersection_area
    return intersection_area / union_area


# 删除检测到骑在摩托车上的人
def del_motor_people(crop_img_list, box_list, label_list):
    # r_crop_img_list=[]
    # r_box_list=[]
    # r_label_list=[]
    for i, label in enumerate(label_list):
        if label == "motorcycle":
            label_list.pop(i)
            r_box = box_list.pop(i)
            crop_img_list.pop(i)
            for j, box in enumerate(box_list):
                if IOU(np.array(r_box[:2]), np.array(r_box[2:]), np.array(box[:2]), np.array(box[2:])) > 0:
                    label_list.pop(j)
                    box_list.pop(j)
                    crop_img_list.pop(j)
    return crop_img_list, box_list, label_list


### 删除所有骑车的人
def del_riding_people(crop_img_list, box_list, label_list):
    ### ["motorcycle", "person",'bicycle']
    bike_idx_list = [idx for idx, i in enumerate(label_list) if i in ['motorcycle', 'bicycle']]
    person_idx_list = [idx for idx, i in enumerate(label_list) if i == 'person']
    remove_idx_ls = []

    ### 找到和车辆有重合的人
    for i in range(len(person_idx_list)):
        person = box_list[person_idx_list[i]]
        for j in bike_idx_list:
            bike = box_list[j]
            if max(Tools().IOU_full(np.array(person[:2]), np.array(person[2:]),
                                    np.array(bike[:2]), np.array(bike[2:]))) > 0.2:
                remove_idx_ls.append(i)

    remove_idx_ls = list(set(remove_idx_ls))
    remove_idx_ls.sort(reverse=True)
    for i in remove_idx_ls:
        person_idx_list.pop(i)

    return [crop_img_list[i] for i in person_idx_list], \
        [box_list[i] for i in person_idx_list], \
        [label_list[i] for i in person_idx_list]


def del_driving_people(person_bbox_ps, car_bbox_ps):
    person_remove_list = []
    for i in range(len(person_bbox_ps)):
        person = person_bbox_ps[i]
        for car in car_bbox_ps:
            if max(Tools().IOU_full(np.array(person[:2]), np.array(person[2:]),
                                    np.array(car[:2]), np.array(car[2:]))) > 0.95:
                person_remove_list.append(i)
    person_remove_list = list(set(person_remove_list))
    person_remove_list.sort(reverse=True)
    for i in person_remove_list:
        person_bbox_ps.pop(i)
    return person_bbox_ps


# 不礼让行人场景主函数
def judge_vio(src_plate_num, file_list, weights_list, resize_scale=0.7, save=False,
              save_path=".", extra_msg=None):
    error_path = f"{save_path}/no_vio"
    vio_path = f"{save_path}/vio"
    if save:
        os.makedirs(error_path, exist_ok=True)
        os.makedirs(vio_path, exist_ok=True)
    # 读取模型
    plate_det_model = weights_list[0][0]
    plate_rec_model = weights_list[0][1]
    demo = weights_list[1]
    lp_model = weights_list[2][0]
    lp_meta = weights_list[2][1]
    yolov7_model = weights_list[3][0]
    yolov7_meta = weights_list[3][1]
    model_cnn = weights_list[-2]
    vio_model = weights_list[-1]

    vio_judge = 'no_vio'
    try:
        split_mode = extra_msg[0]
        black_height = extra_msg[1]
        black_pos = extra_msg[2]
        iieaAuditMode = extra_msg[3]
        iieaFilterVehicle = extra_msg[4]
    except:
        split_mode = 111
        black_height = 0
        black_pos = None
        iieaAuditMode = None
        iieaFilterVehicle = None
    judge_infos = {'plate_num': src_plate_num,
                   'vio_code': 1357,
                   'zebra_line': {},
                   'police': 0,
                   'has_crossline': 0,
                   'vehicle_location': 0,
                   'drv_direction': 0,
                   'drive_direction_light': [],
                   'lamp': [],
                   'stop_line': 0,
                   'lane': [],
                   'target_car': [],
                   'vio_judge': vio_judge,
                   'judgeConf': 1.0,
                   'modelDetectResults': [], 'split_mode': split_mode, "black_height": black_height, "black_pos":black_pos,"iieaAuditMode":iieaAuditMode,"iieaFilterVehicle":iieaFilterVehicle}
    # 特殊车牌废片判定
    if not src_plate_num:
        vio_judge = "no_vio_009"
        judge_infos['vio_judge'] = vio_judge
        return vio_judge, judge_infos
    if src_plate_num[0] not in chars:
        vio_judge = 'no_vio_002'
        judge_infos['vio_judge'] = vio_judge
        return vio_judge, judge_infos
    vio_judge = VIO_JUDGE_TOOL().spe_no_vio_judge(src_plate_num,iieaFilterVehicle)
    if vio_judge.startswith('no_vio'):
        judge_infos['vio_judge'] = vio_judge
        return vio_judge, judge_infos
    vio_judge = 'no_vio'

    judge_infos['target_car'] = []
    plate_list = []
    person_list = []
    target_car_list = []
    resize_img_list = []
    lane2_list = []
    illegal_label = 0

    # 收集车辆位置信息与路面交通标志信息
    for idx, img in enumerate(file_list):
        if idx == 3:
            continue
        temp_car = {}
        if (img.shape[0] > 4000 or img.shape[1] > 4000) and float(resize_scale) < 1:
            img = cv2.resize(img, None, fx=resize_scale, fy=resize_scale)
        resize_img_list.append(img)
        judge_weights_list = [demo, plate_det_model, plate_rec_model]
        judge_infos['img_id'] = idx + 1
        # 寻找目标车辆与双实线
        carinfo = BlendMaskCarInfo(img, src_plate_num, judge_weights_list, yolov7_meta[2])
        img_car_ps, plate_num, car_type, car_bbox_ps, lane2_point = carinfo.get_goal_car_info(judge_infos)  # 包含双实线的部分
        plate_list.append(plate_num)
        target_car_list.append(img_car_ps)
        temp_car['plate_num'] = plate_num
        temp_car['car_ps'] = img_car_ps
        judge_infos['target_car'].append(temp_car)
        lane2_list.append(lane2_point)  # 双实线
        if judge_infos["vio_judge"] == "no_vio_010":
            return "no_vio_010",judge_infos
        # 识别行人与摩托车
        crop_img_list, box_list, label_list = yolov7_detect(img, yolov7_model, yolov7_meta[0], yolov7_meta[1],
                                                            yolov7_meta[2], conf_thres=0.2,
                                                            classes=["motorcycle", "person", 'bicycle'])
        # 删除骑车的人
        crop_img_list, box_list, label_list = del_riding_people(crop_img_list, box_list, label_list)
        # 删除在开车的人
        box_list = del_driving_people(box_list, car_bbox_ps)
        person_list.append(box_list)

    # 双实线筛选
    # 取得最长的lane2_point 用于全局画图
    if len([i for i in lane2_list if len(i) > 0]) > 0:
        lane2_point = max([i for i in lane2_list if len(i) > 0],
                          key=lambda x: (x[0][0] - x[0][2]) ** 2 + (x[0][1] - x[0][3]) ** 2)
    else:
        lane2_point = []
    # iiea审核模式
    if iieaAuditMode is None:
        judge_mode = Precision_Judge_Mode[str(judge_infos['vio_code'])]
    else:
        judge_mode = iieaAuditMode
    judge_thresh = Judge_Thresh[str(judge_infos['vio_code'])][int(judge_mode)]
    # 判罚逻辑
    for i in range(len(resize_img_list)):
        try:
            state = False
            if len(target_car_list[i]):
                img_car_ps = target_car_list[i]
                img = resize_img_list[i]
                comitypedestrian = ComityPedestrianRull(img_car_ps, None, person_list[i], True, lane2_point,judge_mode,judge_thresh)
                # 判定人车位置关系
                state = comitypedestrian.if_car_vio()
                try:
                    x_ratio = ((img_car_ps[0] + img_car_ps[2]) / 2) / img.shape[1]
                except:
                    x_ratio = 0
                #     # 0 代表左边， 1代表右边
                if x_ratio <= 0.5:
                    judge_infos['vehicle_location'] = 0
                else:
                    judge_infos['vehicle_location'] = 1
        except:
            state = True
            traceback.print_exc()
            pass
        if state:
            illegal_label = illegal_label + 1
    res_list = []
    vio_judge = 'vio' if illegal_label > 0 else 'no_vio'

    # 没有一张图片找到目标车辆报废片
    for plate in plate_list:
        if plate == "NO_DETECT":
            res_list.append(0)
        else:
            res_list.append(1)
    if np.sum(res_list) == 0:
        vio_judge = 'no_vio_006'
        judge_infos["judgeConf"] = 0.9
    # 没有一张图片检测到行人报废片
    elif np.sum([len(i) for i in person_list]) == 0:
        vio_judge = 'no_vio_001'
        judge_infos["judgeConf"] = 0.85
    elif vio_judge.startswith('no') and len(res_list) >= 3 and res_list[2] == 0:
        vio_judge = 'no_vio_007'
        judge_infos["judgeConf"] = 0.8

    judge_infos['vio_judge'] = vio_judge
    if vio_judge.startswith('vio'):
        judge_infos['judgeConf'] = 0.9
    else:
        judge_infos['judgeConf'] = 0.8
    try:
        if save:
            show_person(resize_img_list, person_list)
            show_plate2(resize_img_list, target_car_list)
            show_lane2_top_point(resize_img_list, lane2_list)
            result_path = vio_path if vio_judge.startswith("vio") else error_path
            for idx, i in enumerate(resize_img_list):
                cv2.imwrite(result_path + f'/{vio_judge}_{src_plate_num}_img{idx + 1}.jpg', i)
    except:
        print("Error!不礼让行人可视化模块异常")
    return vio_judge, judge_infos


