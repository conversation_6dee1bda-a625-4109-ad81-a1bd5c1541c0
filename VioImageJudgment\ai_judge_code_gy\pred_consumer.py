import sys

sys.path.append("..")
from kafka import KafkaConsumer
from main_config.config import *
import logging
import logging.config
from main_config.log_config import get_logging
logging.config.dictConfig(get_logging("res_consumer"))
logger = logging.getLogger("console_plain_file_logger")


def res_consumer():
    consumer = KafkaConsumer(RES_TOPIC,
                             bootstrap_servers=SERVER,
                             group_id='test-consumer-group',
                             auto_offset_reset='earliest')
    for msg in consumer:
        res_dict = eval(msg.value)
        logger.info(f"{res_dict}")


if __name__ == '__main__':
    res_consumer()
