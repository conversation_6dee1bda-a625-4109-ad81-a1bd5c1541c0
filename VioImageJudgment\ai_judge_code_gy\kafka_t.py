from kafka import Kaf<PERSON><PERSON>onsumer, TopicPartition
from main_config.config import *


def get_topic_partitions():
    consumer = KafkaConsumer(bootstrap_servers=SERVER)
    partitions = consumer.partitions_for_topic(VIO_DATA_TOPIC)
    consumer.close()
    return [TopicPartition(VIO_DATA_TOPIC, p) for p in partitions]


partitions = get_topic_partitions()
num_partitions = len(partitions)
GPU_NUM = 2
Per_Multi = 2
num_consumers = GPU_NUM * Per_Multi
partitions_per_consumer = max(num_partitions // num_consumers, 1)
extra_partitions = num_partitions % num_consumers
for i in range(GPU_NUM):
    for j in range(Per_Multi):
        idx = i * Per_Multi + j
        if idx >= num_partitions:
            print(f"总分区数：{num_partitions} 多余的进程数：{idx + 1}")
            continue
        start = idx * partitions_per_consumer
        end = (idx + 1) * partitions_per_consumer
        process_partitions = partitions[start:end] + partitions[
                                                     num_consumers * partitions_per_consumer + idx:num_consumers * partitions_per_consumer + idx + 1] if idx < extra_partitions else partitions[
                                                                                                                                                                                     start:end]
        set_partition = [i.partition for i in process_partitions]
        print(set_partition)
