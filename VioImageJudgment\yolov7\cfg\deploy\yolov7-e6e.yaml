# parameters
nc: 80  # number of classes
depth_multiple: 1.0  # model depth multiple
width_multiple: 1.0  # layer channel multiple

# anchors
anchors:
  - [ 19,27,  44,40,  38,94 ]  # P3/8
  - [ 96,68,  86,152,  180,137 ]  # P4/16
  - [ 140,301,  303,264,  238,542 ]  # P5/32
  - [ 436,615,  739,380,  925,792 ]  # P6/64

# yolov7-e6e backbone
backbone:
  # [from, number, module, args],
  [[-1, 1, ReOrg, []],  # 0
   [-1, 1, Conv, [80, 3, 1]],  # 1-P1/2
   
   [-1, 1, DownC, [160]],  # 2-P2/4  
   [-1, 1, Conv, [64, 1, 1]],
   [-2, 1, Conv, [64, 1, 1]],
   [-1, 1, Conv, [64, 3, 1]],
   [-1, 1, Conv, [64, 3, 1]],
   [-1, 1, Conv, [64, 3, 1]],
   [-1, 1, Conv, [64, 3, 1]],
   [-1, 1, Conv, [64, 3, 1]],
   [-1, 1, Conv, [64, 3, 1]],
   [[-1, -3, -5, -7, -8], 1, <PERSON><PERSON>, [1]],
   [-1, 1, Conv, [160, 1, 1]],  # 12
   [-11, 1, Conv, [64, 1, 1]],
   [-12, 1, Conv, [64, 1, 1]],
   [-1, 1, Conv, [64, 3, 1]],
   [-1, 1, Conv, [64, 3, 1]],
   [-1, 1, Conv, [64, 3, 1]],
   [-1, 1, Conv, [64, 3, 1]],
   [-1, 1, Conv, [64, 3, 1]],
   [-1, 1, Conv, [64, 3, 1]],
   [[-1, -3, -5, -7, -8], 1, Concat, [1]],
   [-1, 1, Conv, [160, 1, 1]],  # 22
   [[-1, -11], 1, Shortcut, [1]],  # 23
         
   [-1, 1, DownC, [320]],  # 24-P3/8  
   [-1, 1, Conv, [128, 1, 1]],
   [-2, 1, Conv, [128, 1, 1]],
   [-1, 1, Conv, [128, 3, 1]],
   [-1, 1, Conv, [128, 3, 1]],
   [-1, 1, Conv, [128, 3, 1]],
   [-1, 1, Conv, [128, 3, 1]],
   [-1, 1, Conv, [128, 3, 1]],
   [-1, 1, Conv, [128, 3, 1]],
   [[-1, -3, -5, -7, -8], 1, Concat, [1]],
   [-1, 1, Conv, [320, 1, 1]],  # 34
   [-11, 1, Conv, [128, 1, 1]],
   [-12, 1, Conv, [128, 1, 1]],
   [-1, 1, Conv, [128, 3, 1]],
   [-1, 1, Conv, [128, 3, 1]],
   [-1, 1, Conv, [128, 3, 1]],
   [-1, 1, Conv, [128, 3, 1]],
   [-1, 1, Conv, [128, 3, 1]],
   [-1, 1, Conv, [128, 3, 1]],
   [[-1, -3, -5, -7, -8], 1, Concat, [1]],
   [-1, 1, Conv, [320, 1, 1]],  # 44
   [[-1, -11], 1, Shortcut, [1]],  # 45
         
   [-1, 1, DownC, [640]],  # 46-P4/16  
   [-1, 1, Conv, [256, 1, 1]],
   [-2, 1, Conv, [256, 1, 1]],
   [-1, 1, Conv, [256, 3, 1]],
   [-1, 1, Conv, [256, 3, 1]],
   [-1, 1, Conv, [256, 3, 1]],
   [-1, 1, Conv, [256, 3, 1]],
   [-1, 1, Conv, [256, 3, 1]],
   [-1, 1, Conv, [256, 3, 1]],
   [[-1, -3, -5, -7, -8], 1, Concat, [1]],
   [-1, 1, Conv, [640, 1, 1]],  # 56
   [-11, 1, Conv, [256, 1, 1]],
   [-12, 1, Conv, [256, 1, 1]],
   [-1, 1, Conv, [256, 3, 1]],
   [-1, 1, Conv, [256, 3, 1]],
   [-1, 1, Conv, [256, 3, 1]],
   [-1, 1, Conv, [256, 3, 1]],
   [-1, 1, Conv, [256, 3, 1]],
   [-1, 1, Conv, [256, 3, 1]],
   [[-1, -3, -5, -7, -8], 1, Concat, [1]],
   [-1, 1, Conv, [640, 1, 1]],  # 66
   [[-1, -11], 1, Shortcut, [1]],  # 67
         
   [-1, 1, DownC, [960]],  # 68-P5/32  
   [-1, 1, Conv, [384, 1, 1]],
   [-2, 1, Conv, [384, 1, 1]],
   [-1, 1, Conv, [384, 3, 1]],
   [-1, 1, Conv, [384, 3, 1]],
   [-1, 1, Conv, [384, 3, 1]],
   [-1, 1, Conv, [384, 3, 1]],
   [-1, 1, Conv, [384, 3, 1]],
   [-1, 1, Conv, [384, 3, 1]],
   [[-1, -3, -5, -7, -8], 1, Concat, [1]],
   [-1, 1, Conv, [960, 1, 1]],  # 78
   [-11, 1, Conv, [384, 1, 1]],
   [-12, 1, Conv, [384, 1, 1]],
   [-1, 1, Conv, [384, 3, 1]],
   [-1, 1, Conv, [384, 3, 1]],
   [-1, 1, Conv, [384, 3, 1]],
   [-1, 1, Conv, [384, 3, 1]],
   [-1, 1, Conv, [384, 3, 1]],
   [-1, 1, Conv, [384, 3, 1]],
   [[-1, -3, -5, -7, -8], 1, Concat, [1]],
   [-1, 1, Conv, [960, 1, 1]],  # 88
   [[-1, -11], 1, Shortcut, [1]],  # 89
         
   [-1, 1, DownC, [1280]],  # 90-P6/64  
   [-1, 1, Conv, [512, 1, 1]],
   [-2, 1, Conv, [512, 1, 1]],
   [-1, 1, Conv, [512, 3, 1]],
   [-1, 1, Conv, [512, 3, 1]],
   [-1, 1, Conv, [512, 3, 1]],
   [-1, 1, Conv, [512, 3, 1]],
   [-1, 1, Conv, [512, 3, 1]],
   [-1, 1, Conv, [512, 3, 1]],
   [[-1, -3, -5, -7, -8], 1, Concat, [1]],
   [-1, 1, Conv, [1280, 1, 1]],  # 100 
   [-11, 1, Conv, [512, 1, 1]],
   [-12, 1, Conv, [512, 1, 1]],
   [-1, 1, Conv, [512, 3, 1]],
   [-1, 1, Conv, [512, 3, 1]],
   [-1, 1, Conv, [512, 3, 1]],
   [-1, 1, Conv, [512, 3, 1]],
   [-1, 1, Conv, [512, 3, 1]],
   [-1, 1, Conv, [512, 3, 1]],
   [[-1, -3, -5, -7, -8], 1, Concat, [1]],
   [-1, 1, Conv, [1280, 1, 1]],  # 110
   [[-1, -11], 1, Shortcut, [1]],  # 111 
  ]

# yolov7-e6e head
head:
  [[-1, 1, SPPCSPC, [640]], # 112
  
   [-1, 1, Conv, [480, 1, 1]],
   [-1, 1, nn.Upsample, [None, 2, 'nearest']],
   [89, 1, Conv, [480, 1, 1]], # route backbone P5
   [[-1, -2], 1, Concat, [1]],
   
   [-1, 1, Conv, [384, 1, 1]],
   [-2, 1, Conv, [384, 1, 1]],
   [-1, 1, Conv, [192, 3, 1]],
   [-1, 1, Conv, [192, 3, 1]],
   [-1, 1, Conv, [192, 3, 1]],
   [-1, 1, Conv, [192, 3, 1]],
   [-1, 1, Conv, [192, 3, 1]],
   [-1, 1, Conv, [192, 3, 1]],
   [[-1, -2, -3, -4, -5, -6, -7, -8], 1, Concat, [1]],
   [-1, 1, Conv, [480, 1, 1]], # 126
   [-11, 1, Conv, [384, 1, 1]],
   [-12, 1, Conv, [384, 1, 1]],
   [-1, 1, Conv, [192, 3, 1]],
   [-1, 1, Conv, [192, 3, 1]],
   [-1, 1, Conv, [192, 3, 1]],
   [-1, 1, Conv, [192, 3, 1]],
   [-1, 1, Conv, [192, 3, 1]],
   [-1, 1, Conv, [192, 3, 1]],
   [[-1, -2, -3, -4, -5, -6, -7, -8], 1, Concat, [1]],
   [-1, 1, Conv, [480, 1, 1]], # 136
   [[-1, -11], 1, Shortcut, [1]],  # 137
  
   [-1, 1, Conv, [320, 1, 1]],
   [-1, 1, nn.Upsample, [None, 2, 'nearest']],
   [67, 1, Conv, [320, 1, 1]], # route backbone P4
   [[-1, -2], 1, Concat, [1]],
   
   [-1, 1, Conv, [256, 1, 1]],
   [-2, 1, Conv, [256, 1, 1]],
   [-1, 1, Conv, [128, 3, 1]],
   [-1, 1, Conv, [128, 3, 1]],
   [-1, 1, Conv, [128, 3, 1]],
   [-1, 1, Conv, [128, 3, 1]],
   [-1, 1, Conv, [128, 3, 1]],
   [-1, 1, Conv, [128, 3, 1]],
   [[-1, -2, -3, -4, -5, -6, -7, -8], 1, Concat, [1]],
   [-1, 1, Conv, [320, 1, 1]], # 151
   [-11, 1, Conv, [256, 1, 1]],
   [-12, 1, Conv, [256, 1, 1]],
   [-1, 1, Conv, [128, 3, 1]],
   [-1, 1, Conv, [128, 3, 1]],
   [-1, 1, Conv, [128, 3, 1]],
   [-1, 1, Conv, [128, 3, 1]],
   [-1, 1, Conv, [128, 3, 1]],
   [-1, 1, Conv, [128, 3, 1]],
   [[-1, -2, -3, -4, -5, -6, -7, -8], 1, Concat, [1]],
   [-1, 1, Conv, [320, 1, 1]], # 161
   [[-1, -11], 1, Shortcut, [1]],  # 162
   
   [-1, 1, Conv, [160, 1, 1]],
   [-1, 1, nn.Upsample, [None, 2, 'nearest']],
   [45, 1, Conv, [160, 1, 1]], # route backbone P3
   [[-1, -2], 1, Concat, [1]],
   
   [-1, 1, Conv, [128, 1, 1]],
   [-2, 1, Conv, [128, 1, 1]],
   [-1, 1, Conv, [64, 3, 1]],
   [-1, 1, Conv, [64, 3, 1]],
   [-1, 1, Conv, [64, 3, 1]],
   [-1, 1, Conv, [64, 3, 1]],
   [-1, 1, Conv, [64, 3, 1]],
   [-1, 1, Conv, [64, 3, 1]],
   [[-1, -2, -3, -4, -5, -6, -7, -8], 1, Concat, [1]],
   [-1, 1, Conv, [160, 1, 1]], # 176
   [-11, 1, Conv, [128, 1, 1]],
   [-12, 1, Conv, [128, 1, 1]],
   [-1, 1, Conv, [64, 3, 1]],
   [-1, 1, Conv, [64, 3, 1]],
   [-1, 1, Conv, [64, 3, 1]],
   [-1, 1, Conv, [64, 3, 1]],
   [-1, 1, Conv, [64, 3, 1]],
   [-1, 1, Conv, [64, 3, 1]],
   [[-1, -2, -3, -4, -5, -6, -7, -8], 1, Concat, [1]],
   [-1, 1, Conv, [160, 1, 1]], # 186
   [[-1, -11], 1, Shortcut, [1]],  # 187
      
   [-1, 1, DownC, [320]],
   [[-1, 162], 1, Concat, [1]],
   
   [-1, 1, Conv, [256, 1, 1]],
   [-2, 1, Conv, [256, 1, 1]],
   [-1, 1, Conv, [128, 3, 1]],
   [-1, 1, Conv, [128, 3, 1]],
   [-1, 1, Conv, [128, 3, 1]],
   [-1, 1, Conv, [128, 3, 1]],
   [-1, 1, Conv, [128, 3, 1]],
   [-1, 1, Conv, [128, 3, 1]],
   [[-1, -2, -3, -4, -5, -6, -7, -8], 1, Concat, [1]],
   [-1, 1, Conv, [320, 1, 1]], # 199
   [-11, 1, Conv, [256, 1, 1]],
   [-12, 1, Conv, [256, 1, 1]],
   [-1, 1, Conv, [128, 3, 1]],
   [-1, 1, Conv, [128, 3, 1]],
   [-1, 1, Conv, [128, 3, 1]],
   [-1, 1, Conv, [128, 3, 1]],
   [-1, 1, Conv, [128, 3, 1]],
   [-1, 1, Conv, [128, 3, 1]],
   [[-1, -2, -3, -4, -5, -6, -7, -8], 1, Concat, [1]],
   [-1, 1, Conv, [320, 1, 1]], # 209
   [[-1, -11], 1, Shortcut, [1]],  # 210
      
   [-1, 1, DownC, [480]],
   [[-1, 137], 1, Concat, [1]],
   
   [-1, 1, Conv, [384, 1, 1]],
   [-2, 1, Conv, [384, 1, 1]],
   [-1, 1, Conv, [192, 3, 1]],
   [-1, 1, Conv, [192, 3, 1]],
   [-1, 1, Conv, [192, 3, 1]],
   [-1, 1, Conv, [192, 3, 1]],
   [-1, 1, Conv, [192, 3, 1]],
   [-1, 1, Conv, [192, 3, 1]],
   [[-1, -2, -3, -4, -5, -6, -7, -8], 1, Concat, [1]],
   [-1, 1, Conv, [480, 1, 1]], # 222
   [-11, 1, Conv, [384, 1, 1]],
   [-12, 1, Conv, [384, 1, 1]],
   [-1, 1, Conv, [192, 3, 1]],
   [-1, 1, Conv, [192, 3, 1]],
   [-1, 1, Conv, [192, 3, 1]],
   [-1, 1, Conv, [192, 3, 1]],
   [-1, 1, Conv, [192, 3, 1]],
   [-1, 1, Conv, [192, 3, 1]],
   [[-1, -2, -3, -4, -5, -6, -7, -8], 1, Concat, [1]],
   [-1, 1, Conv, [480, 1, 1]], # 232
   [[-1, -11], 1, Shortcut, [1]],  # 233
      
   [-1, 1, DownC, [640]],
   [[-1, 112], 1, Concat, [1]],
   
   [-1, 1, Conv, [512, 1, 1]],
   [-2, 1, Conv, [512, 1, 1]],
   [-1, 1, Conv, [256, 3, 1]],
   [-1, 1, Conv, [256, 3, 1]],
   [-1, 1, Conv, [256, 3, 1]],
   [-1, 1, Conv, [256, 3, 1]],
   [-1, 1, Conv, [256, 3, 1]],
   [-1, 1, Conv, [256, 3, 1]],
   [[-1, -2, -3, -4, -5, -6, -7, -8], 1, Concat, [1]],
   [-1, 1, Conv, [640, 1, 1]], # 245
   [-11, 1, Conv, [512, 1, 1]],
   [-12, 1, Conv, [512, 1, 1]],
   [-1, 1, Conv, [256, 3, 1]],
   [-1, 1, Conv, [256, 3, 1]],
   [-1, 1, Conv, [256, 3, 1]],
   [-1, 1, Conv, [256, 3, 1]],
   [-1, 1, Conv, [256, 3, 1]],
   [-1, 1, Conv, [256, 3, 1]],
   [[-1, -2, -3, -4, -5, -6, -7, -8], 1, Concat, [1]],
   [-1, 1, Conv, [640, 1, 1]], # 255
   [[-1, -11], 1, Shortcut, [1]],  # 256
   
   [187, 1, Conv, [320, 3, 1]],
   [210, 1, Conv, [640, 3, 1]],
   [233, 1, Conv, [960, 3, 1]],
   [256, 1, Conv, [1280, 3, 1]],

   [[257,258,259,260], 1, Detect, [nc, anchors]],   # Detect(P3, P4, P5, P6)
  ]
