import math
from abc import ABCMeta, abstractmethod
from typing import Dict, Optional, List, Tuple
import cv2
from conf.config import OptConf
from conf.judge_code_desc import Judge_Code_Desc_Dict
from libs.plate_recognition.plate_rec import get_plate_rec_landmark
from src.module.image_model import ModelResults, ModelResult
from src.utils.tools import log_error, IOU
from src.service.data_class import JudgeResult, JudgeResCore
import numpy as np


# 规则父类
class VioRule(metaclass=ABCMeta):
    def __init__(self, name: str, params: Dict, model_dict: Dict):
        self.name: str = name
        self._params: dict = params
        self._model_dict: dict = model_dict
        self._conf = OptConf

    def run(self, judgeres: JudgeResult):
        if judgeres.valid:
            self._run(judgeres)

    @abstractmethod
    def _run(self, judgeres: JudgeResult):
        pass


# 融合分割模型和检测模型的机动车识别结果
class MergeVehRule(VioRule):
    def __init__(self, name, params, model_dict):
        super().__init__(name, params, model_dict)
        self._cls_name = self.__class__.__name__
        self._del_head_veh = self._params.get("del_head_veh", False)  # 三图去除车头坐标
        self._del_head_thresh = self._params.get("del_head_veh_thresh", 0.42)  # 三图去除车头坐标阈值
        self._iou_thresh = self._params.get('iou_thresh', 0.75)  # IOU阈值

    def _run(self, judgeres: JudgeResult) -> None:
        img_keys = judgeres.get_act_detail("img_keys")
        self._merge_res_dict = {}  # 并集(弥补分割模型车辆检测能力,相交部分只保留分割模型标签)
        self._intersection_res_dict = {}  # 交集
        for key in img_keys:
            self._filter_special_veh(key, judgeres)  # 找到救护车和车头坐标
            boxes1_array, boxes2_array, subclass1_array, subclass2_array, score1_array, score2_array = self._get_veh_ndarray(
                key, judgeres)
            try:
                iou_res = IOU.compute_iou(boxes1_array, boxes2_array)
                idx1, idx2 = np.where(iou_res > self._iou_thresh)
            except:
                idx1 = np.array([])
                idx2 = np.array([])
            # intersection_merge_boxes = self.merge_boxes(boxes1_array, boxes2_array, idx1, idx2).tolist()  # 调整坐标框范围（暂不使用,过滤逻辑依赖原始坐标框）
            try:
                intersection_merge_boxes = boxes2_array[idx2].tolist()  # 交集部分保留分割模型车框
            except:
                intersection_merge_boxes = []
            intersection_all_subclass = [(subclass1_array[i], subclass2_array[j]) for i, j in zip(idx1, idx2)]
            intersection_all_score = [(score1_array[i], score2_array[j]) for i, j in zip(idx1, idx2)]
            # 只保留分割模型标签和置信度
            intersection_seg_subclass = [i[1] for i in intersection_all_subclass]
            intersection_seg_score = [i[1] for i in intersection_all_score]
            # 得到需要保留的框和标签
            all_idx1 = np.arange(len(boxes1_array))
            save_idx1 = np.setdiff1d(all_idx1, idx1, assume_unique=True)
            save_boxes1 = boxes1_array[save_idx1].tolist()
            all_idx2 = np.arange(len(boxes2_array))
            save_idx2 = np.setdiff1d(all_idx2, idx2, assume_unique=True)
            save_boxes2 = boxes2_array[save_idx2].tolist()
            save_subclasses1 = subclass1_array[save_idx1].tolist()
            save_subclasses2 = subclass2_array[save_idx2].tolist()
            save_score1 = score1_array[save_idx1].tolist()
            save_score2 = score2_array[save_idx2].tolist()
            # 合并框和标签
            merge_boxes = intersection_merge_boxes + save_boxes1 + save_boxes2
            merge_subclass = intersection_seg_subclass + save_subclasses1 + save_subclasses2
            merge_score = intersection_seg_score + save_score1 + save_score2
            self._merge_res_dict[key]["box"] = merge_boxes
            self._merge_res_dict[key]["subclass"] = merge_subclass
            self._merge_res_dict[key]["score"] = merge_score
            self._intersection_res_dict[key]["box"] = intersection_merge_boxes
            self._intersection_res_dict[key]["subclass"] = intersection_all_subclass
            self._intersection_res_dict[key]["score"] = intersection_all_score
        judgeres.add_act_details(self._cls_name, detail={"merge_veh": self._merge_res_dict,
                                                         "intersection_veh": self._intersection_res_dict})

    def _filter_special_veh(self, key: str, judgeres: JudgeResult) -> None:
        # 过滤车头坐标
        if self._del_head_veh and key == "img3":
            head_boxes_array = judgeres.get_tfc_elements(key, "head_veh", "box", to_array=True)
            head_score_array = judgeres.get_tfc_elements(key, "head_veh", "score", to_array=True)
            head_idx = np.where(head_score_array >= self._del_head_thresh)
            head_boxes = head_boxes_array[head_idx].tolist()
            judgeres.add_act_details(self._cls_name, {"head_boxes": head_boxes})
        # 救护车过滤
        if self._conf.Ambulance_Filter and key == "img1":  # TODO：特殊车辆判断移动到其它模块
            special_boxes_array = judgeres.get_tfc_elements(key, "special_vehicle", "box", to_array=True)
            special_subclass_array = judgeres.get_tfc_elements(key, "special_vehicle", "subclass", to_array=True)
            special_score_array = judgeres.get_tfc_elements(key, "special_vehicle", "score", to_array=True)
            ambulance_head_boxes_sure, head_score = self.filter_ambulances(special_subclass_array,
                                                                           special_boxes_array,
                                                                           special_score_array,
                                                                           "ambulance_h",
                                                                           self._conf.Ambulance_Head_Thresh)
            ambulance_boxes_sure, score = self.filter_ambulances(
                special_subclass_array, special_boxes_array, special_score_array,
                "ambulance_b", self._conf.Ambulance_Thresh
            )
            judgeres.add_act_details(self._cls_name, {"ambulance": ambulance_head_boxes_sure + ambulance_boxes_sure,
                                                      "ambulance_score": head_score + score})

    # 分别获取两个模型机动车的框、类别、置信度
    def _get_veh_ndarray(self, key: str, judgeres: JudgeResult) -> Tuple:
        self._merge_res_dict[key] = {}
        self._intersection_res_dict[key] = {}  # TODO:得到两个模型标签一致的框
        boxes1_array = judgeres.get_tfc_elements(key, "vehicle", "box", to_array=True)
        boxes2_array = judgeres.get_tfc_elements(key, "vehicle2", "box", to_array=True)
        subclass1_array = judgeres.get_tfc_elements(key, "vehicle", "subclass", to_array=True)
        subclass2_array = judgeres.get_tfc_elements(key, "vehicle2", "subclass", to_array=True)
        score1_array = judgeres.get_tfc_elements(key, "vehicle", "score", to_array=True)
        score2_array = judgeres.get_tfc_elements(key, "vehicle2", "score", to_array=True)
        return boxes1_array, boxes2_array, subclass1_array, subclass2_array, score1_array, score2_array

    @staticmethod
    def merge_boxes(boxes1: np.ndarray, boxes2: np.ndarray, idx1: np.ndarray, idx2: np.ndarray) -> np.ndarray:
        """
        将两个模型输出的同一目标的坐标框进行最大化处理
        Parameters:
        - boxes1: 人车模型检测的机动车坐标框
        - boxes2: 交通元素分割模型检测的机动车坐标框
        - idx1: boxes1同一个目标的索引列表
        - idx2: boxes2同一个目标的索引列表
        Returns:
        - merged_boxes: 最大化后的坐标框
        """
        try:
            if len(idx1) != len(idx2):
                raise ValueError("Index arrays must have the same length.")
            if not len(idx1):
                raise ValueError("Index arrays is empty.")
            selected_boxes1 = boxes1[idx1]
            selected_boxes2 = boxes2[idx2]
            # Compute the union of each pair of boxes
            x1 = np.minimum(selected_boxes1[:, 0], selected_boxes2[:, 0])
            y1 = np.minimum(selected_boxes1[:, 1], selected_boxes2[:, 1])
            x2 = np.maximum(selected_boxes1[:, 2], selected_boxes2[:, 2])
            y2 = np.maximum(selected_boxes1[:, 3], selected_boxes2[:, 3])
            # Stack the results into a new array of merged boxes
            merged_boxes = np.column_stack((x1, y1, x2, y2))
        except:
            merged_boxes = np.array([])
        return merged_boxes

    @staticmethod
    def filter_ambulances(special_subclass_array: np.ndarray, special_boxes_array: np.ndarray,
                          special_score_array: np.ndarray, subclass: str, score_thresh: float) -> Tuple[list, list]:
        if len(special_subclass_array):
            idx = np.where(special_subclass_array == subclass)
            filtered_boxes = special_boxes_array[idx]
            filtered_score = special_score_array[idx]
            sure_indices = np.where(filtered_score >= score_thresh)
            return filtered_boxes[sure_indices].tolist(), filtered_score[sure_indices].tolist()
        else:
            return [], []


# 根据车框截取图片
class CropVehRule(VioRule):
    def __init__(self, name, params, model_dict):
        super().__init__(name, params, model_dict)
        self._cls_name = self.__class__.__name__

    def _run(self, judgeres: JudgeResult) -> None:
        img_list = judgeres.get_act_detail("split_img")
        img_keys = judgeres.get_act_detail("img_keys")  # 注意图片配置统一
        has_merge_veh = "merge_veh" in judgeres.act_details
        # 获取车框
        if has_merge_veh and judgeres.get_act_detail("merge_veh"):
            res_dict = {
                img_key: judgeres.get_act_detail("merge_veh")[img_key]["box"]
                for img_key in img_keys
            }
        else:
            res_dict = {
                img_key: judgeres.tfc_elements.get(img_key, ModelResults()).get_results(superclass="merge_veh",
                                                                                        attr="box")
                for img_key in img_keys
            }

        veh_img_dict = {}
        veh_img_bias_dict = {}
        for idx, (img_key, boxes) in enumerate(res_dict.items()):
            veh_img_dict[img_key] = []
            veh_img_bias_dict[img_key] = []
            for box in boxes:
                # 车牌识别去除车辆上三分之一部分
                h_bias = (box[3] - box[1]) // 3
                veh_img = img_list[idx][box[1] + h_bias:box[3], box[0]:box[2]]
                veh_img_dict[img_key].append(veh_img)
                veh_img_bias_dict[img_key].append(h_bias)
        judgeres.add_act_details(self._cls_name,
                                 detail={"veh_img": veh_img_dict, "veh_img_bias": veh_img_bias_dict,
                                         "veh_box": res_dict})


# 车牌匹配目标车辆
class PlateMatchRule(VioRule):
    def __init__(self, name, params, model_dict):
        super().__init__(name, params, model_dict)
        self._cls_name = self.__class__.__name__
        self._det_model = model_dict["PlateDetModel"]
        self._rec_model = model_dict["PlateRecModel"]
        self._filter_small_plate_thresh = params.get("small_plate_thresh", 380)  # 小车牌过滤阈值
        self._det_thresh = params.get("det_thresh", 0.15)  # 车牌检测阈值
        self._del_thresh = params.get("del_thresh", 0.95)  # 干扰车牌阈值（车辆位于图片下半部分）
        self._del_thresh2 = params.get("del_thresh2", 0.93)  # 干扰车牌阈值（车辆位于图片上半部分）
        self._match_thresh = self._params.get("plate_thresh", [3, 2])  # 车牌模糊匹配阈值[连续相同位数，对位错误数量]
        self._slice_rec = self._params.get("slice", True)  # 图片切分识别车牌
        self._slice_height = params.get("slice_height", 0.5)  # 可进行切分识别的图片高度比例

    def _run(self, judgeres: JudgeResult) -> None:
        target_plate = judgeres.request.plate_no
        target_veh_box = {}
        target_veh_plate = {}
        target_veh_plate_cor = {}
        target_veh_plate_score = {}
        all_plate_cor = {}
        del_veh_box = {}
        no_det_plate_veh = []  # 第一图未检测到车牌的车辆,切片识别
        veh_img_dict = judgeres.get_act_detail("veh_img")  # 取车辆图片
        veh_box_dict = judgeres.get_act_detail("veh_box")
        veh_plate_bias = judgeres.get_act_detail("veh_img_bias")  # 取车牌坐标偏移量
        img_list = judgeres.get_act_detail("split_img")  # 取切分图片
        img_h_list = [i.shape[0] for i in img_list]
        for idx, (img_key, veh_img_list) in enumerate(veh_img_dict.items()):
            is_match = False
            plate_whole = False
            img_h = img_h_list[idx]
            del_veh_box[img_key] = []  # 存放根据车牌结果删除的无关车框
            all_plate_cor[img_key] = {}  # 存放所有车辆能检测到的车牌坐标
            for idx2, veh_img in enumerate(veh_img_list):
                veh_box = veh_box_dict[img_key][idx2]
                # 车牌检测 TODO:过滤较小车辆，考虑摩托车的情况
                det_results: ModelResults = self._det_model.run(veh_img, thresh=self._det_thresh)
                if det_results.results:
                    judgeres.logger.debug(
                        f"{idx + 1}-{idx2 + 1} 车牌坐标检测结果:{det_results.get_results(attr='box')}")
                elif veh_box[3] > self._slice_height * img_h:
                    no_det_plate_veh.append((veh_img, veh_box))
                judgeres.set_tfc_elements(idx, det_results)
                for det_res in det_results.get_results():
                    tmp_cor = det_res.box
                    # 修正车牌坐标
                    h_bias = veh_plate_bias[img_key][idx2]
                    plate_cor = [tmp_cor[0] + veh_box[0], tmp_cor[1] + veh_box[1] + h_bias,
                                 tmp_cor[2] + veh_box[0],
                                 tmp_cor[3] + veh_box[1] + h_bias]
                    plate_area = (tmp_cor[2] - tmp_cor[0]) * (tmp_cor[3] - tmp_cor[1])
                    if plate_area < self._filter_small_plate_thresh:
                        judgeres.logger.debug(f"忽略检测出的小车牌({plate_area})，对应车辆：{img_key}-{veh_box}")
                        continue
                    plate_type = det_res.subclass
                    key_points = det_res.get_ext_value("key_points")
                    # 车牌抠图加透视变换
                    plate_img = get_plate_rec_landmark(veh_img, key_points, plate_type)
                    # 车牌识别
                    rec_results = self._rec_model.run(plate_img)
                    judgeres.set_tfc_elements(idx, rec_results)
                    if rec_results.results:
                        pred_plate: str = rec_results.get_results(attr='text')[0]
                        plate_score: list = rec_results.get_results(attr='score')[0]
                        judgeres.logger.debug(f"{idx + 1}-{idx2 + 1} 车牌识别结果:{pred_plate}")
                        all_plate_cor[img_key][str(veh_box)] = plate_cor
                        # 车牌匹配判断
                        if pred_plate == target_plate or self.plate_fuzzy_match(target_plate, pred_plate):
                            target_veh_box[img_key] = veh_box  # 保存目标车辆坐标
                            target_veh_plate[img_key] = pred_plate  # 保存目标车辆识别的车牌结果
                            target_veh_plate_cor[img_key] = plate_cor  # 保存目标车辆车牌坐标
                            target_veh_plate_score[img_key] = plate_score  # 保存目标车辆车牌识别置信度
                            judgeres.logger.info(f"第{idx + 1}张图车牌匹配成功!")
                            if (pred_plate[-1] == "警" and
                                    plate_score[-1] >= self._conf.Police_Plate_Thresh and
                                    judgeres.request.filter_police):
                                judgeres.set_judres_core(judge_status=2, judge_confid=plate_score[-1],
                                                         result_code="no_vio_010",
                                                         result_desc="警车过滤，模型识别的车牌结尾字符为'警'")
                            is_match = True
                            break
                        if len(pred_plate) >= 7 and pred_plate[0] in self._conf.First_Plate_No:
                            plate_whole = True  # 判断车牌完整性
                        confidence = np.min(plate_score)
                        # 为后续车辆相似度对比模块去除置信度大的非目标车辆
                        if plate_whole and ((confidence >= self._del_thresh and 0.31 * img_h < (
                                veh_box[1] + veh_box[3]) / 2 <= 0.5 * img_h) or (
                                                    confidence >= self._del_thresh2 and (
                                                    veh_box[1] + veh_box[3]) / 2 > 0.5 *
                                                    img_h) or confidence == 1) and veh_box not in del_veh_box[img_key]:
                            del_veh_box[img_key].append(veh_box)
                if is_match:
                    break
            judgeres.logger.info(f"第{idx + 1}张图车牌检测识别模型推理完成\n")
        # 切片识别车牌
        self.slice_rec_plate(judgeres, target_veh_box, target_veh_plate, no_det_plate_veh)
        judgeres.add_act_details(self._cls_name,
                                 detail={"target_veh_box": target_veh_box, "target_veh_plate": target_veh_plate,
                                         "target_veh_plate_cor": target_veh_plate_cor,
                                         "target_veh_plate_score": target_veh_plate_score,
                                         "all_plate_cor": all_plate_cor,
                                         "del_veh_box": del_veh_box})

    # 车牌模糊匹配
    def plate_fuzzy_match(self, target_plate, pred_plate) -> bool:
        if self._match_thresh == [None, None]:
            return False
        offset_judge = False
        if len(target_plate) == len(pred_plate):
            offset_judge = True
            error_num = 0
            for i in range(len(pred_plate)):
                if i != 0 and pred_plate[i] != target_plate[i]:
                    # 排除易出错字符
                    if (pred_plate[i] in ["Z", "2"] and target_plate[i] in ["Z", "2"]) or (
                            pred_plate[i] in ["D", "0"] and target_plate[i] in ["D", "0"]) or (
                            pred_plate[i] in ["S", "5"] and target_plate[i] in ["S", "5"]) or (
                            pred_plate[i] in ["8", "B"] and target_plate[i] in ["8", "B"]):
                        continue
                    error_num += 1
            if error_num <= self._match_thresh[1]:
                return True
        if offset_judge:
            pred_plate = pred_plate[2:]
        target_plate = target_plate[2:]
        n = len(pred_plate)
        if n > 2:
            num = self._match_thresh[0]
            random_5 = n - num
            index = [i for i in range(random_5)]
            for i in range(int(len(index) + 1)):
                temp_plate = pred_plate[i:i + num]
                # 允许连续三位匹配车牌的起始索引相差小于2
                if temp_plate in target_plate and (
                        not offset_judge or (abs(target_plate.index(temp_plate) - i) < 2 and offset_judge)):
                    return True
        return False

    # 对未检测到车牌的车辆进行竖向切分识别
    def slice_rec_plate(self, judgeres: JudgeResult, target_veh_box: dict, target_veh_plate: dict,
                        no_det_plate_veh: List[Tuple[np.ndarray, list]]) -> None:
        target_plate = judgeres.request.plate_no
        img_key_list = list(target_veh_box.keys())
        if self._slice_rec and "img1" not in img_key_list and "img2" not in img_key_list:
            for veh_img, veh_box in no_det_plate_veh:
                h, w, _ = veh_img.shape
                if h < 150:
                    continue
                # 分别切成2、3、4份
                for num in [2, 3, 4]:
                    slice_y = h // num
                    slice_x = w // 4  # 新增横向切分
                    slice_veh_img = veh_img[(num - 1) * slice_y:, slice_x:w - slice_x]  # 只取底部的切片部分识别车牌
                    # 车牌识别
                    rec_results: ModelResults = self._rec_model.run(slice_veh_img)
                    if rec_results.results:
                        pred_plate = rec_results.get_results(attr='text')[0]
                        plate_score = rec_results.get_results(attr='score')[0]
                        judgeres.logger.debug(f"车辆{num}分识别车牌结果：{pred_plate} 车辆坐标：{veh_box}")
                        # 车牌匹配
                        if pred_plate == target_plate or self.plate_fuzzy_match(target_plate, pred_plate):
                            target_veh_box["img1"] = veh_box
                            target_veh_plate["img1"] = f"{pred_plate}_slice"
                            judgeres.logger.info(f"车辆{num}分识别车牌匹配成功! 车辆坐标：{veh_box}")
                            if (pred_plate[-1] == "警" and
                                    plate_score[-1] >= self._conf.Police_Plate_Thresh and
                                    judgeres.request.filter_police):
                                judgeres.set_judres_core(judge_status=2, judge_confid=plate_score[-1],
                                                         result_code="no_vio_010",
                                                         result_desc="警车过滤，模型识别的车牌结尾字符为'警'")
                            return None


# 通过车辆特征过滤特殊车辆
class FilterVehRule(VioRule):
    def __init__(self, name, params, model_dict):
        super().__init__(name, params, model_dict)
        self._cls_name = self.__class__.__name__

    def _run(self, judgeres: JudgeResult) -> None:
        ambulance_boxes: Dict[list] = judgeres.get_act_detail("ambulance")
        ambulance_scores: dict = judgeres.get_act_detail("ambulance_score")
        target_veh_boxes: dict = judgeres.get_act_detail("target_veh_box")
        merge_veh: Dict = judgeres.get_act_detail("merge_veh", {})
        target_veh_plate: dict = judgeres.get_act_detail("target_veh_plate")
        labels_dict = {}
        for img_key, target_box in target_veh_boxes.items():
            res = self.get_target_veh_label(target_box, merge_veh, img_key)  # TODO:获取目标车辆类别和置信度，考虑移动到其它动作
            labels_dict[img_key] = res
            if target_veh_plate[img_key] == "similar_match":
                continue
            if ambulance_boxes and target_box in ambulance_boxes[img_key]:
                idx = ambulance_boxes[img_key].index(target_box)
                judgeres.set_judres_core(judge_status=2, judge_confid=ambulance_scores[img_key][idx],
                                         result_code="no_vio_010", result_desc="救护车过滤")
                break
        judgeres.add_act_details(self._cls_name, {"target_veh_label": labels_dict})

    def get_target_veh_label(self, veh_box: List, merge_veh: Dict, img_key: str) -> Optional[str]:
        msg_dict = merge_veh.get(img_key, {})
        boxes = msg_dict.get("box")
        classes = msg_dict.get("subclass")
        scores = msg_dict.get("score")
        res = None
        if boxes:
            idx = boxes.index(veh_box)
            cls = classes[idx]
            score = scores[idx]
            res = f"{cls}-{score}"
        return res


# 车辆相似度匹配
class SimilarMatchRule(VioRule):
    def __init__(self, name, params, model_dict):
        super().__init__(name, params, model_dict)
        self._cls_name = self.__class__.__name__
        self._sim_model = model_dict["SimilarModel"]
        self._feature_match = params.get("feature_match", False)  # 特征图匹配
        self._match_range_list = params.get("match_range", [0.32, 0.13])  # 数值越小选择的范围越大
        self._sim_thresh = params.get("match_thresh", 0.35)  # 相似度结果小于等于阈值才匹配成功

    def _run(self, judgeres: JudgeResult) -> None:
        # 取切分图片
        img_list = judgeres.get_act_detail("split_img")
        # 取相似度匹配车框
        similar_match_box: dict = judgeres.get_act_detail("veh_box")
        # 取目标车辆车框
        target_veh_box: dict = judgeres.get_act_detail("target_veh_box")
        self._match_range = self._match_range_list[0]
        self._match_code = judgeres.request.match_viocode
        self._head_boxes: list = judgeres.get_act_detail("head_boxes")
        self._del_veh_box: dict = judgeres.get_act_detail("del_veh_box")
        self._logger = judgeres.logger
        img_num = len(img_list)
        save_sim_res = {}  # 存储相似度匹配数值和对象
        # 特征图和第一张图进行车辆相似度匹配
        if self._feature_match and f"img{img_num}" in target_veh_box and "img1" not in target_veh_box:
            self._logger.info("开始进行特征图与首图车辆相似度匹配...")
            target_img = img_list[-1]
            match_img = img_list[0]
            target_box = target_veh_box[f"img{img_num}"]
            target_veh = target_img[target_box[1]:target_box[3], target_box[0]:target_box[2]]
            self._match_mode = "feature"
            self._target_point = ((target_box[0] + target_box[2]) // 2, target_box[3])  # 计算相似度范围的目标点

            match_box, similar_dist = self.similar_run(target_veh, match_img,
                                                       similar_match_box["img1"])
            if match_box:
                judgeres.act_details["target_veh_plate"]["img1"] = "similar_match"
                target_veh_box["img1"] = match_box
                save_sim_res["feature"] = similar_dist
        target_msg_list = []
        target_img_idx = []
        for img_key, target_box in target_veh_box.items():
            if self._feature_match and img_key == f"img{img_num}":
                continue
            img_idx = int(img_key[-1]) - 1
            target_img_idx.append(img_idx)
            img = img_list[img_idx]
            target_veh = img[target_box[1]:target_box[3], target_box[0]:target_box[2]]
            target_point = ((target_box[0] + target_box[2]) // 2, target_box[3])  # 计算相似度范围的目标点
            target_msg_list.append((target_box, target_veh, target_point))
        tmp_array = np.array(target_img_idx)
        for img_key2, veh_vox_list in similar_match_box.items():
            img_idx2 = int(img_key2[-1]) - 1
            if img_key2 in target_veh_box:  # 已找到目标车辆的图片不做匹配
                continue
            match_img = img_list[img_idx2]
            idx_dist_array = img_idx2 - tmp_array
            if len(idx_dist_array):
                choose_idx = np.argmin(abs(idx_dist_array))  # 选择图片序号最近的已知目标车辆进行匹配
                target_box, target_veh, self._target_point = target_msg_list[choose_idx]
                idx_dist = idx_dist_array[choose_idx]
                self._match_mode = "forward" if idx_dist >= 0 else "reverse"
                if abs(idx_dist) > 1 or img_idx2 == 2:  # 闯红灯第三张图转向角度大
                    self._match_range = self._match_range_list[1]  # TODO:画相似度匹配范围(可视化)
                self._img_key = img_key2
                match_box, similar_dist = self.similar_run(target_veh, match_img,
                                                           veh_vox_list)
                if match_box:
                    judgeres.act_details["target_veh_plate"][img_key2] = "similar_match"
                    target_veh_box[img_key2] = match_box
                    save_sim_res[f"{target_img_idx[choose_idx] + 1}-{img_idx2 + 1}"] = similar_dist
        # 根据相似度匹配到的目标车辆添加目标车辆车牌坐标
        self.add_plate_cor(judgeres)
        judgeres.add_act_details(self._cls_name, {"similar_res": save_sim_res})

    def similar_run(self, target_veh: np.ndarray, match_img: np.ndarray, match_boxes: list) -> list:
        dist_list = []  # 特征差距，越小越相似
        box_list = []
        return_list = [None, None]  # [匹配成功的车框,相似度结果]
        for match_box in match_boxes:
            match_box_cx = (match_box[0] + match_box[2]) // 2
            # 相似度匹配范围筛选
            dist_y = match_box[3] - self._target_point[1] if self._match_mode == "reverse" else self._target_point[1] - \
                                                                                                match_box[3]
            dist_x = max(abs(match_box_cx - self._target_point[0]), 1)
            if self._match_mode == "feature" or self._match_code not in ["1625", "1208"]:
                pass
            elif dist_y / dist_x < self._match_range:  # 过滤匹配范围外的车辆
                continue
            elif self._img_key == "img3" and self._head_boxes and match_box in self._head_boxes:  # 过滤车头坐标
                continue
            elif self._del_veh_box and match_box in self._del_veh_box[self._img_key]:  # 过滤无关车牌车辆
                continue
            match_veh = match_img[match_box[1]:match_box[3], match_box[0]:match_box[2]]
            results = self._sim_model.run([target_veh, match_veh])
            res = results.get_results()[0]
            dist_list.append(res.score)
            box_list.append(match_box)
        if dist_list:
            match_idx = np.argmin(dist_list)
            min_dist = dist_list[match_idx]
            if min_dist <= self._sim_thresh:
                return_list[0] = box_list[match_idx]
                return_list[1] = round(min_dist, 3)
                if self._match_mode == "forward":
                    target_h = target_veh.shape[0]
                    match_h = abs(box_list[match_idx][3] - box_list[match_idx][1])
                    if match_h > target_h * 1.05:  # TODO:后续考虑加到配置参数
                        return_list = [None, None]
                        self._logger.debug("相似度匹配的车辆长度变化异常,已初始化相似度匹配结果！")
        return return_list

    def add_plate_cor(self, judgeres: JudgeResult) -> None:
        all_plate_cor = judgeres.get_act_detail("all_plate_cor")
        target_veh_box = judgeres.get_act_detail("target_veh_box")
        target_veh_plate_cor = judgeres.get_act_detail("target_veh_plate_cor")
        for img_key, box in target_veh_box.items():
            plate_cor = all_plate_cor[img_key].get(str(box), [])
            if img_key not in target_veh_plate_cor and plate_cor:
                target_veh_plate_cor[img_key] = plate_cor


# 目标车辆匹配结果判定
class NoVehRule(VioRule):
    def __init__(self, name, params, model_dict):
        super().__init__(name, params, model_dict)
        self._cls_name = self.__class__.__name__
        self._conf = OptConf
        self._plate_match_num = params.get("plate_match_num", 1)  # 要求车牌匹配成功的最小数量
        self._match_num = params.get("match_num", 0)  # 要求车辆匹配成功的最小数量（包含相似度匹配）
        self._match_img_idx = params.get("match_index", [])  # 要求车辆匹配成功的图片序号（包含相似度匹配）

    def _run(self, judgeres: JudgeResult) -> None:
        img_keys = judgeres.get_act_detail("img_keys")
        target_veh_plate: Dict = judgeres.get_act_detail("target_veh_plate", {})
        if self._plate_match_num == -1:
            self._plate_match_num = len(img_keys)
        plate_list = list(target_veh_plate.values())
        similar_match_num = plate_list.count("similar_match")
        plate_match_num = len(plate_list) - similar_match_num
        match_keys = set(target_veh_plate.keys())
        need_match_keys = {f"img{i + 1}" for i in self._match_img_idx}
        if not need_match_keys.issubset(match_keys):
            judgeres.set_judres_core(2, 0.9, "no_veh_003",
                                     f"作废，车辆匹配成功的图片不满足配置要求(期望：{sorted(need_match_keys)} 实际：{sorted(match_keys)})")
        elif len(plate_list) < self._match_num:
            judgeres.set_judres_core(2, 0.9, "no_veh_002",
                                     f"作废，车辆匹配成功的数量不足({len(plate_list)}<{self._match_num})")
        elif plate_match_num < self._plate_match_num:
            judgeres.set_judres_core(2, 0.9, "no_veh_001",
                                     f"作废，通过车牌匹配成功的数量不足({plate_match_num}<{self._plate_match_num})")

        judgeres.add_act_details(self._cls_name)


# 车辆明显位移判定
class VehMoveRule(VioRule):
    def __init__(self, name, params, model_dict):
        super().__init__(name, params, model_dict)
        self._cls_name = self.__class__.__name__
        self._conf = OptConf
        self._iou_thresh = params.get("iou_thresh", 0.95)  # IOU阈值
        self._yiou_thresh = params.get("yiou_thresh", 0.98)  # y轴方向重叠比例阈值

    def _run(self, judgeres: JudgeResult) -> None:  # TODO:明显位移判定异常处理部分
        veh_box_dict = judgeres.get_act_detail("target_veh_box")
        img_keys = sorted(veh_box_dict.keys())
        for index in range(len(img_keys) - 1):
            key1 = img_keys[index]
            key2 = img_keys[index + 1]
            box1 = veh_box_dict[key1]
            box2 = veh_box_dict[key2]
            iou = IOU.single_iou(box1, box2)
            yiou = self.get_yiou(box1, box2)
            judgeres.logger.debug(f"move_iou:{iou} move_yiou:{yiou}")
            if iou > self._iou_thresh:
                judgeres.set_judres_core(2, self._iou_thresh, "no_move_001",
                                         f"作废，通过IOU计算{key1}和{key2}中的目标车辆未产生明显位移({iou}>{self._iou_thresh})")
                break
            elif yiou > self._yiou_thresh:
                judgeres.set_judres_core(2, self._yiou_thresh, "no_move_002",
                                         f"作废，通过位移比例计算{key1}和{key2}中的目标车辆未产生明显位移({yiou}>{self._yiou_thresh})")
                break
        judgeres.add_act_details(self._cls_name)

    @staticmethod
    def get_yiou(box1: List, box2: List) -> float:
        try:
            top = min(box1[3], box2[3]) - max(box1[1], box2[1])
            bottom = max(box1[3], box2[3]) - min(box1[1], box2[1])
            y_rate = round(top / bottom, 2)
        except:
            y_rate = 0
        return y_rate


# 目标车与地标线判定规则
class VehMarklineRule(VioRule):
    def __init__(self, name, params, model_dict):
        super().__init__(name, params, model_dict)
        self._cls_name = self.__class__.__name__
        self._stop_line_rule = params.get("stop_line_rule", False)  # 机动车与停止线判定规则
        self._cover_line_rule = params.get("cover_line_rule", False)  # 横跨车道压线判定
        self._stop_line_judge_list = params.get("stop_line_judge_list", [1, 2])  # 进行停止线判定的图片序号

    def _run(self, judgeres: JudgeResult) -> None:
        # ObjectProcess.save_object(judgeres,r"C:\Users\<USER>\Desktop\AIJudge_Unit_Test\pkl_data\鲁GB2T71_veh_markline.pkl")
        veh_box_dict = judgeres.get_act_detail("target_veh_box", {})
        target_veh_label = judgeres.get_act_detail("target_veh_label", {})
        label_score = target_veh_label.get("img1", "")
        self._veh1_label = label_score.split("-")[0]
        self._veh_box1 = veh_box_dict.get("img1")
        self._veh_box2 = veh_box_dict.get("img2")
        self._h, self._w, _ = judgeres.get_act_detail("img_shape")
        self._cover_line = False  # 横跨车道判定初始值
        self._target_arrow = []
        if self.simple_rule():
            judgeres.set_judres_core(judge_status=2, result_code="stop_line_rule",
                                     result_desc="作废，停止线简易规则判定")
            judgeres.add_act_details(self._cls_name)
            return None
        merge_tfc_element = judgeres.get_act_detail("merge_tfc_element", {})
        lane_result = merge_tfc_element.get("lane", [])
        arrow_result = merge_tfc_element.get("arrow", [])
        stop_line_result = merge_tfc_element.get("stop_line", [])
        self.lane_judge(lane_result)
        self.arrow_judge(arrow_result)
        if self._cover_line and not self._filter_arrow_list:
            judgeres.set_judres_core(2, 0.85, "mark_line_cover", "废片，目标车辆横跨两个车道")
        elif not self._target_arrow and self._conf.Filter_No_Arrow:
            judgeres.set_judres_core(2, 0.8, "no_arrow_waste", "废片，目标车辆所在车道未检测到导向箭头")
        elif self._stop_line_rule:
            self.stop_line_judge(stop_line_result)
            self.stop_line_rule(judgeres)
        judgeres.add_act_details(self._cls_name,
                                 {"mark_line": self._cover_line_details, "target_arrow": self._target_arrow})

    def simple_rule(self) -> bool:
        try:
            if self._stop_line_rule:
                box1_cy = (self._veh_box1[1] + self._veh_box1[3]) / 2
                box2_y1 = min(self._veh_box2[1], self._veh_box2[3])
                j1 = box1_cy < self._conf.Stop_Line_Height[0] * self._h
                j2 = box2_y1 > self._conf.Stop_Line_Height[1] * self._h
                if j1 or j2:
                    return True
        except:
            pass
        return False

    def lane_judge(self, lane_res: List[ModelResult]) -> None:
        self._arrow_range = range(0, self._w + 1)
        self.lane_list = []
        if self._veh_box1 and lane_res:
            range_judge = self._veh1_label == "motorcycle_b"
            veh_lx = self._veh_box1[0]
            veh_rx = self._veh_box1[2]
            self._veh_box1_w = abs(self._veh_box1[2] - self._veh_box1[0])
            self._veh_box1_h = abs(self._veh_box1[3] - self._veh_box1[1])
            # 单双实线筛选
            self._last_cx = -self._veh_box1_w
            lx = veh_lx - self._veh_box1_w * 0.5
            rx = veh_rx + self._veh_box1_w * 0.5
            min_y = 0.5 * self._h
            for model_res in lane_res:
                lane_box = model_res.box
                lane_cx, lane_cy, lane_w = self._calculate_lane_center(lane_box)
                add = [lane_cx, lane_w, lane_box[1]]
                lane_box.extend(add)
                # [x1,y1,x2,y2,ps_cx,ps_w,ty]
                # 过滤中心点位置过高的框/车道以外的框
                if lane_cy < min_y or lane_cx > rx or lane_cx < lx:
                    continue
                dist = abs(lane_cx - self._last_cx) / 1.5 if range_judge else abs(lane_cx - self._last_cx)
                lane_iou = [IOU.single_iou(lane[:4], lane_box[:4]) for lane in self.lane_list] if self.lane_list else []
                # 修正同一条单双实线的最小高度
                self._update_lane_range(lane_box, lane_cx, dist, lane_iou, lx, rx)

    def _calculate_lane_center(self, lane_box: List[int]) -> tuple:
        """计算车道线的中心点和宽度"""
        lane_cx = (lane_box[0] + lane_box[2]) // 2
        lane_cy = (lane_box[1] + lane_box[3]) / 2
        lane_w = abs(lane_box[0] - lane_box[2])
        return lane_cx, lane_cy, lane_w

    def _update_lane_range(self, lane_box: List[float], lane_cx: int, dist: float, lane_iou: List[float], lx: float,
                           rx: float) -> None:
        for idx, lane in enumerate(self.lane_list):
            lane[-1] = min((lane[-1], lane_box[1]))
        # 修正arrow_range范围
        if lx < lane_cx < rx and dist > abs(self._veh_box1[2] - self._veh_box1[0]):
            self._last_cx = lane_cx
            if len(self.lane_list) < 2:
                self.lane_list.append(lane_box)
                if len(self.lane_list) == 2:
                    self.lane_list = sorted(self.lane_list, reverse=False)
                    self._arrow_range = range(self.lane_list[0][4], self.lane_list[1][4])
                    lane_iou = [IOU.single_iou(lane[:4], lane_box[:4]) for lane in
                                self.lane_list] if self.lane_list else []
            # 修正实线最高点
            if len(self.lane_list) == 2:
                # 识别的实线比之前的判定实线高
                if self.lane_list[0][4] > lane_cx and lane_iou[0] >= 0.7 and self.lane_list[0][1] > lane_box[
                    1] and lane_cx < \
                        self._arrow_range[-1]:
                    self.lane_list[0] = lane_box
                    self._arrow_range = range(lane_cx, self._arrow_range[-1])
                elif self.lane_list[1][4] < lane_cx and lane_iou[1] >= 0.7 and self.lane_list[1][1] > lane_box[
                    1] and lane_cx > \
                        self._arrow_range[0]:
                    self.lane_list[1] = lane_box
                    self._arrow_range = range(self._arrow_range[0], lane_cx)

    def arrow_judge(self, arrow_res: List[ModelResult]) -> None:
        tmp_stop_by = self._stop_by = self._h
        self._last_box = None  # 箭头坐标框
        self._last_label = None  # 箭头类别
        self._last_dist = 1e-5  # 箭头与车辆中心点距离
        self._filter_arrow_list = []  # 筛选当前车道范围内的箭头
        self._cover_line_details = {"boxes": [], "labels": [], "dist": []}  # 存放符合压线条件的信息
        if self._veh_box1 and arrow_res:
            # 箭头线筛选
            lx = self._veh_box1[0] - self._veh_box1_w * 0.25
            rx = self._veh_box1[2] + self._veh_box1_w * 0.25
            if len(self.lane_list) == 2:
                self._judge_lx = max((lx, self._arrow_range[0]))
                self._judge_rx = min((rx, self._arrow_range[-1] + 1))
            else:
                self._judge_lx = lx
                self._judge_rx = rx
            min_y = self._h / 2
            for model_res in arrow_res:
                arrow_box = model_res.box
                arrow_label = model_res.subclass
                arrow_cy = (arrow_box[1] + arrow_box[3]) / 2
                if arrow_cy < min_y:  # y轴限制
                    continue
                self.arrow_process(arrow_box, arrow_label)
                # 所在车道无导向箭头
                tmp_stop_by = min((arrow_box[1], self._stop_by))
            if self._stop_by == self._h:
                self._stop_by = tmp_stop_by

    def arrow_process(self, arrow_box: List, arrow_label: str) -> None:
        arrow_cx = (arrow_box[0] + arrow_box[2]) / 2
        arrow_cy = (arrow_box[1] + arrow_box[3]) / 2
        if self._judge_lx < arrow_cx < self._judge_rx:
            self._target_arrow.append(arrow_label)
            veh_cx = (self._veh_box1[0] + self._veh_box1[2]) / 2
            x_dist = abs(veh_cx - arrow_cx)
            thresh = 0.15
            if self._veh_box1[0] + thresh * self._veh_box1_w < arrow_cx < self._veh_box1[
                2] - thresh * self._veh_box1_w and self._veh_box1[1] + thresh * self._veh_box1_h < arrow_cy < \
                    self._veh_box1[3] - thresh * self._veh_box1_h:
                self._filter_arrow_list.append(arrow_label)
            if self._last_box is None:
                self._last_box = arrow_box
                self._last_label = arrow_label
                self._last_dist = x_dist
            # 严重压线废片判定
            elif (self._cover_line_rule and self._last_label != arrow_label and
                  IOU.single_iou(self._last_box, arrow_box) == 0 and abs(
                        (self._last_box[0] + self._last_box[2]) / 2 - arrow_cx) > self._veh_box1_w * 0.9 and max(
                        self._last_dist, x_dist) / min(
                        self._last_dist, x_dist) < self._conf.Cover_Line_Rate):
                self._cover_line = True  # 控制是否报压线废片
                self.get_cover_line_details(arrow_box, arrow_label, x_dist)
            self._last_box = arrow_box
            # 添加箭头线的stop_by限制停止线区域
            if arrow_box[0] in self._arrow_range and arrow_box[
                2] in self._arrow_range:
                self._stop_by = min((arrow_box[1], self._stop_by))

    def get_cover_line_details(self, arrow_box: List, arrow_lable: str, x_dist: float) -> None:
        self._cover_line_details["boxes"].append((self._last_box, arrow_box))
        self._cover_line_details["labels"].append((self._last_label, arrow_lable))
        self._cover_line_details["dist"].append((self._last_dist, x_dist))

    def stop_line_judge(self, stop_line_res: List[ModelResult]) -> None:
        # 存放确信的停止线
        self._stop_line_s1 = []
        self._stop_line_s2 = []
        self._stop_line_list = []
        if self._veh_box1 and stop_line_res:
            # 停止线筛选
            min_y = self._h / 2
            veh_cx1 = (self._veh_box1[0] + self._veh_box1[2]) / 2
            for model_res in stop_line_res:
                stop_line_box = model_res.box
                stop_line_cx = (stop_line_box[0] + stop_line_box[2]) / 2
                stop_line_cy = (stop_line_box[1] + stop_line_box[3]) / 2
                stop_line_w = abs(stop_line_box[0] - stop_line_box[2])
                if stop_line_cy < min_y or stop_line_cy > self._stop_by or stop_line_cx < self._w * 0.3:
                    continue
                if stop_line_box[0] < veh_cx1 < stop_line_box[2] or (
                        stop_line_box[0] > self._judge_lx and stop_line_box[2] < self._judge_rx):
                    self._stop_line_s1.append(stop_line_box)
                    continue
                # 停止线足够宽则认为识别准确
                if stop_line_w > self._w * 2:
                    self._stop_line_s2.append(stop_line_box)
                    continue
                # 不确定停止线保存在列表中
                if stop_line_w > self._w:
                    self._stop_line_list.append(stop_line_box)

    def stop_line_rule(self, judgeres: JudgeResult):
        lane_ty = []
        judge_res = None
        veh_rate = self._conf.Car1_Stop_Line_Rate
        judge_y1 = self._veh_box1[1] + veh_rate * self._veh_box1_h if self._veh_box1 else self._veh_box1_h
        judge_y2 = self._veh_box2[3] if self._veh_box2 else 0
        if len(self.lane_list) == 2:  ### 推测停止线位置 [x1,y1,x2,y2,ps_cx,ps_w,ty]
            h1 = self.lane_list[0][3] - self.lane_list[0][1]
            h2 = self.lane_list[1][3] - self.lane_list[1][1]
            if abs(self.lane_list[0][-1] - self.lane_list[1][-1]) < h1:
                lane_ty.append(self.lane_list[0][-1])
            if abs(self.lane_list[0][-1] - self.lane_list[1][-1]) < h2:
                lane_ty.append(self.lane_list[1][-1])
        elif len(self.lane_list) == 1 and self._stop_by != self._h:
            dist = abs(self.lane_list[0][-1] - self._stop_by)
            if dist < 0.2 * self._h:
                lane_ty.append(np.average((self.lane_list[0][-1], self._stop_by)))
        line_judge1 = []
        line_judge2 = []
        self._stop_line_process(line_judge1, line_judge2, lane_ty)
        if line_judge1:
            line_judge1 = np.average(line_judge1)
            if judge_y1 < line_judge1 and 1 in self._stop_line_judge_list:
                judge_res = "stop_line_veh1"
                judgeres.set_judres_core(judge_status=2, result_code="stop_line_veh1",
                                         result_desc=f"作废，一图目标车辆超过了停止线({veh_rate})")
        if line_judge2:
            line_judge2 = np.average(line_judge2) + 0.1 * np.average(
                (self._veh_box2[1], self._veh_box2[3])) if self._veh_box2 else np.average(line_judge2)
            if judge_y2 > line_judge2 and 2 in self._stop_line_judge_list:
                judge_res = "stop_line_veh2"
                judgeres.set_judres_core(judge_status=2, result_code="stop_line_veh2",
                                         result_desc="作废，二图目标车辆未越过停止线")
        judgeres.add_act_details(self._cls_name,
                                 {"stop_line_judge": judge_res, "judge_y1": int(judge_y1),
                                  "line_judge1": int(line_judge1),
                                  "judge_y2": int(judge_y2), "line_judge2": int(line_judge2)})

    def _stop_line_process(self, line_judge1: List, line_judge2: List, lane_ty: List) -> None:
        if self._stop_line_s1:
            for i in self._stop_line_s1:
                line_cy = (i[1] + i[3]) / 2
                line_h = abs(i[1] - i[3])
                line_judge1.append(line_cy - line_h)
                line_judge2.append(line_cy + line_h)
        elif self._stop_line_s2:
            for i in self._stop_line_s2:
                line_cy = (i[1] + i[3]) / 2
                line_h = abs(i[1] - i[3])
                line_judge1.append(line_cy - line_h)
                line_judge2.append(line_cy + line_h)
        elif self._stop_line_list:
            for i in self._stop_line_list:
                line_cy = (i[1] + i[3]) / 2
                line_h = abs(i[1] - i[3])
                if lane_ty:
                    line_cy = np.average((np.min(lane_ty), line_cy))
                line_judge1.append(line_cy - line_h)
                line_judge2.append(line_cy + line_h)
        elif lane_ty:
            line_cy = np.min(lane_ty)
            line_judge1.append(line_cy - 20)
            line_judge2.append(line_cy + 20)


# 低速车过滤规则
class LowSpeedVehRule(VioRule):
    def __init__(self, name, params, model_dict):
        super().__init__(name, params, model_dict)
        self._cls_name = self.__class__.__name__

        self._filter_labels = params.get("filter_labels", ["motorcycle_b", "motorcycle_h"])  # 通过车辆特征过滤的车辆类别
        self._filter_thresh = params.get("filter_thresh", 0.8)  # 通过车辆特征过滤阈值
        self._plate_num_judge = params.get("plate_num_judge", False)  # 是否进行车牌长度判定
        self._first_plate_thresh: List[float] = params.get("first_plate_thresh", [1., 1.])  # 车牌首字符规则判定[首字符异常，首字符合规]

    # 找目标车辆距离最近的图片
    def find_nearest_pic(self, veh_box_dict: Dict[str, List]) -> str:
        key = ""
        if veh_box_dict:
            sorted_dict = dict(sorted(veh_box_dict.items(), key=lambda item: item[1][3], reverse=True))
            key = next(iter(sorted_dict))
        return key

    def _run(self, judgeres: JudgeResult) -> None:
        veh_box_dict = judgeres.get_act_detail("target_veh_box", {})
        target_veh_label = judgeres.get_act_detail("target_veh_label", {})
        target_veh_plate = judgeres.get_act_detail("target_veh_plate", {})
        target_veh_plate_cor = judgeres.get_act_detail("target_veh_plate_cor", {})
        self._src_plate = judgeres.request.plate_no
        img_key = self.find_nearest_pic(veh_box_dict)
        plate = target_veh_plate.get(img_key)
        plate_score = target_veh_plate_cor.get(img_key)
        label_score = target_veh_label.get(img_key, "-")
        veh_label, veh_score = label_score.split("-")
        judge_code = self.logic_judge(veh_label, veh_score, plate, plate_score)
        if "waste" in judge_code:
            judge_desc = Judge_Code_Desc_Dict.get(judge_code, "")
            judgeres.set_judres_core(2, 0.8, judge_code, judge_desc)

    def logic_judge(self, veh_label: str, veh_score: str, plate: str, plate_score: List[float]) -> str:
        judge_code = ""
        first_plate_list = self._conf.First_Plate_No
        if veh_label and veh_label in self._filter_labels and float(veh_score) > self._filter_thresh:
            # 车辆特征判定
            judge_code = "veh_feature_waste"
        elif plate:
            # 前端相机识别车牌有误报废片
            if self._plate_num_judge and plate[0] in first_plate_list and plate[
                1] not in self._conf.First_Plate_No and len(plate) - len(self._src_plate) >= 1 and 7 <= len(plate) <= 8:
                judge_code = "plate_num_waste"
            elif plate[0] not in first_plate_list and plate_score[0] > self._first_plate_thresh[0] and len(
                    plate) >= 7:  # 0.8
                judge_code = "first_plate_waste"
            elif plate[0] in first_plate_list and plate[0] != self._src_plate[
                0] and plate_score[0] > self._first_plate_thresh[1]:
                judge_code = "first_plate_waste2"
        return judge_code


# 闯红灯违法判定
class DrvdirLightRule(VioRule):
    def __init__(self, name, params, model_dict):
        super().__init__(name, params, model_dict)
        self._conf = OptConf
        self._cls_name = self.__class__.__name__

    def _run(self, judgeres: JudgeResult) -> None:
        drv_direct = judgeres.get_act_detail("drv_direction")
        light_status = judgeres.get_act_detail("status", [])
        if drv_direct == 'right' and 'red_r' in light_status:
            vio_judge = 'vio_right'
        elif drv_direct == 'left':
            if 'red_l' in light_status:
                vio_judge = 'vio_left'
            elif 'green_l' not in light_status and 'red_s' in light_status:
                vio_judge = 'vio_left2'
            else:
                vio_judge = 'no_vio'
        elif drv_direct == 'straight' and 'red_s' in light_status:
            vio_judge = 'vio_straight'
        elif drv_direct is None:
            vio_judge = 'vio'
        else:
            vio_judge = 'no_vio'
        judge_desc = self.get_judge_desc(vio_judge)
        if vio_judge.startswith("vio"):
            judgeres.set_judres_core(1, 0.9, vio_judge, judge_desc)
        else:
            judgeres.set_judres_core(2, 0.85, vio_judge, judge_desc)
        judgeres.add_act_details(self._cls_name, {"vio_judge": vio_judge, "judge_desc": judge_desc})

    @staticmethod
    def get_judge_desc(vio_judge: str) -> str:
        judge_desc_dict = {"vio_left": "目标车辆左转且左转交通信号灯为红灯",
                           "vio_left2": "目标车辆左转且直行信号灯为红灯",
                           "vio_straight": "目标车辆直行且直行交通信号灯为红灯",
                           "vio_right": "目标车辆右转且右转交通信号灯为红灯",
                           "no_vio": "目标车行驶方向与之对应的交通信号灯一三图不全为红灯"}
        return judge_desc_dict.get(vio_judge, vio_judge)


# 不按导向行驶规则判定
class DrvdirArrowRule(VioRule):
    def __init__(self, name, params, model_dict):
        super().__init__(name, params, model_dict)
        self._conf = OptConf
        self._cls_name = self.__class__.__name__

    def _run(self, judgeres: JudgeResult) -> None:
        arrow_list = judgeres.get_act_detail("target_arrow", [])
        drv_direction = judgeres.get_act_detail("drv_direction")
        arrow_direction = [arrow[-1] for arrow in arrow_list]
        if drv_direction is None:
            judgeres.set_judres_core(1, 0.85, "no_drvdir_vio",
                                     f"缺少行驶方向信息，判定为正片")
        elif drv_direction[0] in arrow_direction:
            judgeres.set_judres_core(2, 0.85, f"{drv_direction}_waste",
                                     f"作废，目标车辆所在车道存在{drv_direction}方向的导向箭头")
        else:
            judgeres.set_judres_core(1, 0.85, f"{drv_direction}_vio",
                                     f"目标车辆所在车道不存在{drv_direction}方向的导向箭头")


# 压线判罚规则
class CoverLineRule(VioRule):
    def __init__(self, name, params, model_dict):
        super().__init__(name, params, model_dict)
        self._cls_name = self.__class__.__name__
        self._bias_rate = params.get('bias_rate', 1.5)  # 轮胎位置水平偏移比例（基数：车辆和车牌中心点距离）
        self._dist_thresh = params.get('dist_thresh', 5)  # 轮胎与最近禁压线距离小于此值违法
        self._lower_bias_rate = params.get('lower_bias_rate', 0.08)  # 禁压线在轮胎下的偏移比例（基数：车宽）
        self._upper_bias_rate = params.get('upper_bias_rate', 0.2)  # 禁压线在轮胎上的偏移比例（基数：车宽）
        self._min_pixel = params.get('min_pixel', 50)  # 车底目标压线的最小像素
        self._show = True  # 可视化

    def _run(self, judgeres: JudgeResult) -> None:
        # ObjectProcess.save_object(judgeres.act_details,fr"C:\Users\<USER>\Desktop\AIJudge_Unit_Test\pkl_data\{judgeres.request.plate_no}_cover_line_details.pkl")
        self._h, self._w, _ = judgeres.get_act_detail("img_shape")
        img_keys = judgeres.get_act_detail("img_keys")
        self.logger = judgeres.logger
        target_veh_box = judgeres.get_act_detail("target_veh_box")
        target_veh_plate_cor = judgeres.get_act_detail("target_veh_plate_cor")
        self._show_img = judgeres.get_act_detail("bkg_img", np.zeros((self._h, self._w, 3), dtype=np.uint8))
        self._save_name = judgeres.request.plate_no + "_" + judgeres.request.uuid.split("-")[0]
        target_point = []
        for key in img_keys:
            box = target_veh_box[key]
            if box:
                plate_cor = target_veh_plate_cor[key]
                angel, bias_dist = self.get_veh_plate_angle(box, plate_cor)
                self.logger.debug(f"bias_dist:{bias_dist} angel:{angel}")
                target_point += self.get_tire_point(angel, box, plate_cor, bias_dist)
        tfc_masks = self.get_tfc_masks(judgeres)
        min_dist = self.find_near_dist_and_direct(tfc_masks, target_point)
        self.logger.debug(f"min_dist: {min_dist} dist_thresh:{self._dist_thresh}")
        if 0 <= min_dist <= self._dist_thresh:
            judgeres.set_judres_core(1, 0.85, "cover_line", "压线违法")
        elif min_dist == -1:
            judgeres.set_judres_core(2, 0.85, "no_vio_001",
                                     f"废片，未检测到禁压地标线")
        else:
            judgeres.set_judres_core(2, 0.85, "no_vio",
                                     f"废片，车辆与地标线的距离({min_dist})大于阈值({self._dist_thresh})")
        judgeres.add_act_details(self._cls_name)

    def get_tfc_masks(self, judgeres: JudgeResult) -> List[np.ndarray]:
        img_keys = judgeres.get_act_detail("img_keys")
        tfc_elements: Dict = judgeres.get_act_detail("merge_tfc_element")
        if tfc_elements is None:
            return [mask for key in img_keys for mask in judgeres.get_tfc_elements(key, "no_cover", "mask")]
        else:
            return [model_res.mask for model_res_list in tfc_elements.values() for model_res in model_res_list]

    def get_veh_plate_angle(self, veh_box: List, veh_plate_cor: List) -> Tuple[int, int]:
        if veh_plate_cor:
            plate_cx = (veh_plate_cor[0] + veh_plate_cor[2]) // 2
            car_cx = (veh_box[0] + veh_box[2]) // 2
            # 判断倾斜程度
            dist_w = car_cx - plate_cx + 1e-5
            dist_h = abs(veh_box[1] - veh_plate_cor[1])
            tanA = dist_h / dist_w
            angel = math.degrees(math.atan(tanA))
            # 修正轮胎位置的比例
            bias_dist = dist_w * self._bias_rate
            if self._show:
                cv2.line(self._show_img, (car_cx, veh_box[1]), (plate_cx, veh_plate_cor[1]), (255, 255, 0), 2)

        else:
            angel = bias_dist = 0
        return int(angel), int(bias_dist)

    def get_tire_point(self, angel: int, veh_box: List, plate_cor: List, bias_dist: int) -> List[Tuple]:
        car_w = abs(veh_box[0] - veh_box[2])
        car_h = abs(veh_box[1] - veh_box[3])
        plate_w = plate_cor[2] - plate_cor[0]
        angel_rate = 1 - abs(angel) / 90
        if 55 < angel < 78:  # 向右倾斜 添加车牌参照物修正,限制大倾斜角度
            target_point = [(min(int(veh_box[0] + angel_rate * car_w),
                                 max(int(plate_cor[0] - plate_w * angel_rate), veh_box[0])), veh_box[3], car_h),
                            (int(veh_box[2] - bias_dist), veh_box[3], car_h)]
        elif -78 < angel < -55:  # 向左倾斜
            target_point = [(int(veh_box[0] - bias_dist), veh_box[3], car_h),
                            (max(int(veh_box[2] - angel_rate * car_w),
                                 min(int(plate_cor[2] + plate_w * angel_rate), veh_box[2])), veh_box[3], car_h)]
        else:
            if 0 > angel >= -55 or 0 < angel <= 55:
                rate = 0
            else:
                rate = 0.01 + angel_rate if angel > 0 else 0.01
            target_point = [(int(veh_box[0] + rate * car_w), veh_box[3], car_h),
                            (int(veh_box[2] - rate * car_w), veh_box[3], car_h)]
        return target_point

    def find_near_dist_and_direct(self, mask_list: List[np.ndarray], point_list: List) -> int:
        masks_point = self.get_masks_point(mask_list)
        if len(masks_point) == 0:
            return -1
        dist_list = []
        judge = False  # 判定禁行线是否在车下
        for idx, point in enumerate(point_list):
            judge_num = self.judge_deep_cover(idx, point, point_list, masks_point)
            if judge_num >= self._min_pixel:
                judge = True
                break
            else:
                # 计算所有mask点与给定点之间的距离
                math_point = (point[0], point[1])
                distances = np.sqrt(np.sum((masks_point - math_point) ** 2, axis=1))
                tmp_min_dist = np.min(distances)
                dist_list.append(tmp_min_dist)
                if self._show:
                    # 可视化
                    color = (0, 0, 255)
                    min_mask_point = masks_point[np.argmin(distances)]
                    cv2.line(self._show_img, min_mask_point, math_point, (255, 255, 255), 2)
                    cv2.circle(self._show_img, min_mask_point, 5, color, -1)
                    cv2.circle(self._show_img, math_point, 5, (255, 0, 0), -1)
                    cv2.putText(self._show_img, f"{tmp_min_dist:.1f}", math_point, cv2.FONT_HERSHEY_SIMPLEX, 1.5, color,
                                2)
        if self._show:
            cv2.imencode(".jpg", self._show_img)[1].tofile(f"../logs/{self._save_name}.jpg")
        min_res = -1
        if judge:
            min_res = 0
        elif dist_list:
            min_res = np.min(dist_list)
        return int(min_res)

    def get_masks_point(self, mask_list) -> np.ndarray:
        masks_point_list = []
        for mask in mask_list:
            # 找到掩码中值为1的像素位置
            tmp_point = np.argwhere(mask == 1)  # TODO:高密部分数据耗时较长
            if len(tmp_point):
                if self._show:
                    tmp_y, tmp_x = np.where(mask == 1)
                    self._show_img[tmp_y, tmp_x, 0] = 255
                    self._show_img[tmp_y, tmp_x, 1] = 255
                    self._show_img[tmp_y, tmp_x, 2] = 0
                tmp_point = tmp_point[:, ::-1]  # 横纵坐标位置互换
                masks_point_list.append(tmp_point)
        masks_point = np.array([])
        try:
            if masks_point_list:
                masks_point = np.concatenate(masks_point_list, 0)
        except:
            log_error(self.logger, "数组拼接异常退出！")
        return masks_point

    def judge_deep_cover(self, idx, point, point_list, masks_point) -> int:
        # 判定线是否在车下
        if idx % 2 == 0:
            left_x = point[0]
            right_x = point_list[idx + 1][0]
        else:
            left_x = point_list[idx - 1][0]
            right_x = point[0]
        # 判定严重压线
        # 水平方向判断
        horizontal_cover = (left_x <= masks_point[:, 0]) & (masks_point[:, 0] <= right_x)
        # 高度差计算
        height_diff = point[1] - masks_point[:, 1]
        upper_height_diff = int(self._upper_bias_rate * point[2])
        lower_height_diff = int(self._lower_bias_rate * point[2])
        # 高度方向判断
        lower_bound = (height_diff <= 0) & (height_diff > -lower_height_diff)  # 禁压线在轮胎下
        upper_bound = (height_diff >= 0) & (height_diff < upper_height_diff)  # 禁压线在轮胎上
        # 组合条件
        cover_line_judge = horizontal_cover & (lower_bound | upper_bound)
        if self._show:
            sp_list = masks_point[cover_line_judge]
            for sp in sp_list:
                cv2.circle(self._show_img, tuple(sp), 5, (0, 255, 0), -1)
            cv2.rectangle(self._show_img, (left_x, (point[1] - upper_height_diff)),
                          (right_x, (point[1] + lower_height_diff)), (0, 255, 0), 2)
            cv2.line(self._show_img, (left_x, point[1]), (right_x, point[1]), (0, 0, 255), 2)

        judge_num = np.sum(cover_line_judge)
        self.logger.debug(
            f"idx:{idx} 轮胎判定点:{point} 上方高度差：{upper_height_diff} 下方高度差：{lower_height_diff}")
        if judge_num > 0:
            self.logger.debug(f"严重压线的像素点总和：{judge_num} 违法最小像素点数量：{self._min_pixel}")
        return judge_num


# 逆行判罚规则
class WrongDrvDirRule(VioRule):
    def __init__(self, name, params, model_dict):
        super().__init__(name, params, model_dict)
        self._cls_name = self.__class__.__name__
        self._veh_avg_width = 0  # 目标车辆平均车宽
        self._show = True
        self._dist_rate = params.get("dist_rate", 0.45)  # 距离比例阈值

    def _run(self, judgeres: JudgeResult) -> None:
        # save pkl data
        # ObjectProcess.save_object(judgeres,fr"C:\Users\<USER>\Desktop\AIJudge_Unit_Test\pkl_data\{judgeres.request.plate_no}_wrong_dir_judgeres.pkl")
        merge_tfc_element = judgeres.get_act_detail("merge_tfc_element")
        target_veh_box = judgeres.get_act_detail("target_veh_box")
        self._logger = judgeres.logger
        self._drive_up = self.is_drive_up(target_veh_box)  # 判定车辆是否向上开
        if self._drive_up is None:
            judgeres.set_judres_core(2, 1, "drive_direction_error", "作废，检测到的目标车辆数量小于2或车辆处于静止")
        else:
            target_veh_masks = self.get_veh_mask_dict(target_veh_box, judgeres.tfc_elements)
            judge_vio, msg = self.logic_judge(target_veh_box, target_veh_masks, merge_tfc_element)
            if judge_vio:
                judgeres.set_judres_core(1, 1, "vio", f"逆行违法{msg}")
            else:
                judgeres.set_judres_core(2, 0.8, "no_vio", f"作废，无逆行违法{msg}")
        judgeres.add_act_details(self._cls_name)

    def get_veh_mask_dict(self, veh_box_dict: Dict, tfc_elements: Dict[str, ModelResults]) -> List[np.ndarray]:
        veh_masks = []
        veh_width_list = []
        for img_key, veh_box in veh_box_dict.items():
            box_list = tfc_elements.get(img_key, ModelResults()).get_results(superclass="vehicle2", attr="box")
            mask_list = tfc_elements.get(img_key, ModelResults()).get_results(superclass="vehicle2", attr="mask")
            if veh_box in box_list:
                index = box_list.index(veh_box)
                veh_mask = mask_list[index]
                veh_masks.append(veh_mask)
            veh_width_list.append(veh_box[2] - veh_box[0])
        # 计算车辆平均宽度
        if veh_width_list:
            self._veh_avg_width = int(np.average(veh_width_list))
        return veh_masks

    def is_drive_up(self, veh_box_dict: Dict) -> Optional[bool]:
        drive_up = True
        sorted_dict = {k: veh_box_dict[k] for k in sorted(veh_box_dict)}
        values = list(sorted_dict.values())
        if len(values) < 2:
            drive_up = None
            self._logger.debug(f"目标车辆数量({len(values)})小于2")
        else:
            last_y = values[-1][3]
            first_y = values[0][3]
            if last_y - first_y == 0:
                drive_up = None
                self._logger.debug(f"目标车辆未产生位移")
            elif last_y - first_y > 0:
                drive_up = False
        return drive_up

    def logic_judge(self, veh_box_dict: Dict, target_veh_masks: List, merge_tfc_element: Dict) -> Tuple[bool, str]:
        tfc_element_dict: Dict = self.get_tfc_element(merge_tfc_element)  # 按类别分组整理
        if self._has_lane2:
            lane2_masks = tfc_element_dict["lane"]
            judge_result = self.judge_position(target_veh_masks, lane2_masks)  # 返回是否为正片
            if judge_result is not None:
                msg = "(双实线判定)" if self._has_lane2 else "(单实线判定)"
                return judge_result, msg
        arrow_array = tfc_element_dict["arrow"]
        arrow_t_array = tfc_element_dict["arrow_t"]
        # 初始化赋值
        arrow_cx = np.array([0])
        arrow_t_cx = np.array([-self._dist_rate * self._veh_avg_width])
        if len(arrow_array):
            arrow_cx = (arrow_array[:, 0] + arrow_array[:, 2]) // 2
        if len(arrow_t_array):
            arrow_t_cx = (arrow_t_array[:, 0] + arrow_t_array[:, 2]) // 2
        dist_thresh = self._dist_rate * self._veh_avg_width
        for img_key, veh_box in veh_box_dict.items():
            veh_cx = (veh_box[0] + veh_box[2]) // 2
            if self._drive_up:
                cx_dist = veh_cx - arrow_t_cx
                min_arrow_cx = min(arrow_cx)
                self._logger.debug(f"向上 dist_thresh:{dist_thresh} veh_cx:{veh_cx} min_arrow_cx:{min_arrow_cx}")
                if len(np.where(cx_dist <= dist_thresh)[0]):
                    self._logger.debug(f"向上，反向导向箭头判定违法！")
                    return True, "(向上，反向导向箭头判定)"
                elif min_arrow_cx - veh_cx >= dist_thresh:
                    self._logger.debug(f"向上，正向导向箭头判定违法！")
                    return True, "(向上，正向导向箭头判定)"
            else:
                cx_dist = arrow_cx - veh_cx
                max_arrow_t_cx = max(arrow_t_cx)
                self._logger.debug(f"向下 dist_thresh:{dist_thresh} veh_cx:{veh_cx} max_arrow_t_cx:{max_arrow_t_cx}")
                if len(np.where(cx_dist <= dist_thresh)[0]):
                    self._logger.debug(f"向下，正向导向箭头判定违法！")
                    return True, "(向下，正向导向箭头判定)"
                elif veh_cx - max_arrow_t_cx >= dist_thresh:
                    self._logger.debug(f"向下，反向导向箭头判定违法！")
                    return True, "(向下，反向导向箭头判定)"
        return False, ""

    def get_tfc_element(self, merge_tfc_element: Dict[str, List[ModelResult]]) -> Dict:
        cls_tfc_dict = {}
        arrows = ["arrow_s", "arrow_l", "arrow_r"]
        arrows_t = ["arrow_s_t", "arrow_l_t", "arrow_r_t"]
        cls_tfc_dict["arrow"] = np.array([res.box for key in arrows for res in merge_tfc_element.get(key, [])])
        cls_tfc_dict["arrow_t"] = np.array([res.box for key in arrows_t for res in merge_tfc_element.get(key, [])])
        tmp_cls = "lane2" if merge_tfc_element.get("lane2") else "lane1"
        self._has_lane2 = "lane2" in merge_tfc_element
        cls_tfc_dict["lane"] = [res.mask for res in merge_tfc_element.get(tmp_cls, [])]

        return cls_tfc_dict

    def judge_position(self, veh_masks: List[np.ndarray], lane_masks: List[np.ndarray]) -> Optional[bool]:
        veh_center_list = self.calculate_center(veh_masks)
        lane_center_list = self.calculate_center(lane_masks)  # 一定非空
        if not veh_center_list:
            self._logger.debug("无有效的车辆掩码信息！")
            return None
        # 避免模型误检双实线造成逻辑错误
        self._logger.debug(f"目标车辆平均车宽：{self._veh_avg_width}")
        lane_dist = max(lane_center_list) - min(lane_center_list)
        if lane_dist > self._veh_avg_width:
            self._logger.debug(f"双黄线：{self._has_lane2} 检测到的实线可能存在误检！")
            return None
        lane_center = int(np.average(lane_center_list))
        for veh_center in veh_center_list:
            if (self._drive_up and veh_center <= lane_center or
                    (not self._drive_up and veh_center >= lane_center)):
                self._logger.info(f"车辆违法：veh_center={veh_center}, lane_center={lane_center}")
                return True
        return False

    def calculate_center(self, masks: List[np.ndarray]) -> List:
        center_list = []
        # 计算掩码x方向的重心
        for mask in masks:
            rows, cols = np.where(mask)
            if len(cols):
                center_x = int(np.mean(cols))
                center_list.append(center_x)
        return center_list


def create_rule_module(module_name, name, params, model_dict):
    return eval(module_name)(name, params, model_dict)
