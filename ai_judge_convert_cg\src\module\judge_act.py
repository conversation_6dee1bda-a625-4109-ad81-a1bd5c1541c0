import time
from abc import ABCMeta, abstractmethod
from typing import Dict, List, Any
import cv2
from conf.config import OptConf, create_conf
from src.module.image_model import ModelResults, ModelResult
from src.module.judge_rule import create_rule_module
from src.utils.time_util import TimeUtil
from src.utils.tools import load_image_from_url, AutoSplit, IOU, split_img, crop_driver_area
from src.service.data_class import JudgeResult
import numpy as np
from copy import deepcopy


# 动作父类
class JudgeAct(metaclass=ABCMeta):
    def __init__(self, name:str, conf:Any, params:Dict, model_dict:Dict):
        self._name: str = name
        self._conf = conf
        self._params: dict = params
        self._rule_dict = conf.rule_dict
        self._model_dict = model_dict
        self._rulers: list = self.init_ruler()

    def init_ruler(self):
        rulers = []
        if 'rule' in self._params:
            for ruleinfo in self._params['rule']:
                name = ruleinfo['rule']
                param = ruleinfo['param']
                module_name = self._rule_dict[name]
                rulers.append(create_rule_module(module_name, name, param, self._model_dict))
        return rulers

    def run(self, judgeres:JudgeResult):
        judgeres.logger.info(f"{self._name}动作正在运行...")
        if judgeres.valid:
            self._run(judgeres)

    @abstractmethod
    def _run(self, judgeres:JudgeResult):
        pass


# 数据请求校验动作
class RequestJudAct(JudgeAct):  # 舍弃：上线测试路口相机配置、按比例分析
    def __init__(self, name, conf, params, model_dict):
        super().__init__(name, conf, params, model_dict)
        self._request_conf = conf
        self._conf = OptConf
        self._cls_name = self.__class__.__name__

    def _run(self, judgeres: JudgeResult) -> None:
        judgeres.add_act_details(self._cls_name, {"UUID": judgeres.request.uuid})
        functions = [
            self.check_viocode,
            self.check_plate_no,
            self.filter_special_vehicle,
            self.judge_overtime,
            self.judge_camera,
            self.judge_cross,
            self.check_truck,
            self.check_datatype,
        ]
        for func in functions:
            if judgeres.valid:
                func(judgeres)

    def check_viocode(self, judgeres: JudgeResult) -> None:
        request = judgeres.request
        if request.match_viocode == "":
            judgeres.set_judres_core(result_code="no_judge_001", result_desc="不支持的违法类型")
        judgeres.add_act_details(act=f"{self._cls_name}.check_viocode", detail={"vio_code": request.match_viocode})

    def check_plate_no(self, judgeres: JudgeResult) -> None:
        request = judgeres.request
        plate = request.plate_no
        if not plate or plate.startswith("无") or plate.startswith("未"):
            judgeres.set_judres_core(judge_status=2, result_code="empty plate no", result_desc="车牌号码为空")
        elif self._request_conf.First_Plate_Judge and plate[0] not in self._conf.First_Plate_No:
            judgeres.set_judres_core(judge_status=2, result_code="error first plate no",
                                     result_desc="车牌号码首字符不满足行政区划要求")
        judgeres.add_act_details(act=f"{self._cls_name}.check_plate_no")

    def filter_special_vehicle(self, judgeres: JudgeResult):
        request = judgeres.request
        plate = request.plate_no
        if plate and plate[-1] == "警" and request.filter_police:
            judgeres.set_judres_core(judge_status=2, result_code="no_vio_010",
                                     result_desc="警车过滤，抓拍设备识别的车牌结尾字符为'警'")
        elif plate and plate.endswith("应急") and len(plate) >= 2 and plate[1] in ["X", "S"] and request.filter_fire:
            judgeres.set_judres_core(judge_status=2, result_code="no_vio_010",
                                     result_desc=f"消防车过滤，抓拍设备识别的车牌结尾字符为'应急'且第二位为{plate[1]}")
        judgeres.add_act_details(act=f"{self._cls_name}.filter_special_vehicle")

    def judge_cross(self, judgeres: JudgeResult):
        request = judgeres.request
        match_code = request.match_viocode
        cross_dict = self._conf.No_Judge_Cross
        if match_code in cross_dict and request.cross_code in cross_dict[match_code]:
            judgeres.set_judres_core(result_code="no_judge_004", result_desc="配置不分析的卡口数据(No_Judge_Cross)")
        judgeres.add_act_details(act=f"{self._cls_name}.judge_cross")

    def judge_camera(self, judgeres: JudgeResult):
        request = judgeres.request
        camera_code = request.camera_code
        no_judge_camera = self._conf.No_Judge_Camera
        filter_camera = self._conf.Filter_Waste_Camera
        match_code = request.match_viocode
        if match_code in no_judge_camera and camera_code in no_judge_camera[match_code]:
            judgeres.set_judres_core(result_code="no_judge_004", result_desc=f"配置不分析的相机数据(No_Judge_Camera)")
        elif match_code in filter_camera and camera_code in filter_camera[match_code]:
            judgeres.set_judres_core(judge_status=2, result_code="waste_camera_conf",
                                     result_desc=f"配置统一作废的相机数据(Filter_Waste_Camera)")
        judgeres.add_act_details(act=f"{self._cls_name}.judge_camera")

    def judge_overtime(self, judgeres: JudgeResult):
        request = judgeres.request
        logger = judgeres.logger
        # delay = request.delay_time
        if request.iiea_time:
            delay = TimeUtil.hikstr2timestamp(request.recv_time) - TimeUtil.hikstr2timestamp(request.iiea_time)
            logger.info(f"通过iieaSendTime计算消费延时:{delay:.2f}秒")
        else:
            delay = TimeUtil.hikstr2timestamp(request.recv_time) - TimeUtil.hikstr2timestamp(request.send_time)
            logger.info(f"通过SendTime计算消费延时:{delay:.2f}秒")
        max_seconds = self._conf.Max_Delay_Seconds
        if delay > max_seconds:
            judgeres.set_judres_core(result_code="no_judge_003",
                                     result_desc=f"已超时数据不做处理，消费延时：{delay:.2f}秒")
        judgeres.add_act_details(act=f"{self._cls_name}.judge_overtime",
                                 detail={"delay": round(delay, 2), "delay_conf": max_seconds})

    def check_truck(self, judgeres: JudgeResult):
        request = judgeres.request
        plate = request.plate_no
        plate_color = request.plate_color
        match_code = request.match_viocode
        if match_code == "8013" and self._conf.Filter_No_Truck:
            if plate_color in ["yellow", "黄", "黄色"] and len(plate) < 8:
                pass
            else:
                judgeres.set_judres_core(result_code="no_judge_002", result_desc="闯禁行场景不分析非货车数据")
            judgeres.add_act_details(act=f"{self._cls_name}.check_truck")

    def check_datatype(self, judgeres: JudgeResult):  # remind jz
        request = judgeres.request
        datatype = request.datatype
        if self._conf.Judge_Itrc and request.match_viocode == "8013" and datatype != "vps_manual":
            judgeres.set_judres_core(result_code="no_judge_001", result_desc="货车闯禁行场景不处理非itrc数据")
            judgeres.add_act_details(act=f"{self._cls_name}.check_datatype", detail={"datatype": datatype})


# 图片读取切分动作
class ImageJudAct(JudgeAct):
    def __init__(self, name, conf, params,model_dict):
        super().__init__(name, conf, params, model_dict)
        self._conf = OptConf
        self._cls_name = self.__class__.__name__

    def _run(self, judgeres: JudgeResult) -> None:
        self._match_code = judgeres.request.match_viocode
        self.set_judge_url(self._match_code)
        self.load_image(judgeres)
        if judgeres.valid:
            self.split_image(judgeres)

    def set_judge_url(self, match_code: str) -> None:
        self._judge_url = self._conf.Judge_Url and match_code not in self._conf.No_Judge_Url_Code

    def load_image(self, judgeres: JudgeResult) -> None:
        request = judgeres.request
        url_list = request.urls
        if self._judge_url:
            st = time.time()
            img_list = [load_image_from_url(url) for url in url_list]

            et = time.time()
            src_list = deepcopy(img_list)
            if None in img_list:
                judgeres.set_judres_core(result_code="no_judge_101",
                                         result_desc=f"Judge Url违法图片获取失败,耗时：{et - st:.2f}秒")
            img_list = self.convert_img_index(img_list)
            img_keys = [f"img{i + 1}" for i in range(len(img_list))]

            judgeres.add_act_details(act=f"{self._cls_name}.load_judge_image",
                                     detail={"src_img": src_list, "split_img": img_list, "img_shape": img_list[0].shape,
                                             "img_keys": img_keys})
            # 根据图片数量初始化elements
            judgeres.init_tfc_elements(len(img_list))
        else:
            st = time.time()
            img = load_image_from_url(url_list[0])
            et = time.time()
            src_list = [img]
            if img is None:
                judgeres.set_judres_core(result_code="no_judge_101",
                                         result_desc=f"违法图片获取失败,耗时：{et - st:.2f}秒")
            judgeres.add_act_details(act=f"{self._cls_name}.load_image", detail={"src_img": src_list})

    # 图片按配置切分和自动切分
    def split_image(self, judgeres: JudgeResult) -> None:
        if not self._judge_url:
            logger = judgeres.logger
            request = judgeres.request
            split_mode = request.split_mode
            black_rate = request.black_rate
            black_pos = request.black_pos
            split_mode_desc = self._conf.SPLIT_MODE_DESC
            src_img = judgeres.get_act_detail("src_img")[0].copy()
            judgeres.add_act_details(act=f"{self._cls_name}.split_image",
                                     detail={"split_mode_src": split_mode_desc[split_mode],
                                             "black_rate_src": black_rate,
                                             "black_pos_src": black_pos})
            # 自动切分
            if (split_mode is None
                    or (self._match_code in ["1625", "1208", "1357"]
                        and self._conf.BLACK_HEIGHT is None
                        and self._conf.SPLIT_DICT[self._match_code] in [411, 611, 621])):
                st = time.time()
                at_split_mode, at_black_rate, at_black_pos = AutoSplit.run(src_img, self._match_code)
                auto_split_time = round(time.time() - st, 3)
                logger.info(
                    f"自动切分模块耗时：{auto_split_time}秒 {split_mode_desc[at_split_mode]} 黑框比例：{at_black_rate} 黑框位置：{at_black_pos}")
                # 选择使用自动切分结果
                if black_rate is None:
                    black_rate = at_black_rate
                if split_mode is None:
                    split_mode = at_split_mode
                if black_pos is None and black_rate > 0:
                    black_pos = at_black_pos
            split_img_list = split_img(src_img, n=split_mode, black_height=black_rate, black_pos=black_pos)
            split_img_list = self.convert_img_index(split_img_list)
            img_keys = [f"img{i + 1}" for i in range(len(split_img_list))]
            judgeres.add_act_details(act=f"{self._cls_name}.split_image",
                                     detail={"split_mode_desc": split_mode_desc[split_mode], "split_mode": split_mode,
                                             "black_rate": black_rate,
                                             "black_pos": black_pos, "split_img": split_img_list,
                                             "img_shape": split_img_list[0].shape, "img_keys": img_keys})
            # 根据图片数量初始化elements
            judgeres.init_tfc_elements(len(split_img_list))

    # 根据配置调整切分后图片的顺序
    def convert_img_index(self, img_list: List[np.ndarray]) -> List[np.ndarray]:
        img_index = self._conf.PIC_JUDGE_INDEX.get(self._match_code)
        new_img_list = img_list
        if img_index:
            new_img_list = [img_list[idx] for idx in img_index]  # 修正图片处理顺序
        return new_img_list


# 红绿灯识别与三图结果整合动作
class LightJudAct(JudgeAct):
    def __init__(self, name, conf, params, model_dict):
        super().__init__(name, conf, params, model_dict)
        self._light_model = self._model_dict["LightModel"]
        self._conf = OptConf
        self._cls_name = self.__class__.__name__
        self._det_thresh = params.get("det_thresh", 0.1)  # 红绿灯阈值
        # self._judge_feature = params.get("judge_feature",False)  # 判断特征图(暂不启用)

    def _run(self, judgeres: JudgeResult) -> None:
        functions = [
            self.detect_light,
            self.judge_light,
        ]
        for func in functions:
            func(judgeres)
            if not judgeres.valid:
                break

    def detect_light(self, judgeres: JudgeResult) -> None:  # TODO:增加判断是否为特征图
        # det_object_num = 0
        # 取图片
        img_list = judgeres.get_act_detail("split_img")
        if len(img_list) < 3:
            split_mode_desc = judgeres.get_act_detail("split_mode_desc")
            judgeres.set_judres_core(result_code="no_judge_101",
                                     result_desc=f"图片经切分后数量不足3张(切分方式:{split_mode_desc})")
        else:
            for idx, img in enumerate(img_list):
                if idx >= 3:
                    break
                results: ModelResults = self._light_model.run(img, thresh=self._det_thresh)
                judgeres.logger.info(f"第{idx + 1}张图LightModel推理完成")
                # judgeres.logger.debug(f"第{idx + 1}张图识别结果：{results}")
                judgeres.set_tfc_elements(idx, results)
                # if results.get_results(attr="box"):
                #     det_object_num += 1
                # if det_object_num == 3:
                #     break
        judgeres.add_act_details(act=f"{self._cls_name}.detect_light")

    def judge_feature_pic(self, idx: int, judgeres: JudgeResult) -> None:  # 未启用，待完善
        if idx >= 3:  # 特征图在第一图 TODO:考虑对后续的details、elements、imgx的影响（信号灯判定，交通元素检测、行驶方向）
            tmp_tfc_elements = judgeres.tfc_elements["img1"]
            judgeres.tfc_elements["img1"] = judgeres.tfc_elements.pop("img2")
            judgeres.tfc_elements["img2"] = judgeres.tfc_elements.pop("img3")
            judgeres.tfc_elements["img3"] = judgeres.tfc_elements.pop("img4")
            judgeres.tfc_elements["img4"] = tmp_tfc_elements

    # 三图信号灯结果整合判断
    def judge_light(self, judgeres: JudgeResult) -> None:  # TODO:拆分
        camera_code = judgeres.request.camera_code
        img_keys = ["img1", "img2", "img3"]
        attr_keys = ["box", "subclass", "score"]
        res_dict = {
            img_key: {
                attr: judgeres.tfc_elements.get(img_key, ModelResults()).get_results(
                    superclass="light", attr=attr)
                for attr in attr_keys
            }
            for img_key in img_keys
        }
        status = []  # 整合三图的红绿灯结果
        status_cx = []
        status_thresh = []
        add_l = False
        single_left = None
        left_ps = []  # [[类别,中点横坐标],]
        # 遍历第一张图的识别结果
        for idx1, box1 in enumerate(res_dict["img1"]["box"]):
            single_left = True  # 判定当只有第一张图检测到某位置的红绿灯
            cls1 = res_dict["img1"]["subclass"][idx1]
            score1 = res_dict["img1"]["score"][idx1]
            cx1 = (box1[0] + box1[2]) // 2
            # 遍历第三张图的识别结果
            for idx3, box3 in enumerate(res_dict["img3"]["box"]):
                cls3 = res_dict["img3"]["subclass"][idx3]
                score3 = res_dict["img3"]["score"][idx3]
                cx3 = (box3[0] + box3[2]) // 2
                # 第一二图左转黄灯、三图左转红灯
                if cls3[-1] == 'l':
                    left_ps.append([cls3, cx3])
                iou13 = IOU.get_xiou(box1, box3)  # 横向iou计算,图片切分误差会影响普通iou计算
                if iou13 > 0.5:
                    single_left = False
                    thresh_list = [score1, score3]
                    if cls1 == cls3:  # 全匹配
                        status.append(cls1)
                        status_cx.append(cx1)
                        status_thresh.append(max(thresh_list))
                    elif cls1[-1] == cls3[-1] == 'l':  # 方向匹配
                        add_l = True
                    elif cls1[0] == cls3[0]:  # 颜色匹配
                        # 结合二图红绿灯结果修正 TODO:灯盘同亮时修正是否有问题
                        color = cls1.split("_")[0]
                        direction_list = [cls1[-1], cls3[-1]]
                        idx = np.argmax(thresh_list)
                        new_thresh = thresh_list[idx]
                        new_light = color + "_" + direction_list[idx]
                        # 遍历第二张图的识别结果
                        for idx2, box2 in enumerate(res_dict["img2"]["box"]):
                            cls2 = res_dict["img2"]["subclass"][idx2]
                            score2 = res_dict["img2"]["score"][idx2]
                            cx2 = (box2[0] + box2[2]) // 2
                            # 第一三图未识别到左转灯
                            if cls2[-1] == 'l':
                                left_ps.append([cls2, cx2])
                            iou23 = IOU.get_xiou(box2, box3)
                            if iou23 > 0.5:
                                thresh_list = [score1, score2, score3]
                                direction_list = [cls1[-1], cls2[-1], cls3[-1]]
                                if direction_list.count("l") > 1:
                                    add_l = True
                                # 求第一和第二置信度的差
                                idx = np.argmax(thresh_list)
                                temp_dt = direction_list.copy()
                                temp_dt.pop(idx)
                                temp_thresh = thresh_list.copy()
                                temp_thresh.pop(idx)
                                dist_thresh = thresh_list[idx] - np.max(temp_thresh)
                                new_thresh = thresh_list[idx]
                                if direction_list.count(direction_list[idx]) == 2:  # 12或23
                                    new_light = color + "_" + direction_list[idx]
                                elif temp_dt[0] == temp_dt[1]:
                                    if dist_thresh > 0.3:
                                        new_light = color + "_" + direction_list[idx]
                                    else:
                                        new_light = color + "_" + temp_dt[0]
                                        new_thresh = np.max(temp_thresh)
                                elif dist_thresh > 0:
                                    new_light = color + "_" + direction_list[idx]
                                # 修正二图识别结果
                                res_dict["img2"]["subclass"][idx2] = new_light + "-" + str(new_thresh)
                        status.append(new_light)
                        status_cx.append(cx1)
                        status_thresh.append(new_thresh)
                        # 修正一三张图的识别结果
                        res_dict["img1"]["subclass"][idx1] = new_light + "-" + str(new_thresh)
                        res_dict["img3"]["subclass"][idx3] = new_light + "-" + str(new_thresh)
            if single_left and not add_l:
                if cls1[-1] == 'l':
                    add_l = True
                else:
                    left_ps.append([cls1, cx1])

        status_direct = [i[-1] for i in status]
        vio_judge = None
        # 同方向颜色冲突修正
        if "red_s" in status and "green_s" in status:
            idx_r = status.index("red_s")
            idx_g = status.index("green_s")
            # 红绿同亮(过滤误识别红灯)
            if abs(status_cx[idx_r] - status_cx[idx_g]) < 20:
                vio_judge = "light_logic_judge"
                judgeres.set_judres_core(judge_status=2, judge_confid=status_thresh[idx_r], result_code=vio_judge,
                                         result_desc="过滤误识别直行红灯")
                status.pop(idx_r)
                status_thresh.pop(idx_r)
                status_cx.pop(idx_r)
            elif status_cx[idx_r] > status_cx[idx_g]:
                if status_thresh[idx_r] < status_thresh[idx_g] and self._conf.Set_Light_Direct.get(camera_code) is None:
                    # 无法补充右转红灯存在特殊情况
                    status.pop(idx_r)
                    status_thresh.pop(idx_r)
                    status_cx.pop(idx_r)
                elif status_thresh[idx_r] > status_thresh[idx_g] and status_direct.count("l") == 0:
                    status[idx_g] = "green_l"
                    status_thresh[idx_g] = 0
            else:
                if status_thresh[idx_r] < status_thresh[idx_g] and status_direct.count("l") == 0:
                    status[idx_r] = "red_l"
                    status_thresh[idx_r] = 0

        if "red_l" in status and "green_l" in status:
            idx_r = status.index("red_l")
            idx_g = status.index("green_l")
            # 红绿同亮(过滤误识别红灯)
            if abs(status_cx[idx_r] - status_cx[idx_g]) < 20:
                vio_judge = "light_logic_judge"
                judgeres.set_judres_core(judge_status=2, judge_confid=status_thresh[idx_r], result_code=vio_judge,
                                         result_desc="过滤误识别左转红灯")

                status.pop(idx_r)
                status_thresh.pop(idx_r)
                status_cx.pop(idx_r)

            # 绿 红
            elif status_cx[idx_r] > status_cx[idx_g]:
                if status_thresh[idx_r] < status_thresh[idx_g] and status_direct.count("s") == 0:
                    status[idx_r] = "red_s"
                    status_thresh[idx_r] = 0
            # 红 绿
            else:
                if status_thresh[idx_r] < status_thresh[idx_g] and self._conf.Set_Light_Direct.get(camera_code) is None:
                    status.pop(idx_r)
                    status_thresh.pop(idx_r)
                    status_cx.pop(idx_r)
                elif status_thresh[idx_r] > status_thresh[idx_g] and status_direct.count("s") == 0:
                    status[idx_g] = "green_s"
                    status_thresh[idx_g] = 0
        # tmp_status = [i.split("_")[0] for i in status]
        # if "yellow" in tmp_status and "red" in tmp_status:
        #     # 红黄同亮
        #     del_idx = []
        #     tmp_dict = {value: [index for index, element in enumerate(tmp_status) if element == value]
        #                 for value in set(tmp_status) if value in ["yellow", "red"]}
        #     for idx1 in tmp_dict["yellow"]:
        #         yellow_cx = status_cx[idx1]
        #         for idx2 in tmp_dict["red"]:
        #             red_cx = status_cx[idx2]
        #             if abs(yellow_cx - red_cx) < 20:
        #                 del_idx.append(idx2)
        #
        #     status = [item for idx, item in enumerate(status) if idx not in del_idx]
        #     status_thresh = [item for idx, item in enumerate(status_thresh) if idx not in del_idx]
        #     status_cx = [item for idx, item in enumerate(status_cx) if idx not in del_idx]
        #     if del_idx:
        #         vio_judge = "no_vio_007"

        if status_direct.count("l") > 0:
            add_l = False
        elif not add_l and status_direct.count("s") == 1:
            idx = status_direct.index('s')
            judge_cx = status_cx[idx]
            for i in left_ps:
                if i[-1] < judge_cx and i[0][-1] == "l":
                    add_l = True
                    break
        if add_l:
            status.append("green_l")
            status_cx.append(0)
        # 预设的红绿灯信息
        try:
            if self._conf.Set_Light_Direct.get(camera_code) is not None:
                status_cx, status = zip(*sorted(zip(status_cx, status), key=lambda x: x[0]))
                status = list(status)
                pred_color = [i.split("_")[0] for i in status]
                pred_direct = [i[-1] for i in status]
                if len(self._conf.Set_Light_Direct.get(camera_code)) == len(status):
                    status = [pred_color[i] + "_" + self._conf.Set_Light_Direct.get(camera_code)[i] for i in
                              range(len(status))]
                elif len(self._conf.Set_Light_Direct.get(camera_code)) > len(status) > 0:  # 漏识别、被遮挡、黄灯
                    for i in self._conf.Set_Light_Direct.get(camera_code):
                        if i not in pred_direct:
                            status.append(f"green_{i}")
                elif len(self._conf.Set_Light_Direct.get(camera_code)) < len(
                        status):  # 识别到非机动车信号灯、识别到多个路口的信号灯、信号灯绿灯和红灯同时亮起
                    pass
        except:
            pass
        judgeres.add_act_details(f"{self._cls_name}.judge_light",
                                 {"light_judge": vio_judge, "status": status, "add_l": add_l,
                                  "single_left": single_left,
                                  "left_ps": left_ps})


# 交通元素识别动作
class ElementDetAct(JudgeAct):
    def __init__(self, name, conf, params, model_dict):
        super().__init__(name, conf, params, model_dict)
        self._seg_model = self._model_dict["SegModel"]
        self._veh_person_model = self._model_dict["VehPersonModel"]
        self._conf = OptConf
        self._cls_name = self.__class__.__name__
        self._filter_feature = params.get("filter_feature", True)  # 过滤特征图
        self._seg_det_thresh = params.get("seg_det_thresh", 0.15)  # 交通元素分割模型阈值
        self._yolo_det_thresh = params.get("yolo_det_thresh", 0.1)  # 人车检测模型阈值
        self._add_det_model = params.get("add_det_model")  # 添加人车检测模型

    def _run(self, judgeres: JudgeResult) -> None:  # TODO:舍弃了摩托车判定逻辑 is_motorbike
        # 取图片
        img_list = judgeres.get_act_detail("split_img")
        img_num = self._params.get("img_num", len(img_list))  # 需要识别的图片数量
        if self._filter_feature and len(img_list) > 2:
            img_list[:] = img_list[:-1]  # 不处理最后一张特征图
            img_num = len(img_list)
        else:
            img_list[:] = img_list[:img_num]  # 切片赋值修改details中的图片列表
        img_keys = [f"img{i + 1}" for i in range(len(img_list))]  # 避免参数给出的数量过高
        person_box = {}
        if img_num and len(img_list) < img_num:
            split_mode_desc = judgeres.get_act_detail("split_mode_desc")
            judgeres.set_judres_core(result_code="no_judge_101",
                                     result_desc=f"图片经切分后数量不足{img_num}张(切分方式:{split_mode_desc})")
        else:
            for idx, img in enumerate(img_list):
                results = self._seg_model.run(img, thresh=self._seg_det_thresh)
                judgeres.logger.info(f"第{idx + 1}张图SegModel推理完成")
                # judgeres.logger.debug(f"第{idx + 1}张图识别结果：{results}")
                judgeres.set_tfc_elements(idx, results)
                if self._add_det_model:
                    results: ModelResults = self._veh_person_model.run(img, thresh=self._yolo_det_thresh)
                    judgeres.logger.info(f"第{idx + 1}张图VehPersonModel推理完成")
                    # judgeres.logger.debug(f"第{idx + 1}张图识别结果：{results}")
                    judgeres.set_tfc_elements(idx, results)
                    person_box[f"img{idx + 1}"] = results.get_results(superclass="person", attr="box")
        judgeres.add_act_details(act=self._cls_name, detail={"img_keys": img_keys, "person_box": person_box})


# 目标车辆匹配动作
class VehicleJudAct(JudgeAct):
    def __init__(self, name, conf, params, model_dict):
        super().__init__(name, conf, params, model_dict)
        self._conf = OptConf
        self._cls_name = self.__class__.__name__

    def _run(self, judgeres: JudgeResult) -> None:
        for ruler in self._rulers:  # 机动车子类调用
            ruler.run(judgeres)


# 路口三图判断行驶方向 TODO：拆分
class DriveDirJudAct(JudgeAct):
    def __init__(self, name, conf, params, model_dict):
        super().__init__(name, conf, params, model_dict)
        self._conf = OptConf
        self._cls_name = self.__class__.__name__

    def _run(self, judgeres: JudgeResult) -> None:
        target_veh_boxes: dict = judgeres.get_act_detail("target_veh_box")
        target_veh_plate_cor: dict = judgeres.get_act_detail("target_veh_plate_cor")

        img_list: List[np.ndarray] = judgeres.get_act_detail("split_img")
        split_mode_desc: str = judgeres.get_act_detail("split_mode_desc")
        if len(img_list) < 3:
            judgeres.set_judres_core(result_code="no_judge_101",
                                     result_desc=f"图片经切分后数量不足3张(切分方式:{split_mode_desc})")
            judgeres.add_act_details(self._cls_name)
            return None
        logger = judgeres.logger
        # 目标车辆坐标
        img1_car_ps = target_veh_boxes.get("img1")
        img2_car_ps = target_veh_boxes.get("img2")
        img3_car_ps = target_veh_boxes.get("img3")
        # 目标车辆车牌坐标
        plate1_cor = target_veh_plate_cor.get("img1")
        plate2_cor = target_veh_plate_cor.get("img2")
        plate3_cor = target_veh_plate_cor.get("img3")
        # 车头中点
        if img1_car_ps:
            img1_car_head_cor = [(img1_car_ps[0] + img1_car_ps[2]) // 2, img1_car_ps[1]]
        else:
            img1_car_head_cor = []

        if img2_car_ps:
            img2_car_head_cor = [(img2_car_ps[0] + img2_car_ps[2]) // 2, img2_car_ps[1]]
        else:
            img2_car_head_cor = []

        if img3_car_ps:
            img3_car_head_cor = [(img3_car_ps[0] + img3_car_ps[2]) // 2, img3_car_ps[1]]
        else:
            img3_car_head_cor = []
        # 车牌中点
        if plate1_cor:
            plate1_point = [(plate1_cor[0] + plate1_cor[2]) // 2, (plate1_cor[1] + plate1_cor[3]) // 2]
        else:
            plate1_point = []

        if plate2_cor:
            plate2_point = [(plate2_cor[0] + plate2_cor[2]) // 2, (plate2_cor[1] + plate2_cor[3]) // 2]
        else:
            plate2_point = []

        if plate3_cor:
            plate3_point = [(plate3_cor[0] + plate3_cor[2]) // 2, (plate3_cor[1] + plate3_cor[3]) // 2]

        else:
            plate3_point = []
        # 车身方向判别
        car_angle1 = None
        car_angle2 = None
        car_angle3 = None
        # 计算一二三图目标车辆夹角（车头中点与车牌中点）
        if len(img1_car_head_cor) and len(plate1_point):
            h1 = img1_car_head_cor[1] - plate1_point[1]
            w1 = img1_car_head_cor[0] - plate1_point[0]
            if w1 != 0:
                tan_theta = h1 / w1
                car_angle1 = round(float(np.degrees(np.arctan(-tan_theta))), 3)
            else:
                car_angle1 = 90

        if len(img2_car_head_cor) and len(plate2_point):
            h2 = img2_car_head_cor[1] - plate2_point[1]
            w2 = img2_car_head_cor[0] - plate2_point[0]
            if w2 != 0:
                tan_theta2 = h2 / w2
                car_angle2 = round(float(np.degrees(np.arctan(-tan_theta2))), 3)
            else:
                car_angle2 = 90
        x_judge = False
        if len(img3_car_head_cor) and len(plate3_point):
            h3 = img3_car_head_cor[1] - plate3_point[1]
            w3 = img3_car_head_cor[0] - plate3_point[0]
            if w3 != 0:
                tan_theta3 = h3 / w3
                car_angle3 = round(float(np.degrees(np.arctan(-tan_theta3))), 3)
            # elif w1 != 0 and w2 != 0 and h1 / w1 < h2 / w2:
            #     # 救护车等车牌在左下的情况
            #     direction = 'left'
            #     print("***test judge***")
            #     return direction, car_angle1, car_angle3, x_judge
            elif car_angle1:  # TODO:判断车牌在左下的情况
                car_angle3 = 90
                # direction = 'straight'
                # print("***test judge2***")
                # return None
        # 角度： 79.847,   82.668, -88.556 | -84.367 -83.808 None
        # 车宽： 597,      383,    286 | 464 370 133
        # 车高: 860,      528,    290 | 416 312 103
        # 宽高比：0.69,    0.72,   0.98 | 1.12 1.19 1.29
        # print("角度：", car_angle1, car_angle2, car_angle3)
        # print("车宽：", img1_car_ps[2] - img1_car_ps[0], img2_car_ps[2] - img2_car_ps[0],
        #       img3_car_ps[2] - img3_car_ps[0])
        # print("车高:", img1_car_ps[3] - img1_car_ps[1], img2_car_ps[3] - img2_car_ps[1],
        #       img3_car_ps[3] - img3_car_ps[1])
        # return
        # 三张均存在车牌
        logger.debug(f"veh_plate_angle: {car_angle1}, {car_angle2}, {car_angle3}")
        direction = None
        if car_angle3 and car_angle1:
            logger.debug("情形一：已知一三图目标车辆角度")
            cx1 = (img1_car_ps[0] + img1_car_ps[2]) / 2
            cx3 = (img3_car_ps[0] + img3_car_ps[2]) / 2
            cx2 = (img2_car_ps[0] + img2_car_ps[2]) / 2 if img2_car_ps else np.average((cx1, cx3))

            cy3 = (img3_car_ps[1] + img3_car_ps[3]) / 2

            # wh_rate2 = (img2_car_ps[2] - img2_car_ps[0]) / (img2_car_ps[3] - img2_car_ps[1]) if img2_car_ps \
            #     else (img1_car_ps[2] - img1_car_ps[0]) / (img1_car_ps[3] - img1_car_ps[1])
            # wh_rate3 = (img2_car_ps[2] - img2_car_ps[0]) / (img2_car_ps[3] - img2_car_ps[1]) if img2_car_ps \
            #     else (img1_car_ps[2] - img1_car_ps[0]) / (img1_car_ps[3] - img1_car_ps[1])
            # print("cy3:", cy3, "dist_y:", dist_y, img_list[2].shape)
            # if cx1 > cx2 > cx3 or cx1 < cx2 < cx3:
            #     dist_judge = cx3 - cx1
            # else:
            dist_judge = cx3 - cx2
            dist_judge_y = img3_car_ps[3] - img2_car_ps[3] if img2_car_ps else (img1_car_ps[3] + img3_car_ps[3]) / 3 * 2
            if (car_angle1 > 0 and car_angle3 < 0) or (car_angle1 < 0 and car_angle3 > 0):
                sup_angle = 180 - abs(car_angle1) - abs(car_angle3)
            else:
                sup_angle = abs(car_angle1 - car_angle3)
            car_w = img2_car_ps[2] - img2_car_ps[0] if img2_car_ps else img1_car_ps[2] - img1_car_ps[0]

            # 当一图角度偏转过大时，三图角度变化与车辆位移存在异常关系时判定为直行
            turn_judge = True
            if car_angle2:
                if (car_angle1 > car_angle2 > car_angle3 > 0 and dist_judge < -0.5 * car_w) or (
                        0 < car_angle1 < car_angle2 < car_angle3 and 0 < dist_judge < 0.5 * car_w and car_angle3 - car_angle1 > 18) or (
                        car_angle1 < car_angle2 < car_angle3 < 0 and dist_judge > 0.5 * car_w):
                    turn_judge = False
            yx_rate = dist_judge_y / dist_judge if dist_judge != 0 else 0
            logger.debug(
                f"dist_judge:{dist_judge}, car_w:{car_w}, sup_angle:{sup_angle}, yx_rate:{yx_rate}, turn_judge:{turn_judge}")
            if car_angle1 < -75:  # 达到偏移角度的最大值
                # 近似抓拍设备与道路的倾斜角度，修正三图转向角度，降低干扰
                bias_angle = -car_angle1 - 90
                angle3 = car_angle3 + bias_angle
                angle3 = angle3 if angle3 > -90 else 180 - abs(angle3)
                if car_angle2:
                    min_angle = max(car_angle2, car_angle3)
                else:
                    min_angle = car_angle3
                if abs(yx_rate) < 0.5 and sup_angle < 12:  # error to vio
                    judgeres.set_judres_core(judge_status=1, result_code="init_vio",
                                             result_desc="匹配的目标车辆横向位移与角度变化异常，置为正片")
                    judgeres.add_act_details(self._cls_name, {"drv_direction": direction})
                    return None
                elif cy3 > img_list[2].shape[
                    0] * 0.27 and (((yx_rate > -2.2 and dist_judge > 0) or sup_angle > 23) and (
                        (0 < angle3 < 68 and dist_judge > 0) or (
                        angle3 > 0 and sup_angle > 21.5 and dist_judge > 0) or (
                                angle3 > 0 and dist_judge > 0 and yx_rate > -1.4 and sup_angle > 8))):
                    direction = 'right'
                elif (-58 < angle3 < 0 or cy3 > img_list[2].shape[
                    0] * 0.27) and (
                        (0 > angle3 > -68 and dist_judge < 0) or (-60 > angle3 > -70 and dist_judge < -0.5 * car_w) or (
                        0 > car_angle3 > car_angle1 and dist_judge < 0 and yx_rate < 1.4 and sup_angle > 8) or (
                                -60 < min_angle < 0 and dist_judge < 0)):
                    direction = 'left'
                else:
                    direction = 'straight'
            elif car_angle1 > 75:
                bias_angle = 90 - car_angle1  # 12
                angle3 = car_angle3 + bias_angle  # 76
                # 转换角度最大值
                angle3 = angle3 if angle3 < 90 else abs(angle3) - 180
                if car_angle2:
                    min_angle = min(car_angle2, car_angle3)
                else:
                    min_angle = car_angle3
                logger.debug(
                    f"debug point1：{dist_judge - 0.8 * car_w}, {cy3 - img_list[2].shape[0] * 0.27}, {yx_rate}")
                if abs(yx_rate) < 0.5 and sup_angle < 12:  # init to vio
                    judgeres.set_judres_core(judge_status=1, result_code="init_vio",
                                             result_desc="匹配的目标车辆横向位移与角度变化异常，置为正片")
                    judgeres.add_act_details(self._cls_name, {"drv_direction": direction})
                    return None
                elif yx_rate > 10 or yx_rate < -10:
                    direction = 'straight'
                elif cy3 > img_list[2].shape[
                    0] * 0.27 and (
                        (0 < angle3 < 65 and dist_judge > 0) or (65 <= angle3 < 70 and dist_judge > 0.5 * car_w) or (
                        0 < car_angle3 < car_angle1 and dist_judge > 0.8 * car_w and 0 > yx_rate > -1.4 and sup_angle >= 10) or (
                                0 < min_angle < 60 and 0 > yx_rate > -1.7) or (
                                dist_judge > 0 and img_list[2].shape[1] - img3_car_ps[2] < 20)):
                    direction = 'right'
                elif (0 > angle3 > -45 or cy3 > img_list[2].shape[
                    0] * 0.27) and (((yx_rate < 2.2 and dist_judge < 0) or 0 > angle3 > -59) and (
                        0 > angle3 > -65 or (0 > angle3 > -73 and dist_judge < 0) or (
                        0 > angle3 > -79 and dist_judge < 0 and sup_angle > 12) or (
                                angle3 < 0 and sup_angle > 19.5 and dist_judge < 0) or (
                                dist_judge < 0 and img3_car_ps[0] < 20))):
                    direction = 'left'
                else:
                    direction = 'straight'
            else:
                if car_angle3 < 0 and (
                        (
                                car_angle3 > -80 and dist_judge < -car_w and yx_rate < 1.4 and turn_judge and sup_angle > 8) or (
                                car_angle3 > -60 and dist_judge < 0) or (
                                car_angle1 > 0 and sup_angle > 19.5 and cx3 < cx1) or (
                                sup_angle > 30 and dist_judge < 0)):
                    direction = 'left'
                elif car_angle3 > 0 and sup_angle > 16 and yx_rate < 1.4:
                    direction = 'left'
                elif car_angle3 > 0 and turn_judge and (
                        (car_angle3 < 80 and dist_judge > car_w and cy3 > img_list[2].shape[0] * 0.3) or (
                        car_angle3 < 60 and dist_judge > 0) or (
                                car_angle3 < 65 and dist_judge > 0.5 * car_w) or (
                                car_angle3 < 72 and dist_judge > 0.7 * car_w and car_angle1 > 0) or (
                                car_angle1 < 0 and sup_angle > 19.5 and cx3 > cx1) or sup_angle > 40):
                    direction = 'right'
                else:
                    direction = 'straight'
        elif img1_car_ps and img3_car_ps:
            logger.debug("情形二：三图目标车辆车牌坐标")
            cx1 = (img1_car_ps[0] + img1_car_ps[2]) / 2
            cx3 = (img3_car_ps[0] + img3_car_ps[2]) / 2

            car_w = img1_car_ps[2] - img1_car_ps[0]
            car_h = img1_car_ps[3] - img1_car_ps[1]

            car3_w = img3_car_ps[2] - img3_car_ps[0]
            car3_h = img3_car_ps[3] - img3_car_ps[1]

            # 车辆长宽比
            car_hw = round(car_h / car_w, 3)
            car3_hw = round(car3_h / car3_w, 3)

            dist_judge = cx3 - cx1
            dist_judge_y = img3_car_ps[3] - img1_car_ps[3]
            yx_rate = abs(dist_judge_y) / dist_judge if dist_judge != 0 else 0

            car3_ch = (img3_car_ps[3] + img3_car_ps[1]) / 2
            if img2_car_ps:
                cx2 = (img2_car_ps[0] + img2_car_ps[2]) / 2
                car2_w = img2_car_ps[2] - img2_car_ps[0]
                car2_h = img2_car_ps[3] - img2_car_ps[1]

                dist_judge12 = cx2 - cx1
                dist_judge23 = cx3 - cx2
                dist_judge_y = img3_car_ps[3] - img2_car_ps[3]
                yx_rate = abs(dist_judge_y) / dist_judge23 if dist_judge23 != 0 else 0

                # min_w = min((car_w, car2_w, car3_w))
                # 大概率有转向
                if car_hw - car3_hw > 0.258:
                    if ((abs(yx_rate) > 2.2 or yx_rate == 0) and abs(dist_judge_y) > car3_h and car3_hw > 0.55) or (
                            car3_ch < 0.27 * img_list[2].shape[
                        0] and -dist_judge_y > 2 * car3_h and car3_hw > 0.65):
                        direction = 'straight'
                    elif dist_judge23 < 0:
                        direction = 'left'
                    elif dist_judge23 > 0:
                        direction = 'right'
                    else:
                        direction = 'straight'
                # 大概率直行
                else:
                    if car3_ch < 0.3 * img_list[2].shape[0] and -dist_judge_y > 2 * car3_h and car3_hw > 0.65:
                        direction = 'straight'
                    elif dist_judge12 * dist_judge23 < 0 and dist_judge23 / dist_judge12 < -1.8:
                        if dist_judge23 < 0:
                            direction = 'left'
                        elif dist_judge23 > 0:
                            direction = 'right'
                    elif -1.4 < yx_rate <= 2 and yx_rate != 0:
                        if dist_judge23 < 0:
                            direction = 'left'
                        elif dist_judge23 > 0:
                            direction = 'right'
                    else:
                        direction = 'straight'

                logger.debug(
                    f"debug point2: {car_hw}, {car3_hw}, {dist_judge12}, {dist_judge23}, {yx_rate}, {abs(dist_judge_y)}, {car3_h}, {car3_ch}")
            else:
                # 大概率有转向
                if car3_ch < 0.3 * img_list[2].shape[0] and -dist_judge_y > 3 * car3_h and car3_hw > 0.65:
                    direction = 'straight'
                elif car_hw - car3_hw > 0.285:
                    if (abs(yx_rate) > 2.2 or yx_rate == 0) and abs(dist_judge_y) > car3_h:
                        direction = 'straight'
                    elif dist_judge < 0:
                        direction = 'left'
                    elif dist_judge > 0:
                        direction = 'right'
                # 大概率直行
                else:
                    if -1.4 < yx_rate <= 2 and yx_rate != 0:
                        if dist_judge < 0:
                            direction = 'left'
                        elif dist_judge > 0:
                            direction = 'right'
                    else:
                        direction = 'straight'

        elif car_angle3 and car_angle2:
            logger.debug("情形三：仅已知二三图目标车辆车牌角度")
            cx3 = (img3_car_ps[0] + img3_car_ps[2]) / 2
            cx2 = (img2_car_ps[0] + img2_car_ps[2]) / 2
            if (car_angle2 > 0 and car_angle3 < 0) or (car_angle2 < 0 and car_angle3 > 0):
                sup_angle = 180 - abs(car_angle2) - abs(car_angle3)
            else:
                sup_angle = abs(car_angle2 - car_angle3)
            if (car_angle3 < 0 and car_angle2 < 0) or (car_angle3 > 0 and car_angle2 > 0):
                dist_23 = car_angle3 - car_angle2
                if (0 > car_angle3 > -60 and cx3 < cx2) or (
                        0 > car_angle3 > -75 and (dist_23 > 12 or dist_23 < -20) and cx3 < cx2):
                    direction = 'left'
                elif (0 < car_angle3 < 60 and cx3 > cx2) or (
                        0 < car_angle3 < 75 and (dist_23 < -12 or dist_23 > 20) and cx3 > cx2):
                    direction = 'right'
                else:
                    direction = 'straight'
            elif sup_angle > 25:
                if 0 > car_angle3 > -80 and cx3 < cx2:
                    direction = 'left'
                elif 0 < car_angle3 < 80 and cx3 > cx2:
                    direction = 'right'
                else:
                    direction = 'straight'
        elif car_angle1 and car_angle2 and img3_car_head_cor:
            logger.debug("情形四：已知一二图目标车辆车牌角度和三图目标车辆坐标")
            cx1 = img1_car_head_cor[0]
            cx2 = img2_car_head_cor[0]
            cx3 = img3_car_head_cor[0]
            cy3 = (img3_car_ps[1] + img3_car_ps[3]) / 2

            dist_judge = cx2 - cx1
            dist_judge32 = cx3 - cx2
            dist_judge31 = cx3 - cx1

            # print(dist_judge, dist_judge32, round(dist_judge32 / dist_judge, 3))
            # print("@@@",(img3_car_ps[3]-img1_car_ps[3])/dist_judge31)
            if (car_angle1 > 0 and car_angle2 < 0) or (car_angle1 < 0 and car_angle2 > 0):
                sup_angle = 180 - abs(car_angle1) - abs(car_angle2)
            else:
                sup_angle = abs(car_angle1 - car_angle2)
            car_w = img1_car_ps[2] - img1_car_ps[0]
            car_h = img1_car_ps[3] - img1_car_ps[1]

            car2_w = img2_car_ps[2] - img2_car_ps[0]

            car3_w = img3_car_ps[2] - img3_car_ps[0]
            car3_h = img3_car_ps[3] - img3_car_ps[1]

            # 3图车辆长宽比
            car3_hw = round(car3_h / car3_w, 3)
            if abs(dist_judge31 / (dist_judge + 0.1)) > 8 and abs(dist_judge31) > car_w and car3_hw < 0.6:
                x_judge = True
            if car_angle1 < -75:  # 达到偏移角度的最大值
                bias_angle = -car_angle1 - 90
                angle2 = car_angle2 + bias_angle
                angle2 = angle2 if angle2 > -90 else 180 - abs(angle2)
                if (0 < angle2 < 75 and dist_judge31 > 0) or (
                        angle2 > 0 and sup_angle > 15 and dist_judge31 > 0.4 * min((car_w, car2_w, car3_w))) or (
                        (car_angle1 > car_angle2 >= -90 or car_angle2 > 0) and min(
                    (car_w, car2_w, car3_w)) * 0.4 < 1.05 * abs(
                    dist_judge) < dist_judge32 and cy3 > img_list[2].shape[0] * 0.3):
                    direction = 'right'
                elif (0 > angle2 > -75 and dist_judge31 < 0) or (
                        car_angle1 < car_angle2 < 0 and dist_judge32 < 2 * -abs(dist_judge) < min(
                    (car_w, car2_w, car3_w)) * -0.4 and cy3 >
                        img_list[2].shape[0] * 0.3):
                    direction = 'left'
                else:
                    direction = 'straight'
            elif car_angle1 > 75:
                bias_angle = 90 - car_angle1
                angle2 = car_angle2 + bias_angle
                angle2 = angle2 if angle2 < 90 else abs(angle2) - 180
                if (0 < angle2 < 75 and dist_judge31 > 0) or (
                        car_angle1 > car_angle2 > 0 and dist_judge32 > 2 * abs(dist_judge) > min(
                    (car_w, car2_w, car3_w)) * 0.4 and cy3 >
                        img_list[2].shape[0] * 0.25):
                    direction = 'right'
                elif (0 > angle2 > -75 and dist_judge31 < 0) or (
                        angle2 < 0 and sup_angle > 15 and dist_judge31 < -0.4 * min((car_w, car2_w, car3_w))) or (
                        (car_angle1 < car_angle2 <= 90 or car_angle2 < 0) and dist_judge32 < -1.05 * abs(
                    dist_judge) < min((car_w, car2_w, car3_w)) * -0.4 and cy3 > img_list[2].shape[0] * 0.3):
                    direction = 'left'
                else:
                    direction = 'straight'
            else:
                logger.debug("情形四待调试部分")
                if car_angle2 < 0 and (car_angle2 > -80 and dist_judge < -car_w * 0.3) or (
                        car_angle2 > -70 and dist_judge < 0) or (
                        dist_judge32 < 4 * -abs(dist_judge) and cy3 > img_list[2].shape[0] * 0.3):
                    direction = 'left'
                elif car_angle2 > 0 and (car_angle2 < 80 and dist_judge > car_w * 0.3) or (
                        car_angle2 < 70 and dist_judge > 0) or (
                        dist_judge32 > 4 * abs(dist_judge) and cy3 > img_list[2].shape[0] * 0.3):
                    direction = 'right'
                else:
                    direction = 'straight'
            if x_judge:
                logger.debug(f"x_judge:{x_judge} direction:{direction}")
                if dist_judge31 < 0:
                    direction = 'left'
                elif dist_judge31 > 0:
                    direction = 'right'
                else:
                    direction = 'stop'
        judgeres.add_act_details(self._cls_name, {"drv_direction": direction})


# 车内人员违法识别
class DriverVioJudAct(JudgeAct):
    def __init__(self, name, conf, params, model_dict):
        super().__init__(name, conf, params, model_dict)
        self._conf = conf
        self._cls_name = self.__class__.__name__
        self._cls_model = model_dict["BeltPhoneModel"]
        self._det_model = model_dict["PhoneHandModel"]
        # 配置参数
        self._no_person_waste = params.get("no_person", True)  # 车内未检测到有效人员是否作废
        self._belt_thresh = params.get("belt_thresh", 0.92)  # 安全带分类模型阈值
        self._phone_thresh = params.get("phone_thresh", 0.95)  # 打电话分类模型阈值 TODO:预设检出检准版本参数
        self._add_phone_logic = params.get("add_phone_logic", True)  # 打电话场景新增检测模型和判罚逻辑
        self._add_phone_side = params.get("add_phone_side", True)  # 添加侧面手机目标进行违法判定
        self._ph_det_thresh = params.get("ph_det_thresh", 0.1)  # 手机和手检测模型的阈值
        self._ph_filter_thresh = params.get("ph_filter_thresh", [0.2, 0.15])  # 过滤小于此值的目标[phone,phone_s]
        self._ph_logic_mode = params.get("ph_logic_mode", 2)  # 1:高检出模式 2：均衡模式 3：强过滤模式
        self._ph_mix_rate = params.get("ph_mix_rate", 0.1)  # 手机和手重叠部分占手机的比例（强过滤模式逻辑）

    def _run(self, judgeres: JudgeResult) -> None:
        # ObjectProcess.save_object(judgeres.act_details,fr"C:\Users\<USER>\Desktop\AIJudge_Unit_Test\pkl_data\{judgeres.request.plate_no}_phone_details.pkl")
        self.init_judgeres_core(judgeres)  # 初始化判罚结果
        img_keys = judgeres.get_act_detail("img_keys")  # 注意图片配置统一
        img_list = judgeres.get_act_detail("split_img")  # 获取图片
        target_veh_boxes = judgeres.get_act_detail("target_veh_box", {})  # 获取目标车辆
        judge_position = []
        has_person = False
        for idx, img_key in enumerate(img_keys):
            img = img_list[idx]
            target_veh_box = target_veh_boxes.get(img_key)  # TODO:规则需要和动作融合
            if target_veh_box is None:
                continue
            person_boxes = judgeres.get_act_detail("person_box", {}).get(img_key, [])
            # 分类主驾和副驾的车内人员坐标框
            target_person_dict = self.judge_person_position(person_boxes, target_veh_box)  # 分类车内人员
            judge_position = self.vio_judge_position()  # 需判定的车内人员类型（主/副驾）
            for pos in judge_position:
                person_box = target_person_dict.get(pos, [])
                if person_box:
                    has_person = True
                    res = self.run_cls_model(img, person_box)  # 分类模型运行
                    judgeres.logger.debug(f"{img_key}-{person_box}-分类模型结果:\n{res}")
                    self.set_cls_model_judgeres(res, pos, judgeres)  # 分类模型结果
                    self.phone_logic_judge(img, target_veh_box, judgeres)  # 手和手机位置判定逻辑
                    if not self._is_waste:
                        break
        if not has_person and not self._no_person_waste:
            judgeres.set_judres_core(1, 0.3, "vio_001", "未检测到目标车内人员置为违法")
        judgeres.add_act_details(self._cls_name, {"judge_position": judge_position})

    def init_judgeres_core(self, judgeres: JudgeResult) -> None:
        self._match_code = judgeres.request.match_viocode
        self._judge_code = judgeres.request.judge_code
        self._is_waste = True
        self._logger = judgeres.logger
        if self._match_code == "1101":
            judgeres.set_judres_core(2, self._belt_thresh, "no_vio", "作废，无未系安全带行为")
        elif self._match_code == "1223":
            judgeres.set_judres_core(2, self._phone_thresh, "no_vio", "作废，无开车打电话行为")

    # 判断车内人员的位置
    def judge_person_position(self, person_boxes: List[list], vehicle_box: list) -> Dict[str, list]:
        target_person = {}
        for person_box in person_boxes:
            px_min, py_min, px_max, py_max = person_box
            vx_min, vy_min, vx_max, vy_max = vehicle_box
            # 计算人的中心点
            person_center_x = (px_min + px_max) / 2
            # 计算车辆的中心点
            vehicle_center_x = (vx_min + vx_max) / 2
            # 判断是否完全在车内
            if (px_min >= vx_min and py_min >= vy_min and
                    px_max <= vx_max and py_max <= vy_max):
                # 在车内，进一步判断是在左侧还是右侧
                if person_center_x < vehicle_center_x:
                    self.update_person_dict(target_person, "副驾", person_box)
                else:
                    self.update_person_dict(target_person, "主驾", person_box)

        return target_person

    # 记录更新主副驾人员信息
    def update_person_dict(self, target_person: dict, position: str, person_box) -> None:
        if position not in target_person:
            target_person[position] = person_box
        else:
            old_person_box = target_person[position]
            old_area = (old_person_box[2] - old_person_box[0]) * (old_person_box[3] - old_person_box[1])
            new_area = (person_box[2] - person_box[0]) * (person_box[3] - person_box[1])
            if new_area > old_area:
                target_person[position] = person_box

    # 判断是否关注副驾驶违法行为
    def vio_judge_position(self) -> List[str]:
        if self._match_code == "1101":
            judge_pos = ["主驾", "副驾"] if self._is_copilot_violation() else ["主驾"]
        else:
            judge_pos = ["主驾"]
        return judge_pos

    # 根据配置和违法代码判定是否需要关注副驾违法
    def _is_copilot_violation(self) -> bool:
        return self._conf.Copilot_Belt_Is_Vio or (
                self._conf.Copilot_Belt_Is_Vio is None and
                self._judge_code in self._conf.Copilot_Belt_Vio_Code
        )

    def run_cls_model(self, img: np.ndarray, person_box: List) -> ModelResult:
        person_img = img[person_box[1]:person_box[3], person_box[0]:person_box[2]]
        thresh = self._belt_thresh if self._match_code == "1101" else self._phone_thresh
        model_res: ModelResults = self._cls_model.run(person_img, thresh=thresh)
        res = model_res.get_results()[0]
        return res

    def set_cls_model_judgeres(self, res: ModelResult, pos: str, judgeres: JudgeResult) -> None:
        if self._match_code == "1101" and res.subclass in ["belt", "belt_phone"]:
            judgeres.set_judres_core(1, res.score, "belt_vio", f"{pos}未系安全带违法")
            self._is_waste = False
        elif self._match_code == "1223" and res.subclass in ["phone", "belt_phone"]:
            judgeres.set_judres_core(1, res.score, "phone_vio", f"{pos}开车打电话违法")
            self._is_waste = False

    # 手机、手检测模型推理
    def phone_logic_judge(self, img: np.ndarray, target_veh_box: List, judgeres: JudgeResult):
        if self._is_waste and self._add_phone_logic and self._match_code == "1223":
            judge_img, x1, y1 = crop_driver_area(img, target_veh_box)
            res: ModelResults = self._det_model.run(judge_img, thresh=self._ph_det_thresh)
            self._logger.debug(f"x_bias:{x1} y_bias:{y1}\n手机检测模型输出：{res}")
            cls_name = "all_phone" if self._add_phone_side else "phone"
            phone_obj = res.get_results(superclass=cls_name)
            hand_obj = res.get_results(superclass="hand")
            self._logger.info(f"手机数量：{len(phone_obj)} 手数量:{len(hand_obj)}")
            self.phone_hand_pos_judge(phone_obj, hand_obj, judgeres)

    # 手持电话逻辑判定
    def phone_hand_pos_judge(self, phone_obj: List[ModelResult], hand_obj: List[ModelResult], judgeres: JudgeResult):
        if phone_obj and hand_obj:
            phone_boxes = [obj.box for obj in phone_obj]
            hand_boxes = [obj.box for obj in hand_obj]
            iou_res = IOU.compute_iou(phone_boxes, hand_boxes)
            idx1, idx2 = np.where(iou_res > 0)
            if self._ph_logic_mode == 1 and len(idx1):
                self._logger.info("检出模式判定违法")
                self._is_waste = False
                judgeres.set_judres_core(1, 0.7, "vio_recall_mode", "正片，检出模式判定违法")
                return None
            for i, j in zip(idx1, idx2):
                phone_box = phone_boxes[i]
                phone_score = phone_obj[i].score  # 阈值筛选
                phone_label = phone_obj[i].subclass
                if phone_label == "phone" and phone_score < self._ph_filter_thresh[0]:
                    continue
                elif phone_label == "phone_s" and phone_score < self._ph_filter_thresh[1]:
                    continue
                hand_box = hand_boxes[j]
                phone_w = max(phone_box[2] - phone_box[0], 1e-3)
                phone_h = phone_box[3] - phone_box[1]
                phone_cy = (phone_box[1] + phone_box[3]) // 2
                hand_cy = (hand_box[1] + hand_box[3]) // 2
                judge_res = phone_cy <= hand_cy + 0.1 * phone_h
                if self._ph_logic_mode == 2 and judge_res:
                    self._logger.info("均衡模式判定违法")
                    judgeres.set_judres_core(1, 0.8, "vio_static_mode", "正片，均衡模式判定违法")
                    self._is_waste = False
                    return None
                inter_w = min(phone_box[2], hand_box[2]) - max(phone_box[0], hand_box[0])
                inter_rate = inter_w / phone_w
                if self._ph_logic_mode == 3 and judge_res and inter_rate > self._ph_mix_rate:
                    self._logger.info("强过滤模式判定违法")
                    judgeres.set_judres_core(1, 0.9, "vio_filter_mode", "正片，强过滤模式判定违法")
                    self._is_waste = False
                    return None


class BkgFusionJudAct(JudgeAct):
    def __init__(self, name, conf, params, model_dict):
        super().__init__(name, conf, params, model_dict)
        self._conf = conf
        self._cls_name = self.__class__.__name__
        self._seg_model = model_dict["SegModel"]
        self._superclass_dict: dict = self._seg_model.conf.superclass
        self._det_thresh = params.get("det_thresh", 0.15)  # 模型检测阈值
        self._det_cls = params.get("det_cls", self.init_det_cls())  # 背景图需检测的类别
        self._bkg_det = params.get("bkg_det", True)  # 是否进行无车背景图推理
        self._sort_merge = params.get("sort_merge", False)  # 是否分类融合结果
        self.judge_has_superclass()  # 判断待检测的类别有无超类配置

    def _run(self, judgeres: JudgeResult) -> None:
        img_list = judgeres.get_act_detail("split_img")
        img_keys = [f"img{i + 1}" for i in range(len(img_list))]
        if self._bkg_det:
            veh_mask_list = [judgeres.get_tfc_elements(img_key, "bkg_fusion", "mask") for img_key in
                             img_keys]  # 存放车辆mask信息
            bkg_img = self.get_non_veh_bkg(img_list, veh_mask_list)
            model_results: ModelResults = self._seg_model.run(bkg_img, thresh=self._det_thresh, det_cls=self._det_cls)
            judgeres.add_act_details(self._cls_name, {"bkg_img": bkg_img})
        else:
            model_results = ModelResults()
        # judgeres.logger.debug(f"无车背景图识别交通元素结果：",model_results)
        # 融合每张图的交通元素识别结果
        for img_key in img_keys:
            model_results.merge_results(judgeres.tfc_elements[img_key])
        tfc_element_dict = self.process_model_results(model_results)
        judgeres.add_act_details(self._cls_name, {"merge_tfc_element": tfc_element_dict})

    # 将多张图片中的非车辆部分抠出并融合到背景图中
    @staticmethod
    def get_non_veh_bkg(images: List[np.ndarray], masks_list: List[List[np.ndarray]]) -> np.ndarray:
        if len(images) == 0 or len(masks_list) != len(images):
            raise ValueError("图片和mask列表的数量不匹配")
        img_height, img_width, _ = images[0].shape
        background_image = np.zeros((img_height, img_width, 3), dtype=np.uint8)  # 或者加载一张实际的背景图片
        for idx, (img, masks) in enumerate(zip(images, masks_list)):
            combined_mask = np.ones((img_height, img_width), dtype=bool)
            # 对每个mask进行处理
            for mask_idx, mask in enumerate(masks):
                # 确保mask是布尔类型
                if not mask.dtype == bool:
                    mask = mask.astype(bool)
                mask_uint8 = (~mask).astype(np.uint8) * 255  # 取反，得到非车辆部分的mask
                # 结合所有mask
                combined_mask = np.logical_and(combined_mask, mask_uint8.astype(bool))
            # 将combined_mask转换为uint8类型以便使用cv2.bitwise_and
            combined_mask_uint8 = combined_mask.astype(np.uint8)
            # 抠出非车辆部分
            non_vehicle_part = cv2.bitwise_and(img, img, mask=combined_mask_uint8)
            # 将非车辆部分添加到背景图中
            background_image[combined_mask] = non_vehicle_part[combined_mask]
        return background_image

    def init_det_cls(self) -> List:
        det_cls = self._superclass_dict.get("tfc_element", [])
        return list(set(det_cls))

    # 判断待检测的类别列表有无超类配置
    def judge_has_superclass(self) -> None:
        self._merge_superclass = None
        cls_list = []
        for superclass, subclasses in self._superclass_dict.items():
            self.judge_multi_superclass(superclass, subclasses, self._det_cls, cls_list)
            if subclasses == self._det_cls:
                self._merge_superclass = [superclass]
                return
        self._merge_superclass = cls_list

    @staticmethod
    def judge_multi_superclass(superclass: str, subclasses: List, det_cls: List, cls_list: List) -> None:
        if set(subclasses).issubset(set(det_cls)):
            cls_list.append(superclass)

    def process_model_results(self, model_results: ModelResults) -> Dict[str, List[ModelResult]]:
        tfc_element_dict = {}
        if self._sort_merge:  # 每个子类分开存放
            for cls in self._det_cls:
                model_result_list = model_results.get_results(superclass=cls)
                if model_result_list:
                    tfc_element_dict[cls] = model_result_list
        elif self._merge_superclass:  # 按超类存放
            for super_class in self._merge_superclass:
                model_result_list = model_results.get_results(superclass=super_class)
                if model_result_list:
                    tfc_element_dict[super_class] = model_result_list
        else:
            model_result_list = []
            for cls in self._det_cls:
                model_result_list += model_results.get_results(superclass=cls)
            if model_result_list:
                tfc_element_dict["all_element"] = model_result_list
        return tfc_element_dict


class VioRuleJudAct(JudgeAct):
    def __init__(self, name, conf, params, model_dict):
        super().__init__(name, conf, params, model_dict)
        self._conf = OptConf
        self._cls_name = self.__class__.__name__

    def _run(self, judgeres: JudgeResult) -> None:
        for ruler in self._rulers:  # 机动车子类调用
            ruler.run(judgeres)


def create_judge_act(module_name, act_name, conf_name, param, model_dict):
    return eval(module_name)(act_name, create_conf(conf_name), param, model_dict)
