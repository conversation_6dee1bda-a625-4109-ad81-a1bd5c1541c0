# 违反禁止标示线场景v2
import os
import time

import cv2
import numpy as np

from init_models import *
import math


# 逆行场景主函数
def judge_vio(src_plate_num, file_list, weights_list, resize_scale=0.7, merge=True, save=False,
              save_path='', highway=False, extra_msg=None):
    error_path = f"{save_path}/no_vio"
    vio_path = f"{save_path}/vio"
    if save:
        os.makedirs(error_path, exist_ok=True)
        os.makedirs(vio_path, exist_ok=True)
    # 读取模型
    plate_det_model = weights_list[0][0]
    plate_rec_model = weights_list[0][1]
    demo = weights_list[1]
    lp_model = weights_list[2][0]
    lp_meta = weights_list[2][1]
    yolov7_model = weights_list[3][0]
    yolov7_meta = weights_list[3][1]
    model_cnn = weights_list[-2]
    vio_model = weights_list[-1]

    vio_judge = 'no_judge'
    try:
        split_mode = extra_msg[0]
        black_height = extra_msg[1]
        black_pos = extra_msg[2]
        iieaAuditMode = extra_msg[3]
        iieaFilterVehicle = extra_msg[4]
    except:
        split_mode = 111
        black_height = 0
        black_pos = None
        iieaAuditMode = None
        iieaFilterVehicle = None
    judge_infos = {'plate_num': src_plate_num,
                   'vio_code': 1345,
                   'vio_judge': vio_judge,
                   'zebra_line': {},
                   'police': 0,
                   'has_crossline': 0,
                   'vehicle_location': {},
                   'drv_direction': 0,
                   'drive_direction_light': [],
                   'lamp': [],
                   'stop_line': 0,
                   'lane': [],
                   'target_car': [],
                   'judgeConf': 1.0,
                   'modelDetectResults': [], 'split_mode': split_mode, "black_height": black_height,
                   "black_pos": black_pos, "iieaAuditMode": iieaAuditMode, "iieaFilterVehicle": iieaFilterVehicle}
    # 特殊车牌判定
    if not src_plate_num:
        vio_judge = "no_vio_009"
        judge_infos['vio_judge'] = vio_judge
        return vio_judge, judge_infos
    if src_plate_num[0] not in chars:
        vio_judge = 'no_vio_002'
        judge_infos['vio_judge'] = vio_judge
        return vio_judge, judge_infos
    vio_judge = VIO_JUDGE_TOOL().spe_no_vio_judge(src_plate_num, iieaFilterVehicle)
    if vio_judge.startswith('no_vio'):
        judge_infos['vio_judge'] = vio_judge
        return vio_judge, judge_infos
    vio_judge = 'vio'

    no_car_list = []
    plate_list = []
    car_ps_list = []
    tf_ps_list = []
    tf_masks_list = []
    tf_labels_list = []
    target_car_list = []
    plate_cor_list = []
    # iiea审核模式
    if iieaAuditMode is None:
        judge_mode = Precision_Judge_Mode[str(judge_infos['vio_code'])]
    else:
        judge_mode = iieaAuditMode
    judge_thresh = Judge_Thresh[str(judge_infos['vio_code'])][int(judge_mode)]
    if judge_thresh is None:
        judge_thresh = [0.5, 5, 50] if judge_mode else [0.25, 15, 150]
    for i, img in enumerate(file_list):
        if i >= 3:
            continue
        temp_car = {}
        judge_infos['img_id'] = i + 1
        # 找目标车
        main_module = BlendMaskModule(img, weights_list, judge_thresh, src_plate_num, save, save_path)
        img_car_ps, plate_num, vio_judge, no_car_img, tf_ps, tf_masks, tf_labels, plate_cor = main_module.match_target_car(
            vio_judge, judge_infos)
        target_car_list.append(img_car_ps)
        no_car_list.append(no_car_img)
        plate_cor_list.append(plate_cor)
        tf_ps_list += tf_ps
        tf_masks_list += tf_masks
        tf_labels_list += tf_labels
        temp_car['plate_num'] = plate_num
        temp_car['car_ps'] = img_car_ps
        judge_infos['target_car'].append(temp_car)
        judge_infos['lane'].append(tf_ps)
        judge_infos['lamp'].append(tf_labels)
        try:
            x_ratio = ((img_car_ps[0] + img_car_ps[2]) / 2) / img.shape[1]
        except:
            x_ratio = 0
        # 0 代表左边， 1代表右边
        if x_ratio <= 0.5:
            judge_infos['vehicle_location'] = 0
        else:
            judge_infos['vehicle_location'] = 1
        plate_list.append(plate_num)
        car_ps_list.append(img_car_ps)
        if vio_judge.startswith("no_vio"):
            break
    if plate_list.count("NO_DETECT") == len(plate_list):
        vio_judge = 'no_vio_006'
        judge_infos['vio_judge'] = vio_judge
        judge_infos['judgeConf'] = 0.9
        # return vio_judge, judge_infos
    if vio_judge.startswith("vio"):
        # 识别叠加图的交通标志
        module = BlendMaskModule(file_list[0], weights_list, judge_thresh, src_plate_num, save=save,
                                 save_path=save_path)
        merge_tf_ps, merge_tf_masks, merge_tf_labels, merge_img = module.merge_detect(
            no_car_list)

        tf_ps_list += merge_tf_ps
        tf_masks_list += merge_tf_masks
        tf_labels_list += merge_tf_labels
        # 逻辑判定
        if tf_masks_list:
            t1 = time.time()
            vio_judge = module.logic_judge(tf_masks_list, target_car_list, plate_cor_list)
            print("逻辑总耗时：", time.time() - t1)
        else:
            vio_judge = "no_vio_001"
            judge_infos['judgeConf'] = 0.75
        if save and merge_tf_ps:
            try:
                vis_path = error_path if vio_judge.startswith("no_vio") else vio_path
                for idx, box in enumerate(merge_tf_ps):
                    pt1 = (box[0], box[1])
                    pt2 = (box[2], box[3])
                    if merge_tf_labels[idx] == "lane2":
                        color = (0, 255, 255)
                    elif merge_tf_labels[idx] == "lane1":
                        color = (255, 255, 0)
                    else:
                        color = (255, 0, 255)
                    # y, x = np.where(merge_tf_masks[idx] > 0)
                    # merge_img[y, x, 0] = 0
                    # merge_img[y, x, 1] = 255
                    # merge_img[y, x, 2] = 0

                    cv2.rectangle(merge_img, pt1, pt2, color, 2)
                    cv2.putText(merge_img, merge_tf_labels[idx], (box[0], (box[1] + box[3]) // 2),
                                cv2.FONT_HERSHEY_SIMPLEX,
                                1.5,
                                color, 2)
                cv2.imwrite(f"{vis_path}/{vio_judge}_{src_plate_num}_merge.jpg", merge_img)
            except:
                msg = catch_error()
                print(msg)
    judge_infos['vio_judge'] = vio_judge
    if vio_judge.startswith('vio'):
        judge_infos['judgeConf'] = 0.8
    if save:
        try:
            vis_path = error_path if vio_judge.startswith("no_vio") else vio_path
            for idx1, boxes in enumerate(judge_infos["lane"]):
                img = file_list[idx1]
                labels = judge_infos["lamp"][idx1]
                target_car = target_car_list[idx1]
                if target_car:
                    cv2.rectangle(img, (target_car[0], target_car[1]), (target_car[2], target_car[3]), (0, 255, 0), 2)
                for idx2, box in enumerate(boxes):
                    pt1 = (box[0], box[1])
                    pt2 = (box[2], box[3])
                    if labels[idx2] == "lane2":
                        color = (0, 255, 255)
                    elif labels[idx2] == "lane1":
                        color = (255, 255, 0)
                    else:
                        color = (255, 0, 255)
                    cv2.rectangle(img, pt1, pt2, color, 2)
                    cv2.putText(img, labels[idx2], (box[0], (box[1] + box[3]) // 2), cv2.FONT_HERSHEY_SIMPLEX, 1.5,
                                color, 2)
                cv2.imwrite(f"{vis_path}/{vio_judge}_{src_plate_num}_{idx1 + 1}.jpg", img)
        except Exception as e:
            erro_msg = catch_error()
            print(erro_msg)
    return vio_judge, judge_infos


class BlendMaskModule:
    def __init__(self, img, model_list, judge_thresh, src_plate="", save=False, save_path=""):
        self.img = img
        self.model_list = model_list
        self.src_plate = src_plate
        self.device = model_list[3][1][2]
        self.save = save
        self.save_path = save_path
        self.tf_cls = ['lane1', 'lane2', 'stop_area']
        self.judge_thresh = judge_thresh
        self.merge_img = None

    def match_target_car(self, vio_judge, judge_infos):
        demo = self.model_list[1]
        plate_det_model = self.model_list[0][0]
        plate_rec_model = self.model_list[0][1]
        yolov7_model = self.model_list[3][0]
        yolov7_meta = self.model_list[3][1]
        # yolo检测车辆 TODO:两个模型耗时大
        t1 = time.time()
        _, car_bbox_ps, labels_list, score_list = yolov7_detect(self.img, yolov7_model, yolov7_meta[0],
                                                                yolov7_meta[1],
                                                                yolov7_meta[2], conf_thres=0.1, need_conf=True)
        t2 = time.time()

        # if not car_bbox_ps:
        #     vio_judge = "no_vio_004"
        #     return [], "NO_CAR", vio_judge, self.img.copy(), [], [], [], []

        det1 = BlendMaskDetect(self.img)
        predictions1, kp_mask1 = det1.mask_predict(demo)
        nms_ls1 = det1.nms_class_distinct(predictions1["instances"].pred_boxes.tensor,
                                          predictions1["instances"].scores,
                                          predictions1["instances"].pred_classes,
                                          threshold=0.8,
                                          class_ditinct_threshold=0.85)
        predictions1, kp_mask1 = det1.prediction_nms_filter(predictions1, nms_ls1, kp_mask1)

        _, mask_list, _, _ = det1.class_info2(predictions1, Blend_Judge_Vehicle,
                                              conf_thresh=0.1,
                                              need_mask=True)
        # 筛选交通标志
        tf_ps, tf_masks, tf_labels, _ = det1.class_info2(predictions1, self.tf_cls, conf_thresh=self.judge_thresh[0],
                                                         need_mask=True)
        t3 = time.time()

        # 抠出车辆区域
        no_car_img = self.get_no_cars_img(mask_list)
        t4 = time.time()
        img_car_ps, plate_num, img_plate_dict, new_car_bbox_ps, first_plate_conf = CarJudge(
            iiea_cfg=judge_infos["iieaFilterVehicle"], need_first_plate=True).confirm_img1_car_ps(self.img,
                                                                                                  self.src_plate,
                                                                                                  plate_det_model,
                                                                                                  plate_rec_model,
                                                                                                  car_bbox_ps,
                                                                                                  self.device,
                                                                                                  label=0,
                                                                                                  show=self.save,
                                                                                                  save_path=self.save_path)
        t5 = time.time()

        # 过滤老头乐
        vio_judge = filter_low_speed_veh(img_car_ps, None, None, None, self.src_plate, plate_num,
                                         first_plate_conf, feat_judge=False)
        t6 = time.time()
        print("耗时情况：", t2 - t1, t3 - t2, t4 - t3, t5 - t4, t6 - t5)
        # 返回检测框和置信度信息
        img_id = judge_infos["img_id"]
        if img_car_ps:
            target_idx = car_bbox_ps.index(img_car_ps)
            percent_coord = convert_src_coord(img_car_ps, self.img, img_id,
                                              judge_infos["split_mode"], judge_infos["black_height"],
                                              judge_infos["black_pos"])
            tmp_res = {
                "objId": img_id,
                "objType": "vehicle",
                "objConf": float(score_list[target_idx]),
                "objCoord": percent_coord
            }
            judge_infos["modelDetectResults"].append(tmp_res)
        # 车牌位置信息
        plate_cor = img_plate_dict[str(img_car_ps)] if img_car_ps else []
        plate_conf = 0
        if plate_cor:
            target_idx = str(img_car_ps) + "conf"
            plate_conf = float(img_plate_dict[target_idx])
            percent_coord = convert_src_coord(plate_cor, self.img, img_id,
                                              judge_infos["split_mode"], judge_infos["black_height"],
                                              judge_infos["black_pos"])
            tmp_res = {
                "objId": img_id,
                "objType": "plate",
                "objConf": plate_conf,
                "objCoord": percent_coord
            }
            judge_infos["modelDetectResults"].append(tmp_res)

        # 警车判定
        if plate_num.endswith("警"):
            vio_judge = 'no_vio_010'
            judge_infos['judgeConf'] = 0.9
        elif img_car_ps:
            target_idx2 = car_bbox_ps.index(img_car_ps)
            target_label = labels_list[target_idx2]
            blend_score = score_list[target_idx2]
            judge_infos['judgeConf'] = float(blend_score)
            # iiea车辆白名单
            try:
                _, ambulance_filter, fire_filter = judge_infos["iieaFilterVehicle"]
            except:
                ambulance_filter = None
                fire_filter = None
            special_type_list = []
            if ambulance_filter:
                special_type_list.extend(['ambulance_h', 'ambulance_b'])
            if fire_filter:
                special_type_list.extend(['fire_engine_h', 'fire_engine_b'])
            if ambulance_filter is None and fire_filter is None:
                special_type_list = Special_Type
            # 特殊车辆判定
            if target_label in special_type_list:
                if target_label == "ambulance_h":
                    if blend_score >= Ambulance_Head_Thresh:
                        vio_judge = "no_vio_010"
                elif blend_score >= Special_Car_Thresh:
                    vio_judge = "no_vio_010"
        return img_car_ps, plate_num, vio_judge, no_car_img, tf_ps, tf_masks, tf_labels, plate_cor

    def get_no_cars_img(self, mask_list):
        no_car_img = self.img.copy()
        for mask in mask_list:
            no_car_img[mask] = 0
        # 可视化
        # num = 1
        # save_path = f"../test_results/1345/no_car"
        # os.makedirs(save_path,exist_ok=True)
        # dst_path = f"{save_path}/{self.src_plate}_{num}.jpg"
        # while os.path.exists(dst_path):
        #     num += 1
        #     dst_path = f"{save_path}/{self.src_plate}_{num}.jpg"
        # cv2.imwrite(dst_path, no_car_img)
        return no_car_img

    def merge_detect(self, img_list):
        try:
            h, w, _ = img_list[0].shape
            # merge img
            bg_img = np.zeros((h, w, 3), dtype=np.uint8)
        except:
            bg_img = np.zeros((500, 500, 3), dtype=np.uint8)
        if len(img_list) > 1:
            try:
                for img in img_list:
                    idx = np.where(bg_img == 0)
                    bg_img[idx] = img[idx]
            except:
                erro_msg = catch_error()
                print(erro_msg)
            demo = self.model_list[1]
            det = BlendMaskDetect(bg_img)
            predictions, kp_mask = det.mask_predict(demo)
            nms_ls = det.nms_class_distinct(predictions["instances"].pred_boxes.tensor,
                                            predictions["instances"].scores,
                                            predictions["instances"].pred_classes,
                                            threshold=0.8,
                                            class_ditinct_threshold=0.85)
            predictions, kp_mask = det.prediction_nms_filter(predictions, nms_ls, kp_mask)
            merge_tf_ps, merge_tf_masks, merge_tf_labels, _ = det.class_info2(predictions, self.tf_cls,
                                                                              conf_thresh=self.judge_thresh[0],
                                                                              need_mask=True)
        else:
            merge_tf_ps = []
            merge_tf_masks = []
            merge_tf_labels = []
        self.merge_img = bg_img
        return merge_tf_ps, merge_tf_masks, merge_tf_labels, bg_img

    def find_near_dist_and_direct(self, mask_list, point_list):
        show_point = self.save
        h, w, _ = self.img.shape
        # self.img = np.zeros((h, w, 3), dtype=np.uint8)
        masks_point_list = []
        for mask in mask_list:
            # 找到掩码中值为1的像素位置
            tmp_point = np.argwhere(mask == 1)
            if len(tmp_point):
                if show_point:
                    tmp_y, tmp_x = np.where(mask == 1)
                    self.merge_img[tmp_y, tmp_x, 0] = 255
                    self.merge_img[tmp_y, tmp_x, 1] = 255
                    self.merge_img[tmp_y, tmp_x, 2] = 0

                tmp_point = tmp_point[:, ::-1]  # 横纵坐标位置互换
                masks_point_list.append(tmp_point)
        try:
            masks_point = np.concatenate(masks_point_list, 0)
        except:
            msg = catch_error()
            print("数组拼接异常退出！", msg)
            return -1
        dist_list = []
        # 判定禁行线是否在车下
        judge = False
        t1 = time.time()

        for idx, point in enumerate(point_list):
            # 判定线是否在车下
            if idx % 2 == 0:
                left_x = point[0]
                right_x = point_list[idx + 1][0]
            else:
                left_x = point_list[idx - 1][0]
                right_x = point[0]
            # 判定严重压线
            # 水平方向判断
            horizontal_cover = (left_x <= masks_point[:, 0]) & (masks_point[:, 0] <= right_x)
            # 高度差计算
            height_diff = point[1] - masks_point[:, 1]
            # 高度方向判断
            lower_bound = (height_diff <= 0) & (height_diff > -0.08 * point[2])
            upper_bound = (height_diff >= 0) & (height_diff < 0.2 * point[2])
            # 组合条件
            cover_line_judge = horizontal_cover & (lower_bound | upper_bound)
            judge_num = np.sum(cover_line_judge)
            print("judge num：", judge_num)
            if judge_num >= self.judge_thresh[2]:
                judge = True
                if show_point:
                    sp_list = masks_point[cover_line_judge]
                    for sp in sp_list:
                        cv2.circle(self.merge_img, tuple(sp), 5, (0, 255, 0), -1)
                break
            else:
                # 计算所有mask点与给定点之间的距离
                math_point = (point[0], point[1])
                distances = np.sqrt(np.sum((masks_point - math_point) ** 2, axis=1))
                tmp_min_dist = np.min(distances)
                dist_list.append(tmp_min_dist)
                if show_point:
                    # 可视化
                    color = (0, 0, 255)
                    min_mask_point = masks_point[np.argmin(distances)]
                    cv2.line(self.merge_img, min_mask_point, math_point, (255, 255, 255), 2)
                    cv2.circle(self.merge_img, min_mask_point, 5, color, -1)
                    cv2.circle(self.merge_img, math_point, 5, (255, 0, 0), -1)
                    cv2.putText(self.merge_img, f"{tmp_min_dist:.1f}", math_point, cv2.FONT_HERSHEY_SIMPLEX, 1.5, color,
                                2)
        # if show_point:
        #     save_root = f"{self.save_path}/point"
        #     os.makedirs(save_root, exist_ok=True)
        #     num = 1
        #     save_path = f"{save_root}/{self.src_plate}_{num}.jpg"
        #     while os.path.exists(save_path):
        #         num += 1
        #         save_path = f"{save_root}/{self.src_plate}_{num}.jpg"
        #     cv2.imwrite(save_path, self.merge_img)
        min_res = -1
        if judge:
            min_res = 0
        elif dist_list:
            min_res = np.min(dist_list)
        return min_res

    def logic_judge(self, tf_masks_list, target_car_list, plate_cor_list):
        h, w, _ = self.img.shape
        target_point = []
        for idx, box in enumerate(target_car_list):
            if box:
                car_w = abs(box[0] - box[2])
                car_h = abs(box[1] - box[3])
                # 修正轮胎位置的比例
                plate_cor = plate_cor_list[idx]
                if plate_cor:
                    plate_cx = (plate_cor[0] + plate_cor[2]) / 2
                    car_cx = (box[0] + box[2]) / 2
                    # 判断倾斜程度
                    dist_w = car_cx - plate_cx + 1e-5
                    dist_h = abs(box[1] - plate_cor[1])
                    tanA = dist_h / dist_w
                    angel = math.degrees(math.atan(tanA))
                    bias_dist = dist_w * Bias_Rate
                    plate_w = plate_cor[2] - plate_cor[0]

                else:
                    bias_dist = angel = plate_w = 0
                print("bias_dist:", bias_dist, " angel:", angel)
                angel_rate = 1 - abs(angel) / 90
                if 55 < angel < 78:  # 向右倾斜 添加车牌参照物修正 添加车牌参照物修正 限制大倾斜角度
                    target_point += [(min(int(box[0] + angel_rate * car_w),
                                          max(int(plate_cor[0] - plate_w * angel_rate), box[0])), box[3], car_h),
                                     (int(box[2] - bias_dist), box[3], car_h)]
                elif -78 < angel < -55:  # 向左倾斜
                    target_point += [(int(box[0] - bias_dist), box[3], car_h),
                                     (max(int(box[2] - angel_rate * car_w),
                                          min(int(plate_cor[2] + plate_w * angel_rate), box[2])), box[3], car_h)]
                else:
                    if 0 > angel >= -55 or 0 < angel <= 55:
                        rate = 0
                    else:
                        rate = 0.01 + angel_rate if angel > 0 else 0.01
                    target_point += [(int(box[0] + rate * car_w), box[3], car_h),
                                     (int(box[2] - rate * car_w), box[3], car_h)]

        min_dist = self.find_near_dist_and_direct(tf_masks_list, target_point)
        print("min_dist:", min_dist, "vio_thresh:", self.judge_thresh[1])
        if 0 <= min_dist < self.judge_thresh[1]:
            return "vio"
        else:
            return "no_vio"
