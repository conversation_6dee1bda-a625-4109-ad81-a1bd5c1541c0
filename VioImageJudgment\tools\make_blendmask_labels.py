import os

os.environ["CUDA_VISIBLE_DEVICES"] = "0"
import sys

sys.path.append('..')
import json
import cv2
import numpy as np
from ai_judge_code_gy.blendmask_detect import BlendMaskModel, BlendMaskDetect
from main_config.config import _LABEL
from tqdm import tqdm


# 获取mask转换二值图的轮廓信息
def find_contours(mask_pic):
    gray = mask_pic
    ret, thresh = cv2.threshold(gray, 200, 255, 0)
    contours, _ = cv2.findContours(thresh, cv2.RETR_TREE,
                                   cv2.CHAIN_APPROX_SIMPLE)
    contour = np.array([])
    if len(contours):
        # 轮廓面积筛选
        area_list = []
        for i in contours:
            area_list.append(cv2.contourArea(i))
        idx = np.argmax(area_list)
        # 轮廓近似
        epsilon = 1  # 精度
        approx = cv2.approxPolyDP(contours[idx], epsilon, True)
        # print(contours[idx].shape, approx.shape)
        contour = np.squeeze(approx)

    return contour.tolist()


def make_labelme_json(masks, labels, img_name, h, w, save_path):
    base_json = {
        "version": "4.5.13",
        "flags": {},
        "shapes": [],
        "imagePath": f"{img_name}.jpg",
        "imageData": None,
        "imageHeight": h,
        "imageWidth": w
    }

    for idx, mask in enumerate(masks):
        class_name = _LABEL[labels[idx]]
        mask_pic = np.array(mask, dtype=np.uint8) * 255
        contour = find_contours(mask_pic)
        contours_msg = {"label": class_name,
                        "points": contour,
                        "group_id": None,
                        "shape_type": "polygon",
                        "flags": {}}
        base_json["shapes"].append(contours_msg)

    with open(f"{save_path}/{img_name}.json", "w", encoding="utf-8") as f:
        json.dump(base_json, f, indent=2)
        print(f"{img_name}.json is saved!")


def split_img(img, mode):
    h, w = img.shape[:2]
    if mode == "nj":
        black_h = (h - 4530) + 210
        if black_h < 140:
            black_h = 140
        cx = w // 2
        cy = (h - black_h) // 2
        img1 = img[:cy, :cx]
        img2 = img[:cy, cx:]
        img3 = img[cy:2 * cy, :cx]
        img4 = img[cy:2 * cy, cx:]
        file_list = [img1, img2, img3]
    elif mode == "nc":
        split_w = w // 3
        img1 = img[:, :split_w]
        img2 = img[:, split_w:2 * split_w]
        img3 = img[:, 2 * split_w:]
        file_list = [img1, img2, img3]
    else:
        file_list = [img]
    return file_list


if __name__ == '__main__':
    img_dir = "/home/<USER>/project/ai_judge_simple/test_dir/nc_data"
    img_list = os.listdir(img_dir)
    demo = BlendMaskModel().load_model()
    save_path = "/home/<USER>/project/add_data_merge_nc_data"
    os.makedirs(save_path, exist_ok=True)
    for img_name in tqdm(img_list):
        image = cv2.imread(f"{img_dir}/{img_name}")

        for idx, img in enumerate(split_img(image, "nc")):
            h, w = img.shape[:2]
            det = BlendMaskDetect(img)
            predictions, _ = det.mask_predict(demo, show=False)
            nms_ls = det.nms(predictions["instances"].pred_boxes.tensor, predictions["instances"].scores,
                               threshold=0.9)
            predictions = det.prediction_nms_filter(predictions,nms_ls)
            label_idx_list = predictions["instances"].pred_classes.cpu().numpy().tolist()
            masks = predictions["instances"].pred_masks.cpu().numpy()
            make_labelme_json(masks, label_idx_list, img_name.split(".")[0] + f"_{idx + 1}", h, w, save_path)
            cv2.imwrite("/home/<USER>/project/add_data_merge_nc_data/" + img_name.split(".")[0] + f"_{idx + 1}.jpg", img)
