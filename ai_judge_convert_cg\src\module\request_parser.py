# from __future__ import annotations
import time
from typing import Dict, <PERSON>, <PERSON><PERSON>, Iterator, Any, NoReturn, Optional, Union
import json
from kafka.consumer.fetcher import ConsumerRecord
import uuid
from datetime import datetime
from src.utils.tools import exception_handler, init_logger, throw_exception, ExceptCode
from src.utils.time_util import TimeUtil
from conf.config import OptConf
import numpy as np
from pydantic import BaseModel, model_validator
import inspect


class IIEAMsg(BaseModel):
    iieaUUID: str
    iieaSendTime: Optional[str]
    iieaMergeType: Optional[int]
    iieaMergeRect: Optional[str]
    iieaAuditMode: Optional[str]
    iieaFilterVehicle: str
    @model_validator(mode='before')
    def set_defaults(cls, values: Dict[str, Any]) -> Dict[str, Any]:
        default_values = {
            'iieaUUID': str(uuid.uuid4()),
            'iieaSendTime': None,
            'iieaMergeType': values["merge_type_conf"],
            'iieaMergeRect': None,
            'iieaAuditMode': values["judge_mode_conf"],
            'iieaFilterVehicle': "#".join(OptConf.To_IIEAVeh_Cfg),
        }
        for field, default in default_values.items():
            if field not in values or values[field] is None:
                values[field] = default
        return values

class RequestCtx:
    def __init__(self):
        self._logger = init_logger(process_type="data", show_in_console=False)
        self._conf = OptConf

    def parse(self, msg: Union[ConsumerRecord,Dict]) -> None:
        # 必要字段解析
        self.parse_data(msg)
        # iiea配置解析
        self.parse_param()

    @throw_exception(ExceptCode.REQUEST_PARSE_DATA)
    def parse_data(self, msg: Union[ConsumerRecord,Dict]) -> None:
        self.recv_time: str = TimeUtil.now_hikstr()
        self.data: Dict = msg if isinstance(msg,Dict) else msg.value
        self.data["modelRecvTime"]: str = self.recv_time
        self.alarmres: Dict = self.data["vehicleAlarmResult"][0]
        self.target_url: str = self.alarmres["targetPicUrl"]
        self.plate_no: str = self.alarmres["target"][0]["vehicle"]["plateNo"]["value"]
        self.viocode: str = str(self.alarmres["targetAttrs"]["alarmType"])
        self.match_viocode: str = self.viocode_match()
        self.data["modelVioCode"]: str = self.match_viocode
        self.urls: List[str] = self.parse_urls()
        # 切分方式配置
        self.data["merge_type_conf"]: Optional[int] = self.parse_merge_type_conf()
        self.data["judge_mode_conf"]: Optional[str] = self.parse_judge_mode_conf()

    def viocode_match(self) -> str:
        match_code = ""
        self.judge_code = self.viocode[:4] if len(self.viocode) > 4 else self.viocode
        if "1000" in self._conf.Vio_Type_Match and self.judge_code in self._conf.Vio_Type_Match["1000"]:
            match_code = "1000"
        else:
            for k, v in self._conf.Vio_Type_Match.items():
                if self.judge_code in v:
                    match_code = k
                    break
        return match_code

    def parse_urls(self) -> List[Optional[str]]:  # remind
        if self._conf.Judge_Url and self.match_viocode not in self._conf.No_Judge_Url_Code:
            url_num = str(self.alarmres).lower().count("picurl")
            # 去除2个url：targetPicUrl、platePicUrl
            urls = [self.alarmres["targetAttrs"].get(f"vehiclePicUrl{i + 1}") for i in range(url_num - 2)]
            urls.insert(0, self.target_url)
        else:
            urls = [self.target_url]
        return urls

    def parse_merge_type_conf(self) -> Optional[int]:  # 合成方式特殊配置解析
        cross_dict: Dict[str, int] = self._conf.Cross_Split_Set
        camera_dict: Dict[str, int] = self._conf.Camera_Split_Set
        split_mode = self._conf.SPLIT_DICT.get(self.match_viocode)
        # 配置的拼接方式
        if self.match_viocode in self._conf.Split_Set_Type:
            if self.camera_code in camera_dict:
                split_mode = camera_dict[self.camera_code]
                self._logger.info(f"根据相机配置修改切分方式：{split_mode}")
            elif self.cross_code in cross_dict:
                split_mode = cross_dict[self.cross_code]
                self._logger.info(f"根据卡口配置修改切分方式：{split_mode}")
        return split_mode

    def parse_judge_mode_conf(self) -> Optional[str]:  # 判罚策略特殊配置解析
        cross_dict: Dict[str, int] = self._conf.Judge_Mode_Cross_Set
        camera_dict: Dict[str, int] = self._conf.Judge_Mode_Camera_Set
        match_code = self.match_viocode
        judge_mode = str(self._conf.Judge_Mode[match_code]) if match_code else None
        # 配置的判罚策略
        if self.match_viocode in self._conf.Judge_Mode_Set_Type:
            if self.camera_code in camera_dict:
                judge_mode = str(camera_dict[self.camera_code])
                self._logger.info(f"根据相机配置修改审核策略：{judge_mode}")
            elif self.cross_code in cross_dict:
                judge_mode = str(cross_dict[self.cross_code])
                self._logger.info(f"根据卡口配置修改审核策略：{judge_mode}")
        return judge_mode

    @throw_exception(ExceptCode.REQUEST_PARSE_PARAM)  # TODO:异常输出信息不全
    def parse_param(self) -> None:
        iiea_msg = IIEAMsg(**self.data)
        black_rate, black_pos = self.parse_black_area(iiea_msg.iieaMergeRect)
        police, ambulance, fire = self.parse_veh_filter(iiea_msg.iieaFilterVehicle)
        self.judge_mode: Optional[int] = int(iiea_msg.iieaAuditMode) if iiea_msg.iieaAuditMode else None
        self.split_mode: Optional[int] = iiea_msg.iieaMergeType
        self.black_rate: float = black_rate
        self.black_pos: str = black_pos
        self.filter_police: bool = police
        self.filter_ambulance: bool = ambulance
        self.filter_fire: bool = fire
        self.iiea_time: Optional[str] = iiea_msg.iieaSendTime
        self.uuid: str = iiea_msg.iieaUUID

    def parse_black_area(self, msg: Optional[str]) -> Tuple[float, Optional[str]]:
        if msg is None:
            black_pos = self._conf.BLACK_POSITION_DICT[
                self.match_viocode] if self.match_viocode in self._conf.BLACK_POSITION_DICT else None
            return self._conf.BLACK_HEIGHT, black_pos
        else:
            x, y, w, h = map(float, msg.split(','))
            y2 = y + h
            y_dist = [y, abs(1 - y2)]
            black_pos = ["up", "down"]
            idx = np.argmax(y_dist)
            self._logger.info(f"iiea图片有效区域配置加载成功！黑框高度比：{y_dist[idx]} 黑框位置：{black_pos[idx]}")
            return y_dist[idx], black_pos[idx]

    def parse_veh_filter(self, msg: str) -> Iterator[bool]:
        return map(bool, map(int, msg.split("#")))

    @property
    @exception_handler(return_value="")
    def cross_code(self) -> str:
        return self.alarmres["targetAttrs"]["crossingIndexCode"]

    @property
    @exception_handler(return_value="")
    def camera_code(self) -> str:
        return self.alarmres["targetAttrs"]["cameraIndexCode"]

    @property
    @exception_handler(return_value="")
    def datatype(self) -> str:
        return self.data["dataSource"]

    @property
    @exception_handler(return_value=TimeUtil.now_hikstr())
    def send_time(self) -> str:
        return self.data["sendTime"]

    @property
    @exception_handler(return_value="")
    def plate_color(self) -> str:
        return self.alarmres["target"][0]["vehicle"]["plateColor"]["value"]

    def print_parse_data(self) -> None:
        self._logger.info("打印必要字段：")
        for var_name, value in vars(self).items():
            if var_name.startswith("_"):
                continue
            self._logger.info(f"{var_name}: {value}")
        self._logger.info("\n打印非必要字段：")
        # 获取类的所有属性（包括父类的属性）
        for name, value in inspect.getmembers(self.__class__, lambda x: isinstance(x, property)):
            try:
                prop_value = getattr(self, name)
                self._logger.info(f"{name}: {prop_value}")
            except Exception as e:
                self._logger.error(f"Error accessing property {name}: {e}")
