wandb_version: 1

weights:
  desc: null
  value: ../models/yolov7-e6e.pt
cfg:
  desc: null
  value: cfg/training/yolov7e6e.yaml
data:
  desc: null
  value: data/motor_helmet.yaml
hyp:
  desc: null
  value:
    lr0: 0.001
    lrf: 0.2
    momentum: 0.937
    weight_decay: 0.0005
    warmup_epochs: 3.0
    warmup_momentum: 0.8
    warmup_bias_lr: 1.0e-05
    box: 0.05
    cls: 0.3
    cls_pw: 1.0
    obj: 0.7
    obj_pw: 1.0
    iou_t: 0.2
    anchor_t: 4.0
    fl_gamma: 0.0
    hsv_h: 0.5
    hsv_s: 0.7
    hsv_v: 0.4
    degrees: 5.0
    translate: 0.2
    scale: 0.9
    shear: 0.0
    perspective: 0.001
    flipud: 0.0
    fliplr: 0.3
    mosaic: 1.0
    mixup: 0.5
    copy_paste: 1.0
    paste_in: 1.0
epochs:
  desc: null
  value: 1000
batch_size:
  desc: null
  value: 15
img_size:
  desc: null
  value:
  - 1280
  - 1280
rect:
  desc: null
  value: false
resume:
  desc: null
  value: false
nosave:
  desc: null
  value: false
notest:
  desc: null
  value: false
noautoanchor:
  desc: null
  value: false
evolve:
  desc: null
  value: false
bucket:
  desc: null
  value: ''
cache_images:
  desc: null
  value: false
image_weights:
  desc: null
  value: true
device:
  desc: null
  value: 1,2,3
multi_scale:
  desc: null
  value: true
single_cls:
  desc: null
  value: false
adam:
  desc: null
  value: true
sync_bn:
  desc: null
  value: true
local_rank:
  desc: null
  value: -1
workers:
  desc: null
  value: 8
project:
  desc: null
  value: runs/train
entity:
  desc: null
  value: null
name:
  desc: null
  value: exp
exist_ok:
  desc: null
  value: false
quad:
  desc: null
  value: false
linear_lr:
  desc: null
  value: false
label_smoothing:
  desc: null
  value: 0.0
upload_dataset:
  desc: null
  value: false
bbox_interval:
  desc: null
  value: -1
save_period:
  desc: null
  value: -1
artifact_alias:
  desc: null
  value: latest
world_size:
  desc: null
  value: 1
global_rank:
  desc: null
  value: -1
save_dir:
  desc: null
  value: runs/train/exp8
total_batch_size:
  desc: null
  value: 15
_wandb:
  desc: null
  value:
    python_version: 3.7.12
    cli_version: 0.15.3
    framework: torch
    is_jupyter_run: false
    is_kaggle_kernel: false
    start_time: 1684390020.094145
    t:
      1:
      - 1
      - 41
      - 55
      3:
      - 13
      - 16
      - 23
      4: 3.7.12
      5: 0.15.3
      8:
      - 5
