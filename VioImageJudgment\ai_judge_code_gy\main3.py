# -*- coding: UTF-8 -*-
import os

os.environ["CUDA_VISIBLE_DEVICES"] = "2"
import os.path
import shutil

from init_models import *

if Is_Allowed_Type["1625"]:
    from ai_judge_code_gy.vio_judge_16251 import judge_vio as judge_16251
if Is_Allowed_Type["1208"]:
    from ai_judge_code_gy.vio_judge_12080 import judge_vio as judge_12080
if Is_Allowed_Type["1345"]:
    from ai_judge_code_gy.vio_judge_13450 import judge_vio as judge_13450
if Is_Allowed_Type["1301"]:
    from ai_judge_code_gy.vio_judge_13010 import judge_vio as judge_13010
if Is_Allowed_Type["1352"]:
    from ai_judge_code_gy.vio_judge_13522 import judge_vio as judge_13522
if Is_Allowed_Type["1357"]:
    from ai_judge_code_gy.vio_judge_13570v2 import judge_vio as judge_13570
if Is_Allowed_Type["1223"] or Is_Allowed_Type["1101"]:
    from ai_judge_code_gy.vio_judge_12230_11010 import judge_vio as judge_12230_11010
if Is_Allowed_Type["7102"]:
    from ai_judge_code_gy.vio_judge_71020 import judge_vio as judge_71020
if Is_Allowed_Type["8013"]:
    from ai_judge_code_gy.vio_judge_80130 import judge_vio as judge_80130
if Is_Allowed_Type["1344"]:
    from ai_judge_code_gy.vio_judge_13440_v2 import judge_vio as judge_13440
from fastapi import FastAPI, Request
from fastapi.exceptions import RequestValidationError
from fastapi.responses import JSONResponse
# from pydantic import BaseModel
import json
import urllib.request
import socket
import base64
import traceback

socket.setdefaulttimeout(10)
# 初始化所有模型
weights_list = init_models(traffic=True, lp=True, yolov7=True, trace=False, vio_model=True, cnn=True)

app = FastAPI()


# @app.exception_handler(RequestValidationError)
# async def request_validation_exception_handler(request: Request, exc: RequestValidationError):
#     print(f"参数不对{request.method} {request.url} {exc.errors()}")
#     return JSONResponse({"code": "400", "message": exc.errors()})


# class Item(BaseModel):
#     session_id: str
#     mode: int  # 0为URL模式, 1为file　base64模式
#     violation_type: str
#     plate_number: str
#     images: list
#     shoot_scene: str
#     extra: str
#     dev_desc: str
#     special_car_type: list
#     place_type: str


def get_imgs_header(url, file_name):
    opener = urllib.request.build_opener()
    opener.addheaders = [('User-Agent',
                          'Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/36.0.1941.0 Safari/537.36')]
    urllib.request.install_opener(opener)
    urllib.request.urlretrieve(url, filename=file_name)


def base2img(img_path, data):
    file = open(img_path, 'wb')
    file.write(data)
    file.close()


# 拼接方式
def img_split(img, n):
    # 4图横向拼接去除下黑框
    img_list = []
    if n == 1:
        h, w, _ = img.shape
        black_h = int(BLACK_HEIGHT * h)
        cx = w // 2
        cy = (h - black_h) // 2
        img1 = img[:cy, :cx]
        img2 = img[:cy, cx:]
        img3 = img[cy:2 * cy, :cx]
        img4 = img[cy:2 * cy, cx:]
        img_list = [img1, img2, img3, img4]
    # 4图横向拼接均分
    elif n == 2:
        h, w, _ = img.shape
        cx = w // 2
        cy = h // 2
        img1 = img[:cy, :cx]
        img2 = img[:cy, cx:]
        img3 = img[cy:2 * cy, :cx]
        img4 = img[cy:2 * cy, cx:]
        img_list = [img1, img2, img3, img4]
    # 2图横向拼接
    elif n == 3:
        h, w, _ = img.shape
        cx = w // 2
        img1 = img[:, :cx]
        img2 = img[:, cx:]
        img_list = [img1, img2]
    # 3图横向拼接
    elif n == 4:
        h, w, _ = img.shape

        split_w = w // 3
        img1 = img[:, :split_w]
        img2 = img[:, split_w:2 * split_w]
        img3 = img[:, 2 * split_w:]
        img_list = [img1, img2, img3]
    # 4图横拼去除上黑框
    elif n == 5:
        h, w, _ = img.shape
        black_h = int(BLACK_HEIGHT * h)
        cx = w // 2
        cy = (h - black_h) // 2
        img1 = img[black_h:cy + black_h, :cx]
        img2 = img[black_h:cy + black_h, cx:]
        img3 = img[cy + black_h:, :cx]
        img4 = img[cy + black_h:, cx:]
        img_list = [img1, img2, img3, img4]
    # 6图横拼去除上黑框（漳州）
    elif n == 6:
        h, w, _ = img.shape
        black_h = int(BLACK_HEIGHT * h)
        cx = w // 3
        cy = (h - black_h) // 2
        img1 = img[black_h:cy + black_h, :cx]
        img2 = img[black_h:cy + black_h, cx:2 * cx]
        img3 = img[cy + black_h:, :cx]
        img4 = img[cy + black_h:, cx:2 * cx]
        img_list = [img1, img2, img3, img4]
    # 上2下1
    elif n == 7:
        h, w, _ = img.shape
        cx = w // 2
        cy = h // 3
        img1 = img[:cy, :cx]
        img2 = img[:cy, cx:]
        img3 = img[cy:]
        img_list = [img1, img2, img3]
    elif n == 0:
        img_list = [img]
    return img_list


def get_images(images, plate_num, vio_code):
    raw_img_dir = '../temp/raw_imgs_%s' % str(vio_code)
    if not os.path.exists(raw_img_dir):
        os.makedirs(raw_img_dir)
    if len(os.listdir(raw_img_dir)) > 100:
        os.system('find {} -mmin +5 -name "*.*" -delete'.format(raw_img_dir))
    file_list = []
    src_list = []
    if len(images) != 0:
        try:
            if "url" in images[0] and images[0]['url']:
                img_num = len(images)
                if img_num == 1:
                    try:
                        try:
                            img_path = raw_img_dir + '/%s.jpg' % plate_num
                            img_url = images[0]['url']
                            urllib.request.urlretrieve(img_url, filename=img_path)
                            img = cv2.imread(img_path)
                            file_list = img_split(img, n=SPLIT_DICT[str(vio_code)])
                            src_list.append(img.copy())
                        except:
                            traceback.print_exc()
                            pass
                        urllib.request.urlcleanup()
                    except socket.timeout:
                        traceback.print_exc()
                        pass
                elif img_num > 1:
                    try:
                        try:
                            img1_path = raw_img_dir + '/%s_1.jpg' % plate_num
                            img1_url = images[0]['url']
                            urllib.request.urlretrieve(img1_url, filename=img1_path)
                            file_list.append(cv2.imread(img1_path))
                        except:
                            traceback.print_exc()
                            pass
                        try:
                            img2_path = raw_img_dir + '/%s_2.jpg' % plate_num
                            img2_url = images[1]['url']
                            urllib.request.urlretrieve(img2_url, filename=img2_path)
                            file_list.append(cv2.imread(img2_path))
                        except:
                            traceback.print_exc()
                            pass
                        try:
                            img3_path = raw_img_dir + '/%s_3.jpg' % plate_num
                            img3_url = images[2]['url']
                            urllib.request.urlretrieve(img3_url, filename=img3_path)
                            file_list.append(cv2.imread(img3_path))
                        except:
                            traceback.print_exc()
                            pass
                        try:
                            img4_path = raw_img_dir + '/%s_4.jpg' % plate_num
                            img4_url = images[3]['url']
                            urllib.request.urlretrieve(img4_url, filename=img4_path)
                            file_list.append(cv2.imread(img4_path))
                        except:
                            traceback.print_exc()
                            pass
                        urllib.request.urlcleanup()
                    except socket.timeout:
                        traceback.print_exc()
                        pass
                    src_list = file_list.copy()
                else:
                    print("Input url-image error!")
            elif "file" in images[0] and images[0]['file']:
                img_num = len(images)
                if img_num == 1:
                    try:
                        img_path = raw_img_dir + '/%s.jpg' % plate_num
                        imgdata = base64.b64decode(images[0]['file'].encode())
                        base2img(img_path, imgdata)
                        img = cv2.imread(img_path)
                        src_list.append(img.copy())
                        # if vio_code in ["1625", "1208"]:
                        #     file_list = img_split(img, n=1)
                        # elif vio_code in ["1344", "1357"]:
                        #     file_list = img_split(img, n=2)
                        # elif vio_code in ["1352"]:
                        #     file_list = img_split(img, n=3)
                        # else:
                        #     print("图片拼接方式未配置！")

                        file_list = img_split(img, n=SPLIT_DICT[str(vio_code)])
                    except:
                        traceback.print_exc()
                        pass
                elif img_num > 1:
                    try:
                        img1_path = raw_img_dir + '/%s_1.jpg' % plate_num
                        imgdata = base64.b64decode(images[0]['file'])
                        base2img(img1_path, imgdata)
                        file_list.append(cv2.imread(img1_path))
                    except:
                        traceback.print_exc()
                        pass
                    try:
                        img2_path = raw_img_dir + '/%s_2.jpg' % plate_num
                        imgdata = base64.b64decode(images[1]['file'])
                        base2img(img2_path, imgdata)
                        file_list.append(cv2.imread(img2_path))
                    except:
                        traceback.print_exc()
                        pass
                    try:
                        img3_path = raw_img_dir + '/%s_3.jpg' % plate_num
                        imgdata = base64.b64decode(images[2]['file'])
                        base2img(img3_path, imgdata)
                        file_list.append(cv2.imread(img3_path))
                    except:
                        traceback.print_exc()
                        pass
                    try:
                        img4_path = raw_img_dir + '/%s_4.jpg' % plate_num
                        imgdata = base64.b64decode(images[3]['file'])
                        base2img(img4_path, imgdata)
                        file_list.append(cv2.imread(img4_path))
                    except:
                        traceback.print_exc()
                        pass
                    src_list = file_list.copy()
                else:
                    print("Input file-image error!")
            else:
                print("Input image error!")
        except:
            traceback.print_exc()
            pass

    return file_list, src_list


@app.post('/judgeVioPics')
async def calculate(data: Request):
    try:
        if os.path.exists("./logs/log3.txt") and (os.path.getsize("./logs/log3.txt") / 1024 / 1024) > LOG_MAX_SIZE:
            with open("./logs/log3.txt", "w", encoding="utf-8") as f:
                f.close()
    except:
        print(f"out日志初始化失败！")
    global dev_desc
    import time
    import datetime as dt
    h = time.localtime()[3]
    d = time.localtime()[2]
    # UTC时区转换为东八区
    # pro_time = time.strftime(f'%Y-%m-{d + 1 if h + 8 >= 24 else d} {(h + 8) % 24}:%M:%S', time.localtime())

    s_time = dt.datetime.now()
    try:
        request_data = await data.json()

        session_id = request_data["session_id"]
        mode = request_data["mode"]
        violation_type = request_data["violation_type"]
        plate_num = request_data["plate_number"]
        images = request_data["images"]

        shoot_scene = request_data["shoot_scene"] if "shoot_scene" in request_data.keys() else ""
        extra = request_data["extra"] if "extra" in request_data.keys() else ""
        dev_desc = request_data["dev_desc"] if "dev_desc" in request_data.keys() else ""
        special_car_type = request_data["special_car_type"] if "special_car_type" in request_data.keys() else []
        place_type = request_data["place_type"] if "place_type" in request_data.keys() else ""
    except Exception as e:
        error_msg = catch_error()
        print("下发任务数据存在异常！", e,
              error_msg)
        return {'code': -1, 'msg': "下发任务数据存在异常！", 'data': None}
    aiinfo = {
        'lane': None,
        'stop_line': None,
        'zebra_line': None,
        'lamp': None,
        'target_car': None,
        'split_rect': None,
        'best_car': None,
        'drive_direction': None,
        'vehicle_direction': None,
        'vehicle_complety': None,
        'vehicle_location': None,
        'has_police': None,
        'has_blet': None,
        'has_phone': None,
        'has_crossline': None,
        'has_construction': None,
        'drive_direction_light': None,
        'belt_phone_smoke_info': None,
        'tunnel_lanes': None,
        'nonmotor_lanesign': None
    }

    data = {
        'session_id': session_id,
        'is_valid': True,
        'reason': 'no_judge',
        'mode': mode,
        'aiinfo': aiinfo,
        'extra': extra,
        'modelJudgeResult': {"code": "", "desc": ""}
    }

    res_infos = {
        'code': 0,
        'msg': None,
        'data': data
    }

    match_code = None
    for k, v in Vio_Type_Match.items():
        if str(violation_type) in v:
            match_code = k
            break
    if match_code is None:
        res_infos["msg"] = f"{violation_type}违法类型暂不支持！"
        res_infos["code"] = -1
        res_infos["data"]["reason"] = f"{violation_type}违法类型暂不支持！"
        return res_infos
    print("session_id:", session_id, " mode:", mode, " violation_type:", violation_type, " plate_num:", plate_num,
          " place_type:", place_type)
    # print("images:", images, " shoot_scene:", shoot_scene, " extra:", extra, " dev_desc:", dev_desc,
    #       " special_car_type:", special_car_type)

    vio_judge = 'no_judge'

    judge_infos = {'plate_num': plate_num,
                   'vio_code': violation_type,
                   'zebra_line': {},
                   'police': 0,
                   'has_crossline': 0,
                   'vehicle_location': {},
                   'drv_direction': 0,
                   'drive_direction_light': [],
                   'lamp': [],
                   'stop_line': [0],
                   'lane': [],
                   'target_car': [],
                   'vio_judge': vio_judge}

    try:
        file_list, src_list = get_images(images, f"{session_id}_{plate_num}_{violation_type}", match_code)
        if file_list == []:
            return {'code': -1, 'msg': "图片数据缺失！", 'data': data}
        scale = 0.7
        # print('=========', file_list)
        # 1
        if Is_Allowed_Type[str(match_code)] and int(match_code) == 1208:
            vio_judge, judge_infos = \
                judge_12080(plate_num, file_list, weights_list, resize_scale=scale, merge=False, save=IS_SAVE_VIS_PIC,
                            save_path=f"{SAVE_RES_DIR}/{match_code}/vis")
        # 2
        if Is_Allowed_Type[str(match_code)] and int(match_code) == 1625:
            vio_judge, judge_infos = \
                judge_16251(plate_num, file_list, weights_list, resize_scale=scale, merge=False, save=IS_SAVE_VIS_PIC,
                            save_path=f"{SAVE_RES_DIR}/{match_code}/vis")
        # 3
        if Is_Allowed_Type[str(match_code)] and int(match_code) == 1345:
            vio_judge, judge_infos = \
                judge_13450(plate_num, file_list, weights_list, resize_scale=scale, save=IS_SAVE_VIS_PIC,
                            save_path=f"{SAVE_RES_DIR}/{match_code}/vis")
        # 4
        if Is_Allowed_Type[str(match_code)] and int(match_code) == 1301:
            is_highway = False
            if place_type in ["高速", "匝道"]:
                is_highway = True
            vio_judge, judge_infos = \
                judge_13010(plate_num, file_list, weights_list, resize_scale=scale, merge=True, save=IS_SAVE_VIS_PIC,
                            save_path=f"{SAVE_RES_DIR}/{match_code}/vis", highway=is_highway)
        # 5
        if Is_Allowed_Type[str(match_code)] and int(match_code) == 1352:
            vio_judge, judge_infos = \
                judge_13522(plate_num, file_list, weights_list, resize_scale=scale, save=IS_SAVE_VIS_PIC,
                            save_path=f"{SAVE_RES_DIR}/{match_code}/vis")
        # 6
        if Is_Allowed_Type[str(match_code)] and int(match_code) == 1357:
            vio_judge, judge_infos = \
                judge_13570(plate_num, file_list, weights_list, resize_scale=scale, save=IS_SAVE_VIS_PIC,
                            save_path=f"{SAVE_RES_DIR}/{match_code}/vis")
        # 7
        if Is_Allowed_Type[str(match_code)] and int(match_code) == 7102:
            vio_judge, judge_infos = \
                judge_71020(plate_num, file_list, weights_list, resize_scale=scale, save=IS_SAVE_VIS_PIC,
                            save_path=f"{SAVE_RES_DIR}/{match_code}/vis")
        # 8
        if Is_Allowed_Type[str(match_code)] and int(match_code) == 8013:
            vio_judge, judge_infos = \
                judge_80130(plate_num, file_list, weights_list, resize_scale=scale, save=IS_SAVE_VIS_PIC,
                            save_path=f"{SAVE_RES_DIR}/{match_code}/vis")
        # 9,10
        if Is_Allowed_Type[str(match_code)] and int(match_code) in [1223, 1101]:
            vio_judge, judge_infos = \
                judge_12230_11010([match_code, violation_type], plate_num, file_list, weights_list, resize_scale=scale,
                                  save=IS_SAVE_VIS_PIC,
                                  save_path=f"{SAVE_RES_DIR}/{match_code}/vis")
        # 11
        if Is_Allowed_Type[str(match_code)] and int(match_code) == 1344:
            vio_judge, judge_infos, score_list = \
                judge_13440(plate_num, file_list, weights_list, save=IS_SAVE_VIS_PIC,
                            save_path=f"{SAVE_RES_DIR}/{match_code}/vis")

            data['extra'] += str(score_list)
        if vio_judge.startswith('vio'):
            data['is_valid'] = True
        elif vio_judge == "no_judge":
            data['is_valid'] = True
        else:
            data['is_valid'] = False
        data['reason'] = vio_judge
        data['session_id'] = session_id
        data['mode'] = mode
        data['modelJudgeResult']['code'] = vio_judge
        try:
            if vio_judge in JudgeResult[match_code].keys():
                data["modelJudgeResult"] = {"code": vio_judge, "desc": JudgeResult[match_code][vio_judge]}
            elif match_code == "1344" and vio_judge.startswith("no_vio"):
                data["modelJudgeResult"] = {"code": vio_judge, "desc": "不违法，车牌匹配结果" + vio_judge.split("-")[-1]}
            else:
                data["modelJudgeResult"] = {"code": vio_judge, "desc": vio_judge}
        except Exception as e:
            pass
        # data['aiinfo'] = judge_infos
        aiinfo['lane'] = judge_infos['lane']
        aiinfo['stop_line'] = list([judge_infos['stop_line']])
        aiinfo['zebra_line'] = list(judge_infos['zebra_line'])
        aiinfo['lamp'] = judge_infos['lamp']
        aiinfo['target_car'] = judge_infos['target_car']
        aiinfo['drive_direction'] = judge_infos['drv_direction']
        aiinfo['vehicle_location'] = judge_infos['vehicle_location']
        aiinfo['has_police'] = judge_infos['police']
        aiinfo['has_crossline'] = judge_infos['has_crossline']
        aiinfo['drive_direction_light'] = str(judge_infos['drive_direction_light'])
        res_infos['code'] = int(violation_type)
        res_infos['msg'] = 'success'
        res_infos['data'] = data
    except Exception as e:
        error_msg = catch_error()
        print('{}=========Error!====={}=={}=========='.format(session_id, violation_type, plate_num), e,
              error_msg)
        res_infos['code'] = -1
        res_infos['msg'] = f'{error_msg}'
        res_infos['data'] = data
        return res_infos
    e_time = dt.datetime.now()
    time_delta = round((e_time - s_time).seconds, 2)
    # tt_time += time_delta
    print(time.strftime("%Y-%m-%d %H:%M:%S", time.localtime()), f' {session_id} use time', time_delta, '\n', res_infos,
          "\n\n", "-" * 50, "\n")
    # res_infos = json.dumps(res_infos)
    torch.cuda.empty_cache()

    # 保存判罚结果图片
    try:
        pred_result = f"{SAVE_RES_DIR}/{match_code}/vio" if vio_judge.startswith(
            "vio") else f"{SAVE_RES_DIR}/{match_code}/no_vio"

        os.makedirs(pred_result, exist_ok=True)

        if len(src_list) == 1:
            cv2.imwrite(f"{pred_result}/{session_id}_{plate_num}_{violation_type}.jpg", src_list[0])
        elif len(src_list) > 1:
            for idx, img in enumerate(src_list):
                cv2.imwrite(f"{pred_result}/{session_id}_{plate_num}_{violation_type}_{idx + 1}.jpg", img)
        if len(file_list) == 0:
            print(time.strftime("%Y-%m-%d %H:%M:%S", time.localtime()), f" {session_id} 图片为空！")

        try:
            src_vio_num = len(os.listdir(f"{SAVE_RES_DIR}/{match_code}/vio"))
        except:
            src_vio_num = 0
        try:
            src_no_vio_num = len(os.listdir(f"{SAVE_RES_DIR}/{match_code}/no_vio"))
        except:
            src_no_vio_num = 0
        try:
            if (src_vio_num + src_no_vio_num) > Max_Save_Num:
                # 清理保存的图片
                shutil.rmtree(f"{SAVE_RES_DIR}/{match_code}")
        except Exception as e:
            error_msg = catch_error()
            print(time.strftime("%Y-%m-%d %H:%M:%S", time.localtime()) +
                  f" task_id:{id} match_code:{match_code} plate_num:{plate_num} {e}\n{error_msg} {match_code}图片清理失败！")
    except Exception as e:
        error_msg = catch_error()
        print('{}=========Error!====={}=={}=========='.format(session_id, match_code, plate_num), e,
              error_msg)
        print(time.strftime("%Y-%m-%d %H:%M:%S", time.localtime()), f" {session_id} 保存判罚结果图片失败!")
    # tmp = open("kafka.txt", "w")
    # tmp.write(str(res_infos))
    # tmp.close()
    # os.system('cat kafka.txt | /home/<USER>/ai_judge/kafka/bin/kafka-console-producer.sh --topic wordsender '
    #           '--broker-list localhost:9092')
    return res_infos


def catch_error():
    exc_type, exc_value, exc_traceback = sys.exc_info()
    tb_info = traceback.extract_tb(exc_traceback)
    filename, line, func, text = tb_info[-1]
    error_str = f"\nerror file:{filename}\nerror func:{func} line {line}\nerror text:{text} —— {exc_value}"
    return error_str


if __name__ == '__main__':
    import uvicorn

    uvicorn.run(app=app, host="0.0.0.0", port=9002)
