import os
import time
import json
from kafka import KafkaConsumer, TopicPartition, OffsetAndMetadata
from main_config.config import *
from multiprocessing import Process, Manager


# 获取主题的所有分区
def get_topic_partitions():
    consumer = KafkaConsumer(bootstrap_servers=SERVER)
    partitions = consumer.partitions_for_topic(VIO_DATA_TOPIC)
    consumer.close()
    return [TopicPartition(VIO_DATA_TOPIC, p) for p in partitions]


# 更新进程状态
def update_process_status(process_status, pid, status):
    process_status[pid] = status


# 检查进程状态
def check_process_status(process_status, partitions, num_consumers):
    while True:
        time.sleep(10)  # 每10秒检查一次
        active_pids = set(pid for pid, status in process_status.items() if status == 'active')
        if len(active_pids) < num_consumers:
            # 有进程挂掉了，重新分配分区
            reassign_partitions(process_status, partitions, num_consumers)


# 重新分配分区
def reassign_partitions(process_status, partitions, num_consumers):
    active_pids = [pid for pid, status in process_status.items() if status == 'active']
    num_active_consumers = len(active_pids)
    partitions_per_consumer = len(partitions) // num_active_consumers

    new_partition_assignments = {}
    for i, pid in enumerate(active_pids):
        start = i * partitions_per_consumer
        end = (i + 1) * partitions_per_consumer if i < num_active_consumers - 1 else len(partitions)
        new_partition_assignments[pid] = partitions[start:end]

    for pid, new_partitions in new_partition_assignments.items():
        update_partitions(pid, new_partitions)


# 更新分区分配
def update_partitions(pid, new_partitions):
    with open(f'partition_assignment_{pid}.json', 'w') as f:
        json.dump([str(tp) for tp in new_partitions], f)


# 消费者函数
def consume(pid, process_status, partitions_file):
    Max_Poll_Interval_Ms = 60000  # 增加 max_poll_interval_ms
    max_poll_records = 2  # 减少 max_poll_records
    session_timeout_ms = 45000  # 增加 session_timeout_ms
    heartbeat_interval_ms = 15000  # 增加 heartbeat_interval_ms
    GROUP_ID = "ai_judge_1106_10"

    # 读取分区分配
    with open(partitions_file, 'r') as f:
        partitions_str = json.load(f)
    partitions = [TopicPartition(*eval(tp_str)) for tp_str in partitions_str]

    consumer = KafkaConsumer(
        bootstrap_servers=SERVER,
        group_id=GROUP_ID,
        auto_offset_reset=AUTO_OFFSET_RESET,
        client_id=f"{CLIENT_ID}_{pid}",
        max_poll_interval_ms=Max_Poll_Interval_Ms,
        request_timeout_ms=int(Max_Poll_Interval_Ms * 1.2) + 1000,
        session_timeout_ms=session_timeout_ms,
        connections_max_idle_ms=int(Max_Poll_Interval_Ms * 1.2) + 5000,
        enable_auto_commit=False,
        max_poll_records=max_poll_records,
        metadata_max_age_ms=Max_Poll_Interval_Ms,
        heartbeat_interval_ms=heartbeat_interval_ms,
        max_partition_fetch_bytes=5242880
    )

    # 手动分配分区
    consumer.assign(partitions)

    update_process_status(process_status, pid, 'active')
    print(f"进程 {pid} 消费已就绪！")

    try:
        while True:
            try:
                # 拉取消息
                records = consumer.poll(timeout_ms=1000)
            except Exception as e:
                records = {}
                print(f"进程 {pid} 拉取消息失败: {e}")

            poll_st = time.time()
            for tp, messages in records.items():
                for msg in messages:
                    request_dict = json.loads(msg.value)
                    s_id = request_dict["vehicleAlarmResult"][0]["taskID"]
                    try:
                        topic_partition = TopicPartition(msg.topic, msg.partition)
                        offset = OffsetAndMetadata(msg.offset + 1, metadata="")
                        print(f"pid: {pid}, partition: {msg.partition}, offset: {msg.offset}, id: {s_id}")

                    except Exception as e:
                        print(time.strftime("%Y-%m-%d %H:%M:%S", time.localtime()) +
                              f"{e}\nTopic分区和偏移量初始化异常!")
                        continue
                    detect()
                    try:
                        # 手动提交偏移量
                        consumer.commit({topic_partition: offset})
                        print(
                            f"pid: {pid}, partition: {msg.partition}, offset: {msg.offset}, id: {s_id}, 完成偏移量提交")
                    except Exception as e:
                        print(time.strftime("%Y-%m-%d %H:%M:%S", time.localtime()) +
                              f"{e}\ntopic offset update error!")
                        retry_commit(consumer, {topic_partition: offset})
            poll_et = time.time()
            if records:
                print(f"进程 {pid} 处理拉取数据总耗时: {poll_et - poll_st:.2f}秒")
    except KeyboardInterrupt:
        update_process_status(process_status, pid, 'inactive')
        consumer.close()
        print(f"进程 {pid} 已关闭")


def detect():
    time.sleep(5)
    print("detect finished!")


def retry_commit(consumer, offsets, max_retries=3):
    retries = 0
    while retries < max_retries:
        try:
            consumer.commit(offsets)
            break
        except Exception as e:
            print(f"偏移量提交第{retries + 1}次重试失败! {e}")
            retries += 1
    if retries == max_retries:
        print(f"重试{max_retries}次提交偏移量失败！")


# 主函数
def main(num_consumers):
    manager = Manager()
    process_status = manager.dict()
    partitions = get_topic_partitions()
    num_partitions = len(partitions)
    partitions_per_consumer = num_partitions // num_consumers

    processes = []
    for i in range(num_consumers):
        pid = os.getpid()
        start = i * partitions_per_consumer
        end = (i + 1) * partitions_per_consumer if i < num_consumers - 1 else num_partitions
        process_partitions = partitions[start:end]
        partitions_file = f'partition_assignment_{pid}.json'
        with open(partitions_file, 'w') as f:
            json.dump([str(tp) for tp in process_partitions], f)
        p = Process(target=consume, args=(pid, process_status, partitions_file))
        p.start()
        processes.append(p)

    # 启动监控进程
    monitor = Process(target=check_process_status, args=(process_status, partitions, num_consumers))
    monitor.start()

    for p in processes:
        p.join()
    monitor.terminate()


if __name__ == '__main__':
    num_consumers = 3
    main(num_consumers)