import sys
import os

sys.path.append('..')
import argparse
import time
from pathlib import Path

import cv2
import torch
import torch.backends.cudnn as cudnn
from numpy import random

from yolov7.models.experimental import attempt_load
from yolov7.utils.datasets import LoadStreams, LoadImages, letterbox
from yolov7.utils.general import check_img_size, check_requirements, check_imshow, non_max_suppression, \
    apply_classifier, \
    scale_coords, xyxy2xywh, strip_optimizer, set_logging, increment_path
from yolov7.utils.plots import plot_one_box
from yolov7.utils.torch_utils import select_device, load_classifier, time_synchronized, TracedModel
from ai_judge_code_gy.judge_tools import Tools
import numpy as np
import xml.etree.ElementTree as ET
from tqdm import tqdm


def get_iou(box1, box2):
    data1 = np.array(box1)
    data2 = np.array(box2)
    tool = Tools()
    return tool.IOU(data1[:2], data1[2:], data2[:2], data2[2:])


def check_result(xml_path, name, light_res, total_right):
    all_right = True
    with open(f'{xml_path}/{name}.xml', mode='r', encoding='utf-8') as f:
        tree = ET.parse(f)
        root = tree.getroot()
        label_list = []
        for obj in root.iter('object'):
            cls = obj.find('name').text
            if cls.startswith('police'):
                continue
            xmlbox = obj.find('bndbox')
            pos = [float(xmlbox.find('xmin').text), float(xmlbox.find('ymin').text), float(xmlbox.find('xmax').text),
                   float(xmlbox.find('ymax').text), cls]
            label_list.append(pos)
    if len(light_res) != len(label_list):
        all_right = False
    for item in light_res:
        if not label_list:
            all_right = False
        for idx, label in enumerate(label_list):
            label_pos = label[:4]
            pred_pos = eval(item[0])[0] + eval(item[0])[1]
            iou = get_iou(label_pos, pred_pos)
            # print("pred:", item[1], "label:", label[-1], iou)
            if iou > 0.2:
                if item[1].split("-")[0] == label[-1]:
                    total_right[label[-1]] += 1
                else:
                    all_right = False
                label_list.remove(label)
                break
            elif idx == len(label_list) - 1:
                all_right = False
    if all_right:
        total_right["all"] += 1
    return all_right


def detect(save_img=False):
    source, weights, view_img, save_txt, imgsz, trace = opt.source, opt.weights, opt.view_img, opt.save_txt, opt.img_size, not opt.no_trace
    save_img = not opt.nosave and not source.endswith('.txt')  # save inference images
    webcam = source.isnumeric() or source.endswith('.txt') or source.lower().startswith(
        ('rtsp://', 'rtmp://', 'http://', 'https://'))

    # Directories
    save_dir = Path(increment_path(Path(opt.project) / opt.name, exist_ok=opt.exist_ok))  # increment run
    (save_dir / 'labels' if save_txt else save_dir).mkdir(parents=True, exist_ok=True)  # make dir

    # Initialize
    set_logging()
    device = select_device(opt.device)
    half = device.type != 'cpu'  # half precision only supported on CUDA

    # Load model
    model = attempt_load(weights, map_location=device)  # load FP32 model
    stride = int(model.stride.max())  # model stride
    imgsz = check_img_size(imgsz, s=stride)  # check img_size

    if trace:
        model = TracedModel(model, device, opt.img_size)

    if half:
        model.half()  # to FP16

    # Second-stage classifier
    classify = False
    if classify:
        modelc = load_classifier(name='resnet101', n=2)  # initialize
        modelc.load_state_dict(torch.load('weights/resnet101.pt', map_location=device)['model']).to(device).eval()
    # Set Dataloader
    vid_path, vid_writer = None, None
    if webcam:
        view_img = check_imshow()
        cudnn.benchmark = True  # set True to speed up constant image size inference
        dataset = LoadStreams(source, img_size=imgsz, stride=stride)
    else:
        dataset = LoadImages(source, img_size=imgsz, stride=stride)

    # Get names and colors
    names = model.module.names if hasattr(model, 'module') else model.names
    colors = [[random.randint(0, 255) for _ in range(3)] for _ in names]

    # Run inference
    if device.type != 'cpu':
        model(torch.zeros(1, 3, imgsz, imgsz).to(device).type_as(next(model.parameters())))  # run once
    t0 = time.time()
    for path, img, im0s, vid_cap in tqdm(dataset):
        img = torch.from_numpy(img).to(device)
        img = img.half() if half else img.float()  # uint8 to fp16/32
        img /= 255.0  # 0 - 255 to 0.0 - 1.0
        if img.ndimension() == 3:
            img = img.unsqueeze(0)

        # Inference
        
        pred = model(img, augment=opt.augment)[0]

        # Apply NMS
        pred = non_max_suppression(pred, opt.conf_thres, opt.iou_thres, classes=opt.classes, agnostic=opt.agnostic_nms)
        t2 = time_synchronized()

        # Apply Classifier
        if classify:
            pred = apply_classifier(pred, modelc, img, im0s)
        # Process detections
        for i, det in enumerate(pred):  # detections per image
            if webcam:  # batch_size >= 1
                p, s, im0, frame = path[i], '%g: ' % i, im0s[i].copy(), dataset.count
            else:
                p, s, im0, frame = path, '', im0s, getattr(dataset, 'frame', 0)

            p = Path(p)  # to Path
            save_path = str(save_dir / p.name)  # img.jpg
            txt_path = str(save_dir / 'labels' / p.stem) + ('' if dataset.mode == 'image' else f'_{frame}')  # img.txt
            s += '%gx%g ' % img.shape[2:]  # print string
            gn = torch.tensor(im0.shape)[[1, 0, 1, 0]]  # normalization gain whwh
            if len(det):
                # Rescale boxes from img_size to im0 size
                det[:, :4] = scale_coords(img.shape[2:], det[:, :4], im0.shape).round()
                # Print results
                for c in det[:, -1].unique():
                    n = (det[:, -1] == c).sum()  # detections per class
                    s += f"{n} {names[int(c)]}{'s' * (n > 1)}, "  # add to string

                # Write results
                for *xyxy, conf, cls in reversed(det):
                    if save_txt:  # Write to file
                        xywh = (xyxy2xywh(torch.tensor(xyxy).view(1, 4)) / gn).view(-1).tolist()  # normalized xywh
                        line = (cls, *xywh, conf) if opt.save_conf else (cls, *xywh)  # label format
                        with open(txt_path + '.txt', 'a') as f:
                            f.write(('%g ' * len(line)).rstrip() % line + '\n')

                    if save_img or view_img:  # Add bbox to image
                        label = f'{names[int(cls)]} {conf:.2f}'
                        plot_one_box(xyxy, im0, label=label, color=colors[int(cls)], line_thickness=3)

            # Print time (inference + NMS)
            # print(f'{s}Done. ({t2 - t1:.3f}s)')

            # Stream results
            if view_img:
                cv2.imshow(str(p), im0)
                cv2.waitKey(1)  # 1 millisecond

            # Save results (image with detections)
            if save_img:
                if dataset.mode == 'image':
                    cv2.imwrite(save_path, im0)
                    print(f" The image with the result is saved in: {save_path}")
                else:  # 'video' or 'stream'
                    if vid_path != save_path:  # new video
                        vid_path = save_path
                        if isinstance(vid_writer, cv2.VideoWriter):
                            vid_writer.release()  # release previous video writer
                        if vid_cap:  # video
                            fps = vid_cap.get(cv2.CAP_PROP_FPS)
                            w = int(vid_cap.get(cv2.CAP_PROP_FRAME_WIDTH))
                            h = int(vid_cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
                        else:  # stream
                            fps, w, h = 30, im0.shape[1], im0.shape[0]
                            save_path += '.mp4'
                        vid_writer = cv2.VideoWriter(save_path, cv2.VideoWriter_fourcc(*'mp4v'), fps, (w, h))
                    vid_writer.write(im0)

    if save_txt or save_img:
        s = f"\n{len(list(save_dir.glob('labels/*.txt')))} labels saved to {save_dir / 'labels'}" if save_txt else ''
        # print(f"Results saved to {save_dir}{s}")

    print(f'Done. ({time.time() - t0:.3f}s)')


def test_lp(save_img=False):
    num_dict = get_all_label_num(label_dir="../light_test/Annotations")
    test_results = open("./light_test_results.txt", "w")
    score_list = []
    cls_name = ["red_l", "red_s", "red_r", "green_l", "green_s", "green_r", "all"]
    total_right = {"red_l": 0, "red_s": 0, "red_r": 0, "green_l": 0, "green_s": 0, "green_r": 0, "all": 0}

    source, weights, view_img, save_txt, imgsz, trace = opt.source, opt.weights, opt.view_img, opt.save_txt, opt.img_size, not opt.no_trace
    save_img = not opt.nosave and not source.endswith('.txt')  # save inference images
    webcam = source.isnumeric() or source.endswith('.txt') or source.lower().startswith(
        ('rtsp://', 'rtmp://', 'http://', 'https://'))
    w_name = weights[0].split("/")[-1].split(".")[0]
    # Directories
    save_dir = Path(increment_path(Path(opt.project) / opt.name, exist_ok=opt.exist_ok))  # increment run
    (save_dir / 'labels' if save_txt else save_dir).mkdir(parents=True, exist_ok=True)  # make dir

    # Initialize
    set_logging()
    device = select_device(opt.device)
    half = device.type != 'cpu'  # half precision only supported on CUDA
    # half = False
    # Load model
    model = attempt_load(weights[0], map_location=device)  # load FP32 model
    stride = int(model.stride.max())  # model stride
    imgsz = check_img_size(imgsz, s=stride)  # check img_size

    if trace:
        model = TracedModel(model, device, opt.img_size)

    if half:
        model.half()  # to FP16
    # Second-stage classifier
    classify = False
    if classify:
        modelc = load_classifier(name='resnet101', n=2)  # initialize
        modelc.load_state_dict(torch.load('./resnet101.pth', map_location=device)['model']).to(device).eval()

    # Set Dataloader
    vid_path, vid_writer = None, None
    if webcam:
        view_img = check_imshow()
        cudnn.benchmark = True  # set True to speed up constant image size inference
        dataset = LoadStreams(source, img_size=imgsz, stride=stride)
    else:
        dataset = LoadImages(source, img_size=imgsz, stride=stride)

    # Get names and colors
    names = model.module.names if hasattr(model, 'module') else model.names
    colors = [[random.randint(0, 255) for _ in range(3)] for _ in names]

    # Run inference
    if device.type != 'cpu':
        model(torch.zeros(1, 3, imgsz, imgsz).to(device).type_as(next(model.parameters())))  # run once
    t0 = time.time()
    for path, img, im0s, vid_cap in tqdm(dataset):
        file_name = path.split("/")[-1].split(".")[0]
        print(file_name)
        img = torch.from_numpy(img).to(device)
        img = img.half() if half else img.float()  # uint8 to fp16/32
        img /= 255.0  # 0 - 255 to 0.0 - 1.0
        if img.ndimension() == 3:
            img = img.unsqueeze(0)

        # Inference
        
        pred = model(img, augment=opt.augment)[0]

        # Apply NMS
        pred = non_max_suppression(pred, opt.conf_thres, opt.iou_thres, classes=opt.classes, agnostic=opt.agnostic_nms)
        t2 = time_synchronized()

        # Apply Classifier
        if classify:
            pred = apply_classifier(pred, modelc, img, im0s)

        # Process detections
        for i, det in enumerate(pred):  # detections per image
            if webcam:  # batch_size >= 1
                p, s, im0, frame = path[i], '%g: ' % i, im0s[i].copy(), dataset.count
            else:
                p, s, im0, frame = path, '', im0s, getattr(dataset, 'frame', 0)

            p = Path(p)  # to Path
            save_path = str(save_dir / p.name)  # img.jpg
            txt_path = str(save_dir / 'labels' / p.stem) + ('' if dataset.mode == 'image' else f'_{frame}')  # img.txt
            s += '%gx%g ' % img.shape[2:]  # print string
            gn = torch.tensor(im0.shape)[[1, 0, 1, 0]]  # normalization gain whwh
            if len(det):
                # Rescale boxes from img_size to im0 size
                det[:, :4] = scale_coords(img.shape[2:], det[:, :4], im0.shape).round()
                # 转换格式
                det = det.cpu()
                conf = det[:, 4].data.numpy()
                a = np.asarray(det, dtype=np.int32)
                conf = [str(i)[:4] if len(str(i)) > 4 else str(i) for i in conf]
                res = []
                names = ['green_s', 'green_r', 'green_l', 'red_s', 'red_r', 'red_l', 'police_1', 'police_2']
                for idx, i in enumerate(a):
                    if i[-1] > 5:
                        continue
                    box = [[i[0], i[1]], [i[2], i[3]]]
                    cls = names[i[-1]]
                    res.append([str(box), f"{cls}-{conf[idx]}"])
                all_right = check_result("../voc_data/Annotations", file_name, res, total_right)

                h_bias = 40
                if save_img:
                    fontsize = 1.5
                    thickness = 2
                    used_h = []
                    for idx, item in enumerate(res):
                        point = eval(item[0])
                        labels = item[1]

                        pt_tx = int(point[0][0])
                        pt_ty = int(point[0][1])
                        pt_bx = int(point[1][0])
                        pt_by = int(point[1][1])
                        color, org = get_color_org_new((pt_tx, pt_ty), (pt_bx, pt_by), labels)
                        cls = labels.split("-")[0][-1]
                        conf = labels.split("-")[1]
                        cv2.putText(im0s, cls, org, cv2.FONT_HERSHEY_COMPLEX,
                                    fontsize, color, thickness)
                        long_txt = (org[0], org[1] + h_bias * (idx + 1))
                        cv2.putText(im0s, conf, long_txt, cv2.FONT_HERSHEY_COMPLEX,
                                    fontsize, color, thickness)
                        cv2.rectangle(im0s, (pt_tx, pt_ty), (pt_bx, pt_by), color, 1)

                    if not os.path.exists(save_dir):
                        os.makedirs(save_dir)
                    right_img = f"{save_dir}/right"
                    error_img = f"{save_dir}/error"
                    os.makedirs(right_img, exist_ok=True)
                    os.makedirs(error_img, exist_ok=True)
                    if all_right:
                        cv2.imwrite(f"{right_img}/{file_name}.jpg", im0s)
                    else:
                        cv2.imwrite(f"{error_img}/{file_name}.jpg", im0s)
    right_list = list(total_right.values())
    num_list = list(num_dict.values())
    line = ""
    for idx, n in enumerate(right_list):
        score = round(n / num_list[idx], 3)
        # if idx == len(right_list) - 1:
        #     score_list.append([weights, score])
        line += f"{cls_name[idx]}:{score}\t"
    test_results.write(w_name + ":\n")
    test_results.write(line + "\n")
    test_results.flush()
    test_results.close()

    print(f'Done. ({time.time() - t0:.3f}s)')


def load_lp(weights="best.pt", imgsz=1472, trace=False):
    sys.path.append("../yolov5")
    # Directories
    device = "cuda" if torch.cuda.is_available() else "cpu"
    device = select_device(device)
    half = device.type != 'cpu'  # half precision only supported on CUDA
    # Load model
    model = attempt_load([weights], map_location=device)  # load FP32 model
    stride = int(model.stride.max())  # model stride
    imgsz = check_img_size(imgsz, s=stride)  # check img_size

    if trace:
        model = TracedModel(model, device, imgsz)

    if half:
        model.half()  # to FP16

    # Run inference
    if device.type != 'cpu':
        model(torch.zeros(1, 3, imgsz, imgsz).to(device).type_as(next(model.parameters())))  # run once
    return model, imgsz, stride, device


def detect_lp(img0, model, img_size=1472, stride=32, device="cuda:0", conf_thres=0.25, iou_thres=0.45,
              classes=None, agnostic_nms=True, augment=False, test_lp=False):
    t0 = time.time()
    half = device.type != 'cpu'  # half precision only supported on CUDA
    # Padded resize
    img = letterbox(img0, img_size, stride=stride)[0]

    # Convert
    img = img[:, :, ::-1].transpose(2, 0, 1)  # BGR to RGB, to 3x416x416
    img = np.ascontiguousarray(img)

    img = torch.from_numpy(img).to(device)
    img = img.half() if half else img.float()  # uint8 to fp16/32
    img /= 255.0  # 0 - 255 to 0.0 - 1.0
    if img.ndimension() == 3:
        img = img.unsqueeze(0)

    # Inference
    
    pred = model(img, augment=augment)[0]

    # Apply NMS
    pred = non_max_suppression(pred, conf_thres, iou_thres, classes=classes, agnostic=agnostic_nms)
    t2 = time_synchronized()
    # print("time inference + NMS:", f'Done. ({t2 - t1:.3f}s)')
    # Process detections
    l_res = []
    p_res = []
    for idx, det in enumerate(pred):  # detections per image
        if len(det):
            # Rescale boxes from img_size to im0 size
            det[:, :4] = scale_coords(img.shape[2:], det[:, :4], img0.shape).round()
            # 转换格式
            det = det.cpu()
            conf = det[:, 4].data.numpy()
            a = np.asarray(det, dtype=np.int32)
            conf = [str(i)[:4] if len(str(i)) > 4 else str(i) for i in conf]
            names = ['green_s', 'green_r', 'green_l', 'red_s', 'red_r', 'red_l', 'police_1', 'police_2']
            for idx, i in enumerate(a):
                box = [[i[0], i[1]], [i[2], i[3]]]
                cls = names[i[-1]]
                if i[-1] > 5:
                    if eval(conf[idx]) < 0.95:
                        continue
                    p_res.append([str(box), f"{cls}-{conf[idx]}"])
                else:
                    l_res.append([str(box), f"{cls}-{conf[idx]}"])
    # print(f'Done. {time.time() - t0:.3f}s')
    if test_lp:
        return l_res
    else:
        return l_res, p_res


def get_color_org_new(pt1, pt2, cls):
    org = (pt1[0], pt2[1] + 50)
    if cls.startswith('red'):
        color = (0, 0, 255)
    else:
        color = (0, 255, 0)
    return color, org


def get_all_label_num(label_dir="../light_test/Annotations"):
    temp = os.listdir(label_dir)
    num_dict = {"red_l": 0, "red_s": 0, "red_r": 0, "green_l": 0, "green_s": 0, "green_r": 0, "all": len(temp)}
    for i in temp:
        xml = f"{label_dir}/{i}"
        with open(xml, mode='r', encoding='utf-8') as f:
            tree = ET.parse(f)
            root = tree.getroot()
            for obj in root.iter('object'):
                cls = obj.find('name').text
                if cls in num_dict.keys():
                    num_dict[cls] += 1
    return num_dict


if __name__ == '__main__':
    parser = argparse.ArgumentParser()
    parser.add_argument('--weights', nargs='+', type=str, default='../models/yolov7-e6e.pt', help='model.pt path(s)')
    parser.add_argument('--source', type=str, default='imgs',
                        help='source')  # file/folder, 0 for webcam
    parser.add_argument('--img-size', type=int, default=1472, help='inference size (pixels)')
    parser.add_argument('--conf-thres', type=float, default=0.25, help='object confidence threshold')
    parser.add_argument('--iou-thres', type=float, default=0.45, help='IOU threshold for NMS')
    parser.add_argument('--device', default='', help='cuda device, i.e. 0 or 0,1,2,3 or cpu')
    parser.add_argument('--view-img', action='store_true', help='display results')
    parser.add_argument('--save-txt', action='store_true', help='save results to *.txt')
    parser.add_argument('--save-conf', action='store_true', help='save confidences in --save-txt labels')
    parser.add_argument('--nosave', action='store_true', help='do not save images/videos')
    parser.add_argument('--classes', nargs='+', type=int, help='filter by class: --class 0, or --class 0 2 3')
    parser.add_argument('--agnostic-nms', action='store_true', help='class-agnostic NMS')
    parser.add_argument('--augment', action='store_true', help='augmented inference')
    parser.add_argument('--update', action='store_true', help='update all models')
    parser.add_argument('--project', default='runs/detect', help='save results to project/name')
    parser.add_argument('--name', default='exp', help='save results to project/name')
    parser.add_argument('--exist-ok', action='store_true', help='existing project/name ok, do not increment')
    parser.add_argument('--no-trace', action='store_true', help='don`t trace model')
    opt = parser.parse_args()
    print(opt)
    # check_requirements(exclude=('pycocotools', 'thop'))

    with torch.no_grad():
        if opt.update:  # update all models (to fix SourceChangeWarning)
            for opt.weights in ['yolov7.pt']:
                detect()
                strip_optimizer(opt.weights)
        else:
            detect()
