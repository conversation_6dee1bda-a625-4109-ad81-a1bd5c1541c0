import os
import shutil
from glob import glob
import zipfile
from main_config.config import Max_Get_Camera_Num
def pack_folder(folder_path, output_file):
    """打包指定的文件夹到一个zip文件中。

    参数:
    folder_path -- 要打包的文件夹路径。
    output_file -- 输出的zip文件路径。
    """
    with zipfile.ZipFile(output_file, 'w', zipfile.ZIP_DEFLATED) as zipf:
        for root, dirs, files in os.walk(folder_path):
            for file in files:
                file_path = os.path.join(root, file)
                file_in_zip_path = os.path.relpath(file_path, os.path.dirname(folder_path))
                zipf.write(file_path, file_in_zip_path)




root = "./test_results/1625"
save_pic = glob(f"{root}/no_vio/*.jpg") + glob(f"{root}/vio/*.jpg")
save_pic = [path for path in save_pic if len(path.split("/")[-1].split("_")) >= 4]
save_pic = sorted(save_pic, key=lambda x: x.split("/")[-1].split("_")[0])
num = 0
last_camera = None
dst_path = "./camera_pics"
if os.path.exists(dst_path):
    shutil.rmtree(dst_path)
os.makedirs(dst_path, exist_ok=True)
if os.path.exists("get_pic_finished.txt"):
    os.remove("get_pic_finished.txt")
for path in save_pic:
    num += 1
    camera = path.split("/")[-1].split("_")[0]
    if last_camera is None:
        last_camera = camera
    elif camera != last_camera:
        num = 1
        last_camera = camera
    elif num > Max_Get_Camera_Num:
        continue
    shutil.copy(path, dst_path)
print("filter finished!")
# 打包
folder_to_pack = dst_path  # 要打包的文件夹路径
output_zip_file = 'jz_camera_pics.zip'  # 输出的zip文件路径
if os.path.exists(output_zip_file):
    os.remove(output_zip_file)
pack_folder(folder_to_pack, output_zip_file)
with open("get_pic_finished.txt","w") as f:
    f.write("finished!")