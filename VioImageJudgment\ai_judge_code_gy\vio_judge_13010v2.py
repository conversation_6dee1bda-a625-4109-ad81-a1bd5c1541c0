# 逆行场景
import os

import cv2
import numpy as np

from init_models import *
import math


# 逆行场景主函数
def judge_vio(src_plate_num, file_list, weights_list, resize_scale=0.7, merge=True, save=False,
              save_path='', highway=False, extra_msg=None):
    error_path = f"{save_path}/no_vio"
    vio_path = f"{save_path}/vio"
    if save:
        os.makedirs(error_path, exist_ok=True)
        os.makedirs(vio_path, exist_ok=True)
    # 读取模型
    plate_det_model = weights_list[0][0]
    plate_rec_model = weights_list[0][1]
    demo = weights_list[1]
    lp_model = weights_list[2][0]
    lp_meta = weights_list[2][1]
    yolov7_model = weights_list[3][0]
    yolov7_meta = weights_list[3][1]
    model_cnn = weights_list[-2]
    vio_model = weights_list[-1]

    vio_judge = 'no_judge'
    try:
        split_mode = extra_msg[0]
        black_height = extra_msg[1]
        black_pos = extra_msg[2]
        iieaAuditMode = extra_msg[3]
        iieaFilterVehicle = extra_msg[4]
    except:
        split_mode = 111
        black_height = 0
        black_pos = None
        iieaAuditMode = None
        iieaFilterVehicle = None
    judge_infos = {'plate_num': src_plate_num,
                   'vio_code': 1301,
                   'vio_judge': vio_judge,
                   'zebra_line': {},
                   'police': 0,
                   'has_crossline': 0,
                   'vehicle_location': {},
                   'drv_direction': 0,
                   'drive_direction_light': [],
                   'lamp': [],
                   'stop_line': 0,
                   'lane': [],
                   'target_car': [],
                   'judgeConf': 1.0,
                   'modelDetectResults': [], 'split_mode': split_mode, "black_height": black_height,
                   "black_pos": black_pos, "iieaAuditMode": iieaAuditMode, "iieaFilterVehicle": iieaFilterVehicle}
    # 特殊车牌判定
    if not src_plate_num:
        vio_judge = "no_vio_009"
        judge_infos['vio_judge'] = vio_judge
        return vio_judge, judge_infos
    if src_plate_num[0] not in chars:
        vio_judge = 'no_vio_002'
        judge_infos['vio_judge'] = vio_judge
        return vio_judge, judge_infos
    vio_judge = VIO_JUDGE_TOOL().spe_no_vio_judge(src_plate_num, iieaFilterVehicle)
    if vio_judge.startswith('no_vio'):
        judge_infos['vio_judge'] = vio_judge
        return vio_judge, judge_infos
    vio_judge = 'vio'

    no_car_list = []
    plate_list = []
    car_ps_list = []
    tf_ps_list = []
    tf_masks_list = []
    tf_labels_list = []
    target_car_list = []
    direct_list = []

    for i, img in enumerate(file_list):
        if len(file_list) > 3 and i == (len(file_list) - 1):
            continue
        temp_car = {}
        judge_infos['img_id'] = i + 1
        # 找目标车
        main_module = BlendMaskModule(img, weights_list, src_plate_num, save, save_path)
        img_car_ps, plate_num, vio_judge, no_car_img, tf_ps, tf_masks, tf_labels = main_module.match_target_car(
            vio_judge, judge_infos)
        target_car_list.append(img_car_ps)
        no_car_list.append(no_car_img)
        tf_ps_list += tf_ps
        tf_masks_list += tf_masks
        tf_labels_list += tf_labels
        temp_car['plate_num'] = plate_num
        temp_car['car_ps'] = img_car_ps
        judge_infos['target_car'].append(temp_car)
        judge_infos['lane'].append(tf_ps)
        judge_infos['lamp'].append(tf_labels)
        try:
            x_ratio = ((img_car_ps[0] + img_car_ps[2]) / 2) / img.shape[1]
        except:
            x_ratio = 0
        # 0 代表左边， 1代表右边
        if x_ratio <= 0.5:
            judge_infos['vehicle_location'] = 0
        else:
            judge_infos['vehicle_location'] = 1
        plate_list.append(plate_num)
        car_ps_list.append(img_car_ps)
        if vio_judge.startswith("no_vio"):
            break
    if plate_list.count("NO_DETECT") == len(plate_list):
        vio_judge = 'no_vio_006'
        judge_infos['vio_judge'] = vio_judge
        judge_infos['judgeConf'] = 0.9
        return vio_judge, judge_infos
    if vio_judge.startswith("vio"):
        # 识别叠加图的交通标志
        merge_tf_ps, merge_tf_masks, merge_tf_labels, merge_img = BlendMaskModule(None, weights_list).merge_detect(
            no_car_list)
        tf_ps_list += merge_tf_ps
        tf_masks_list += merge_tf_masks
        tf_labels_list += merge_tf_labels
        # 逻辑判定
        if tf_ps_list:
            vio_judge = logic_judge(tf_ps_list, tf_masks_list, tf_labels_list, target_car_list, direct_list)
        else:
            vio_judge = "no_vio_001"
            judge_infos['judgeConf'] = 0.6

        if save and merge_tf_ps:
            try:
                vis_path = error_path if vio_judge.startswith("no_vio") else vio_path
                for idx, box in enumerate(merge_tf_ps):
                    pt1 = (box[0], box[1])
                    pt2 = (box[2], box[3])
                    if merge_tf_labels[idx] == "lane2":
                        color = (0, 255, 255)
                    elif merge_tf_labels[idx].endswith("t"):
                        color = (255, 255, 0)
                    else:
                        color = (255, 0, 255)
                    cv2.rectangle(merge_img, pt1, pt2, color, 2)
                    cv2.putText(merge_img, merge_tf_labels[idx], (box[0], (box[1] + box[3]) // 2),
                                cv2.FONT_HERSHEY_SIMPLEX,
                                1.5,
                                color, 2)
                cv2.imwrite(f"{vis_path}/{vio_judge}_{src_plate_num}_merge.jpg", merge_img)
            except:
                pass
    judge_infos['vio_judge'] = vio_judge
    if vio_judge.startswith('vio'):
        judge_infos['judgeConf'] = 0.9
    if save:
        try:
            vis_path = error_path if vio_judge.startswith("no_vio") else vio_path
            for idx1, boxes in enumerate(judge_infos["lane"]):
                img = file_list[idx1]
                labels = judge_infos["lamp"][idx1]
                target_car = target_car_list[idx1]
                if target_car:
                    cv2.rectangle(img, (target_car[0], target_car[1]), (target_car[2], target_car[3]), (0, 255, 0), 2)
                for idx2, box in enumerate(boxes):
                    pt1 = (box[0], box[1])
                    pt2 = (box[2], box[3])
                    if labels[idx2] == "lane2":
                        color = (0, 255, 255)
                    elif labels[idx2].endswith("t"):
                        color = (255, 255, 0)
                    else:
                        color = (255, 0, 255)
                    cv2.rectangle(img, pt1, pt2, color, 2)
                    cv2.putText(img, labels[idx2], (box[0], (box[1] + box[3]) // 2), cv2.FONT_HERSHEY_SIMPLEX, 1.5,
                                color, 2)
                cv2.imwrite(f"{vis_path}/{vio_judge}_{src_plate_num}_{idx1 + 1}.jpg", img)
        except Exception as e:
            erro_msg = catch_error()
            print(erro_msg)
    return vio_judge, judge_infos


class BlendMaskModule:
    def __init__(self, img, model_list, src_plate="", save=False, save_path=""):
        self.img = img
        self.model_list = model_list
        self.src_plate = src_plate
        self.device = model_list[3][1][2]
        self.save = save
        self.save_path = save_path
        self.tf_cls = ['lane2', "arrow_l", "arrow_s", "arrow_r", 'arrow_s_t', 'arrow_l_t', 'arrow_r_t']

    def match_target_car(self, vio_judge, judge_infos):
        demo = self.model_list[1]
        plate_det_model = self.model_list[0][0]
        plate_rec_model = self.model_list[0][1]
        det1 = BlendMaskDetect(self.img)
        predictions1, kp_mask1 = det1.mask_predict(demo)
        nms_ls1 = det1.nms_class_distinct(predictions1["instances"].pred_boxes.tensor,
                                          predictions1["instances"].scores,
                                          predictions1["instances"].pred_classes,
                                          threshold=0.8,
                                          class_ditinct_threshold=0.85)
        predictions1, kp_mask1 = det1.prediction_nms_filter(predictions1, nms_ls1, kp_mask1)
        car_bbox_ps, mask_list, labels_list, score_list = det1.class_info2(predictions1, Blend_Judge_Vehicle,
                                                                           conf_thresh=0.1,
                                                                           need_mask=True)

        # 筛选交通标志
        tf_ps, tf_masks, tf_labels, _ = det1.class_info2(predictions1, self.tf_cls, conf_thresh=0.1, need_mask=True)

        # 抠出车辆区域
        no_car_img = self.get_no_cars_img(mask_list)
        img_car_ps, plate_num, img_plate_dict, new_car_bbox_ps,first_plate_conf = CarJudge(
            iiea_cfg=judge_infos["iieaFilterVehicle"],need_first_plate=True).confirm_img1_car_ps(self.img,
                                                                           self.src_plate,
                                                                           plate_det_model,
                                                                           plate_rec_model,
                                                                           car_bbox_ps,
                                                                           self.device,
                                                                           label=0,
                                                                           show=self.save,
                                                                           save_path=self.save_path)
        # 过滤老头乐
        vio_judge = filter_low_speed_veh(img_car_ps, car_bbox_ps, labels_list,score_list, self.src_plate, plate_num,
                                         first_plate_conf)

        # 返回检测框和置信度信息
        img_id = judge_infos["img_id"]
        if img_car_ps:
            target_idx = car_bbox_ps.index(img_car_ps)
            percent_coord = convert_src_coord(img_car_ps, self.img, img_id,
                                              judge_infos["split_mode"], judge_infos["black_height"],
                                              judge_infos["black_pos"])
            tmp_res = {
                "objId": img_id,
                "objType": "vehicle",
                "objConf": float(score_list[target_idx]),
                "objCoord": percent_coord
            }
            judge_infos["modelDetectResults"].append(tmp_res)
        # 车牌位置信息
        plate_cor = img_plate_dict[str(img_car_ps)] if img_car_ps else []
        plate_conf = 0
        if plate_cor:
            target_idx = str(img_car_ps) + "conf"
            plate_conf = float(img_plate_dict[target_idx])
            percent_coord = convert_src_coord(plate_cor, self.img, img_id,
                                              judge_infos["split_mode"], judge_infos["black_height"],
                                              judge_infos["black_pos"])
            tmp_res = {
                "objId": img_id,
                "objType": "plate",
                "objConf": plate_conf,
                "objCoord": percent_coord
            }
            judge_infos["modelDetectResults"].append(tmp_res)

        # 警车判定
        if plate_num.endswith("警"):
            vio_judge = 'no_vio_010'
            judge_infos['judgeConf'] = 0.9
        elif img_car_ps:
            if plate_num[0] not in chars and plate_conf >= 0.85:
                vio_judge = "no_vio_007"

            target_idx2 = car_bbox_ps.index(img_car_ps)
            target_label = labels_list[target_idx2]
            blend_score = score_list[target_idx2]
            judge_infos['judgeConf'] = float(blend_score)
            # iiea车辆白名单
            try:
                _, ambulance_filter, fire_filter = judge_infos["iieaFilterVehicle"]
            except:
                ambulance_filter = None
                fire_filter = None
            special_type_list = []
            if ambulance_filter:
                special_type_list.extend(['ambulance_h', 'ambulance_b'])
            if fire_filter:
                special_type_list.extend(['fire_engine_h', 'fire_engine_b'])
            if ambulance_filter is None and fire_filter is None:
                special_type_list = Special_Type
            # 特殊车辆判定
            if target_label in special_type_list:
                if target_label == "ambulance_h":
                    if blend_score >= Ambulance_Head_Thresh:
                        vio_judge = "no_vio_010"
                elif blend_score >= Special_Car_Thresh:
                    vio_judge = "no_vio_010"
        return img_car_ps, plate_num, vio_judge, no_car_img, tf_ps, tf_masks, tf_labels

    def get_no_cars_img(self, mask_list):
        no_car_img = self.img.copy()
        for mask in mask_list:
            no_car_img[mask] = 0
        # num = 1
        # save_path = f"./tmp/{self.src_plate}_{num}.jpg"
        # while os.path.exists(save_path):
        #     num += 1
        #     save_path = f"./tmp/{self.src_plate}_{num}.jpg"
        # cv2.imwrite(save_path, no_car_img)
        return no_car_img

    def merge_detect(self, img_list):
        try:
            h, w, _ = img_list[0].shape
            # merge img
            bg_img = np.zeros((h, w, 3), dtype=np.uint8)
        except:
            bg_img = np.zeros((500, 500, 3), dtype=np.uint8)
        if len(img_list) > 1:

            try:
                for img in img_list:
                    idx = np.where(bg_img == 0)
                    bg_img[idx] = img[idx]
            except:
                erro_msg = catch_error()
                print(erro_msg)
            demo = self.model_list[1]
            det = BlendMaskDetect(bg_img)
            predictions, kp_mask = det.mask_predict(demo)
            nms_ls = det.nms_class_distinct(predictions["instances"].pred_boxes.tensor,
                                            predictions["instances"].scores,
                                            predictions["instances"].pred_classes,
                                            threshold=0.8,
                                            class_ditinct_threshold=0.85)
            predictions, kp_mask = det.prediction_nms_filter(predictions, nms_ls, kp_mask)
            merge_tf_ps, merge_tf_masks, merge_tf_labels, _ = det.class_info2(predictions, self.tf_cls,
                                                                              conf_thresh=0.1, need_mask=True)
        else:
            merge_tf_ps = []
            merge_tf_masks = []
            merge_tf_labels = []
        return merge_tf_ps, merge_tf_masks, merge_tf_labels, bg_img


def find_near_dist_and_direct(mask, point, tf_box, car_w, direct_list, is_up):
    # 判断倾斜程度
    w = tf_box[2] - tf_box[0] + 1e-5
    h = tf_box[3] - tf_box[1]
    tanA = h / w
    angel = math.degrees(math.atan(tanA))
    judge_res = "vio"
    # 直接比较中心点
    if angel >= 60:
        tf_cx = (tf_box[2] + tf_box[0]) // 2
        if is_up and point[0] - tf_cx > 0.25 * car_w:
            judge_res = "no_vio_005"
        elif not is_up and tf_cx - point[0] > 0.25 * car_w:
            judge_res = "no_vio_005"

    else:
        # 找到掩码中值为1的像素位置
        masks_point = np.argwhere(mask == 1)

        if len(masks_point) == 0:
            return judge_res
        masks_point = masks_point[:, ::-1]  # 交换横纵坐标
        # 计算所有mask点与给定点之间的距离
        distances = np.sqrt(np.sum((masks_point - point) ** 2, axis=1))
        min_dist = np.min(distances)
        if min_dist <= 1.5 * car_w:
            # 找到距离最近的点
            nearest_index = np.argmin(distances)
            nearest_point = masks_point[nearest_index]

            # 判断位置关系
            dx = nearest_point[1] - point[1]
            # dy = nearest_point[0] - point[0]

            if dx > 0:
                direction = "right"
            elif dx < 0:
                direction = "left"
            else:
                direction = "same"
            direct_list.append(direction)
    return judge_res


def logic_judge(tf_boxes_list, tf_masks_list, tf_lables_list, target_car_list, direct_list):
    # 行驶方向判定（前后）
    last_cy = None
    up_direct = True
    cx_list = []
    cy_list = []
    car_w = 0
    judge_res = "vio"
    for box in target_car_list:
        if box:
            cy = (box[1] + box[3]) // 20.4
            cy_list.append(cy)
            if last_cy is None:
                last_cy = cy
                # 取第一图的目标车宽
                car_w = abs(box[2] - box[0])
            elif cy <= last_cy:
                up_direct = True
                last_cy = cy
            else:
                up_direct = False
                last_cy = cy

            cx = (box[0] + box[2]) // 2
            cx_list.append(cx)
    print("is_up:", up_direct)

    tmp_tf = [i[-1] for i in tf_lables_list]
    # 车子向上开
    if up_direct:
        for idx, label in enumerate(tf_lables_list):
            tf_box = tf_boxes_list[idx]
            tf_cx = (tf_box[0] + tf_box[2]) // 2
            if label in ["arrow_s", "arrow_l", "arrow_r"] and "t" not in tmp_tf:
                for cx in cx_list:
                    if tf_cx - cx <= car_w * 0.45:
                        judge_res = "no_vio_003"
                        return judge_res
            elif label == 'lane2':

                tf_mask = tf_masks_list[idx]
                for j, cx in enumerate(cx_list):
                    # 目标点
                    target_point = (cx, cy_list[j])

                    # 找到最近的点
                    judge_res = find_near_dist_and_direct(tf_mask, target_point, tf_box, car_w, direct_list, up_direct)
                    if judge_res.startswith("no"):
                        return judge_res

        if direct_list and direct_list.count("left") == len(direct_list):
            judge_res = "no_vio_004"

    else:
        for idx, label in enumerate(tf_lables_list):
            tf_box = tf_boxes_list[idx]
            tf_cx = (tf_box[0] + tf_box[2]) // 2
            if label in ["arrow_s_t", "arrow_l_t", "arrow_r_t"]:
                for cx in cx_list:
                    if cx - tf_cx <= car_w * 0.45:
                        judge_res = "no_vio_003"
                        return judge_res
            elif label == 'lane2':

                tf_mask = tf_masks_list[idx]
                for j, cx in enumerate(cx_list):
                    # 目标点
                    target_point = (cx, cy_list[j])
                    # 找到最近的点
                    judge_res = find_near_dist_and_direct(tf_mask, target_point, tf_box, car_w, direct_list, up_direct)
                    if judge_res.startswith("no"):
                        return judge_res
        if direct_list and direct_list.count("right") == len(direct_list):
            judge_res = "no_vio_004"
    return judge_res
