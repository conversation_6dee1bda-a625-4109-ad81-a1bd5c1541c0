import os
import shutil
import pandas as pd
from tqdm import tqdm
root = r"E:\AI审片项目和资料汇总\宁德\违法数据_20240911104350_1_2000"
img_root = f"{root}/merge_data"
pd_data = pd.read_excel(f"{root}/filter.xlsx")
url_list = pd_data['过车图片1'].tolist()
# pred_res = pd_data['人工审核结果是否一致'].tolist()
check_res = pd_data['人工复审审核状态'].tolist()
convert_res = pd_data['备注'].tolist()
sure_vio = f"{root}/sure_vio"
sure_no_vio = f"{root}/sure_no_vio"
filter_path = f"{root}/filter"
os.makedirs(sure_vio,exist_ok=True)
os.makedirs(sure_no_vio,exist_ok=True)
os.makedirs(filter_path,exist_ok=True)

for idx,name in enumerate(tqdm(url_list)):
    if check_res[idx] == "确认违法":
        if convert_res[idx] == "修正":
            dst_path = sure_no_vio
        else:
            dst_path = sure_vio
    else:
        if convert_res[idx] == "修正":
            dst_path = sure_vio
        else:
            dst_path = sure_no_vio
    shutil.copy(f"{img_root}/{name}", dst_path)

