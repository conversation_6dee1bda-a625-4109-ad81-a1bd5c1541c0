import cv2
import sys

sys.path.append('..')

import numpy as np
from ai_judge_code_gy.judge_tools import Tools


# 替换红绿灯模型后已弃用
class LightJudge():
    def __init__(self):
        pass

    def judge_light_ps_old(self, light_dict):
        ps_dict = {}
        for k, v in light_dict.items():
            if len(v) == 1:
                ps_dict[str(v[0])] = k
            if len(v) > 1:
                for i in v:
                    ps_dict[str(i)] = k
        ps_list = list(ps_dict.keys())
        ps_label = list(ps_dict.values())
        ps_list = [eval(i) for i in ps_list]
        drop_list = []
        final_dict = {}
        for i in range(len(ps_list)):
            ps = ps_list[i]
            ps_xvalue = [ps[0], ps[1]]
            ps_yvalue = [ps[2], ps[3]]
            final_dict[str([ps_xvalue, ps_yvalue])] = [ps_label[i]]
            if ps in drop_list:
                continue
            for psx in ps_list[i + 1:]:
                psx_xvalue = [psx[0], psx[1]]
                psx_yvalue = [psx[2], psx[3]]
                iou = Tools().IOU(np.array(ps[:2]), np.array(ps[2:]), np.array(psx[:2]), np.array(psx[2:]))
                if iou <= 0.2:
                    pass
                else:
                    drop_list.append([psx_xvalue, psx_yvalue])
                    if not ps_label[ps_list.index(psx)] in final_dict[str([ps_xvalue, ps_yvalue])]:
                        final_dict[str([ps_xvalue, ps_yvalue])].append(ps_label[ps_list.index(psx)])
        for i in drop_list:
            try:
                del final_dict[str(i)]
            except:
                pass
        res = sorted(final_dict.items(), key=lambda x: x[0], reverse=False)
        return res

    def judge_light_ps(self, light_dict):
        ps_dict = {}
        for k, v in light_dict.items():
            if len(v) == 1:
                if str(v[0]) not in ps_dict.keys():
                    ps_dict[str(v[0])] = [k]
                else:
                    ps_dict[str(v[0])].append(k)
            elif len(v) > 1:
                for i in v:
                    if str(i) not in ps_dict.keys():
                        ps_dict[str(i)] = [k]
                    else:
                        ps_dict[str(i)].append(k)
        ps_list = list(ps_dict.keys())
        ps_label = list(ps_dict.values())
        ps_list = [eval(i) for i in ps_list]
        drop_list = []
        final_dict = {}
        for i in range(len(ps_list)):
            ps = ps_list[i]
            ps_xvalue = [ps[0], ps[1]]
            ps_yvalue = [ps[2], ps[3]]
            if ps in drop_list:
                continue
            final_dict[str([ps_xvalue, ps_yvalue])] = ps_label[i]
            for psx in ps_list[i + 1:]:
                iou = Tools().IOU(np.array(ps[:2]), np.array(ps[2:]), np.array(psx[:2]), np.array(psx[2:]))
                if iou <= 0.2:
                    pass
                else:
                    # print("success!")
                    drop_list.append(psx)
                    final_dict[str([ps_xvalue, ps_yvalue])] += ps_label[ps_list.index(psx)]
        for k, v in final_dict.items():
            thresh = [eval(i.split("-")[-1]) for i in v]
            idx = np.argmax(thresh)
            final_dict[k] = v[idx]
        res = sorted(final_dict.items(), key=lambda x: x[0], reverse=False)
        res = [list(i) for i in res]
        # 同图不同目标位置都识别为直行结果
        direct = [i[1].split("-")[0][-1] for i in res]
        thresh = [eval(i[1].split("-")[1]) for i in res]
        color = [i[1].split("_")[0] for i in res]
        if direct.count("s") > 1 and direct[0] == direct[1] == "s" and thresh[0] < thresh[1]:
            res[0][1] = f'{color[0]}_l-0'
        if direct == ["l", "l", "r"]:
            res[1][1] = f'{color[1]}_s-0'
        return res

    # def confirm_light(self, img_np, light_net, light_meta, light_threshold, show=False):
    #     # label 代表 0 不直接进行切片, 1 代表默认进行切片检测红绿灯
    #     n = 0
    #     # Iorig = cv2.imread(img_path)
    #     Iorig = img_np
    #     WH = np.array(Iorig.shape[1::-1], dtype=float)
    #     light_dict = {}
    #     # R1 = dn.detect(light_net, light_meta, img_path.encode('utf-8'), light_threshold)
    #     R1 = dn.detect_image(light_net, light_meta, img_np, light_threshold)
    #     R1 = [r for r in R1 if r[0] in [b'red_s', b'red_l', b'red_r', b'green_s', b'green_l', b'green_r']]
    #     if len(R1):
    #         for i, r in enumerate(R1):
    #             cx, cy, w, h = (np.array(r[2]) / np.concatenate((WH, WH))).tolist()
    #             thresh = f"{r[1]}"[:4] if len(f"{r[1]}") >= 4 else f"{r[1]}"
    #             class_name = r[0].decode('utf-8') + "-" + thresh
    #             # print("r2",np.array(r[2]))
    #             # print(cx, cy, w, h)
    #             tl = [cx - w / 2., cy - h / 2.]
    #             br = [cx + w / 2., cy + h / 2.]
    #             x1 = int(max(Iorig.shape[1] * tl[0], 0))
    #             y1 = int(max(Iorig.shape[0] * tl[1], 0))
    #             x2 = int(max(Iorig.shape[1] * br[0], 0))
    #             y2 = int(max(Iorig.shape[0] * br[1], 0))
    #             if class_name in light_dict.keys():
    #                 light_dict[class_name].append([x1, y1, x2, y2])
    #             else:
    #                 light_dict[class_name] = [[x1, y1, x2, y2]]
    #             n += 1
    #             # =====================================================================
    #             # cv2.imwrite('%s/temp_%s_%s.png' % (save_dir, class_name, str(n)), temp_img)
    #             # =====================================================================
    #     print("light_dict", light_dict)
    #     res = self.judge_light_ps(light_dict)
    #     print("res", res)
    #     if show:
    #         n = 80
    #         for item in res:
    #             # print(item)
    #             point = eval(item[0])
    #             labels = item[1]
    #             # print(point[0][0])
    #             pt_tx = int(point[0][0])
    #             pt_ty = int(point[0][1])
    #             pt_bx = int(point[1][0])
    #             pt_by = int(point[1][1])
    #             cv2.rectangle(Iorig, (pt_tx, pt_ty), (pt_bx, pt_by), (0, 255, 0), 3)
    #             cv2.putText(Iorig, str(labels), (int(point[0][0]), int((point[0][1]) + n)),
    #                         cv2.FONT_HERSHEY_COMPLEX, 1.5, (0, 0, 255), 3)
    #             n += 80
    #         cv2.imshow('img1', cv2.resize(Iorig, None, fx=0.3, fy=0.3))
    #         cv2.waitKey(0)
    #         cv2.destroyAllWindows()
    #     return res

    # 已弃用
    def status_change(self, res, img1_res, img2_res, img3_res):
        if 'red_s' in res and 'green_s' in res:
            red_s_list = []
            green_s_list = []
            try:
                for i in img1_res:
                    # print(i)
                    if ['red_s'] in i:
                        cor = i[0]
                        red_s_list.append(cor)
                    if ['green_s'] in i:
                        cor = i[0]
                        green_s_list.append(cor)
            except:
                pass
            try:
                for i in img2_res:
                    # print(i)
                    if ['red_s'] in i:
                        cor = i[0]
                        red_s_list.append(cor)
                    if ['green_s'] in i:
                        cor = i[0]
                        green_s_list.append(cor)
            except:
                pass
            try:
                for i in img3_res:
                    # print(i)
                    if ['red_s'] in i:
                        cor = i[0]
                        red_s_list.append(cor)
                    if ['green_s'] in i:
                        cor = i[0]
                        green_s_list.append(cor)
            except:
                pass
            # print(red_s_list)
            # print(green_s_list)
            red_s_p_c = []
            for res_p in red_s_list:
                res_p = eval(res_p)
                # print(type(res_p))
                x1 = res_p[0][0]
                x2 = res_p[1][0]
                # print(x1, x2)
                x_c = (x1 + x2) / 2
                red_s_p_c.append(x_c)
            green_s_p_c = []
            for res_p in green_s_list:
                res_p = eval(res_p)
                # print(type(res_p))
                x1 = res_p[0][0]
                x2 = res_p[1][0]
                # print(x1, x2)
                x_c = (x1 + x2) / 2
                green_s_p_c.append(x_c)
            red_s_c = np.mean(red_s_p_c)
            green_s_c = np.mean(green_s_p_c)
            # print(red_s_c)
            # print(green_s_c)
            if red_s_c > green_s_c:
                res.remove('green_s')
                res.append('green_l')
            elif red_s_c < green_s_c:
                res.remove('red_s')
                res.append('red_l')
            else:
                pass
        return res


# 图片切分不精准导致y轴方向有偏差不能直接进行iou计算
def get_xiou(box1, box2):
    x1 = (box1[0][0], box1[1][0])
    x2 = (box2[0][0], box2[1][0])
    top = min(x1[1], x2[1]) - max(x1[0], x2[0])
    bottom = max(x1[1], x2[1]) - min(x1[0], x2[0])
    return top / bottom


# 三图红绿灯结果逻辑整合为红绿灯违法判定结果
def judge_light_status(img1_res, img2_res, img3_res, light_config):
    vio_judge = "vio"
    status = []
    status_cx = []
    status_thresh = []
    add_l = False
    left_ps = []
    for i1 in img1_res:
        single_left = True  # 判定当只有第一张图检测到某位置的红绿灯
        ps1 = eval(i1[0])
        l1 = i1[1]
        light1 = l1.split("-")[0]
        thresh1 = eval(l1.split("-")[1])
        cx = (ps1[0][0] + ps1[1][0]) // 2
        for i3 in img3_res:
            ps3 = eval(i3[0])
            l3 = i3[1]
            light3 = l3.split("-")[0]
            # 第一二图左转黄灯、三图左转红灯
            cx3 = (ps3[0][0] + ps3[1][0]) // 2
            if light3[-1] == 'l':
                left_ps.append([light3, cx3])
            iou13 = get_xiou(ps1, ps3)
            if iou13 > 0.5:
                single_left = False
                thresh_list = [thresh1, eval(l3.split("-")[1])]
                if light1 == light3:
                    # judge.append([light1[-1]] + [cx])
                    status.append(light1)
                    status_cx.append(cx)
                    status_thresh.append(np.max(thresh_list))
                elif light1[-1] == light3[-1] == 'l':
                    # judge.append([light1[-1]] + [cx])
                    add_l = True
                elif light1[0] == light3[0]:
                    # 结合二图红绿灯结果修正
                    color = light1.split("_")[0]
                    direction_list = [light1[-1], light3[-1]]
                    idx = np.argmax(thresh_list)
                    new_thresh = thresh_list[idx]
                    new_light = color + "_" + direction_list[idx]
                    for i2 in img2_res:
                        # 第一二图左转黄灯、三图左转红灯
                        if light3[-1] == 'l':
                            left_ps.append([light3, cx3])
                        ps2 = eval(i2[0])
                        l2 = i2[1]
                        light2 = l2.split("-")[0]
                        cx2 = (ps2[0][0] + ps2[1][0]) // 2
                        if light2[-1] == 'l':
                            left_ps.append([light2, cx2])
                        iou12 = get_xiou(ps1, ps2)
                        if iou12 > 0.5:
                            thresh_list = [thresh1, eval(l2.split("-")[1]), eval(l3.split("-")[1])]
                            direction_list = [light1[-1], light2[-1], light3[-1]]
                            if direction_list.count("l") > 1:
                                add_l = True
                            idx = np.argmax(thresh_list)
                            temp_dt = direction_list.copy()
                            temp_dt.pop(idx)
                            temp_thresh = thresh_list.copy()
                            temp_thresh.pop(idx)
                            dist_thresh = thresh_list[idx] - np.max(temp_thresh)
                            new_thresh = thresh_list[idx]
                            if direction_list.count(direction_list[idx]) == 2:  # 12或23
                                new_light = color + "_" + direction_list[idx]
                            elif temp_dt[0] == temp_dt[1]:
                                if dist_thresh > 0.3:
                                    new_light = color + "_" + direction_list[idx]
                                else:
                                    new_light = color + "_" + temp_dt[0]
                                    new_thresh = np.max(temp_thresh)
                            elif dist_thresh > 0:
                                new_light = color + "_" + direction_list[idx]
                            # 修正二图识别结果
                            i2[1] = new_light + "-" + str(new_thresh)
                    status.append(new_light)
                    status_cx.append(cx)
                    status_thresh.append(new_thresh)
                    # 修正一三张图的识别结果
                    i1[1] = i3[1] = new_light + "-" + str(new_thresh)
        if single_left and not add_l:
            if light1[-1] == 'l':
                add_l = True
            # elif light1[0] == 'g':
            #     left_ps.append([light1, cx])
            else:
                left_ps.append([light1, cx])
    status_direct = [i[-1] for i in status]
    if "red_s" in status and "green_s" in status:
        idx_r = status.index("red_s")
        idx_g = status.index("green_s")
        # 红绿同亮(过滤误识别红灯)
        if abs(status_cx[idx_r] - status_cx[idx_g]) < 20:
            status.pop(idx_r)
            status_thresh.pop(idx_r)
            status_cx.pop(idx_r)
            vio_judge = "no_vio_007"

        elif status_cx[idx_r] > status_cx[idx_g]:
            if status_thresh[idx_r] < status_thresh[idx_g] and light_config is None:
                # 无法补充右转红灯存在特殊情况
                status.pop(idx_r)
                status_thresh.pop(idx_r)
                status_cx.pop(idx_r)
            elif status_thresh[idx_r] > status_thresh[idx_g] and status_direct.count("l") == 0:
                status[idx_g] = "green_l"
                status_thresh[idx_g] = 0
        else:
            if status_thresh[idx_r] < status_thresh[idx_g] and status_direct.count("l") == 0:
                status[idx_r] = "red_l"
                status_thresh[idx_r] = 0

    if "red_l" in status and "green_l" in status:
        idx_r = status.index("red_l")
        idx_g = status.index("green_l")
        # 红绿同亮(过滤误识别红灯)
        if abs(status_cx[idx_r] - status_cx[idx_g]) < 20:
            status.pop(idx_r)
            status_thresh.pop(idx_r)
            status_cx.pop(idx_r)
            vio_judge = "no_vio_007"

        # 绿 红
        elif status_cx[idx_r] > status_cx[idx_g]:
            if status_thresh[idx_r] < status_thresh[idx_g] and status_direct.count("s") == 0:
                status[idx_r] = "red_s"
                status_thresh[idx_r] = 0
        # 红 绿
        else:
            if status_thresh[idx_r] < status_thresh[idx_g] and light_config is None:
                status.pop(idx_r)
                status_thresh.pop(idx_r)
                status_cx.pop(idx_r)
            elif status_thresh[idx_r] > status_thresh[idx_g] and status_direct.count("s") == 0:
                status[idx_g] = "green_s"
                status_thresh[idx_g] = 0
    tmp_status = [i.split("_")[0] for i in status]
    if "yellow" in tmp_status and "red" in tmp_status:
        # 红黄同亮
        del_idx = []
        tmp_dict = {value: [index for index, element in enumerate(tmp_status) if element == value]
                    for value in set(tmp_status) if value in ["yellow", "red"]}
        for idx1 in tmp_dict["yellow"]:
            yellow_cx = status_cx[idx1]
            for idx2 in tmp_dict["red"]:
                red_cx = status_cx[idx2]
                if abs(yellow_cx - red_cx) < 20:
                    del_idx.append(idx2)

        status = [item for idx, item in enumerate(status) if idx not in del_idx]
        status_thresh = [item for idx, item in enumerate(status_thresh) if idx not in del_idx]
        status_cx = [item for idx, item in enumerate(status_cx) if idx not in del_idx]
        if del_idx:
            vio_judge = "no_vio_007"

    if status_direct.count("l") > 0:
        add_l = False
    elif not add_l and status_direct.count("s") == 1:
        idx = status_direct.index('s')
        judge_cx = status_cx[idx]
        for i in left_ps:
            if i[-1] < judge_cx and i[0][-1] == "l":
                add_l = True
                break
    if add_l:
        status.append("green_l")
        status_cx.append(0)
    # 预设的红绿灯信息
    try:
        if light_config is not None:
            status_cx, status = zip(*sorted(zip(status_cx, status), key=lambda x: x[0]))
            status = list(status)
            pred_color = [i.split("_")[0] for i in status]
            pred_direct = [i[-1] for i in status]
            if len(light_config) == len(status):
                status = [pred_color[i] + "_" + light_config[i] for i in range(len(status))]
            elif len(light_config) > len(status) > 0:  # 漏识别、被遮挡、黄灯
                for i in light_config:
                    if i not in pred_direct:
                        status.append(f"green_{i}")
            elif len(light_config) < len(status):  # 识别到非机动车信号灯、识别到多个路口的信号灯、信号灯绿灯和红灯同时亮起
                pass
    except:
        pass
    return list(set(status)), vio_judge


def judge_light_status_with_config(img1_res, img2_res, img3_res, light_config):
    status = []
    status_cx = []
    status_thresh = []
    add_l = False
    left_ps = []
    for i1 in img1_res:
        single_left = True  # 判定当只有第一张图检测到某位置的红绿灯
        ps1 = eval(i1[0])
        l1 = i1[1]
        light1 = l1.split("-")[0]
        thresh1 = eval(l1.split("-")[1])
        cx = (ps1[0][0] + ps1[1][0]) // 2
        for i3 in img3_res:
            ps3 = eval(i3[0])
            l3 = i3[1]
            light3 = l3.split("-")[0]
            # 第一二图左转黄灯、三图左转红灯
            cx3 = (ps3[0][0] + ps3[1][0]) // 2
            if light3[-1] == 'l':
                left_ps.append([light3, cx3])
            iou13 = get_xiou(ps1, ps3)
            if iou13 > 0.5:
                single_left = False
                thresh_list = [thresh1, eval(l3.split("-")[1])]
                if light1 == light3:
                    # judge.append([light1[-1]] + [cx])
                    status.append(light1)
                    status_cx.append(cx)
                    status_thresh.append(np.max(thresh_list))
                elif light1[-1] == light3[-1] == 'l':
                    # judge.append([light1[-1]] + [cx])
                    add_l = True
                elif light1[0] == light3[0]:
                    # 结合二图红绿灯结果修正
                    color = light1.split("_")[0]
                    direction_list = [light1[-1], light3[-1]]
                    idx = np.argmax(thresh_list)
                    new_thresh = thresh_list[idx]
                    new_light = color + "_" + direction_list[idx]
                    for i2 in img2_res:
                        # 第一二图左转黄灯、三图左转红灯
                        if light3[-1] == 'l':
                            left_ps.append([light3, cx3])
                        ps2 = eval(i2[0])
                        l2 = i2[1]
                        light2 = l2.split("-")[0]
                        cx2 = (ps2[0][0] + ps2[1][0]) // 2
                        if light2[-1] == 'l':
                            left_ps.append([light2, cx2])
                        iou12 = get_xiou(ps1, ps2)
                        if iou12 > 0.5:
                            thresh_list = [thresh1, eval(l2.split("-")[1]), eval(l3.split("-")[1])]
                            direction_list = [light1[-1], light2[-1], light3[-1]]
                            if direction_list.count("l") > 1:
                                add_l = True
                            idx = np.argmax(thresh_list)
                            temp_dt = direction_list.copy()
                            temp_dt.pop(idx)
                            temp_thresh = thresh_list.copy()
                            temp_thresh.pop(idx)
                            dist_thresh = thresh_list[idx] - np.max(temp_thresh)
                            new_thresh = thresh_list[idx]
                            if direction_list.count(direction_list[idx]) == 2:
                                new_light = color + "_" + direction_list[idx]
                            elif temp_dt[0] == temp_dt[1]:
                                if dist_thresh > 0.3:
                                    new_light = color + "_" + direction_list[idx]
                                else:
                                    new_light = color + "_" + temp_dt[0]
                                    new_thresh = np.max(temp_thresh)
                            elif dist_thresh > 0:
                                new_light = color + "_" + direction_list[idx]
                            # 修正二图识别结果
                            i2[1] = new_light + "-" + str(new_thresh)
                    status.append(new_light)
                    status_cx.append(cx)
                    status_thresh.append(new_thresh)
                    # 修正一三张图的识别结果
                    i1[1] = i3[1] = new_light + "-" + str(new_thresh)
        if single_left and not add_l:
            if light1[-1] == 'l':
                add_l = True
            # elif light1[0] == 'g':
            #     left_ps.append([light1, cx])
            else:
                left_ps.append([light1, cx])
    status_direct = [i[-1] for i in status]
    if "red_s" in status and "green_s" in status:
        idx_r = status.index("red_s")
        idx_g = status.index("green_s")
        if status_cx[idx_r] > status_cx[idx_g]:
            if status_thresh[idx_r] < status_thresh[idx_g]:
                status.pop(idx_r)
                status_thresh.pop(idx_r)
                status_cx.pop(idx_r)
            elif status_thresh[idx_r] > status_thresh[idx_g] and status_direct.count("l") == 0:
                status[idx_g] = "green_l"
                status_thresh[idx_g] = 0
        else:
            if status_thresh[idx_r] < status_thresh[idx_g] and status_direct.count("l") == 0:
                status[idx_r] = "red_l"
                status_thresh[idx_r] = 0

    if "red_l" in status and "green_l" in status:
        idx_r = status.index("red_l")
        idx_g = status.index("green_l")
        # 绿 红
        if status_cx[idx_r] > status_cx[idx_g]:
            if status_thresh[idx_r] < status_thresh[idx_g] and status_direct.count("s") == 0:
                status[idx_r] = "red_s"
                status_thresh[idx_r] = 0
        # 红 绿
        else:
            if status_thresh[idx_r] < status_thresh[idx_g]:
                status.pop(idx_r)
                status_thresh.pop(idx_r)
                status_cx.pop(idx_r)
            elif status_thresh[idx_r] > status_thresh[idx_g] and status_direct.count("s") == 0:
                status[idx_g] = "green_s"
                status_thresh[idx_g] = 0
    if status_direct.count("l") > 0:
        add_l = False
    elif not add_l and status_direct.count("s") == 1:
        idx = status_direct.index('s')
        judge_cx = status_cx[idx]
        for i in left_ps:
            if i[-1] < judge_cx and i[0][-1] == "l":
                add_l = True
                break
    if add_l:
        status.append("green_l")
    return list(set(status))


# 旧版本已弃用
def judge_light_status_old(img1_np, img2_np, img3_np, img1_res, img2_res, img3_res, show=False):
    img1 = img1_np
    img2 = img2_np
    img3 = img3_np
    temp = []
    judge = []
    status = []
    status_cx = []
    img2_l = []
    img2_l.extend(i[1][n] for i in img2_res for n in range(len(i[1])))
    add_l = False
    left_ps = []
    for i1 in img1_res:
        ps1 = eval(i1[0])
        l1 = i1[1]
        for i3 in img3_res:
            ps3 = eval(i3[0])
            l3 = i3[1]
            cx = (ps3[0][0] + ps3[1][0]) // 2
            for i in l3:
                if i[-1] == "l":
                    left_ps.append(cx)
                    break
            iou = get_xiou(ps1, ps3)
            if iou > 0.5:
                l = list(set(l1 + l3))
                if len(l1) == len(l3) == 1:
                    if l1 == l3:
                        judge.append([l1[0][-1]] + [cx])
                        status += l1
                        status_cx.append(cx)
                    elif l1[0][-1] == l3[0][-1]:
                        judge.append([l1[0][-1]] + [cx])
                        if l1[0][-1] == l3[0][-1] == 'l':
                            add_l = True
                    elif l1[0][0] == l3[0][0]:
                        temp.append(l + [cx])
                else:
                    inter = set(l1).intersection(set(l3))
                    if len(inter) == 1:
                        status.append(list(inter)[0])
                        status_cx.append(cx)
                        judge.append([list(inter)[0][-1]] + [cx])
                    elif len(inter) > 1:
                        temp.append(list(inter) + [cx])
                    t = True
                    for i in range(len(l) - 1):
                        if l[i][0] != l[i + 1][0]:
                            t = False
                    if t:
                        temp.append(l + [cx])

    for i in temp:
        cx = i[-1]
        l_num = 0
        s_num = 0
        r_num = 0
        color = i[0].split("_")[0]
        for j in judge:
            j_cx = j[-1]
            if cx < j_cx:
                if j[0] == "s":
                    l_num += 1
                elif j[0] == "r":
                    l_num += 1
                    s_num += 1
            else:
                if j[0] == "l":
                    s_num += 1
                    r_num += 1
                elif j[0] == "s":
                    r_num += 1
        num_list = [l_num, s_num, r_num]
        name_list = ["l", "s", "r"]
        idx = np.argmax(num_list)
        new_light = f"{color}_" + name_list[idx]
        if max(num_list):
            status.append(new_light)
            status_cx.append(cx)
    left_ps = np.array(left_ps)
    if 'red_l' in status or 'green_l' in status:
        add_l = False
    elif not add_l:
        for i in judge:
            if i[0] == "l":
                add_l = True
                break
            elif i[0] == "s":
                if sum(left_ps < i[-1]):
                    add_l = True
                    break
    if "red_s" in status and "green_s" in status:
        idx_r = status.index("red_s")
        idx_g = status.index("green_s")
        if status_cx[idx_r] > status_cx[idx_g]:
            status.pop(idx_r)

    if add_l:
        status.append("green_l")
    # add_s
    if not status and len(img1_res) == len(img2_res) == len(img3_res) == 1:
        add_s = True
        all_res = img1_res[0][1] + img2_res[0][1] + img3_res[0][1]
        last_color = None
        for i in all_res:
            if last_color is None:
                last_color = i.split("_")[0]
            elif last_color != i.split("_")[0]:
                add_s = False
                break
            else:
                last_color = i.split("_")[0]
            if i.split("_")[1] == "r":
                add_s = False
                break

        if add_s:
            status.append(f"{last_color}_s")

    return list(set(status))
