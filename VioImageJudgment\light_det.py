import argparse
import os
import time
from pathlib import Path

import cv2
import torch
import torch.backends.cudnn as cudnn
from numpy import random

from models.experimental import attempt_load
from utils.datasets import LoadStreams, LoadImages, letterbox
from utils.general import check_img_size, check_requirements, check_imshow, non_max_suppression, apply_classifier, \
    scale_coords, xyxy2xywh, strip_optimizer, set_logging, increment_path
from utils.plots import plot_one_box
from utils.torch_utils import select_device, load_classifier, time_synchronized, TracedModel
import numpy as np


def load(weights="best.pt", imgsz=1472, trace=False, device=None):
    # Directories
    if device is None:
        device = torch.device("cuda:0" if torch.cuda.is_available() else "cpu")
    else:
        device = torch.device(device)
    half = device.type != 'cpu'  # half precision only supported on CUDA
    # Load model
    model = attempt_load([weights], map_location=device)  # load FP32 model
    stride = int(model.stride.max())  # model stride
    imgsz = check_img_size(imgsz, s=stride)  # check img_size

    if trace:
        model = TracedModel(model, device, imgsz)

    if half:
        model.half()  # to FP16

    # Run inference
    if device.type != 'cpu':
        model(torch.zeros(1, 3, imgsz, imgsz).to(device).type_as(next(model.parameters())))  # run once
    return model, imgsz, stride, device


def detect(img0, model, img_size=1472, stride=32, device=None, conf_thres=0.25, iou_thres=0.45,
           classes=None, agnostic_nms=True, augment=False):
    if device is None:
        device = torch.device("cuda:0" if torch.cuda.is_available() else "cpu")
    half = device.type != 'cpu'  # half precision only supported on CUDA
    # Padded resize
    img = letterbox(img0, img_size, stride=stride)[0]

    # Convert
    img = img[:, :, ::-1].transpose(2, 0, 1)  # BGR to RGB, to 3x416x416
    img = np.ascontiguousarray(img)

    img = torch.from_numpy(img).to(device)
    img = img.half() if half else img.float()  # uint8 to fp16/32
    img /= 255.0  # 0 - 255 to 0.0 - 1.0
    if img.ndimension() == 3:
        img = img.unsqueeze(0)
    # Inference
    with torch.no_grad():
        pred = model(img, augment=augment)[0]
    # Apply NMS
    pred = non_max_suppression(pred, conf_thres, iou_thres, classes=classes, agnostic=agnostic_nms)
    # Process detections
    l_res = []
    names = ['green_s', 'green_r', 'green_l', 'red_s', 'red_r', 'red_l', 'yellow_s', 'yellow_r', 'yellow_l']
    for idx, det in enumerate(pred):  # detections per image
        if len(det):
            # print("det",det.shape)    # [n,6]
            # Rescale boxes from img_size to im0 size
            det[:, :4] = scale_coords(img.shape[2:], det[:, :4], img0.shape).round()
            # 转换格式
            det = det.cpu()
            conf = det[:, 4].data.numpy()
            conf = [round(i, 3) for i in conf]
            a = np.asarray(det, dtype=np.int32)

            for idx, i in enumerate(a):
                box = [i[0], i[1], i[2], i[3]]
                cls = names[i[-1]]
                l_res.append((box, cls, conf[idx]))
    return l_res


def img_split(img):
    h, w, _ = img.shape
    # 4图横向拼接
    cx = w // 2
    cy = h // 2
    img1 = img[:cy, :cx]
    img2 = img[:cy, cx:]
    img3 = img[cy:2 * cy, :cx]
    img4 = img[cy:2 * cy, cx:]
    img_list = [img1, img2, img3, img4]

    return img_list


if __name__ == '__main__':
    img_dir = "./img_data"
    save_path = "./test_results"
    os.makedirs(save_path, exist_ok=True)
    # 加载模型
    lp_model, imgsz, stride, device = load("./light.pt", trace=False)
    for name in os.listdir(img_dir):
        img_path = f"{img_dir}/{name}"
        img_list = img_split(cv2.imread(img_path))[:3]
        save_name = name.split(".")[0]
        for idx, img in enumerate(img_list):
            print(f"{name} 第{idx + 1}张图正在检测中...")
            st = time.time()
            h = img.shape[0]
            light_res = detect(img[:h // 2], lp_model, imgsz, stride, device, conf_thres=0.2,
                               iou_thres=0.45)

            print(light_res, f" 耗时：{time.time() - st:.2f}秒\n")
            # 结果可视化
            for idx2, res in enumerate(light_res):
                box = res[0]  # 坐标框
                cls = res[1]  # 类别
                conf = res[2]  # 置信度
                if cls.startswith("red"):
                    color = (0, 0, 255)
                elif cls.startswith("green"):
                    color = (0, 255, 0)
                else:
                    color = (0, 255, 255)
                cv2.rectangle(img, (box[0], box[1]), (box[2], box[3]), color, 2)
                cv2.putText(img, f"{cls}-{conf:.2f}", (box[2], int((box[1] + box[3]) // 2) * (idx2 + 1)),
                            cv2.CHAIN_APPROX_SIMPLE, 1,
                            color, 2)
                cv2.imwrite(f"{save_path}/{save_name}_{idx + 1}.jpg", img)
