import os
import shutil
from tqdm import tqdm


def get_device_num(data_list):
    d = {}
    del_s1 = []
    del_s2 = []
    for i in data_list:
        device = i.split("_")[0]
        if device not in d.keys():
            d[device] = 1
        else:
            d[device] += 1
        if device == "320100000000013319":
            del_s1.append(i)
        if device == "320100000000012483":
            del_s2.append(i)
    # print(len(del_s1), len(del_s2))
    return d, set(del_s1), set(del_s2)


def print_mode(pred_vio, pred_waste, vio_error, waste_error):
    total_num = pred_vio + pred_waste  # 总数

    truth_vio = pred_vio - vio_error + waste_error  # 真实正片数
    truth_waste = pred_waste - waste_error + vio_error  # 真实废片数
    waste_rate = truth_waste / total_num

    vio_recall = (pred_vio - vio_error) / truth_vio
    vio_precision = (pred_vio - vio_error) / pred_vio

    waste_recall = (pred_waste - waste_error) / truth_waste
    waste_precision = (pred_waste - waste_error) / pred_waste
    # model_precision = (total_num - vio_error - waste_error) / total_num
    # print(f"正片检出率:{vio_recall * 100:.2f}%\t正片检准率:{vio_precision * 100:.2f}%")
    # print(f"废片检出率:{waste_recall * 100:.2f}%\t正片检准率:{waste_precision * 100:.2f}%\n")
    model_filter_rate = pred_waste / total_num
    model_precision = (total_num-vio_error-waste_error)/total_num
    print(
        f"共计{total_num}组图片，模型判定正片{pred_vio}组（{vio_error}组误判），判定废片{pred_waste}组（{waste_error}组误判），人工审核正片{truth_vio}组，人工审核废片{truth_waste}组，废片率为{waste_rate * 100:.2f}%\n正片检出率：{vio_recall * 100:.2f}%，正片检准率：{vio_precision * 100:.2f}%，废片检出率：{waste_recall * 100:.2f}%，废片检准率：{waste_precision * 100:.2f}%\n废片过滤比例：{model_filter_rate * 100:.2f}%，模型准确率：{model_precision*100:.2f}%")


if __name__ == '__main__':
    del_device1 = False
    del_device2 = False
    cp_error_data = True
    pred_root = r"E:\AI审片项目和资料汇总\已审核数据\闯红灯\高检出率测试\1625_jzfeat"
    sure_root = r"E:\AI审片项目和资料汇总\已审核数据\闯红灯\高检出率测试"  # jz328-407  jz408-415
    sure_no_vio = set(os.listdir(f"{sure_root}/废片"))
    sure_vio = set(os.listdir(f"{sure_root}/正片"))

    pred_no_vio = set(os.listdir(f"{pred_root}/no_vio"))
    pred_vio = set(os.listdir(f"{pred_root}/vio"))
    # os.makedirs("./result", exist_ok=True)
    pred_no_vio_error = pred_no_vio.intersection(sure_vio)
    pred_vio_error = pred_vio.intersection(sure_no_vio)
    if cp_error_data:
        try:
            vio_vis = os.listdir(f"{pred_root}/vis/vio")
        except:
            vio_vis = []
        error_vis = f"{pred_root}/error_vis"
        os.makedirs(error_vis, exist_ok=True)
        error_src = f"{pred_root}/error_src"
        os.makedirs(error_src, exist_ok=True)
        for i in tqdm(pred_vio_error):
            # 复制原图
            if not os.path.exists(f"{error_src}/{i}"):
                shutil.copy(f"{pred_root}/vio/{i}", error_src)

            plate = i.split("_")[-2]  # index
            for j in vio_vis:
                src_plate = j.split("_")[-2]
                if src_plate == plate and not os.path.exists(f"{error_vis}/{j}"):
                    shutil.copy(f"{pred_root}/vis/vio/{j}", error_vis)
        try:
            no_vio_vis = os.listdir(f"{pred_root}/vis/no_vio")
        except:
            no_vio_vis = []
        for i in tqdm(pred_no_vio_error):
            # 复制原图
            if not os.path.exists(f"{error_src}/{i}"):
                shutil.copy(f"{pred_root}/no_vio/{i}", error_src)

            plate = i.split("_")[-2]  # index
            for j in no_vio_vis:
                src_plate = j.split("_")[-2]
                if src_plate == plate and not os.path.exists(f"{error_vis}/{j}"):
                    shutil.copy(f"{pred_root}/vis/no_vio/{j}", error_vis)

        vis_dir = f"{pred_root}/error_vis"
        sim_dir = f"{pred_root}/vis/similar_show"
        head_dir = f"{pred_root}/vis/del_head"
        vis_list = os.listdir(vis_dir)
        for name in tqdm(vis_list):
            plate = name.split("_")[-2]
            tmp = ""
            tmp2 = name.split("_")[:-1]
            for i in tmp2:
                tmp += f"{i}_"
            sim_tmp = ["12", "23", "13", "21"]
            for i in sim_tmp:
                src = f"{sim_dir}/{plate}_{i}.jpg"
                dst = f"{vis_dir}/{tmp}img4_{i}.jpg"
                if os.path.exists(src) and not os.path.exists(dst):
                    shutil.copy(src, dst)
            num = 1
            src = f"{head_dir}/{plate}_{num}.jpg"
            while os.path.exists(src):
                dst = f"{vis_dir}/{tmp}img5_{num}.jpg"
                if not os.path.exists(dst):
                    shutil.copy(src, dst)
                num += 1
                src = f"{head_dir}/{plate}_{num}.jpg"

                # 统计各设备数量及废片率
    # path = r"C:\Users\<USER>\Desktop\raw_imgs_1625"
    # # path = r"E:\nj0715_test\all_data"
    # data_list = os.listdir(path)
    # device_dict, _, _ = get_device_num(data_list)
    # device_list = []
    # for k, v in device_dict.items():
    #     device_list.append([k, v])
    # device_list = sorted(device_list, key=lambda x: x[1], reverse=True)
    # device_out = open("./waste_rate1028.txt", "w")
    # device_out.write("device" + "\t\t\t\t\t" + "total_num" + "\t\t\t" + "waste_rate" + "\n")
    # waste_dict, snv_s1, snv_s2 = get_device_num(sure_no_vio)
    # for i in device_list:
    #     if i[0] in waste_dict.keys():
    #         waste = round((waste_dict[i[0]] / i[1]), 4)
    #     else:
    #         waste = 0
    #     device_out.write(i[0] + "\t\t" + str(i[1]) + "\t\t\t\t\t" + f"{waste * 100:.2f}%" + "\n")
    # device_out.flush()
    # device_out.close()

    _, sv_s1, sv_s2 = get_device_num(sure_vio)
    _, pnv_s1, pnv_s2 = get_device_num(pred_no_vio)
    _, pv_s1, pv_s2 = get_device_num(pred_vio)
    _, pnve_s1, pnve_s2 = get_device_num(pred_no_vio_error)
    _, pve_s1, pve_s2 = get_device_num(pred_vio_error)

    if not del_device1 and not del_device2:
        snv_s1 = snv_s2 = sv_s1 = sv_s2 = pnv_s1 = pnv_s2 = pv_s1 = pv_s2 = pnve_s1 = pnve_s2 = pve_s1 = pve_s2 = set()
    elif not del_device2:
        snv_s2 = sv_s2 = pnv_s2 = pv_s2 = pnve_s2 = pve_s2 = set()
    elif not del_device1:
        snv_s1 = sv_s1 = pnv_s1 = pv_s1 = pnve_s1 = pve_s1 = set()

    # 正片
    top = len(pred_vio - pv_s1 - pv_s2) - len(pred_vio_error - pve_s1 - pve_s2)
    bottom = len(sure_vio - sv_s1 - sv_s2)
    vio_recall = round((top / bottom), 4)
    # print(f"正片检出率:{vio_recall * 100:.2f}%")

    top2 = len(pred_vio - pv_s1 - pv_s2) - len(pred_vio_error - pve_s1 - pve_s2)
    bottom2 = len(pred_vio - pv_s1 - pv_s2)
    vio_precision = round((top2 / bottom2), 4)
    # print(f"正片检准率:{vio_precision * 100:.2f}%")

    # 废片
    top = len(pred_no_vio - pnv_s1 - pnv_s2) - len(pred_no_vio_error - pnve_s1 - pnve_s2)
    bottom = len(sure_no_vio - snv_s1 - snv_s2)
    no_vio_recall = round((top / bottom), 4)
    # print(f"废片检出率:{no_vio_recall * 100:.2f}%")

    # Precision计算
    top1 = len(pred_no_vio - pnv_s1 - pnv_s2) - len(pred_no_vio_error - pnve_s1 - pnve_s2)
    bottom1 = len(pred_no_vio - pnv_s1 - pnv_s2)
    no_vio_precision = round((top1 / bottom1), 4)
    # print(f"废片检准率:{no_vio_precision * 100:.2f}%")

    top = top1 + top2
    bottom = bottom1 + bottom2
    model_precision = round((top / bottom), 4)

    print(f"模型准确率:{model_precision * 100:.2f}%", "\n")
    print("总数：", bottom)
    print("判定正片：", len(pred_vio))
    print("判定废片：", len(pred_no_vio))
    print("判定正片错误：", len(pred_vio_error))
    print("判定废片错误：", len(pred_no_vio_error), "\n")
    print_mode(len(pred_vio), len(pred_no_vio), len(pred_vio_error), len(pred_no_vio_error))
