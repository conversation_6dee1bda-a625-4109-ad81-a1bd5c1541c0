import argparse
import torch
import os
import sys
sys.path.append('..')
print(torch.cuda.is_available())
import cv2
from plate_detctor.detect_plate import plate_load_model, detect_one
import yolov7.detect
from tqdm import tqdm
from PIL import Image, ImageDraw, ImageFont
import numpy as np
import shutil


def show_plate(img_list, CAR_LIST, PLATE_LIST=None, plate_num=""):
    # 画目标车和车牌的位置
    for idx, i in enumerate(CAR_LIST):
        if CAR_LIST[idx]:
            h, w, _ = img_list[idx].shape
            pt1 = (CAR_LIST[idx][0], CAR_LIST[idx][1])
            pt2 = (CAR_LIST[idx][2], CAR_LIST[idx][3])
            plate_ps = ((pt1[0] + pt2[0]) // 2, (pt1[1] + pt2[1]) // 2)
            cv2.rectangle(img_list[idx], pt1, pt2, color=(0, 255, 0), thickness=2)
            if PLATE_LIST:
                pt1 = (CAR_LIST[idx][0] + PLATE_LIST[idx][0],
                       CAR_LIST[idx][1] + PLATE_LIST[idx][1])
                pt2 = (pt1[0] + PLATE_LIST[idx][2],
                       pt1[1] + PLATE_LIST[idx][3])
                cv2.rectangle(img_list[idx], pt1, pt2, color=(0, 0, 255), thickness=2)
                cv2.putText(img_list[idx], f"{plate_num[1:]}",
                            plate_ps,
                            cv2.FONT_HERSHEY_COMPLEX, 1.5,
                            (0, 0, 255), 2)


def show_plate2(img_list, CAR_LIST, PLATE_LIST=None, plate_num=""):
    # 画目标车和车牌的位置
    for idx, i in enumerate(CAR_LIST):
        if CAR_LIST[idx]:
            h, w, _ = img_list[idx].shape
            # pt1 = (CAR_LIST[idx][0], CAR_LIST[idx][1])
            # pt2 = (CAR_LIST[idx][2], CAR_LIST[idx][3])
            # plate_ps = ((pt1[0] + pt2[0]) // 2, (pt1[1] + pt2[1]) // 2)
            # cv2.rectangle(img_list[idx], pt1, pt2, color=(0, 255, 0), thickness=2)
            if PLATE_LIST:
                pt1 = (CAR_LIST[idx][0] + PLATE_LIST[idx][0],
                       CAR_LIST[idx][1] + PLATE_LIST[idx][1])
                pt2 = (pt1[0] + PLATE_LIST[idx][2],
                       pt1[1] + PLATE_LIST[idx][3])
                cv2.rectangle(img_list[idx], pt1, pt2, color=(0, 0, 255), thickness=2)
                cv2.putText(img_list[idx], f"{plate_num[1:]}",
                            plate_ps,
                            cv2.FONT_HERSHEY_COMPLEX, 1.5,
                            (0, 0, 255), 2)


if __name__ == '__main__':
    parser = argparse.ArgumentParser()
    parser.add_argument('--weights', nargs='+', type=str, default='../plate_detctor/weights/best.pt',
                        help='model.pt path(s)')
    parser.add_argument('--image_path', type=str, default='../plate_test/car_pic',
                        help='source')  # file/folder, 0 for webcam
    opt = parser.parse_args()
    print(opt)
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    from ai_judge_code_gy.car_detect import LPR

    detect_model = plate_load_model(opt.weights)
    xml_model = "../models/ocr/cascade.xml"
    h5_model = "../models/ocr/model12.h5"
    gru_model = "../models/ocr/ocr_plate_all_gru.h5"
    lpr_model = LPR(xml_model, h5_model, gru_model)

    save_path = f'../plate_test/pred_result/old'
    if os.path.exists(save_path):
        shutil.rmtree(save_path)
    os.makedirs(save_path, exist_ok=True)
    if not os.path.isfile(opt.image_path):
        file_name = os.listdir(opt.image_path)

        for i in tqdm(file_name):
            path = f"{opt.image_path}/{i}"
            img = cv2.imread(path)
            h, w, _ = img.shape
            res_list = detect_one(detect_model, lpr_model, img)
            for res in res_list:
                plate_num = res[0]
                confidence = res[1]
                plate_cor = res[2]
                # 可视化
                pt1 = (plate_cor[0], plate_cor[1])
                pt2 = (plate_cor[0] + plate_cor[2], plate_cor[1] + plate_cor[3])
                plate_ps = (w // 3, h // 3)
                cv2.rectangle(img, pt1, pt2, color=(0, 0, 255), thickness=2)
                IMG = Image.fromarray(img)
                draw = ImageDraw.Draw(IMG)
                font = ImageFont.truetype("msyh.ttc", 30, encoding="unic")
                draw.text(plate_ps, plate_num + " " + str(round(confidence, 2)), (0, 0, 255), font)
                img = np.asarray(IMG, dtype=np.uint8)
                # cv2.putText(img, f"{plate_num[1:]}",
                #             plate_ps,
                #             cv2.FONT_HERSHEY_COMPLEX, 1,
                #             (0, 0, 255), 2)

            cv2.imwrite(f'{save_path}/{i}', img)

            # for car_ps in car_bbox_ps:
            #     car_np = img[car_ps[1]:car_ps[3], car_ps[0]:car_ps[2]]
            #     res_list = detect_one(detect_model, lpr_model, car_np)
            #     for res in res_list:
            #         plate_num = res[0]
            #         confidence = res[1]
            #         plate_cor = res[2]
            #         # 可视化
            #         show_plate([img], [car_ps], [plate_cor], plate_num)
            # save_path = "./detect_one"
            # os.makedirs(save_path, exist_ok=True)
            # cv2.imwrite(f"{save_path}/{i}", img)
            # print(f"{save_path}/{i}")
