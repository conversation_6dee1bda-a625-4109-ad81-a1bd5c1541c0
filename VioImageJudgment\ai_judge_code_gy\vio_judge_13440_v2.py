# 违法停车场景v2
import os
import time

# os.environ["CUDA_VISIBLE_DEVICES"] = "2"
from init_models import *


def match_target_car(img_np, car_ps_list, src_plate_num, plate_det_model, plate_rec_model, save=False, save_path=".",
                     save_name=""):
    save_dir = f"{save_path}/plate_num_show"
    if save:
        os.makedirs(save_dir, exist_ok=True)
    device = "cuda:0" if torch.cuda.is_available() else "cpu"
    show_img = img_np.copy()
    img_car_ps = []
    plate_num = "NO_DETECT"
    img_plate_dict = {}
    vio_score = 0
    temp = sorted(car_ps_list, key=lambda x: x[1], reverse=True)
    for car_ps in temp:
        # 车牌坐标初始化
        img_plate_dict[str(car_ps)] = []
        temp_img = img_np[car_ps[1]:car_ps[3], car_ps[0]:car_ps[2]]
        # 车牌检测+识别
        dict_list = detect_Recognition_plate(plate_det_model, temp_img, device, plate_rec_model, 640)
        if save:
            show_img = draw_result2(show_img, dict_list, car_ps)

        for idx, res in enumerate(dict_list):
            plate_num = res["plate_no"]
            tmp_cor = res["rect"]
            if plate_num and plate_num[-1] == "警" and res["score"][-1] < Police_Plate_Thresh:
                plate_num = plate_num[:-1]
            plate_cor = [tmp_cor[0] + car_ps[0], tmp_cor[1] + car_ps[1], tmp_cor[2] + car_ps[0],
                         tmp_cor[3] + car_ps[1]]

            img_plate_dict[str(car_ps)] = plate_cor
            # 车牌全匹配
            if plate_num == src_plate_num:
                img_car_ps.extend(car_ps)
                vio_score = 2
                if save:
                    cv2.imwrite(f"{save_dir}/{src_plate_num}_{save_name}_2.jpg", show_img)
                return img_car_ps, plate_num, img_plate_dict, vio_score
            # 车牌模糊匹配
            elif Tools().random_choice(plate_num, src_plate_num, random_list=[3]):
                img_car_ps.extend(car_ps)
                vio_score = 1
                if save:
                    cv2.imwrite(f"{save_dir}/{src_plate_num}_{save_name}_1.jpg", show_img)
                return img_car_ps, plate_num, img_plate_dict, vio_score

    if save:
        cv2.imwrite(f"{save_dir}/{src_plate_num}_{save_name}_car.jpg", show_img)
    # 直接检测识别车牌
    if len(car_ps_list) == 0:
        dict_list = detect_Recognition_plate(plate_det_model, img_np, device, plate_rec_model, 640)
        if save:
            show_img = draw_result2(show_img, dict_list, [0, 0, 0, 0])
        for res in dict_list:
            plate_num = res["plate_no"]
            plate_cor = res["rect"]
            if plate_num and plate_num[-1] == "警" and res["score"][-1] < Police_Plate_Thresh:
                plate_num = plate_num[:-1]
            img_plate_dict["img"] = plate_cor
            if plate_num == src_plate_num:
                vio_score = 2
                if save:
                    cv2.imwrite(f"{save_dir}/{src_plate_num}_{save_name}_2.jpg", show_img)
                return img_car_ps, plate_num, img_plate_dict, vio_score
            elif Tools().random_choice(plate_num, src_plate_num, random_list=[3]):
                vio_score = 1
                if save:
                    cv2.imwrite(f"{save_dir}/{src_plate_num}_{save_name}_1.jpg", show_img)
                return img_car_ps, plate_num, img_plate_dict, vio_score
        if save:
            cv2.imwrite(f"{save_dir}/{src_plate_num}_{save_name}_nocar.jpg", show_img)
    return img_car_ps, "NO_DETECT", img_plate_dict, vio_score

# 违法停车场景主函数
def judge_vio(src_plate_num, file_list, weights_list, save=False, save_path="."):
    # 读取模型
    plate_det_model = weights_list[0][0]
    plate_rec_model = weights_list[0][1]
    demo = weights_list[1]
    lp_model = weights_list[2][0]
    lp_meta = weights_list[2][1]
    yolov7_model = weights_list[3][0]
    yolov7_meta = weights_list[3][1]
    model_cnn = weights_list[-2]
    vio_model = weights_list[-1]

    img_np_list = file_list
    vio_judge = 'no_judge'

    judge_infos = {'plate_num': src_plate_num,
                   'vio_code': 1344,
                   'vio_judge': vio_judge,
                   'drv_direction': 0,
                   'drive_direction_light': [],
                   'lamp': [],
                   'target_car': [],
                   'stop_line': 0,
                   'police': 0,
                   'has_crossline': 0,
                   'vehicle_location': 0,
                   'lane': [],
                   'zebra_line': []}
    score_list = []
    # 特殊车牌废片判定
    if src_plate_num[0] not in chars:
        vio_judge = 'no_vio_002'
        judge_infos['vio_judge'] = vio_judge
        return vio_judge, judge_infos, score_list
    vio_judge = VIO_JUDGE_TOOL().spe_no_vio_judge(src_plate_num)
    if vio_judge.startswith('no_vio'):
        judge_infos['vio_judge'] = vio_judge
        return vio_judge, judge_infos, score_list
    vio_judge = "no_vio"
    for idx, img_np in enumerate(img_np_list):
        if idx == 0:
            vio_judge+="-"
        # 找目标车辆
        crop_img_list, cars_bbox_ps, _ = yolov7_detect(img_np, yolov7_model, yolov7_meta[0], yolov7_meta[1],
                                                       yolov7_meta[2])
        img_car_ps, plate_num, img_plate_dict, vio_score = match_target_car(img_np, cars_bbox_ps, src_plate_num,
                                                                            plate_det_model, plate_rec_model,
                                                                            save, save_path, f"img{idx + 1}")

        if plate_num.endswith("警"):
            vio_judge = 'no_vio_010'
            break
        if filter_special_vehicle(demo,img_np,img_car_ps,vio_judge,save) == 'no_vio_010':
            vio_judge = 'no_vio_010'
            break
        vio_judge += str(vio_score)
        score_list.append(vio_score)

    score_sum = np.sum(score_list)
    if score_sum >= len(img_np_list) + 1 and not score_list.count(0):
        vio_judge = "vio_001"
    elif score_sum >= len(img_np_list) and not score_list.count(0):
        vio_judge = "vio_002"
    elif len(score_list) >= 2 and score_list[0] != 0 and score_list[-1] != 0:
        vio_judge = "vio_003"
    elif Vio_Park_High_Recall:
        vio_judge = "vio_004"
    judge_infos['vio_judge'] = vio_judge
    return vio_judge, judge_infos, score_list


