# Copyright (c) Facebook, Inc. and its affiliates. All Rights Reserved
import warnings

warnings.filterwarnings('ignore')
import argparse
import multiprocessing as mp
import cv2
import numpy as np
import sys

sys.path.append('..')
from CondInst.demo.predictor import VisualizationDemo
from CondInst.adet.config import get_cfg
from ai_judge_code_gy.car_detect import CarJudge
from ai_judge_code_gy.judge_tools import Tools, convert_src_coord
from main_config.config import _LABEL, Blend_Judge_Vehicle, Blend_Conf, Special_Type, Ambulance_Head_Thresh, \
    Special_Car_Thresh
import torch


class BlendMaskModel():
    def __init__(self):
        pass

    # 设置配置信息
    def setup_cfg(self, args, device):
        # load config from file and command-line arguments
        cfg = get_cfg()
        # args.config_file = os.path.join(ROOT_DIR, "CondInst/configs/CondInst/MS_R_101_3x_sem.yaml")
        args.config_file = '../CondInst/configs/BlendMask/R_101_3x.yaml'
        cfg.merge_from_file(args.config_file)
        cfg.merge_from_list(args.opts)
        cfg.DATASETS.TEST = ("coco_my_train",)
        args.confidence_threshold = 0.1  # 过小导致模型输出的置信度偏低
        # args.confidence_threshold = 0.4
        # Set score_threshold for builtin models
        cfg.MODEL.RETINANET.SCORE_THRESH_TEST = args.confidence_threshold
        cfg.MODEL.ROI_HEADS.SCORE_THRESH_TEST = args.confidence_threshold
        cfg.MODEL.FCOS.INFERENCE_TH_TEST = args.confidence_threshold
        cfg.MODEL.PANOPTIC_FPN.COMBINE.INSTANCES_CONFIDENCE_THRESH = args.confidence_threshold
        cfg.MODEL.DEVICE = device
        cfg.MODEL.FCPOSE.BASIS_MODULE.BN_TYPE = "SyncBN" if device != "cpu" else "BN"
        cfg.MODEL.BASIS_MODULE.NORM = "SyncBN" if device != "cpu" else "BN"

        cfg.freeze()
        return cfg

    # 参数解析
    def get_parser(self):
        parser = argparse.ArgumentParser(description="Detectron")
        parser.add_argument(
            "--config-file",
            default="../CondInst/configs/quick_schedules/e2e_mask_rcnn_R_50_FPN_inference_acc_test.yaml",
            metavar="FILE",
            help="path to config file",
        )
        parser.add_argument(
            "--confidence-threshold",
            type=float,
            default=0.3,
            help="Minimum score for instance predictions to be shown",
        )
        parser.add_argument(
            "--opts",
            help="Modify config options using the command-line 'KEY VALUE' pairs",
            default=[],
            nargs="+"
        )

        return parser

    # 读取模型
    def load_model(self, device=None):
        if device is None:
            device = "cuda:0" if torch.cuda.is_available() else "cpu"
        # mp.set_start_method("spawn", force=True)
        args = self.get_parser().parse_args()
        # logger = setup_logger()
        # logger.info("Arguments: " + str(args))
        args.opts = ['MODEL.WEIGHTS', '../models/blendmask_model_best.pth']
        # (args.input)[0]=IMAGE_PATH
        cfg = self.setup_cfg(args, device)

        # logger.info("Arguments: " + str(cfg))
        demo = VisualizationDemo(cfg)
        return demo

    def load_specific_model(self, load_path, device=None):
        if device is None:
            device = "cuda:0" if torch.cuda.is_available() else "cpu"
        ### load_path 需要是字串类型，指定需要加载的模型路径
        # mp.set_start_method("spawn", force=True)
        args = self.get_parser().parse_args()
        # logger = setup_logger()
        # logger.info("Arguments: " + str(args))
        args.opts = ['MODEL.WEIGHTS', load_path]
        # (args.input)[0]=IMAGE_PATH
        cfg = self.setup_cfg(args, device)

        # logger.info("Arguments: " + str(cfg))
        demo = VisualizationDemo(cfg)
        return demo


class BlendMaskDetect():
    def __init__(self, img_np):
        self.img_np = img_np

    # 模型侦测
    def mask_predict(self, demo, show=False):
        img = self.img_np
        with torch.no_grad():
            predictions, visualized_output, kp_masks = demo.run_on_image(img)
        # for i in visualized_output:
        if show:
            img = visualized_output.get_image()[:, :, ::-1]
            img_test1 = cv2.resize(img, None, fx=0.3, fy=0.3)
            cv2.imshow('img', img_test1)
            cv2.waitKey()
            cv2.destroyAllWindows()
        return predictions, kp_masks

    def nms(self, boxes, scores, threshold=0.5):
        '''
        Args:
            boxes: 预测出的box, shape[M,4]
            scores: 预测出的置信度，shape[M]
            threshold: 阈值
        Return:
            keep: nms筛选后的box的新的index数组
            count: 保留下来box的个数
        '''
        keep = scores.new(scores.size(0)).zero_().long()

        x1 = boxes[:, 0]
        y1 = boxes[:, 1]
        x2 = boxes[:, 2]
        y2 = boxes[:, 3]

        area = (x2 - x1) * (y2 - y1)  # 面积,shape[M]
        _, idx = scores.sort(0, descending=True)  # 降序排列scores的值大小
        count = 0

        while idx.numel():
            # 记录最大score值的index
            i = idx[0]
            # 保存到keep中
            keep[count] = i
            # keep 的序号
            count += 1

            if idx.size(0) == 1:  # 保留框只剩一个
                break

            idx = idx[1:]  # 移除已经保存的index

            # 计算boxes[i]和其他boxes之间的iou
            xx1 = x1[idx].clamp(min=x1[i])
            yy1 = y1[idx].clamp(min=y1[i])
            xx2 = x2[idx].clamp(max=x2[i])
            yy2 = y2[idx].clamp(max=y2[i])

            w = (xx2 - xx1).clamp(min=0)
            h = (yy2 - yy1).clamp(min=0)

            # 交集的面积
            inter = w * h  # shape[M-1]
            iou = inter / (area[i] + area[idx] - inter)

            # iou满足条件的idx
            idx = idx[iou.le(threshold)]  # Shape[M-1]
        return keep[:count]

    def nms_class_distinct(self, boxes, scores, classes, threshold=0.5, class_ditinct_threshold=0.9):
        '''
        Args:
            boxes: 预测出的box, shape[M,4]
            scores: 预测出的置信度，shape[M]
            classes: 预测出的class
            threshold: 阈值
        Return:
            keep: nms筛选后的box的新的index数组
            count: 保留下来box的个数

        20230728 加入新逻辑，当两个框存在大包小情况且class 相同时留下大的
        '''
        keep = scores.new(scores.size(0)).zero_().long()

        x1 = boxes[:, 0]
        y1 = boxes[:, 1]
        x2 = boxes[:, 2]
        y2 = boxes[:, 3]

        area = (x2 - x1) * (y2 - y1)  # 面积,shape[M]
        _, idx = scores.sort(0, descending=True)  # 降序排列scores的值大小
        count = 0
        while idx.numel():
            # 记录最大score值的index
            i = idx[0]
            class_temp = classes[i]
            # 保存到keep中
            keep[count] = i
            # keep 的序号
            count += 1

            if idx.size(0) == 1:  # 保留框只剩一个
                break

            idx = idx[1:]  # 移除已经保存的index

            # 计算boxes[i]和其他boxes之间的iou
            xx1 = x1[idx].clamp(min=x1[i])
            yy1 = y1[idx].clamp(min=y1[i])
            xx2 = x2[idx].clamp(max=x2[i])
            yy2 = y2[idx].clamp(max=y2[i])

            w = (xx2 - xx1).clamp(min=0)
            h = (yy2 - yy1).clamp(min=0)

            # 交集的面积
            inter = w * h  # shape[M-1]
            iou = inter / (area[i] + area[idx] - inter)

            ### 交集面积与本身的比例
            iou1 = inter / (area[i])
            iou2 = inter / (area[idx])

            ### 筛掉重合面积大于自身95% 且与其相同class的情况

            ###判断重合
            iou1_class_mask = iou1.ge(class_ditinct_threshold) + 0
            iou2_class_mask = iou2.ge(class_ditinct_threshold) + 0
            ### class
            class_mask = (classes == class_temp)[idx] + 0

            ### 汇总
            iou1_result = (class_mask + iou1_class_mask).ge(2) + 0
            iou2_result = (class_mask + iou2_class_mask).ge(2) + 0
            iou_class_result = (iou1_result + iou2_result + 0).ge(1) + 0

            ### 与基础iou条件汇总 拿到要被扔掉的全部
            iou_basic_result = iou.gt(threshold) + 0
            keep_ls = (iou_class_result + iou_basic_result + 0).lt(1)

            # iou满足条件的idx
            idx = idx[keep_ls]  # Shape[M-1]

        return keep[:count]

    # 转换模型识别结果，按class_info筛选结果输出
    def class_info(self, predictions, class_info, div_label=False, conf_thresh=0.15):
        img = self.img_np
        or_mask = np.zeros(img.shape[:2])
        if not div_label:
            bbox_list = []
            masks_list = []
        else:
            bbox_list = {}
            masks_list = {}
        boxes = predictions["instances"].pred_boxes
        classes = predictions["instances"].pred_classes
        scores = predictions["instances"].scores
        labels = [_LABEL[i] for i in classes if i is not None]
        # print("label:",labels)
        # print('+++++++', labels)
        for classa in class_info:
            if classa not in labels:
                pass
            else:
                index = _LABEL.index(classa)
                one_label_indexes = [i for i, cl in enumerate(classes) if cl == index]
                boxes_info = [boxes[index] for index in one_label_indexes if scores[index] >= conf_thresh]

                if not div_label:
                    for box in boxes_info:
                        for i in box:
                            x1, y1, x2, y2 = i
                            bbox_list.append([int(x1), int(y1), int(x2), int(y2)])
                        # x1, y1, x2, y2 = box.box_to_tensor()
                        # bbox_list.append([int(x1), int(y1), int(x2), int(y2)])
                else:
                    temp_list = []
                    for box in boxes_info:
                        # x1, y1, x2, y2 = box
                        # print(x1, y1, x2, y2)
                        # exit()
                        for i in box:
                            x1, y1, x2, y2 = i[0], i[1], i[2], i[3]
                            # x1, y1, x2, y2 = box.box_to_tensor()
                            temp_list.append([int(x1), int(y1), int(x2), int(y2)])
                    bbox_list[classa] = temp_list
        try:
            masks = predictions["instances"].pred_masks
            for classa in class_info:
                if classa not in labels:
                    if not div_label:
                        pass
                        ### masks_list.append(or_mask)
                        ### 正常情況下這裏不應該再加一個mask， 但是不知道div_label = True 故 後續暫時不修改
                    else:
                        temp_masks_list = []
                        temp_masks_list.append(or_mask)
                        masks_list[classa] = temp_masks_list
                else:
                    index = _LABEL.index(classa)
                    one_label_indexes = [i for i, cl in enumerate(classes) if cl == index]
                    masks_info = [masks[index] for index in one_label_indexes if scores[index] >= conf_thresh]
                    if not div_label:
                        if torch.cuda.is_available():
                            temp_mask_list = [mask.data.cpu().numpy() for mask in masks_info]
                        else:
                            temp_mask_list = [mask.data.cpu().numpy() for mask in masks_info]
                        masks_list.extend(temp_mask_list)
                    else:
                        temp_masks_list = []
                        if torch.cuda.is_available():
                            temp_mask_list = [mask.data.cpu().numpy() for mask in masks_info]
                        else:
                            temp_mask_list = [mask.data.cpu().numpy() for mask in masks_info]
                        temp_masks_list.append(temp_mask_list)
                        masks_list[classa] = temp_masks_list
        except:
            if not div_label:
                masks_list.append(or_mask)
            else:
                temp_masks_list = []
                temp_masks_list.append(or_mask)
                for classa in class_info:
                    masks_list[classa] = temp_masks_list

        return bbox_list, masks_list

    def get_target_conf(self, predictions, target_box):
        boxes = predictions["instances"].pred_boxes.tensor.cpu().data.numpy().astype(int).tolist()
        scores = predictions["instances"].scores.cpu().data.numpy().tolist()
        target_idx = boxes.index(target_box)
        return scores[target_idx]

    def class_info2(self, predictions, class_info, conf_thresh=0.2, need_mask=False):
        bbox_list = []
        labels_list = []
        score_list = []
        masks_list = []
        boxes = predictions["instances"].pred_boxes.tensor.cpu().data.numpy()
        classes = predictions["instances"].pred_classes.cpu().data.numpy()
        scores = predictions["instances"].scores.cpu().data.numpy()
        masks = predictions["instances"].pred_masks.cpu().data.numpy()

        labels = [_LABEL[i] for i in classes if i is not None]

        for classa in class_info:
            if classa not in labels:
                pass
            else:
                index = _LABEL.index(classa)
                # print([(labels[i],scores[i]) for i, cl in enumerate(classes) if cl == index])
                one_label_indexes = [i for i, cl in enumerate(classes) if cl == index and scores[i] >= conf_thresh]
                boxes_info = [boxes[index] for index in one_label_indexes]
                masks_info = [masks[index] for index in one_label_indexes]
                tmp_labels_list = [labels[index] for index in one_label_indexes]
                tmp_score_list = [scores[index] for index in one_label_indexes]
                labels_list += tmp_labels_list
                score_list += tmp_score_list
                masks_list += masks_info
                for box in boxes_info:
                    x1, y1, x2, y2 = box
                    bbox_list.append([int(x1), int(y1), int(x2), int(y2)])
        if need_mask:
            return bbox_list, masks_list, labels_list, score_list
        else:
            return bbox_list, labels_list, score_list

    # 保存blendmask模型识别结果
    def save_infos(self, src_plate_num, demo, class_info, predictions, kp_masks, car_ps, car_mask,
                   height_restrict=False, height_bond=0.65):
        info_dict = {}
        img = self.img_np
        info_dict["plate_num"] = src_plate_num
        info_dict["imageHeight"] = img.shape[0]
        info_dict["imageWidth"] = img.shape[1]
        car_points = Tools().get_polygon_from_mask(demo, car_mask)
        info_dict["shapes"] = []
        info_dict["shapes"].append({'label': 'car', 'bbox': [float(i) for i in car_ps], 'points': car_points})
        boxes = predictions["instances"].pred_boxes
        classes = predictions["instances"].pred_classes
        if torch.cuda.is_available():
            classes_d = classes.data.cpu().numpy()
        else:
            classes_d = classes.data.cpu().numpy()
        try:
            masks = predictions["instances"].pred_masks
        except:
            print('no instances detected!')
            return info_dict
        for classa in class_info:
            if classa not in _LABEL or _LABEL.index(classa) not in classes_d:
                pass
            else:
                index = _LABEL.index(classa)
                one_label_indexes = [i for i, cl in enumerate(classes) if cl == index]
                boxes_info = [boxes[index] for index in one_label_indexes]
                boxes_list = []
                masks_info = [masks[index] for index in one_label_indexes]
                masks_kp = [kp_masks[index].polygons for index in one_label_indexes]
                # print('======', masks_kp)
                # 记录信息  类别名字  {"label" : classa,
                #                      "bbox":bbox,
                #                      "points": [[], [], []]}
                for box in boxes_info:
                    for i in box:
                        x1, y1, x2, y2 = i[0], i[1], i[2], i[3]
                        # x1, y1, x2, y2 = box.box_to_tensor()
                        boxes_list.append([float(x1), float(y1), float(x2), float(y2)])
                # =====================重点关注数据格式=====================
                if torch.cuda.is_available():
                    temp_mask_list = [mask.data.cpu().numpy() for mask in masks_info]
                else:
                    temp_mask_list = [mask.data.cpu().numpy() for mask in masks_info]
                # print(classa, len(boxes_info), len(temp_mask_list), len(masks_kp), '=======================')
                assert len(boxes_list) == len(temp_mask_list) == len(masks_kp), 'info detect error!'
                for info_index in range(len(boxes_list)):
                    if height_restrict:
                        if classa in ["lane1", "lane2", "arrow_l", "arrow_s", "arrow_r", "arrow_t", "stop_line",
                                      "arrow_s_t", "arrow_l_t", "arrow_r_t"]:
                            if max(boxes_list[info_index][1], boxes_list[info_index][3]) > img.shape[0] * (
                                    1 - height_bond):
                                polygons = masks_kp[info_index]
                                ps_list = Tools().get_polygon(polygons)
                                info_dict['shapes'].append({
                                    'label': classa,
                                    'bbox': boxes_list[info_index],
                                    'points': ps_list
                                })
                        else:
                            polygons = masks_kp[info_index]
                            ps_list = Tools().get_polygon(polygons)
                            info_dict['shapes'].append({
                                'label': classa,
                                'bbox': boxes_list[info_index],
                                'points': ps_list
                            })
                    else:
                        polygons = masks_kp[info_index]
                        ps_list = Tools().get_polygon(polygons)
                        info_dict['shapes'].append({
                            'label': classa,
                            'bbox': boxes_list[info_index],
                            'points': ps_list
                        })
        return info_dict

    # 扣除mask车辆区域，生成扣除后图片，以备后续融合图片进行车道信息检测
    def cars_mask_pro(self, masks_list):
        pro_img = self.img_np
        if len(masks_list) > 0:
            for i, mask in enumerate(masks_list):
                temp = np.expand_dims(mask, 2).repeat(3, axis=2)
                pro_img = np.where(temp == True, 0, pro_img)
        return pro_img

    def merge_class(self, predictions, class_info, roi_cor, label):
        _, temp_mask = self.class_info(predictions, class_info)
        if len(temp_mask) == 0:
            mask = np.zeros(self.img_np.shape[:2])
        else:
            # 合并blendmask detect出的mask信息
            mask = Tools().merge_masks(roi_cor, temp_mask, label=label)
        return mask

    ###加入了nms 过滤的结果
    def prediction_nms_filter(self, predictions, nms_ls, kp_masks):
        '''
        输入为nms函数产生的tensor[M-1]和mask_predict(demo, show=False) 返回的预测结果
        '''
        # print('the original instance cnt is ', len(predictions["instances"].pred_boxes.tensor))
        nms_result = predictions.copy()
        if len(nms_ls) > 0:
            ### 根据nms结果过滤值
            nms_result["instances"].pred_boxes.tensor = nms_result["instances"].pred_boxes.tensor[nms_ls]
            nms_result["instances"].scores = nms_result["instances"].scores[nms_ls]
            nms_result["instances"].pred_classes = nms_result["instances"].pred_classes[nms_ls]
            nms_result["instances"].pred_masks = nms_result["instances"].pred_masks[nms_ls]
            # print('the instance cnt before nms is ----- ', len(predictions["instances"].pred_boxes.tensor))
            # print('the instance cnt after nms is ', len(nms_ls))

            kp_masks = [kp_masks[i] for i in nms_ls.tolist()]
            ### 同时也对于kp——mask进行过滤

        else:
            print('nms not applied due to no instance detected')
        return nms_result, kp_masks

    def class_info_withnms(self, predictions, class_info, nms_ls, div_label=False):
        img = self.img_np
        or_mask = np.zeros(img.shape[:2])
        if not div_label:
            bbox_list = []
            masks_list = []
        else:
            bbox_list = {}
            masks_list = {}

        ### 单纯加入nms过滤
        # print('-------------------------------------------------')
        # print('the original instance cnt is ', len(predictions["instances"].pred_boxes.tensor))
        nms_result = predictions.copy()
        if len(nms_ls) > 0:
            ### 根据nms结果过滤值
            nms_result["instances"].pred_boxes.tensor = nms_result["instances"].pred_boxes.tensor[nms_ls]
            nms_result["instances"].scores = nms_result["instances"].scores[nms_ls]
            nms_result["instances"].pred_classes = nms_result["instances"].pred_classes[nms_ls]
            nms_result["instances"].pred_masks = nms_result["instances"].pred_masks[nms_ls]
            # print('the instance cnt before nms is ----- ', len(predictions["instances"].pred_boxes.tensor))
            # print('the instance cnt after nms is ', len(nms_ls))
        else:
            print('nms not applied due to no instance detected')

        boxes = nms_result["instances"].pred_boxes
        classes = nms_result["instances"].pred_classes
        scores = nms_result["instances"].scores
        # classes_d = classes.data.cpu().numpy()

        labels = [_LABEL[i] for i in classes if i is not None]
        # print("label:",labels)
        # print('+++++++', labels)
        for classa in class_info:
            if classa not in labels:
                pass
            else:
                index = _LABEL.index(classa)
                one_label_indexes = [i for i, cl in enumerate(classes) if cl == index]
                boxes_info = [boxes[index] for index in one_label_indexes]
                if not div_label:
                    for box in boxes_info:
                        for i in box:
                            x1, y1, x2, y2 = i
                            bbox_list.append([int(x1), int(y1), int(x2), int(y2)])
                        # x1, y1, x2, y2 = box.box_to_tensor()
                        # bbox_list.append([int(x1), int(y1), int(x2), int(y2)])
                else:
                    temp_list = []
                    for box in boxes_info:
                        # x1, y1, x2, y2 = box
                        # print(x1, y1, x2, y2)
                        # exit()
                        for i in box:
                            x1, y1, x2, y2 = i[0], i[1], i[2], i[3]
                            # x1, y1, x2, y2 = box.box_to_tensor()
                            temp_list.append([int(x1), int(y1), int(x2), int(y2)])
                    bbox_list[classa] = temp_list
        try:
            masks = nms_result["instances"].pred_masks
            for classa in class_info:
                if classa not in labels:
                    if not div_label:
                        masks_list.append(or_mask)
                    else:
                        temp_masks_list = []
                        temp_masks_list.append(or_mask)
                        masks_list[classa] = temp_masks_list
                else:
                    index = _LABEL.index(classa)
                    one_label_indexes = [i for i, cl in enumerate(classes) if cl == index]
                    masks_info = [masks[index] for index in one_label_indexes]
                    if not div_label:
                        if torch.cuda.is_available():
                            temp_mask_list = [mask.data.cpu().numpy() for mask in masks_info]
                        else:
                            temp_mask_list = [mask.data.cpu().numpy() for mask in masks_info]
                        masks_list.extend(temp_mask_list)
                    else:
                        temp_masks_list = []
                        if torch.cuda.is_available():
                            temp_mask_list = [mask.data.cpu().numpy() for mask in masks_info]
                        else:
                            temp_mask_list = [mask.data.cpu().numpy() for mask in masks_info]
                        temp_masks_list.append(temp_mask_list)
                        masks_list[classa] = temp_masks_list
        except:
            if not div_label:
                masks_list.append(or_mask)
            else:
                temp_masks_list = []
                temp_masks_list.append(or_mask)
                for classa in class_info:
                    masks_list[classa] = temp_masks_list

        return bbox_list, masks_list


# 目标车辆检测及交通元素识别复合函数(未使用)
# def mask_gen_single(src_plate_num, file_list, weight_list, device=None, merge=False, show=False):
#     if device is None:
#         device = "cuda:0" if torch.cuda.is_available() else "cpu"
#     plate_det_model, plate_rec_model, demo, carinfo = weight_list
#     img1_np = file_list[0]
#     cars_ps = []
#     plate_list = []
#     cars_masks = []
#     stops_masks = []
#     lanes_masks = []
#     cars_remove_img = []
#     kp_masks_list = []
#     predictions_list = []
#     show = False
#     for i, img_np in enumerate(file_list):
#         det = BlendMaskDetect(img_np)
#         predictions, kp_masks = det.mask_predict(demo, show=show)
#         cars_bbox_ps, cars_mask_list = det.class_info(predictions, car_list)
#         if len(cars_bbox_ps) == 0:
#             crop_img1_list, cars_bbox_ps, _ = carinfo.get_goal_class_box(img_np, ['car', 'truck', 'bus'])
#         img_car_ps, plate_num, img_conf = \
#             CarJudge().confirm_img1_car_ps(img_np, src_plate_num, plate_det_model, plate_rec_model, cars_bbox_ps,
#                                            device, label=1)
#         # print(img_car_ps, plate_num, img_conf)
#         plate_list.append(plate_num)
#         img_cars_remove_img = det.cars_mask_pro(cars_mask_list)
#         _, img_lane_mask = det.class_info(predictions, ['lane1', 'lane2'])
#         img_lane_mask = Tools().merge_masks([], img_lane_mask, label=0)
#
#         _, img_stop_mask = det.class_info(predictions, ['stop_area'])
#         img_stop_mask = Tools().merge_masks([], img_stop_mask, label=0)
#         # print(cars_bbox_ps)
#         # print('----', img_car_ps)
#         if len(img_car_ps):
#             img_car_mask = cars_mask_list[cars_bbox_ps.index(img_car_ps[:4])]
#         else:
#             img_car_mask = np.zeros((img_np.shape[1], img_np.shape[0]), dtype=np.uint8)
#         cars_ps.append(img_car_ps)
#         stops_masks.append(img_stop_mask)
#         lanes_masks.append(img_lane_mask)
#         cars_masks.append(img_car_mask)
#         cars_remove_img.append(img_cars_remove_img)
#         kp_masks_list.append(kp_masks)
#         predictions_list.append(predictions)
#
#     if merge:
#         temp = np.where(cars_remove_img[0] <= 10,
#                         cv2.resize(cars_remove_img[1], (cars_remove_img[0].shape[1], cars_remove_img[0].shape[0])),
#                         cars_remove_img[0])
#         if len(file_list) == 3:
#             temp = np.where(temp <= 10, cv2.resize(cars_remove_img[2], (temp.shape[1], temp.shape[0])), temp)
#         roi4_np = temp
#         det4 = BlendMaskDetect(roi4_np)
#         predictions4, kps_mask = det4.mask_predict(demo, show=show)
#         _, img4_lane_mask = det4.class_info(predictions4, ['lane1', 'lane2'])
#         img4_lane_mask1 = Tools().merge_masks([], img4_lane_mask, label=0)
#         _, img4_stop_mask = det4.class_info(predictions4, ['stop_area'])
#         img4_stop_mask1 = Tools().merge_masks([], img4_stop_mask, label=0)
#         kp_masks_list.append(kps_mask)
#         stops_masks.append(img4_stop_mask1)
#         lanes_masks.append(img4_lane_mask1)
#
#     if len(file_list) == 2:
#         if np.array([len(cars_ps[0]), len(cars_ps[1])]).all() == 0 \
#                 or len(cars_masks[0]) == 0 or len(cars_masks[1]) == 0:
#             return predictions_list, cars_ps, plate_list, cars_masks, kp_masks_list, [], []
#         # predictions, cars_ps, plate_list, cars_masks_list, kp_masks_list, lane_mask, stop_mask
#     if len(file_list) == 3:
#         if np.array([len(cars_ps[0]), len(cars_ps[1]), len(cars_ps[2])]).all() == 0 \
#                 or len(cars_masks[0]) == 0 or len(cars_masks[1]) == 0:
#             return predictions_list, cars_ps, plate_list, cars_masks, kp_masks_list, [], []
#
#     if len(file_list) == 2:
#         roi_cor = Tools().area_pro(img1_np, cars_ps[0], cars_ps[1])
#     else:
#         roi_cor = Tools().area_pro(img1_np, cars_ps[0], cars_ps[1], cars_ps[2])
#     img1_car_roi_mask = cars_masks[0][roi_cor[1]:roi_cor[3], roi_cor[0]:roi_cor[2]]
#     img2_car_roi_mask = cars_masks[1][roi_cor[1]:roi_cor[3], roi_cor[0]:roi_cor[2]]
#     img1_car_roi_mask = np.where(img1_car_roi_mask == 0, 0, 255)
#     img2_car_roi_mask = np.where(img2_car_roi_mask == 0, 0, 255)
#
#     lane_mask = Tools().merge_masks(roi_cor, lanes_masks, label=1)
#     stop_mask = Tools().merge_masks(roi_cor, stops_masks, label=1)
#     cars_masks_list = []
#     if len(file_list) == 2:
#         cars_masks_list = [img1_car_roi_mask, img2_car_roi_mask]
#     if len(file_list) == 3:
#         img3_car_roi_mask = cars_masks[2][roi_cor[1]:roi_cor[3], roi_cor[0]:roi_cor[2]]
#         img3_car_roi_mask = np.where(img3_car_roi_mask == 0, 0, 255)
#         cars_masks_list = [img1_car_roi_mask, img2_car_roi_mask, img3_car_roi_mask]
#
#     return predictions_list, cars_ps, plate_list, cars_masks_list, kp_masks_list, lane_mask, stop_mask


# 车辆检测、车牌匹配、车辆相似度匹配、交通基础元素多图融合的复合函数
def mask_gen(src_plate_num, file_list, plate_det_model, plate_rec_model, demo, device=None, merge=False, save=False,
             save_path=".", get_lane_info=False, judge_infos=None):
    if judge_infos is None:
        judge_infos = {}
    if device is None:
        device = "cuda:0" if torch.cuda.is_available() else "cpu"
    # car_apply_list = Blend_Judge_Vehicle
    all_car_ps = []
    try:
        img1_np, img2_np, img3_np = file_list
        db_label = 0
    except:
        img1_np, img2_np = file_list
        db_label = 1

    cars_ps = []
    plate_list = []
    cars_masks = []
    stops_masks = []
    lanes_masks = []
    cars_remove_img = []
    kp_masks_list = []
    predictions_list = []
    lane_info_list = []
    show = False
    # 找目标车
    det1 = BlendMaskDetect(img1_np)
    predictions1, kp_mask1 = det1.mask_predict(demo, show=show)
    nms_ls1 = det1.nms_class_distinct(predictions1["instances"].pred_boxes.tensor,
                                      predictions1["instances"].scores,
                                      predictions1["instances"].pred_classes,
                                      threshold=0.8,
                                      class_ditinct_threshold=0.85)
    predictions1, kp_mask1 = det1.prediction_nms_filter(predictions1, nms_ls1, kp_mask1)
    # car1_bbox_ps, cars1_mask_list = det1.class_info(predictions1, car_apply_list, div_label=False)
    car1_bbox_ps, cars1_mask_list, labels_list1, score_list1 = det1.class_info2(predictions1, Blend_Judge_Vehicle,
                                                                                Blend_Conf, need_mask=True)

    img1_car_ps, plate1_num, img1_plate_dict, new_car1_bbox_ps = CarJudge(
        iiea_cfg=judge_infos["iieaFilterVehicle"]).confirm_img1_car_ps(img1_np, src_plate_num,
                                                                       plate_det_model,
                                                                       plate_rec_model,
                                                                       car1_bbox_ps,
                                                                       device, label=0,
                                                                       show=save,
                                                                       save_path=save_path)
    plate_list.append(plate1_num)
    # iiea白名单配置
    try:
        _, ambulance_filter, fire_filter = judge_infos["iieaFilterVehicle"]
    except:
        ambulance_filter = None
        fire_filter = None
    special_type_list = []
    if ambulance_filter:
        special_type_list.extend(['ambulance_h', 'ambulance_b'])
    if fire_filter:
        special_type_list.extend(['fire_engine_h', 'fire_engine_b'])
    if ambulance_filter is None and fire_filter is None:
        special_type_list = Special_Type
    if img1_car_ps:
        if plate1_num.endswith("警"):
            judge_infos["vio_judge"] = "no_vio_010"
        tmp_idx1 = car1_bbox_ps.index(img1_car_ps)
        tmp_label1 = labels_list1[tmp_idx1]
        tmp_score1 = score_list1[tmp_idx1]
        judge_infos['judgeConf'] = float(tmp_score1)
        # 特殊车辆判定
        if tmp_label1 in special_type_list:
            if tmp_label1 == "ambulance_h":
                if tmp_score1 >= Ambulance_Head_Thresh:
                    judge_infos["vio_judge"] = "no_vio_010"
            elif tmp_score1 >= Special_Car_Thresh:
                judge_infos["vio_judge"] = "no_vio_010"

    det2 = BlendMaskDetect(img2_np)
    predictions2, kp_mask2 = det2.mask_predict(demo, show=show)
    nms_ls2 = det2.nms_class_distinct(predictions2["instances"].pred_boxes.tensor,
                                      predictions2["instances"].scores,
                                      predictions2["instances"].pred_classes,
                                      threshold=0.8,
                                      class_ditinct_threshold=0.85)
    predictions2, kp_mask2 = det2.prediction_nms_filter(predictions2, nms_ls2, kp_mask2)
    # car2_bbox_ps, cars2_mask_list = det2.class_info(predictions2, car_apply_list, div_label=False)
    car2_bbox_ps, cars2_mask_list, labels_list2, score_list2 = det1.class_info2(predictions2, Blend_Judge_Vehicle,
                                                                                Blend_Conf, need_mask=True)

    img2_car_ps, plate2_num, img2_plate_dict, new_car2_bbox_ps = CarJudge(
        iiea_cfg=judge_infos["iieaFilterVehicle"]).confirm_img1_car_ps(img2_np, src_plate_num,
                                                                       plate_det_model,
                                                                       plate_rec_model,
                                                                       car2_bbox_ps,
                                                                       device, label=0,
                                                                       show=save,
                                                                       save_path=save_path)
    plate_list.append(plate2_num)
    # 过滤白名单车辆
    if img2_car_ps:
        if plate2_num.endswith("警"):
            judge_infos["vio_judge"] = "no_vio_010"

        tmp_idx2 = car2_bbox_ps.index(img2_car_ps)
        tmp_label2 = labels_list2[tmp_idx2]
        tmp_score2 = score_list2[tmp_idx2]
        judge_infos['judgeConf'] = float(tmp_score2)
        # 特殊车辆判定
        if tmp_label2 in special_type_list:
            if tmp_label2 == "ambulance_h":
                if tmp_score2 >= Ambulance_Head_Thresh:
                    judge_infos["vio_judge"] = "no_vio_010"
            elif tmp_score2 >= Special_Car_Thresh:
                judge_infos["vio_judge"] = "no_vio_010"

    car3_bbox_ps = img3_car_ps = []
    det3 = predictions3 = None
    if not db_label:
        det3 = BlendMaskDetect(img3_np)
        predictions3, kp_mask3 = det3.mask_predict(demo, show=show)
        nms_ls3 = det3.nms_class_distinct(predictions3["instances"].pred_boxes.tensor,
                                          predictions3["instances"].scores,
                                          predictions3["instances"].pred_classes,
                                          threshold=0.8,
                                          class_ditinct_threshold=0.85)
        predictions3, kp_mask3 = det3.prediction_nms_filter(predictions3, nms_ls3, kp_mask3)
        # car3_bbox_ps, cars3_mask_list = det3.class_info(predictions3, car_apply_list, div_label=False)
        car3_bbox_ps, cars3_mask_list, labels_list3, score_list3 = det1.class_info2(predictions3, Blend_Judge_Vehicle,
                                                                                    Blend_Conf, need_mask=True)

        img3_car_ps, plate3_num, img3_plate_dict, new_car3_bbox_ps = CarJudge(
            iiea_cfg=judge_infos["iieaFilterVehicle"]).confirm_img1_car_ps(img3_np,
                                                                           src_plate_num,
                                                                           plate_det_model,
                                                                           plate_rec_model,
                                                                           car3_bbox_ps,
                                                                           device,
                                                                           label=0, show=save,
                                                                           save_path=save_path)
        plate_list.append(plate3_num)
        # 过滤白名单车辆
        if img3_car_ps:
            if plate3_num.endswith("警"):
                judge_infos["vio_judge"] = "no_vio_010"
            tmp_idx3 = car3_bbox_ps.index(img3_car_ps)
            tmp_label3 = labels_list3[tmp_idx3]
            tmp_score3 = score_list3[tmp_idx3]
            judge_infos['judgeConf'] = float(tmp_score3)
            # 特殊车辆判定
            if tmp_label3 in special_type_list:
                if tmp_label3 == "ambulance_h":
                    if tmp_score3 >= Ambulance_Head_Thresh:
                        judge_infos["vio_judge"] = "no_vio_010"
                elif tmp_score3 >= Special_Car_Thresh:
                    judge_infos["vio_judge"] = "no_vio_010"
        all_car_ps = [car1_bbox_ps, car2_bbox_ps, car3_bbox_ps]

    # 车辆相似度匹配
    if not len(img2_car_ps) and img1_car_ps:
        img2_car_ps, plate2_num, img2_conf = CarJudge(device).confirm_img23_car_ps(img1_car_ps, img1_np, img2_np,
                                                                                   src_plate_num, new_car2_bbox_ps,
                                                                                   wf_label=True, show=save,
                                                                                   save_path=save_path,
                                                                                   save_name="cl_12")
    rejudge_label = 0
    if (not img1_car_ps) and img2_car_ps:
        img1_car_ps, plate1_num, img1_conf, _ = \
            CarJudge(device).confrim_img23_plate(img2_car_ps, img2_np, img1_np, new_car1_bbox_ps, src_plate_num,
                                                 wf_label=True,
                                                 show=save, save_path=save_path, save_name="cl_21")
        if len(img1_car_ps):
            rejudge_label = 1
            # cars_ps[0] = img1_car_ps
            # plate_list[0] = plate1_num
    if not db_label:
        if not len(img3_car_ps) and img2_car_ps:
            img3_car_ps, plate3_num, img3_conf = CarJudge(device).confirm_img23_car_ps(img2_car_ps, img2_np, img3_np,
                                                                                       src_plate_num, new_car3_bbox_ps,
                                                                                       wf_label=True, show=save,
                                                                                       save_path=save_path,
                                                                                       save_name="cl_23")
        if not len(img1_car_ps) and not len(img2_car_ps) and img3_car_ps:
            img2_car_ps, plate2_num, img2_conf = CarJudge(device).confirm_img23_car_ps(img3_car_ps, img3_np, img2_np,
                                                                                       src_plate_num, new_car2_bbox_ps,
                                                                                       wf_label=True, show=save,
                                                                                       save_path=save_path,
                                                                                       save_name="cl_32")
            img1_car_ps, plate1_num, img1_conf = CarJudge(device).confirm_img23_car_ps(img3_car_ps, img3_np, img1_np,
                                                                                       src_plate_num,
                                                                                       new_car1_bbox_ps, wf_label=True,
                                                                                       show=save, save_path=save_path,
                                                                                       save_name="cl_31")
            if len(img1_car_ps) and len(img2_car_ps):
                rejudge_label = 2

    # 返回检测框和置信度信息
    img_np_list = file_list
    img_car_ps_list = [img1_car_ps, img2_car_ps, img3_car_ps] if not db_label else [img1_car_ps, img2_car_ps]
    model_class_list = [det1, det2, det3]
    res_list = [predictions1, predictions2, predictions3]

    for idx, box in enumerate(img_car_ps_list):
        if box:
            conf = model_class_list[idx].get_target_conf(res_list[idx], box)
            img_id = idx + 1
            percent_coord = convert_src_coord(box, img_np_list[idx], img_id, judge_infos["split_mode"],
                                              judge_infos["black_height"], judge_infos["black_pos"])
            tmp_res = {
                "objId": img_id,
                "objType": "vehicle",
                "objConf": float(conf),
                "objCoord": percent_coord
            }
            judge_infos["modelDetectResults"].append(tmp_res)
    # 车牌位置信息
    plate1_cor = img1_plate_dict[str(img1_car_ps)] if img1_car_ps else []
    plate2_cor = img2_plate_dict[str(img2_car_ps)] if img2_car_ps else []
    plate3_cor = img3_plate_dict[str(img3_car_ps)] if img3_car_ps else []
    plate_box_list = [plate1_cor, plate2_cor, plate3_cor]
    plate_dict_list = [img1_plate_dict, img2_plate_dict, img3_plate_dict] if img3_car_ps else [img1_plate_dict,
                                                                                               img2_plate_dict]
    for idx, box in enumerate(plate_box_list):
        if box:
            target_idx = str(img_car_ps_list[idx]) + "conf"
            img_id = idx + 1
            percent_coord = convert_src_coord(box, img_np_list[idx], img_id, judge_infos["split_mode"],
                                              judge_infos["black_height"], judge_infos["black_pos"])
            tmp_res = {
                "objId": img_id,
                "objType": "plate",
                "objConf": float(plate_dict_list[idx][target_idx]),
                "objCoord": percent_coord
            }
            judge_infos["modelDetectResults"].append(tmp_res)

    # print(img1_car_ps, plate1_num)
    img1_cars_remove_img = det1.cars_mask_pro(cars1_mask_list)
    _, img1_lane_mask = det1.class_info(predictions1, ['lane1', 'lane2'], div_label=False)

    img1_lane_mask1 = Tools().merge_masks([], img1_lane_mask, label=0, img_shape=det1.img_np.shape[:2])

    _, img1_stop_mask = det1.class_info(predictions1, ['stop_area'], div_label=False)
    img1_stop_mask1 = Tools().merge_masks([], img1_stop_mask, label=0, img_shape=det1.img_np.shape[:2])
    if len(img1_car_ps):
        img1_car_mask = cars1_mask_list[car1_bbox_ps.index(img1_car_ps[:4])]

        # print('-------dsadasd---------', img1_car_mask.shape)
    else:
        img1_car_mask = np.zeros((img1_np.shape[0], img1_np.shape[1]), dtype=np.uint8)
        # print('----------------', img1_car_mask.shape)
    cars_ps.append(img1_car_ps)
    stops_masks.append(img1_stop_mask1)
    lanes_masks.append(img1_lane_mask1)
    cars_masks.append(img1_car_mask)
    cars_remove_img.append(img1_cars_remove_img)
    kp_masks_list.append(kp_mask1)
    predictions_list.append(predictions1)

    img2_cars_remove_img = det2.cars_mask_pro(cars2_mask_list)
    _, img2_lane_mask = det2.class_info(predictions2, ['lane1', 'lane2'], div_label=False)
    img2_lane_mask1 = Tools().merge_masks([], img2_lane_mask, label=0, img_shape=det2.img_np.shape[:2])
    _, img2_stop_mask = det2.class_info(predictions2, ['stop_area'], div_label=False)
    img2_stop_mask1 = Tools().merge_masks([], img2_stop_mask, label=0, img_shape=det2.img_np.shape[:2])

    if len(img2_car_ps):
        img2_car_mask = cars2_mask_list[car2_bbox_ps.index(img2_car_ps[:4])]
    else:
        img2_car_mask = np.zeros((img2_np.shape[1], img2_np.shape[0]), dtype=np.uint8)

    cars_ps.append(img2_car_ps)
    stops_masks.append(img2_stop_mask1)
    lanes_masks.append(img2_lane_mask1)
    cars_masks.append(img2_car_mask)
    cars_remove_img.append(img2_cars_remove_img)
    kp_masks_list.append(kp_mask2)
    predictions_list.append(predictions2)

    if not db_label:

        img3_cars_remove_img = det3.cars_mask_pro(cars3_mask_list)
        _, img3_lane_mask = det3.class_info(predictions3, ['lane1', 'lane2'], div_label=False)
        img3_lane_mask1 = Tools().merge_masks([], img3_lane_mask, label=0, img_shape=det3.img_np.shape[:2])

        _, img3_stop_mask = det3.class_info(predictions3, ['stop_area'], div_label=False)
        img3_stop_mask1 = Tools().merge_masks([], img3_stop_mask, label=0, img_shape=det3.img_np.shape[:2])
        if len(img3_car_ps):
            img3_car_mask = cars3_mask_list[car3_bbox_ps.index(img3_car_ps[:4])]
        else:
            img3_car_mask = np.zeros((img3_np.shape[1], img3_np.shape[0]), dtype=np.uint8)

        cars_ps.append(img3_car_ps)
        stops_masks.append(img3_stop_mask1)
        lanes_masks.append(img3_lane_mask1)
        cars_masks.append(img3_car_mask)
        cars_remove_img.append(img3_cars_remove_img)
        kp_masks_list.append(kp_mask3)
        predictions_list.append(predictions3)
    else:
        img3_lane_mask1 = []
        img3_stop_mask1 = []
    # print('===================', img1_car_ps, img2_car_ps, img3_car_ps)
    if db_label:
        if np.array([len(img1_car_ps), len(img2_car_ps)]).all() == 0:
            # predictions, cars_ps, plate_list, cars_masks_list, kp_mask_list, lane_mask, stop_mask, rejudge_label
            if get_lane_info:
                return predictions_list, cars_ps, all_car_ps, plate_list, cars_masks, kp_masks_list, [], [], rejudge_label, lane_info_list
            else:
                return predictions_list, cars_ps, all_car_ps, plate_list, cars_masks, kp_masks_list, [], [], rejudge_label
    if not db_label:
        if np.array([len(img1_car_ps), len(img2_car_ps), len(img3_car_ps)]).all() == 0:
            if get_lane_info:
                return predictions_list, cars_ps, all_car_ps, plate_list, cars_masks, kp_masks_list, [], [], rejudge_label, lane_info_list
            else:
                return predictions_list, cars_ps, all_car_ps, plate_list, cars_masks, kp_masks_list, [], [], rejudge_label
    if merge:
        temp = np.where(img1_cars_remove_img <= 10, cv2.resize(img2_cars_remove_img, (
            img1_cars_remove_img.shape[1], img1_cars_remove_img.shape[0])), img1_cars_remove_img)
        if not db_label:
            temp = np.where(temp <= 10, cv2.resize(img3_cars_remove_img, (temp.shape[1], temp.shape[0])), temp)
        roi4_np = temp
        det4 = BlendMaskDetect(roi4_np)
        predictions4, kp_mask4 = det4.mask_predict(demo, show=show)
        nms_ls4 = det4.nms_class_distinct(predictions4["instances"].pred_boxes.tensor,
                                          predictions4["instances"].scores,
                                          predictions4["instances"].pred_classes,
                                          threshold=0.8,
                                          class_ditinct_threshold=0.85)
        predictions4, kp_mask4 = det2.prediction_nms_filter(predictions4, nms_ls4, kp_mask4)
        _, img4_lane_mask = det4.class_info(predictions4, ['lane1', 'lane2'], div_label=False)
        img4_lane_mask1 = Tools().merge_masks([], img4_lane_mask, label=0, img_shape=det4.img_np.shape[:2])
        _, img4_stop_mask = det4.class_info(predictions4, ['stop_area'], div_label=False)
        img4_stop_mask1 = Tools().merge_masks([], img4_stop_mask, label=0, img_shape=det4.img_np.shape[:2])
    else:
        img4_lane_mask1 = []
        img4_stop_mask1 = []

    if db_label:
        roi_cor = list(Tools().area_pro(img1_np, img1_car_ps, img2_car_ps))
    else:
        roi_cor = list(Tools().area_pro(img1_np, img1_car_ps, img2_car_ps, img3_car_ps))
    # print(roi_cor)
    img1_car_roi_mask = img1_car_mask[roi_cor[1]:roi_cor[3], roi_cor[0]:roi_cor[2]]
    # print('++1', img1_car_roi_mask.shape)
    img2_car_roi_mask = img2_car_mask[roi_cor[1]:roi_cor[3], roi_cor[0]:roi_cor[2]]

    # print('++2',img2_car_roi_mask.shape)
    img1_car_roi_mask = np.where(img1_car_roi_mask == 0, 0, 255)
    img2_car_roi_mask = np.where(img2_car_roi_mask == 0, 0, 255)

    if not db_label:
        img3_car_roi_mask = img3_car_mask[roi_cor[1]:roi_cor[3], roi_cor[0]:roi_cor[2]]
        img3_car_roi_mask = np.where(img3_car_roi_mask == 0, 0, 255)

    lane_mask_list = [i for i in [img1_lane_mask1, img2_lane_mask1, img3_lane_mask1, img4_lane_mask1] if len(i) > 0]
    stop_mask_list = [i for i in [img1_stop_mask1, img2_stop_mask1, img3_stop_mask1, img4_stop_mask1] if len(i) > 0]
    # for lane in lane_mask_list:
    #     print('lane', lane.shape)
    # for stop in stop_mask_list:
    #     print('stop', stop.shape)
    lane_mask = Tools().merge_masks(roi_cor, lane_mask_list, label=1, img_shape=det1.img_np.shape[:2])
    stop_mask = Tools().merge_masks(roi_cor, stop_mask_list, label=1, img_shape=det1.img_np.shape[:2])

    if db_label:
        cars_masks_list = [img1_car_roi_mask, img2_car_roi_mask]
    else:
        cars_masks_list = [img1_car_roi_mask, img2_car_roi_mask, img3_car_roi_mask]
    # print(lane_mask.shape)
    # print(stop_mask.shape)
    # for car_mask_t in cars_masks_list:
    #     print(car_mask_t.shape)
    if not get_lane_info:
        return predictions_list, cars_ps, all_car_ps, plate_list, cars_masks_list, kp_masks_list, lane_mask, stop_mask, rejudge_label
    else:
        lanes_img1, _ = det1.class_info(predictions1,
                                        ['lane1', 'lane2', "arrow_l", "arrow_s", "arrow_r", 'stop_line', 'zebra_line',
                                         'arrow_s_t', 'arrow_l_t', 'arrow_r_t'],
                                        div_label=True)
        lanes_img2, _ = det2.class_info(predictions2,
                                        ['lane1', 'lane2', "arrow_l", "arrow_s", "arrow_r", 'stop_line', 'zebra_line',
                                         'arrow_s_t', 'arrow_l_t', 'arrow_r_t'],
                                        div_label=True)
        lane_info_list = [lanes_img1, lanes_img2]

        if not db_label:
            lanes_img3, _ = det3.class_info(predictions3,
                                            ['lane1', 'lane2', "arrow_l", "arrow_s", "arrow_r", 'stop_line',
                                             'zebra_line', 'arrow_s_t', 'arrow_l_t', 'arrow_r_t'],
                                            div_label=True)
            lane_info_list.append(lanes_img3)

        return predictions_list, cars_ps, all_car_ps, plate_list, cars_masks_list, kp_masks_list, lane_mask, stop_mask, rejudge_label, lane_info_list


# blendmask识别斑马线
def get_zebra_line_info(img_np, show=False):
    demo = BlendMaskModel().load_model()
    det = BlendMaskDetect(img_np)
    predictions1 = det.mask_predict(demo, show=show)
    lanes_bbox_ps, cars_mask_list = det.class_info(predictions1, ["zebra_line"], div_label=True)
    try:
        bbox_ps = lanes_bbox_ps["zebra_line"]
    except:
        bbox_ps = []

    return bbox_ps


# 道路信息识别及融合
def road_infos_recover(file_list, demo, label, show=False):
    tmp_mask_list = []
    for img_np in file_list:
        det = BlendMaskDetect(img_np)
        predictions, kps_mask = det.mask_predict(demo, show)
        box_info, mask_info = det.class_info(predictions, label, div_label=True)
        cls_lables = box_info.keys()
        mask_temp = []
        for mask_key, mask_values in mask_info.items():
            if mask_key in cls_lables:
                mask_tmps = [mask for mask in mask_values[0]]
                mask_temp.extend(mask_tmps)
        if len(mask_temp):
            tmp_mask = Tools().merge_masks([], mask_temp, label=0).astype(np.uint8)
            tmp_mask_list.append(tmp_mask)
            # cv2.imwrite('arrow_mask.jpg', tmp_mask)
    tmp_mask = Tools().merge_masks([], tmp_mask_list, label=0)
    # cv2.imwrite('arrow_mask_final.jpg', tmp_mask)
    return tmp_mask


# 道路信息融合
def road_infos_recover_mask(box_info_list, mask_info_list):
    tmp_mask_list = []
    for i, box_index in enumerate(box_info_list):
        mask_temp = []
        box_info = box_info_list[i]
        mask_info = mask_info_list[i]
        # print(box_info)
        cls_lables = box_info.keys()
        for mask_key, mask_values in mask_info.items():
            if mask_key in cls_lables:
                # print(mask_key)
                # print('***', mask_values[0])
                mask_tmps = [mask for mask in mask_values[0]]
                mask_temp.extend(mask_tmps)
        if len(mask_temp):
            tmp_mask = Tools().merge_masks([], mask_temp, label=0).astype(np.uint8)
            tmp_mask_list.append(tmp_mask)
        # cv2.imwrite('{}.jpg'.format(i), tmp_mask)
    if len(tmp_mask_list):
        tmp_mask = Tools().merge_masks([], tmp_mask_list, label=0).astype(np.uint8)
        # cv2.imwrite('{}.jpg'.format(5555), tmp_mask)
    else:
        tmp_mask = []
    return tmp_mask
