import os

os.environ["CUDA_VISIBLE_DEVICES"] = "1"
import sys

sys.path.append('..')
from glob import glob
import json
import cv2
import numpy as np
from ai_judge_code_gy.blendmask_detect import BlendMaskModel, BlendMaskDetect
from main_config.config import _LABEL
from tqdm import tqdm
from make_blendmask_labels import find_contours

update_class_list = ['car_h', 'car_b', 'bus_h', 'bus_b', 'truck_h', 'truck_b', 'ambulance_h', 'ambulance_b',
                     'police_car_h', 'police_car_b', 'engineering_car_h', 'engineering_car_b', 'motorbike_h',
                     'motorbike_b', 'arrow_s', 'arrow_l', 'arrow_r']


def update_labels(masks, labels, json_path):
    name = json_path.split("/")[-1]
    with open(json_path, "r", encoding="utf-8") as f:
        src_json = json.load(f)
        shapes = src_json["shapes"]
        for i in shapes:
            if i["label"] in update_class_list:
                shapes.remove(i)

        for idx, mask in enumerate(masks):
            class_name = _LABEL[labels[idx]]
            if class_name not in update_class_list:
                continue
            mask_pic = np.array(mask, dtype=np.uint8) * 255
            contour = find_contours(mask_pic)
            contours_msg = {"label": class_name,
                            "points": contour,
                            "group_id": None,
                            "shape_type": "polygon",
                            "flags": {}}
            src_json["shapes"].append(contours_msg)

    with open(json_path, "w", encoding="utf-8") as f2:
        json.dump(src_json, f2, indent=2)
        print(f"{name} is saved!")


if __name__ == '__main__':
    root = "./update_labels"
    img_path_list = glob(root + "/*.jpg")
    demo = BlendMaskModel().load_model()
    for img_path in tqdm(img_path_list):
        json_path = root + "/" + img_path.split("/")[-1].split(".")[0] + ".json"
        img = cv2.imread(img_path)
        det = BlendMaskDetect(img)
        predictions, _ = det.mask_predict(demo, show=False)
        label_idx_list = predictions["instances"].pred_classes.cpu().numpy().tolist()
        masks = predictions["instances"].pred_masks.cpu().numpy()
        update_labels(masks, label_idx_list, json_path)
