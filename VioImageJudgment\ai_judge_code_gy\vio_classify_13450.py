from PIL import Image
import numpy as np
from torchvision import transforms as T
import torch as t
import torch.nn as nn
import torch.nn.functional as F


class Net(nn.Module):
    def __init__(self):
        super(Net, self).__init__()
        # 输入3通道，输出2通道，kernel 3*3
        self.conv1 = nn.Conv2d(3, 10, kernel_size=3)
        self.conv2 = nn.Conv2d(10, 30, kernel_size=3)
        self.conv3 = nn.Conv2d(30, 50, kernel_size=3)
        self.conv4 = nn.Conv2d(50, 70, kernel_size=3)
        self.conv5 = nn.Conv2d(70, 90, kernel_size=3)
        self.mp = nn.MaxPool2d(2)
        self.dropout = nn.Dropout(p=0.5)
        # fully connect
        self.fc1 = nn.Linear(10080, 1024)
        self.fc2 = nn.Linear(2250, 512)
        self.fc3 = nn.Linear(512, 2)
        self.fc4 = nn.Linear(1024, 2)

    def forward(self, x):
        # in_size = 16
        in_size = x.size(0)  # one batch
        # x: 16*10*12*12
        x = F.relu(self.mp(self.conv1(x)))
        # x:
        x = F.relu(self.mp(self.conv2(x)))
        #
        x = F.relu(self.mp(self.conv3(x)))
        #         x = self.dropout(x)
        x = F.relu(self.mp(self.conv4(x)))  # 4320
        #         x = F.relu(self.mp(self.conv5(x)))#250
        x = self.dropout(x)

        x = x.view(in_size, -1)  # flatten the tensor
        #         print(x.shape)
        # x: 64*10
        x = F.relu(self.fc1(x))
        x = self.dropout(x)
        #         x = F.relu(self.fc2(x))
        #         x = self.dropout(x)
        #         x = self.fc3(x)
        x = self.fc4(x)

        #         return t.sigmoid(x)
        return x


# 模型判定压线违法
class Vio_pred:
    def __init__(self, img_arr, model, device):
        self.model = model
        # self.model.eval()
        self.img4pred = Image.fromarray(np.uint8(img_arr))
        transform = T.Compose([
            T.Resize((224, 224)),  # 缩放图片（Image），保持长宽比不变，最短边为224像素
            T.ToTensor(),  # 将图片（Image）转成Tensor，归一化至[0,1]
            T.Normalize(mean=[.5, .5, .5], std=[.5, .5, .5])  # 标准化至[-1,1],规定均值和标准差
        ])
        self.tran_img = t.reshape(transform(self.img4pred), (1, 3, 224, 224))
        self.device = device

    def pred(self, judge_mode, thresh):
        if thresh is None:
            thresh = 8
        model = self.model.to(self.device)
        img = self.tran_img.to(self.device)
        with t.no_grad():
            output = model(img)

        value, label = output.data.max(1, keepdim=True)

        value = value.data.cpu().numpy()[0][0]
        label = label.data.cpu().numpy()[0][0]
        # 检准模式
        if judge_mode:
            if value >= thresh:
                label = 1
            else:
                label = 0

        return label
