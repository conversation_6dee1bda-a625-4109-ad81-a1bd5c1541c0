import requests
import base64
from pydantic import BaseModel
import urllib
import os
import time
import datetime
import threading, multiprocessing


def file_input():
    # img_dir = "../test_data/0715"
    img_dir = r"D:\Python_Project\code_king\img"
    cnt = 0
    for i in os.listdir(img_dir):
        cnt += 1
        if cnt > 1:
            continue
        thread1 = threading.Thread(name=str(i), target=webrequest,
                                   args=(img_dir, i))
        thread1.start()


def webrequest(img_dir, i):
    root = f"{img_dir}/{i}"
    plate = i.split("_")[0]
    with open(root, "rb") as f:
        image = f.read()
        base64data = base64.b64encode(image)
        input_data = [{"file": base64data.decode()}]
        request_data = {"session_id": "001", "mode": 1, "violation_type": "1625", "plate_number": plate,
                        "images": input_data,
                        "shoot_scene": "", "extra": "", "dev_desc": "", "special_car_type": 0}
        # request_data['images'] = input_data
        print("\n发送请求:", datetime.datetime.now())
        x = requests.post('http://10.184.48.200:9000/judgeVioPics', json=request_data)
        print("请求完成:", plate, datetime.datetime.now())
        print(x.text, "\n")

        # str1 = base64data.decode()
        # print('str1 =', type(str1))
        # ## post 传输
        # byte1 = str1.encode()
        # print('byte1 =', type(byte1))
        #
        # b64_data = byte1 # base64data
        # data = base64.b64decode(b64_data)
        # with open("2.jpg", "wb") as f:
        #     f.write(data)

def webrequest2(img_dir, i):
    root = f"{img_dir}/{i}"
    plate = i.split("_")[-2]
    with open(root, "rb") as f:
        image = f.read()
        base64data = base64.b64encode(image)
        input_data = [{"file": base64data.decode()}]
        request_data = {"session_id": "001", "mode": 1, "violation_type": "1625", "plate_number": plate,
                        "images": input_data,
                        "shoot_scene": "1", "extra": "1", "dev_desc": "1", "special_car_type": 0}
        # request_data['images'] = input_data
        print("\n发送请求:", datetime.datetime.now())
        x = requests.post('http://10.184.48.200:7777/post')
        print("请求完成:", plate, datetime.datetime.now())
        print(x.text, "\n")

        # str1 = base64data.decode()
        # print('str1 =', type(str1))
        # ## post 传输
        # byte1 = str1.encode()
        # print('byte1 =', type(byte1))
        #
        # b64_data = byte1 # base64data
        # data = base64.b64decode(b64_data)
        # with open("2.jpg", "wb") as f:
        #     f.write(data)

def url_input():
    input_data = [{
        "url": "https://gimg2.baidu.com/image_search/src=http%3A%2F%2Fimg2.autotimes.com.cn%2Fnews%2F2021%2F05%2F0520_095731464189.jpg&refer=http%3A%2F%2Fimg2.autotimes.com.cn&app=2002&size=f9999,10000&q=a80&n=0&g=0n&fmt=auto?sec=1663308575&t=1343e895f05d6777f9d51827432994d0"}]
    request_data = {"session_id": "001", "mode": 0, "violation_type": "1625", "plate_number": u"苏A01713D",
                    "images": input_data,
                    "shoot_scene": "1", "extra": "1", "dev_desc": "1", "special_car_type": 0}
    # x = requests.post('http://10.184.48.200:7000/post', json=request_data)
    img_path = "1.jpg"
    img_url = input_data[0]['url']
    urllib.request.urlretrieve(img_url, filename=img_path)


if __name__ == '__main__':
    file_input()
