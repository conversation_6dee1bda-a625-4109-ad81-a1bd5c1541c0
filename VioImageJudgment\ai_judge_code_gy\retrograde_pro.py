import json
import numpy as np
import matplotlib.pyplot as plt
import PIL.Image
import PIL.ImageDraw
import cv2
from skimage import measure
from ai_judge_code_gy.judge_tools import Tools


# 核心函数，其中有两个类Road和Point4region
# Road用来读取道路信息
# Point4region用来生成判定范围


class Road:
    def __init__(self, road_info):
        height = road_info['imageHeight']
        width = road_info['imageWidth']
        self.address = road_info['dev_desc']
        road_info = road_info['shapes']
        # 读取直行箭头,返回所有直行箭头的向量
        self.arrow_down = []
        try:
            self.arrow_straight = self.arr_s_init(
                [x['points'] for x in road_info if x["label"] == "arrow_s"],
                height, width)
        except:
            self.arrow_straight = None
        try:
            # 读取左转箭头，返回左转箭头向量
            self.arrow_left = self.arr_l_init(
                [x['points'] for x in road_info if x["label"] == "arrow_l"],
                height, width)
        except:
            self.arrow_left = None
        try:
            # 读取右转箭头
            self.arrow_right = self.arr_r_init(
                [x['points'] for x in road_info if x["label"] == "arrow_r"],
                height, width)
        except:
            self.arrow_right = None
        try:
            # 读取反向箭头
            self.arrow_transposition = self.arr_r_init(
                [x['points'] for x in road_info if x["label"] in ['arrow_s_t', 'arrow_l_t', 'arrow_r_t'] ],
                height, width)
        except:
            self.arrow_transposition = None
        # 读取最左侧车道线
        lane_tmp = [x['points'] for x in road_info if x["label"] == "lane2"]
        if lane_tmp:
            self.lane2 = True
        else:
            lane_tmp = [
                x['points'] for x in road_info if x["label"] == "lane1"
            ]
            self.lane2 = False

        self.lane = self.lane2_init(lane_tmp, height, width)
        # 同时生成一个lane的全量集合用于后续筛选
        lane_tmp = [x['points'] for x in road_info if x["label"] in ["lane2", "lane1"]]
        self.lane_all = self.lane2_init(lane_tmp, height, width, True)

        # 读取停止线
        self.stop_line = self.stop_line_init(
            [x['points'] for x in road_info if x["label"] == "stop_line"],
            height, width)
        # 读取禁止驶入
        self.no_entry = self.no_entry_init(
            [x['points'] for x in road_info if x["label"] == "no_entry"],
            height, width)
        # 读取斑马线，逻辑与停止线完全相同--- 这里直接复用
        self.zebra_line = self.zebra_line_init(
            [x['points'] for x in road_info if x["label"] == "zebra_line"],
            height, width)

        # 生成直行区域mask打点
        #         print(self.stop_line)
        region = Points4region(height,
                               width,
                               lane_vec=self.lane,
                               arrow_l_vec=self.arrow_left,
                               arrow_r_vec=self.arrow_right,
                               arrow_s_vec=self.arrow_straight,
                               arrow_back=self.arrow_down,
                               stop_line_vec=self.stop_line,
                               lane_all_vec=self.lane_all,
                               lane2=self.lane2,
                               zebra_line=self.zebra_line,
                               lane2_condition=self.lane2,
                               arrow_t_vec=self.arrow_transposition
                               )
        #         print(self.arrow_straight)
        area_points = region.draw_points()
        self.area_points = area_points  #### 方便后续画图
        self.area = self.area_init(area_points, height, width)
        self.back_area_points = region.draw_back_points()
        self.back_area = self.area_init(self.back_area_points, height, width)
        self.height = height
        self.width = width

    # 直行箭头计算函数

    def arr_s_init(self, points, height, width):
        if points:  # 防止没有直行箭头
            points_temp = []
            for p1 in points:
                for p2 in p1:
                    points_temp.append(p2)
            points = points_temp
            output = []
            mask = np.zeros((height * 2, width), dtype=np.uint8)  # 生成空白mask
            mask = PIL.Image.fromarray(mask)
            draw = PIL.ImageDraw.Draw(mask)
            #             print(points)
            for i in range(np.shape(points)[0]):
                xy = [tuple(point) for point in points[i]]
                draw.polygon(xy=xy, fill=1)  # 把直行箭头画上去
            mask = np.array(mask)
            # plt.imshow(mask)
            labeled_img, num = measure.label(mask,
                                             background=0,
                                             return_num=True)  # 分成连通区域
            # 我真的没有搞懂为什么要添加一个判断箭头方向逻辑
            for i in range(num):
                p = np.where(labeled_img == i + 1)
                p_mid = np.mean(p, 1)
                p_mid = np.array([p_mid[1], p_mid[0]])  # 找到箭头中点
                p_top_index = np.where(p[0] == min(p[0]))[0][0]  # 找到箭头最上点
                p_top = np.array([p[1][p_top_index], p[0][p_top_index]])
                p_bottom = p_mid * 2 - p_top
                output.append([p_bottom, p_top])

                # if self.arrow_direct(np.array(labeled_img == i + 1)):
                #     p_top_index = np.where(p[0] == min(p[0]))[0][0]  # 找到箭头最上点
                #     p_top = np.array([p[1][p_top_index], p[0][p_top_index]])
                #     p_bottom = p_mid * 2 - p_top
                #     output.append([p_bottom, p_top])
                # else:
                #     p_down_index = np.where(p[0] == max(p[0]))[0][0]  # 找到箭头最上点
                #     p_down = np.array([p[1][p_down_index], p[0][p_down_index]])
                #     p_bottom = p_mid * 2 - p_down
                #     self.arrow_down.append([p_bottom, p_down])

            output.sort(key=lambda vector: vector[0][0])  # 按从左到右排序
            self.arrow_down.sort(key=lambda vector: vector[0][0])  # 按从左到右排序
            return output
        else:
            return []

    def arr_l_init(self, points, height, width):
        if points:  # 防止没有左转箭头
            points_temp = []
            for p1 in points:
                for p2 in p1:
                    points_temp.append(p2)
            points = points_temp
            output = []
            mask = np.zeros((height, width), dtype=np.uint8)  # 生成空白mask
            mask = PIL.Image.fromarray(mask)
            draw = PIL.ImageDraw.Draw(mask)
            for i in range(np.shape(points)[0]):
                xy = [tuple(point) for point in points[i]]
                draw.polygon(xy=xy, fill=1)  # 把左转箭头画上去
            mask = np.array(mask)
            mask = cv2.Canny(mask, 0, 1)
            loc = np.where(mask == 255)  # 获取左转箭头轮廓位置
            x = loc[1]  # 横坐标array
            y = loc[0]  # 纵坐标array
            idx_list = np.argsort(x)[:300]  # 取最左的三百个点(防止因角度问题 左转箭头尖端不是图片中最左)
            idx_left = np.argsort(y[idx_list])[0]  # 取300个点中最高的点
            p_left = np.array([x[idx_list][idx_left], y[idx_list][idx_left]])
            idx_bottom = np.argsort(y)[-1]  # 获取最底部点索引
            p_bottom = np.array([x[idx_bottom], y[idx_bottom]])
            output.append([p_left, p_bottom])
            output.sort(key=lambda vector: vector[0][0])  # 按从左到右排序
            return output
        else:
            return []

    def arr_r_init(self, points, height, width):
        if points:  # 防止没有右转箭头
            points_temp = []
            for p1 in points:
                for p2 in p1:
                    points_temp.append(p2)
            points = points_temp
            output = []
            mask = np.zeros((height, width), dtype=np.uint8)  # 生成空白mask
            mask = PIL.Image.fromarray(mask)
            draw = PIL.ImageDraw.Draw(mask)
            for i in range(np.shape(points)[0]):
                xy = [tuple(point) for point in points[i]]
                draw.polygon(xy=xy, fill=1)  # 把右转箭头画上去
            mask = np.array(mask)
            mask = cv2.Canny(mask, 0, 1)
            loc = np.where(mask == 255)  # 获取右转箭头轮廓位置
            x = loc[1]  # 横坐标array
            y = loc[0]  # 纵坐标array
            # 取最右的三百个点(防止因角度问题 右转箭头尖端不是图片中最右)
            idx_list = np.argsort(x)[-300:-1]
            idx_right = np.argsort(y[idx_list])[0]  # 取300个点中最高的点
            p_right = np.array(
                [x[idx_list][idx_right], y[idx_list][idx_right]])
            idx_bottom = np.argsort(y)[-1]  # 获取最底部点索引
            p_bottom = np.array([x[idx_bottom], y[idx_bottom]])
            output.append([p_right, p_bottom])
            output.sort(key=lambda vector: vector[0][0])  # 按从左到右排序
            return output
        else:
            return []

    def lane_init(self, points, height, width, all_info=False):
        if points:  # 防止没有车道线
            points_temp = []
            for p1 in points:
                for p2 in p1:
                    points_temp.append(p2)
            points = points_temp
            output = []
            mask = np.zeros((height, width), dtype=np.uint8)  # 空白mask
            mask = PIL.Image.fromarray(mask)
            draw = PIL.ImageDraw.Draw(mask)
            for i in range(np.shape(points)[0]):
                xy = [tuple(point) for point in points[i]]
                draw.polygon(xy=xy, fill=1)
            mask = np.array(mask)
            labeled_img, num = measure.label(mask,
                                             background=0,
                                             return_num=True)
            for i in range(num):
                p = np.where(labeled_img == i + 1)
                p_t = np.add(p[0], p[1])
                p_top_index = np.where(p_t == min(p_t))[0][0]  # 车道线左上点
                p_top = np.array([p[1][p_top_index], p[0][p_top_index]])
                p_t = p[1] - p[0]
                p_bottom_index = np.where(p_t == min(p_t))[0][0]  # 车道线左下点
                p_bottom = np.array(
                    [p[1][p_bottom_index], p[0][p_bottom_index]])
                output.append([p_top, p_bottom])
            output.sort(key=lambda vector: vector[0][0])  # 从左到右排序
            # 只要最左侧的车道线,返回([[x_top,y_top],[x_bottom,y_b]])

            if all_info:
                return output
            else:
                return [output[0]]
        else:
            return []

    def lane2_init(self, points, height, width, all_info=False):
        if points:  # 防止没有车道线
            points_temp = []
            for p1 in points:
                for p2 in p1:
                    points_temp.append(p2)
            points = points_temp
            output = []
            mask = np.zeros((height, width), dtype=np.uint8)  # 空白mask
            mask = PIL.Image.fromarray(mask)
            draw = PIL.ImageDraw.Draw(mask)
            for i in range(np.shape(points)[0]):
                xy = [tuple(point) for point in points[i]]
                draw.polygon(xy=xy, fill=1)
            mask = np.array(mask)
            labeled_img, num = measure.label(mask,
                                             background=0,
                                             return_num=True)
            for i in range(num):
                p = np.where(labeled_img == i + 1)

                # ### 求最高点的索引
                # p_top_index = min(np.where(p[0] == min(p[0]))[0])
                # p_top = np.array([p[1][p_top_index], p[0][p_top_index]])
                # p_bottom_index = min(np.where(p[0] == max(p[0]))[0])
                p_t = np.add(p[0], p[1])
                p_top_index = np.where(p_t == min(p_t))[0][0]  # 车道线左上点
                p_top = np.array([p[1][p_top_index], p[0][p_top_index]])
                p_t = p[1] - p[0]
                p_bottom_index = np.where(p_t == min(p_t))[0][0]  # 车道线左下点
                p_bottom = np.array(
                    [p[1][p_bottom_index], p[0][p_bottom_index]])
                output.append([p_top, p_bottom])
            ### output.sort(key=lambda vector: vector[0][0])  # 从左到右排序
            # 只要最左侧的车道线,返回([[x_top,y_top],[x_bottom,y_b]])

            ### 根据长度和位置简单去重

            output.sort(key=lambda vector: (vector[0][0] - vector[1][0]) ** 2 + (vector[0][1] - vector[1][1]) ** 2,
                        reverse=True)

            output_cp = [[(i[0][0] + i[1][0]) / 2, (i[0][1] + i[1][1]) / 2] for i in output]

            cnt = 0
            while cnt < len(output):
                drop_list = []
                line = output[cnt]
                for j in range(cnt + 1, len(output)):
                    foot_point = Tools().get_foot_point(output_cp[j], line)

                    ### 判断在不在线段上
                    if foot_point[0] >= min(line[0][0], line[1][0]) and foot_point[0] <= max(line[0][0], line[1][0]):
                        # 如在
                        dis = Tools().get_point_line_distance(output_cp[j], line)
                    else:
                        dis = \
                            min(((output_cp[j][0] - line[0][0]) ** 2 + (output_cp[j][1] - line[0][1]) ** 2) ** 0.5,
                                ((output_cp[j][0] - line[1][0]) ** 2 + (output_cp[j][1] - line[1][1]) ** 2) ** 0.5)

                    if dis < 100:
                        drop_list.append(j)

                drop_list.reverse()
                for idx in drop_list:
                    output.pop(idx)
                    output_cp.pop(idx)

                cnt = cnt + 1
            output.sort(key=lambda vector: vector[0][0])
            if all_info:
                return output
            else:
                return [output[0]]
        else:
            return []

    def stop_line_init(self, points, height, width):
        if points:
            points_temp = []
            for p1 in points:
                for p2 in p1:
                    points_temp.append(p2)
            points = points_temp
            output = []
            mask = np.zeros((height, width), dtype=np.uint8)
            mask = PIL.Image.fromarray(mask)
            draw = PIL.ImageDraw.Draw(mask)
            for i in range(np.shape(points)[0]):
                xy = [tuple(point) for point in points[i]]
                draw.polygon(xy=xy, fill=1)
            mask = np.array(mask)
            labeled_img, num = measure.label(mask,
                                             background=0,
                                             return_num=True)
            for i in range(num):
                p = np.where(labeled_img == i + 1)
                p_t = np.add(p[0], p[1])
                p_left_index = np.where(p_t == min(p_t))[0][0]
                p_left = np.array([p[1][p_left_index], p[0][p_left_index]])
                p_t = p[0] - p[1]
                p_right_index = np.where(p_t == min(p_t))[0][0]
                p_right = np.array([p[1][p_right_index], p[0][p_right_index]])
                output.append([p_left, p_right])
            output.sort(key=lambda vector: vector[0][0])
            return output
        else:
            return []

    def zebra_line_init(self, points, height, width):
        if points:
            points_temp = []
            for p1 in points:
                for p2 in p1:
                    points_temp.append(p2)
            points = points_temp
            output = []
            mask = np.zeros((height, width), dtype=np.uint8)
            mask = PIL.Image.fromarray(mask)
            draw = PIL.ImageDraw.Draw(mask)
            for i in range(np.shape(points)[0]):
                xy = [tuple(point) for point in points[i]]
                draw.polygon(xy=xy, fill=1)
            mask = np.array(mask)
            labeled_img, num = measure.label(mask,
                                             background=0,
                                             return_num=True)
            for i in range(num):
                p = np.where(labeled_img == i + 1)
                p_t = np.add(p[0], p[1])
                p_left_index = np.where(p_t == min(p_t))[0][0]
                p_left = np.array([p[1][p_left_index], p[0][p_left_index]])
                p_t = p[0] - p[1]
                p_right_index = np.where(p_t == min(p_t))[0][0]
                p_right = np.array([p[1][p_right_index], p[0][p_right_index]])
                output.append([p_left, p_right])

            output = [i for i in output if max(i[0][1],i[1][1]) > 0.35 * height]
            if output:
                x = int((min([i[0][0] for i in output] + [i[1][0] for i in output]) + max([i[0][0] for i in output] + [i[1][0] for i in output]))/2)
                y = max([i[0][1] for i in output] + [i[1][1] for i in output])
                output = np.array([x,y])
                return [output]
            else:
                return []
        else:
            return []

    def no_entry_init(self, points, height, width):
        if points:
            points_temp = []
            for p1 in points:
                for p2 in p1:
                    points_temp.append(p2)
            points = points_temp
            output = []
            mask = np.zeros((height, width), dtype=np.uint8)
            mask = PIL.Image.fromarray(mask)
            draw = PIL.ImageDraw.Draw(mask)
            for i in range(np.shape(points)[0]):
                xy = [tuple(point) for point in points[i]]
                draw.polygon(xy=xy, fill=1)
            mask = np.array(mask)
            p = np.where(mask > 0)
            p_index = np.where(p[0] == max(p[0]))
            return p[0][p_index][0]
        else:
            return 0

    def area_init(self, area_points, height, width):  # 用于生成逆行区域
        mask = np.zeros((height, width), dtype=np.uint8)  # 空白mask
        mask = PIL.Image.fromarray(mask)
        draw = PIL.ImageDraw.Draw(mask)
        #         print(area_points)
        if area_points:
            draw.polygon(area_points, fill=1)
        mask = np.array(mask)
        return mask

    def retrograde(self, car):
        car_mask = []
        car_loc = []
        region = self.area
        back_region = self.back_area

        for i in range(len(car)):
            mask = np.zeros((self.height, self.width), dtype=np.uint8)
            mask = PIL.Image.fromarray(mask)
            draw = PIL.ImageDraw.Draw(mask)
            xy = [tuple(point) for point in car[i]]
            if xy:
                draw.polygon(xy=xy, fill=1)
            mask = np.array(mask)
            car_mask.append(mask)
            p = np.where(mask > 0)
            p_mid = np.mean(p, 1)
            car_loc.append(p_mid[0])

        if self.no_entry:  # 在有禁止驶入的时候
            return (self.ret_no_entry(car_mask[0], car_mask[1])
                    or self.ret_no_entry(car_mask[1], car_mask[2]))
        if self.address.find('出口') > 0:
            if car_loc[0] > car_loc[1] and car_loc[1] > car_loc[2]:
                return True
            else:
                return False

        #         print(car_loc)
        if car_loc[0] < car_loc[1] or (car_loc[0] < car_loc[1] and car_loc[1] < car_loc[2]):  # 车子向下开
            try:
                region[car[0] == 0 or car[1] == 0 or car[2] == 0] = 0
            except:
                region[car[0] == 0 or car[1] == 0] = 0
            if np.sum(region) < 5000:
                return False
            else:
                #                 print(np.sum(region))
                return True

        if car_loc[0] > car_loc[1] or (car_loc[0] > car_loc[1] and car_loc[1] > car_loc[2]):  # 车子向上开
            return True
            try:
                back_region[car[0] == 0 or car[1] == 0 or car[2] == 0] = 0
            except:
                back_region[car[0] == 0 or car[1] == 0] = 0
            if np.sum(back_region) < 5000:
                return False
            else:
                return True
        else:
            return False

    def ret_no_entry(self, car1, car2):  # 判断是否违反了禁止驶入
        p_car1 = np.where(car1 > 0)
        y_car1_index = np.where(p_car1[0] == max(p_car1[0]))
        #         print(p_car1)
        y_car1 = p_car1[0][y_car1_index][0]
        p_car2 = np.where(car2 > 0)
        y_car2_index = np.where(p_car2[0] == min(p_car2[0]))
        y_car2 = p_car2[0][y_car2_index][0]
        if y_car1 > self.no_entry and y_car2 < self.no_entry:
            return True
        else:
            return False

    def arrow_direct(self, mask):  # 判断箭头方向
        s = np.sum(mask, 1)
        exist = np.where(s > 0)
        up = np.min(exist)
        down = np.max(exist)
        mid = np.where(s == max(s))[0][0]
        if down - mid > mid - up:
            return True  # 箭头朝上
        else:
            return False  # 箭头朝下


class Points4region:
    def __init__(self,
                 height,
                 width,
                 lane2=False,
                 lane_vec=None,
                 arrow_l_vec=None,
                 arrow_s_vec=None,
                 arrow_back=None,
                 stop_line_vec=None,
                 arrow_r_vec=None,
                 lane_all_vec=None,
                 lane2_condition=False,
                 zebra_line=None,
                 arrow_t_vec=None
                 ):
        self.h = height
        self.w = width
        self.lane2 = lane2  # 双实线布尔值 存在为True
        self.lane_vec = lane_vec  # 车道线向量
        self.arrow_l_vec = arrow_l_vec  # 左转箭头向量
        self.arrow_s_vec = arrow_s_vec  # 直行箭头向量
        self.arrow_b_vec = arrow_back  # 反向车道箭头
        self.stop_line_vec = stop_line_vec  # 停止线向量
        self.arrow_r_vec = arrow_r_vec
        self.lane_all_vec = lane_all_vec,
        self.lane2_condition = lane2_condition
        self.zebra_line = zebra_line
        self.arrow_t_vec = arrow_t_vec

    def cross_point(self, top, down, right, left):  # 计算交点函数
        x1 = top[0]  # 取四点坐标
        y1 = top[1]
        x2 = down[0]
        y2 = down[1]

        x3 = right[0]
        y3 = right[1]
        x4 = left[0]
        y4 = left[1]

        if x2 != x1:
            k1 = (y2 - y1) * 1.0 / (x2 - x1)  # 计算k1,由于点均为整数，需要进行浮点数转化
            b1 = y1 * 1.0 - x1 * k1 * 1.0  # 整型转浮点型是关键
            if (x4 - x3) == 0:  # L2直线斜率不存在操作
                k2 = None
                b2 = 0
            else:
                k2 = (y4 - y3) * 1.0 / (x4 - x3)  # 斜率存在操作
                b2 = y3 * 1.0 - x3 * k2 * 1.0
            if k2 == None:
                x = x3
            else:
                x = (b2 - b1) * 1.0 / (k1 - k2)
            y = k1 * x * 1.0 + b1 * 1.0

        else:
            x = x1
            if (x4 - x3) == 0:  # L2直线斜率不存在操作
                k2 = None
                b2 = 0
            else:
                k2 = (y4 - y3) * 1.0 / (x4 - x3)  # 斜率存在操作
                b2 = y3 * 1.0 - x3 * k2 * 1.0
            if k2 == None:
                y = min(y1, y2, y3, y4)
            else:
                y = k2 * x * 1.0 + b2 * 1.0

        return tuple([x, y])

    def draw_points(self):

        top_down_arrow_judge = False
        zebra_judge = False
        ### 箭头合并
        temp_arrow_vec = []
        if self.arrow_s_vec or self.arrow_l_vec or self.arrow_r_vec:
            if self.arrow_s_vec:
                temp_arrow_vec.extend(self.arrow_s_vec)
            if self.arrow_l_vec:
                temp_arrow_vec.extend(self.arrow_l_vec)
            if self.arrow_r_vec:
                temp_arrow_vec.extend(self.arrow_r_vec)

            temp_arrow_vec = [i for i in temp_arrow_vec if abs(i[0][1] - i[1][1]) > 50]
            # 加入逆向箭头过滤逻辑
            if self.arrow_t_vec:
                bound = max(self.arrow_t_vec[-1][0][0], self.arrow_t_vec[-1][0][0])
                temp_arrow_vec = [i for i in temp_arrow_vec if min(i[0][0], i[1][0]) > bound]

            temp_arrow_vec.sort(key=lambda vector: vector[0][0])

        if self.lane2 and (not (self.stop_line_vec)) and (
                not (self.arrow_s_vec)) and (
                not (self.arrow_l_vec)) and (
                not (self.arrow_r_vec)):
            #             print(self.lane_vec)
            top = self.lane_vec[0][0]  # 车道线顶部坐标
            down = self.lane_vec[0][1]  # 车道线底部坐标

            top_cross = self.cross_point(top, down, (0, 0), (10, 0))
            down_cross = self.cross_point(top, down, (0, self.h), (10, self.h))
            polygon_points = []
            if top_cross[0] < 0:
                # 计算双实线与图像左边交点
                #                 left_y = int((0 - top[0]) * (down[1] - top[1]) /
                #                              (down[0] - top[0]) + top[1])
                left_cross = self.cross_point(top, down, (0, 0), (0, 10))
                polygon_points.append(down_cross)
                polygon_points.append(left_cross)
                polygon_points.append((0, 0))
            if down_cross[0] < 0:
                # 计算双实线与图像左边交点
                left_cross = self.cross_point(top, down, (0, 0), (0, 10))
                polygon_points.append((0, self.h))
                polygon_points.append(left_cross)
                polygon_points.append(top_cross)
            else:
                polygon_points.append(down_cross)
                polygon_points.append(top_cross)

            polygon_points.append((self.w, 0))
            polygon_points.append((self.w, self.h))
            return polygon_points
        elif self.stop_line_vec:
            left = self.stop_line_vec[0][0]  # 最左停止线的左坐标
            right = self.stop_line_vec[0][1]  # 最左停止线的右坐标

        elif temp_arrow_vec:
            # 合并所有的 vec, 进行筛选, 通过箭头筛出来的值还得适当后退一点，因为停止线和箭头亦有差距
            idx = np.argsort(np.array(
                temp_arrow_vec[0])[:, 1])  # 取最左直行箭头顶点索引
            left = temp_arrow_vec[0][idx[0]]  # 以直行箭头顶点为左端点
            left = np.array([left[0], left[1]])
            right = np.array([self.w, left[1]])

            top_down_arrow_judge = True
            # 存一个所有识别箭头最高点后续备用
            max_arrow_height = min(min([i[0][1] for i in temp_arrow_vec]), min([i[1][1] for i in temp_arrow_vec]))
        elif self.zebra_line:
            zebra_judge = True
            left = self.zebra_line[0]
            right = np.array([self.w , left[1]])

        else:
            print("无上边界无法准确绘制逆行区域")
            return

        ### 拿到离stopline和arrow最近的车道信息

        if zebra_judge and self.lane_all_vec:  #### 如果直接是上一步用斑马线猜的。。。 那就是直接用车道线猜
            top = left
            gt = left[0]
            try:
                lane_vec_judge = [i for i in self.lane_all_vec[0] if i[0][0] >= gt or i[1][0] >= gt][0]
                x_diff = lane_vec_judge[1][0] - lane_vec_judge[0][0]
                y_diff = lane_vec_judge[1][1] - lane_vec_judge[0][1]
            except:
                x_diff = self.lane_all_vec[0][0][1][0] - self.lane_all_vec[0][0][0][0]
                y_diff = self.lane_all_vec[0][0][1][1] - self.lane_all_vec[0][0][0][1]
            down = np.array([top[0] + x_diff, top[1] + y_diff])

        elif zebra_judge:  #### 如果直接是上一步用斑马线猜的。。。 那就是直接过
            top = left
            down = np.array([left[0], self.h])

        ### 有lane2且有箭头的情况
        elif self.lane2 and temp_arrow_vec:

            # top = self.lane_vec[0][0]  # 车道线顶部坐标
            # down = self.lane_vec[0][1]  # 车道线底部坐标
            ### 看看效果再考虑后期
            top_arr_l = temp_arrow_vec[0][0]  # 左转箭头最左点坐标
            top_lane = self.lane_vec[0][0]  # 车道线顶部坐标
            down_lane = self.lane_vec[0][1]  # 车道线底部坐标
            # 比较并取最左的点
            top = top_arr_l if top_arr_l[0] < top_lane[0] else top_lane
            #             print(top)
            if (top == top_arr_l).all():
                # 构造与最左车道线平行的向量并获取其底部坐标
                down = np.array([
                    top[0] + (down_lane[0] - top_lane[0]),
                    top[1] + (down_lane[1] - top_lane[1])
                ])
            else:
                down = down_lane
        ### 只有箭头的情况
        elif temp_arrow_vec and (not (self.lane_all_vec)):
            top = self.temp_arrow_vec[0][0]  # 左转箭头最左点坐标
            down = self.temp_arrow_vec[0][1]  # 左转箭头底部坐标

        ### 有lane1 且有箭头的情况  取出在导向箭头左边且 离最左侧箭头最近的一个
        elif temp_arrow_vec and (not self.lane2) and self.lane_all_vec:

            ### 避免因为摄像头方向造成的误差
            addition_lane_vec_judge = False
            gt = min(left[0] + 50, temp_arrow_vec[0][0][0] + 50, temp_arrow_vec[0][1][0] + 50)
            try:
                lane_vec_judge = [i for i in self.lane_all_vec[0] if i[0][0] <= gt or i[1][0] <= gt][-1]
                addition_lane_vec_judge = True
            except:  #### 万一真的是没有在左边的，直接用箭头画平行线赋值
                if left[0] < min(temp_arrow_vec[0][0][0], temp_arrow_vec[0][1][0]):
                    x_diff = self.lane_all_vec[0][0][1][0] - self.lane_all_vec[0][0][0][0]
                    y_diff = self.lane_all_vec[0][0][1][1] - self.lane_all_vec[0][0][0][1]
                    lane_vec_judge = [left, np.array([left[0] + x_diff, left[1] + y_diff])]
                else:
                    lane_vec_judge = temp_arrow_vec[0]

            ### 合法性判断
            if addition_lane_vec_judge:
                foot_point = Tools().get_foot_point(temp_arrow_vec[0][0], lane_vec_judge)
                if temp_arrow_vec[0][0][0] <= foot_point[0]:
                    x_diff = lane_vec_judge[1][0] - lane_vec_judge[0][0]
                    y_diff = lane_vec_judge[1][1] - lane_vec_judge[0][1]
                    lane_vec_judge = [temp_arrow_vec[0][0],
                                      np.array([temp_arrow_vec[0][0][0] + x_diff, temp_arrow_vec[0][0][1] + y_diff])]

            top = lane_vec_judge[0]
            down = lane_vec_judge[1]


        elif (not temp_arrow_vec) and (not self.lane2) and self.lane_all_vec and self.stop_line_vec:
            if self.arrow_t_vec:
                bound = max(self.arrow_t_vec[-1][0][0], self.arrow_t_vec[-1][0][0])
                gt = max(left[0] + 50, bound)
            else:
                gt = left[0] + 50
            try:

                lane_vec_judge = [i for i in self.lane_all_vec[0] if i[0][0] <= gt or i[1][0] <= gt][-1]
            except:

                x_diff = self.lane_all_vec[0][0][1][0] - self.lane_all_vec[0][0][0][0]
                y_diff = self.lane_all_vec[0][0][1][1] - self.lane_all_vec[0][0][0][1]
                lane_vec_judge = [left, np.array([left[0] + x_diff, left[1] + y_diff])]
            top = lane_vec_judge[0]
            down = lane_vec_judge[1]

            # top_arr_l = temp_arrow_vec[0][0]  # 左转箭头最左点坐标
            # top_lane = lane_vec_judge[0]  # 车道线顶部坐标
            # down_lane = lane_vec_judge[1]  # 车道线底部坐标
            # # 比较并取最左的点
            # top = top_arr_l if top_arr_l[0] < top_lane[0] else top_lane
            # #             print(top)
            # if (top == top_arr_l).all():
            #     # 构造与最左车道线平行的向量并获取其底部坐标
            #     down = np.array([
            #         top[0] + (down_lane[0] - top_lane[0]),
            #         top[1] + (down_lane[1] - top_lane[1])
            #     ])
            # else:
            #     down = down_lane
        else:
            print("无左边界无法准确绘制逆行区域")
            return

        # 如果是用arrow得出的最大高度，则有可能需要修正最高点坐标(如果实在差距过大则不修正,考虑有安全岛的情况）
        if top_down_arrow_judge \
                and max_arrow_height - min(top[1], down[1]) < 150 \
                and max_arrow_height > min(top[1], down[1]):
            left[1] = min(top[1], down[1])
            right[1] = left[1]
        elif top_down_arrow_judge:
            left[1] = max_arrow_height - 75
            right[1] = left[1]
        # 如果两点差异不大则认为是平行的
        if abs(right[1] - left[1]) <= 10:
            right[1] = left[1]

        # 同时避免出现 right left 点重合情况
        if right[0] == left[0] and right[1] == left[1]:
            right[0] = right[0] + 1

        # 计算上边界与左边界交点
        cross_point = self.cross_point(top, down, right, left)

        #### 不知道为什么当时云飞选择用这种方式计算这个问题。。。 造成可能存在一些小bug
        #### 此前已经统一过高度，此时目的是 避免 left right重合
        right_cross = self.cross_point(right, left, (self.w, 0), (self.w, 10))

        top_cross = self.cross_point(top, down, (0, 0), (10, 0))
        down_cross = self.cross_point(top, down, (0, self.h), (10, self.h))
        # 绘制逆行区域所需所有坐标
        polygon_points = []
        polygon_points.append(cross_point)  # 上边界与左边界交点

        polygon_points.append(right_cross)  # 上边界与右边界交点
        polygon_points.append(tuple([self.w, self.h]))
        if down_cross[0] < 0:
            polygon_points.append(tuple([0, self.h]))  # 左下角坐标
            cross_point1 = self.cross_point(top, down, (0, self.h), (0, 0))
            polygon_points.append(cross_point1)  # 最左车道线与左边界交点
        else:
            polygon_points.append(down_cross)  # 左边界与底边交点
        return polygon_points

        # if self.stop_line_vec:
        #     for i in range(len(self.stop_line_vec)):
        #         if i == 0:
        #             polygon_points.append(tuple(self.stop_line_vec[i][1]))
        #         else:
        #             polygon_points.append(tuple(self.stop_line_vec[i][0]))
        #             polygon_points.append(tuple(
        #                 self.stop_line_vec[i][1]))  # stop_line点
        # if not (self.stop_line_vec) and temp_arrow_vec:
        #     for i in range(len(self.arrow_s_vec)):
        #         order = np.argsort(np.array(
        #             temp_arrow_vec[i])[:, 1])  # 取直行箭头顶点索引
        #         arrow_top = temp_arrow_vec[0][order[0]]
        #         polygon_points.append(tuple(arrow_top))
        # polygon_points.append(right_cross)  # 上边界与右边界交点
        # polygon_points.append(tuple([self.w, self.h]))  # 右下角坐标
        # if down_cross[0] < 0:
        #     polygon_points.append(tuple([0, self.h]))  # 左下角坐标
        #     cross_point1 = self.cross_point(top, down, (0, self.h), (0, 0))
        #     # print(cross_point1)
        #     polygon_points.append(cross_point1)  # 最左车道线与左边界交点
        # else:
        #     polygon_points.append(down_cross)  # 左边界与底边交点
        # print('valid_area', polygon_points)
        # return polygon_points

    def draw_back_points(self):
        if self.arrow_b_vec:
            top = self.arrow_b_vec[0][0]
            down = self.arrow_b_vec[0][1]
            # 计算与底边交点
            down_cross = self.cross_point(top, down, (0, self.h), (10, self.h))
            back_polygon_points = []
            if down_cross[0] < 0:
                left_cross = self.cross_point(top, down, (0, 0), (0, 10))
                back_polygon_points.append(left_cross)
            else:
                back_polygon_points.append(down_cross)
                back_polygon_points.append((0, self.h))
            back_polygon_points.append((0, top[1]))
            back_polygon_points.append(tuple(top))
            return back_polygon_points
        else:
            # print("无反向箭头，无法判断绘制反向逆行区域")
            return


if __name__ == '__main__':
    import matplotlib.image as mpimg

    with open('/media/yyf/学习文件/detect_res/冀DK8838/冀DK8838_1_infos.json', 'r') as f:
        data1 = json.load(f)

    with open('/media/yyf/学习文件/detect_res/冀DK8838/冀DK8838_2_infos.json', 'r') as f:
        data2 = json.load(f)

    with open('/media/yyf/学习文件/detect_res/冀DK8838/冀DK8838_3_infos.json', 'r') as f:
        data3 = json.load(f)

    # 从读取的数据取出需要的道路信息
    data = dict()
    data['shapes'] = data1['shapes'] + data2['shapes'] + data3['shapes']
    data['imageHeight'] = data1['imageHeight']
    data['imageWidth'] = data1['imageWidth']
    data['dev_desc'] = data1['dev_desc']

    # 从读取的数据取出目标车辆信息
    points = [x['points'] for x in data1['shapes'] if x["label"] == "car"]
    car1 = points[0]
    points = [x['points'] for x in data2['shapes'] if x["label"] == "car"]
    car2 = points[0]
    points = [x['points'] for x in data3['shapes'] if x["label"] == "car"]
    car3 = points[0]

    road = Road(data)  # 初始化道路信息（其实同一个镜头的初始化是可以复用的）
    print(road.retrograde([car1, car2, car3]))  # 输出当前道路信息下的车辆逆行判定

    # 下面这段没什么用就是给你看看这个路口以及车辆长什么样子
    # 程序运行大概需要600ms,但是画图这里要2.2s,要有耐心哦
    I = mpimg.imread('/media/yyf/学习文件/detect_res/冀DK8838/冀DK8838_1.jpg')
    plt.subplot(2, 2, 1)
    plt.imshow(I)
    plt.axis('off')
    I = mpimg.imread('/media/yyf/学习文件/detect_res/冀DK8838/冀DK8838_2.jpg')
    plt.subplot(2, 2, 2)
    plt.imshow(I)
    plt.axis('off')
    I = mpimg.imread('/media/yyf/学习文件/detect_res/冀DK8838/冀DK8838_3.jpg')
    plt.subplot(2, 2, 3)
    plt.imshow(I)
    plt.axis('off')
    plt.subplot(2, 2, 4)
    plt.imshow(road.area)  # 开到黄色区域里而且不断向下的车辆就是逆行
    plt.axis('off')
    plt.show()
