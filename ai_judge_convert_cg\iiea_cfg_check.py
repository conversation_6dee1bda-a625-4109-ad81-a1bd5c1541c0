from pydantic import BaseModel, model_validator
from typing import Dict, Any
import uuid
from conf.config import OptConf  # 假设 OptConf 是从某个模块导入的

class IIEAMsg(BaseModel):
    iieaUUID: str
    iieaMergeType: int
    iieaMergeRect: str
    iieaAuditMode: str
    iieaFilterVehicle: str


    @model_validator(mode='before')
    def set_defaults(cls, values: Dict[str, Any]) -> Dict[str, Any]:
        default_values = {
            'iieaUUID': str(uuid.uuid4()),
            'iieaMergeType': 411,
            'iieaMergeRect': "0.1,0.2,0.3,0.4",
            'iieaAuditMode': "0",
            'iieaFilterVehicle': "#".join(OptConf.To_IIEAVeh_Cfg),
        }

        field_types = {
            'iieaUUID': str,
            'iieaMergeType': int,
            'iieaMergeRect': str,
            'iieaAuditMode': str,
            'iieaFilterVehicle': str,
        }

        for field, expected_type in field_types.items():
            if field not in values or values[field] is None:
                values[field] = default_values[field]
            elif not isinstance(values[field], expected_type):
                print(f"Field {field} has incorrect type, using default value.")
                values[field] = default_values[field]

        return values

# 示例数据
data = {
    'iieaUUID': 123,  # 错误类型
    'iieaMergeType': "not an integer",  # 错误类型
    'iieaMergeRect': None,  # 缺失或为 None
    'iieaAuditMode': "custom_mode",  # 正确类型
    'iieaFilterVehicle': None,  # 缺失或为 None
}

try:
    msg = IIEAMsg.model_validate(data)
    print(msg)
except Exception as e:
    print(e)