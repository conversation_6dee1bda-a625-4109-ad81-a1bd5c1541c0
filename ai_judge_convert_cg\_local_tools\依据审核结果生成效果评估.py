pred_vio = 38       #模型预测正片数
pred_waste =120       # 模型预测废片数
total_num = pred_vio+ pred_waste   # 总数
vio_error = 28#9        #判定正片错误数
waste_error =0#6        # 判定废片错误数

truth_vio = pred_vio - vio_error + waste_error  # 真实正片数
truth_waste = pred_waste - waste_error + vio_error  # 真实废片数
waste_rate = truth_waste / total_num

vio_recall = (pred_vio - vio_error) / truth_vio
vio_precision = (pred_vio - vio_error) / pred_vio

waste_recall = (pred_waste - waste_error) / truth_waste
waste_precision = (pred_waste - waste_error) / pred_waste
model_precision = (total_num - vio_error - waste_error) / total_num
# print(f"正片检出率:{vio_recall * 100:.2f}%\t正片检准率:{vio_precision * 100:.2f}%")
# print(f"废片检出率:{waste_recall * 100:.2f}%\t正片检准率:{waste_precision * 100:.2f}%\n")
model_filter_rate = pred_waste/total_num
filter_right_rate = 1-waste_error/pred_waste
print(
    f"共计{total_num}组图片，模型判定正片{pred_vio}组（{vio_error}组误判），判定废片{pred_waste}组（{waste_error}组误判），人工审核正片{truth_vio}组，人工审核废片{truth_waste}组，废片率为{waste_rate * 100:.2f}%\n\
正片检出率：{vio_recall * 100:.2f}%，正片检准率：{vio_precision * 100:.2f}%，\
废片检出率：{waste_recall * 100:.2f}%，废片检准率：{waste_precision * 100:.2f}%\n\
实际废片率：{truth_waste /total_num * 100:.2f}%，模型准确率：{model_precision*100:.2f}%，废片过滤比例：{model_filter_rate * 100:.2f}% ")
