import argparse
import torch
import os
import sys

sys.path.append('..')
import cv2
from yolov5.yolov5_detect import Yolov5PersonInfo
import yolov7.detect  # 不导入导包路径会出错
from tqdm import tqdm


def show_plate(img_list, CAR_LIST, PLATE_LIST=None, plate_num=""):
    # 画目标车和车牌的位置
    for idx, i in enumerate(CAR_LIST):
        if CAR_LIST[idx]:
            h, w, _ = img_list[idx].shape
            pt1 = (CAR_LIST[idx][0], CAR_LIST[idx][1])
            pt2 = (CAR_LIST[idx][2], CAR_LIST[idx][3])
            plate_ps = ((pt1[0] + pt2[0]) // 2, (pt1[1] + pt2[1]) // 2)
            cv2.rectangle(img_list[idx], pt1, pt2, color=(0, 255, 0), thickness=2)
            if PLATE_LIST:
                pt1 = (CAR_LIST[idx][0] + PLATE_LIST[idx][0],
                       CAR_LIST[idx][1] + PLATE_LIST[idx][1])
                pt2 = (pt1[0] + PLATE_LIST[idx][2],
                       pt1[1] + PLATE_LIST[idx][3])
                cv2.rectangle(img_list[idx], pt1, pt2, color=(0, 0, 255), thickness=2)
                cv2.putText(img_list[idx], f"{plate_num[1:]}",
                            plate_ps,
                            cv2.FONT_HERSHEY_COMPLEX, 1.5,
                            (0, 0, 255), 2)


if __name__ == '__main__':
    parser = argparse.ArgumentParser()
    parser.add_argument('--image_path', type=str, default='plate_test', help='source')  # file/folder, 0 for webcam
    opt = parser.parse_args()
    print(opt)
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    from ai_judge_code_gy.car_detect import LPR

    carinfo = Yolov5PersonInfo()
    img_list = []

    import shutil
    save_path = "../plate_test/car_pic"
    if os.path.exists(save_path):
        shutil.rmtree(save_path)
    os.makedirs(save_path, exist_ok=True)

    if not os.path.isfile(opt.image_path):
        file_name = os.listdir(opt.image_path)

        for i in tqdm(file_name):
            path = f"{opt.image_path}/{i}"
            img = cv2.imread(path)
            h, w, _ = img.shape
            black_h = (h - 4530) + 210
            if black_h < 140:
                black_h = 140
            cx = w // 2
            cy = (h - black_h) // 2
            img1 = img[:cy, :cx]
            img2 = img[:cy, cx:]
            img3 = img[cy:2 * cy, :cx]
            img4 = img[cy:2 * cy, cx:]
            file_list = [img1, img2, img3]
            img_list.extend(file_list)
        print("图片切分完成！")
        print("开始车辆检测...")
        for idx1, img in enumerate(tqdm(img_list)):
            crop_img_list, car_bbox_ps, _ = carinfo.get_goal_class_box(img, ['car', 'bus', 'truck'])

            for idx2, car_ps in enumerate(car_bbox_ps):
                car_np = img[car_ps[1]:car_ps[3], car_ps[0]:car_ps[2]]
                cv2.imwrite(f'{save_path}/car_{idx1}_{idx2}.jpg', car_np)

    print("车牌测试图片准备完成！")