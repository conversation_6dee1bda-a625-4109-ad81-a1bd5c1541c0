import os
import shutil
import sys
import time

import cv2
from tqdm import tqdm

sys.path.append('..')
os.environ["CUDA_VISIBLE_DEVICES"] = "4"
from ai_judge_code_gy.init_models import *


def crop_driver_area(img, veh_box):
    w = veh_box[2] - veh_box[0]
    h = veh_box[3] - veh_box[1]
    x1 = int(veh_box[0] + 0.45 * w)
    y1 = int(veh_box[1] + 0.2 * h)
    x2 = int(veh_box[2] - 0.15 * w)
    y2 = int(veh_box[3] - 0.35 * h)
    return img[y1:y2, x1:x2], x1, y1


weights_list = init_models(traffic=False, phone=True)
plate_det_model = weights_list[0][0]
plate_rec_model = weights_list[0][1]
yolov7_model = weights_list[3][0]
yolov7_meta = weights_list[3][1]
phone_model = weights_list[4][0]
phone_meta = weights_list[4][1]
# 读取图片
root = "../dev_pics/phone"
save_path = "../dev_pics/phone_train"
if os.path.exists(save_path):
    shutil.rmtree(save_path)
os.makedirs(save_path, exist_ok=True)
vis_path = "../dev_pics/phone_vis"
if os.path.exists(vis_path):
    shutil.rmtree(vis_path)
os.makedirs(vis_path, exist_ok=True)
num = 1
for name in tqdm(os.listdir(root)):
    plate = name.split("_")[0]
    path = f"{root}/{name}"
    img = cv2.imread(path)
    # 检测车辆
    t1 = time.time()
    _, veh_boxes, labels_list = yolov7_detect(img, yolov7_model, yolov7_meta[0],
                                              yolov7_meta[1],
                                              yolov7_meta[2],conf_thres=0.1)
    print("yolo use time:", round(time.time() - t1, 3), name)
    print(name,veh_boxes)


    # 车牌匹配
    target_veh_box, plate_num, _, _ = CarJudge().confirm_img1_car_ps(img, plate,
                                                                     plate_det_model,
                                                                     plate_rec_model,
                                                                     veh_boxes,
                                                                     yolov7_meta[2], label=0,
                                                                     show=True,
                                                                     save_path=vis_path)
    if target_veh_box:
        veh_x1, veh_y1, veh_x2, veh_y2 = target_veh_box
        crop_img, x1, y1 = crop_driver_area(img, target_veh_box)
        cv2.imwrite(f"{save_path}/gaomi_test_{num}.jpg", crop_img)
        # 检测手机、手
        ph_boxes, ph_labels, ph_scores = phone_detect(crop_img, phone_model, phone_meta[0],
                                                      phone_meta[1],
                                                      phone_meta[2])
        # 可视化
        cv2.rectangle(img, (veh_x1, veh_y1), (veh_x2, veh_y2), (0, 255, 0), 2)
        for idx, box in enumerate(ph_boxes):
            cls = ph_labels[idx]
            score = ph_scores[idx]
            if cls == "hand":
                color = (255, 255, 0)
            elif cls == "phone":
                color = (0, 255, 0)
            else:
                color = (0, 0, 255)
            cv2.rectangle(img, ((x1 + box[0]), (y1 + box[1])), ((x1 + box[2]), (y1 + box[3])), color, 2)
            # cv2.putText(img, cls[:1], ((x1 + box[2]), (y1 + box[1])), cv2.FONT_HERSHEY_SIMPLEX, 1, color, 2)
    cv2.imwrite(f"{vis_path}/phone_hand_{num}.jpg", img)
    num += 1
