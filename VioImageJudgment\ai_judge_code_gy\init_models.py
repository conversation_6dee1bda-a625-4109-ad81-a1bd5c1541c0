import sys

sys.path.append('..')
sys.path.append('../yolov7')
from main_config.config import *
import warnings

warnings.filterwarnings('ignore')
import os

if DEVICE in ["cpu", "CPU"]:
    os.environ["CUDA_VISIBLE_DEVICES"] = "-1"
import shutil
import numpy as np
import cv2
import time
import math
from tqdm import tqdm
import torch
import traceback
from shapely import geometry
from PIL import Image
import torch.nn.functional as F
import torchvision.transforms as transforms
from src.EfficientNet import efficientnet
from ai_judge_code_gy.blendmask_detect import BlendMaskModel, BlendMaskDetect, mask_gen
from yolov7.yolo_interface import load_lp, detect_lp, yolov7_detect, phone_detect, load_plate_model, \
    detect_Recognition_plate, \
    draw_result2
from yolov7.plate_recognition.plate_rec import get_plate_result
from ai_judge_code_gy.car_detect import CarJudge, get_car_type
from ai_judge_code_gy.retrograde_pro import Road
from ai_judge_code_gy.judge_tools import Tools, VIO_JUDGE_TOOL, convert_src_coord, crop_driver_area, IOU,show_chinese
from ai_judge_code_gy.car_direction import CarDriveDirectionJudge
from ai_judge_code_gy.light_detect import judge_light_status
from ai_judge_code_gy.vio_classify_13450 import Vio_pred, Net
from ai_judge_code_gy.visualization import *
from ai_judge_code_gy.sign_request import send_request
import requests


def catch_error():
    exc_type, exc_value, exc_traceback = sys.exc_info()
    tb_info = traceback.extract_tb(exc_traceback)
    filename, line, func, text = tb_info[-1]
    error_str = f"error file:{filename}\nerror func:{func} line {line}\nerror text:{text} —— {exc_value}\n"
    return error_str


# 初始化所有模型
def init_models(traffic=True, lp=True, yolov7=True, trace=False, phone=False, vio_model=False, cnn=False,
                init_device=None):
    # 模型授权
    sign_bool = True
    while sign_bool:
        try:
            res = send_request()
            if os.path.exists("logs/sign_msg.txt") and os.path.getsize("logs/sign_msg.txt") / 1024 / 1024 > 10:
                mode = "w"
            else:
                mode = "a"

            f = open("logs/sign_msg.txt", mode, encoding="utf-8")
            f.write(str(res) + "\n")
            if isinstance(res, dict) and "data" in res.keys() and res["data"]:
                f.write(time.strftime("%Y-%m-%d %H:%M:%S", time.localtime()) + " 模型授权成功！")
                f.flush()
                f.close()
                break
            else:
                if isinstance(Sign_Mode, str) and Sign_Mode.lower() == "iiea":
                    f.write(time.strftime("%Y-%m-%d %H:%M:%S",
                                          time.localtime()) + " iiea授权失败，请检查配置文件中TPC_IP是否正确！" + str(
                        res))
                else:
                    f.write(time.strftime("%Y-%m-%d %H:%M:%S",
                                          time.localtime()) + " MR授权失败，请检查配置文件中IP、AppKey、AppSecret是否正确！" + str(
                        res))
                f.flush()
                f.close()
        except Exception as e:
            error_msg = catch_error()
            print("Error！", error_msg)
        time.sleep(30)

    if init_device is None:
        init_device = "cuda:0" if torch.cuda.is_available() else "cpu"
    yolov7_module = [None, (None, None, init_device)]
    lp_module = [None, (None, None, init_device)]
    phone_module = [None, (None, None, init_device)]
    demo = vio_judge_model = model_cnn = None
    # 车牌检测识别模型
    plate_det_model, plate_rec_model = load_plate_model("../models/plate_detect.pt",
                                                        "../models/plate_rec.pth", device=init_device)
    plate_module = [plate_det_model, plate_rec_model]
    if traffic:
        # 交通标志识别模型
        demo = BlendMaskModel().load_specific_model("../models/model_0041299.pth", device=init_device)
        # 仅仅是作为测试看看效果   --- 对黑夜场景的识别能力不好
        # demo = BlendMaskModel().load_specific_model("../models/blend_old.pth", device=init_device)
    if yolov7:
        # 车辆行人检测模型
        yolov7_model, imgsz, stride, device = load_lp("../models/yolov7-e6e.pt", trace=trace, device=init_device)
        yolov7_meta = (imgsz, stride, device)
        yolov7_module = [yolov7_model, yolov7_meta]
    if lp:
        # 红绿灯识别模型
        lp_net, imgsz, stride, device = load_lp("../models/best.pt", trace=trace, device=init_device, half=False)
        lp_meta = (imgsz, stride, device)
        lp_module = [lp_net, lp_meta]
    if phone:
        # 手机识别模型
        phone_net, imgsz, stride, device = load_lp("../models/phone_best.pt", imgsz=640, trace=trace,
                                                   device=init_device,
                                                   half=False)
        phone_meta = (imgsz, stride, device)
        phone_module = [phone_net, phone_meta]

    if vio_model:
        # 打电话和不系安全带违法判定模型
        vio_judge_model = VioJudgeModel("../models/EfficientNet_B7_n_hc.pth", device=init_device).load_model()
    if cnn:
        # 压线判定模型
        model_cnn = torch.load('../models/cnn_class_model.pth', map_location=init_device)
        model_cnn.eval()
    return [plate_module, demo, lp_module, yolov7_module, phone_module, model_cnn, vio_judge_model]


# 判别12230,12440类别违法信息
class VioJudgeModel(object):
    def __init__(self, model_path, device=None):
        if device is None:
            self.device = torch.device("cuda:0" if torch.cuda.is_available() else "cpu")
        else:
            self.device = torch.device(device)
        self.model_path = model_path
        self.model = efficientnet(net="B7")

    def load_model(self):
        model_dict = self.model.state_dict()
        state_dict = torch.load(self.model_path, map_location=self.device)
        filtered_state_dict = {k: v for k, v in state_dict.items() if k in model_dict}
        self.model.load_state_dict(filtered_state_dict, strict=False)
        self.model.to(self.device)
        self.model.eval()
        return self.model


def img_split(img, n, black_height=None, black_pos=None):
    h, w, _ = img.shape
    if black_pos is None:
        black_h = 0
    elif black_height is None:
        black_h = int(BLACK_HEIGHT * h) if BLACK_HEIGHT else 0
    else:
        black_h = int(black_height * h)
    img_list = []
    # 单图无拼接
    if n == 111:
        img_list = [img]
    # 2图横向拼接
    elif n == 211:
        cx = w // 2
        img1 = img[:, :cx]
        img2 = img[:, cx:]
        img_list = [img1, img2]
    # 2图竖向拼接
    elif n == 221:
        split_h = h // 2
        img1 = img[:split_h]
        img2 = img[split_h:]
        img_list = [img1, img2]
    # 3图横向拼接
    elif n == 311:
        split_w = w // 3
        img1 = img[:, :split_w]
        img2 = img[:, split_w:2 * split_w]
        img3 = img[:, 2 * split_w:]
        img_list = [img1, img2, img3]
    # 倒品字型
    elif n == 312:
        cx = w // 2
        cy = h // 2
        img1 = img[:cy, :cx]
        img2 = img[:cy, cx:]
        img3 = img[cy:]
        img_list = [img1, img2, img3]
    # 品字型
    elif n == 313:
        cx = w // 2
        cy = h // 2
        img1 = img[:cy]
        img2 = img[cy:, :cx]
        img3 = img[cy:, cx:]
        img_list = [img1, img2, img3]
    # 3图竖向拼接
    elif n == 321:
        split_h = h // 3
        img1 = img[:split_h]
        img2 = img[split_h:2 * split_h]
        img3 = img[2 * split_h:]
        img_list = [img1, img2, img3]
    # 品字型顺时针旋转90度
    elif n == 322:
        cx = w // 2
        cy = h // 2
        img1 = img[:cy, :cx]
        img2 = img[cy:, :cx]
        img3 = img[:, cx:]
        img_list = [img1, img2, img3]
    # 品字型逆时针旋转90度
    elif n == 323:
        cx = w // 2
        cy = h // 2
        img1 = img[:, :cx]
        img2 = img[:cy, cx:]
        img3 = img[cy:, cx:]
        img_list = [img1, img2, img3]
    # 4图横向拼接
    if n == 411:
        cx = w // 2
        cy = (h - black_h) // 2
        if black_pos == "up":
            img1 = img[black_h:cy + black_h, :cx]
            img2 = img[black_h:cy + black_h, cx:]
            img3 = img[cy + black_h:, :cx]
            img4 = img[cy + black_h:, cx:]
        else:
            img1 = img[:cy, :cx]
            img2 = img[:cy, cx:]
            img3 = img[cy:2 * cy, :cx]
            img4 = img[cy:2 * cy, cx:]
        img_list = [img1, img2, img3, img4]
    # 6图横向拼接
    elif n == 611:
        cx = w // 3
        cy = (h - black_h) // 2
        if black_pos == "up":
            img1 = img[black_h:cy + black_h, :cx]
            img2 = img[black_h:cy + black_h, cx:2 * cx]
            img3 = img[cy + black_h:, :cx]
            img4 = img[cy + black_h:, cx:2 * cx]
        else:
            img1 = img[:cy, :cx]
            img2 = img[:cy, cx:2 * cx]
            img3 = img[cy:2 * cy, :cx]
            img4 = img[cy:2 * cy, cx:2 * cx]
        img_list = [img1, img2, img3, img4]
    # 6图竖向拼接
    elif n == 621:
        cx = w // 2
        cy = (h - black_h) // 3
        if black_pos == "up":
            img1 = img[black_h:cy + black_h, :cx]
            img2 = img[black_h:cy + black_h, cx:]
            img3 = img[cy + black_h:cy * 2 + black_h, :cx]
            img4 = img[cy + black_h:cy * 2 + black_h, cx:]
            img5 = img[cy * 2 + black_h:, :cx]
            img6 = img[cy * 2 + black_h:, cx:]
        else:
            img1 = img[:cy, :cx]
            img2 = img[:cy, cx:]
            img3 = img[cy:2 * cy, :cx]
            img4 = img[cy:2 * cy, cx:]
            img5 = img[2 * cy:3 * cy, :cx]
            img6 = img[2 * cy:3 * cy, cx:]
        img_list = [img1, img2, img3, img4, img5, img6]

    return img_list


def filter_special_vehicle(model, img_np, target_ps, vio_judge, save, judge_infos):
    # 过滤特殊车辆
    if target_ps and Special_Type:
        det = BlendMaskDetect(img_np)
        predictions, kps = det.mask_predict(model, show=False)
        # iiea车辆白名单
        try:
            _, ambulance_filter, fire_filter = judge_infos["iieaFilterVehicle"]
        except:
            ambulance_filter = None
            fire_filter = None
        special_type_list = []
        if ambulance_filter:
            special_type_list.extend(['ambulance_h', 'ambulance_b'])
        if fire_filter:
            special_type_list.extend(['fire_engine_h', 'fire_engine_b'])
        if ambulance_filter is None and fire_filter is None:
            special_type_list = Special_Type
        # 筛选进行违法判定的车辆类型
        car_bbox_ps2, labels_list2, score_list2 = det.class_info2(predictions, special_type_list,
                                                                  Special_Car_Vis_Thresh)
        for target_idx, special_car in enumerate(car_bbox_ps2):
            target_label = labels_list2[target_idx]
            tmp1 = np.asarray(target_ps)
            tmp2 = np.asarray(special_car)
            tl1, br1, tl2, br2 = tmp1[:2], tmp1[2:], tmp2[:2], tmp2[2:]
            IOU = Tools().IOU(tl1, br1, tl2, br2, is_min_union=False)
            if IOU >= Head_IOU:
                if target_label == "ambulance_h":
                    if score_list2[target_idx] >= Ambulance_Head_Thresh:
                        vio_judge = "no_vio_010"
                elif score_list2[target_idx] >= Special_Car_Thresh:
                    vio_judge = "no_vio_010"
                judge_infos['judgeConf'] = score_list2[target_idx]
            try:
                if save:
                    vis_area = (special_car[0], (2 * special_car[1] + special_car[3]) // 3)
                    cv2.putText(img_np, target_label + f"{score_list2[target_idx]:.3f}", vis_area,
                                cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 0, 255), 2)
            except:
                pass
    return vio_judge


def filter_low_speed_veh(target_car_ps, car_box_list, car_label_list,car_score_list, src_plate, pred_plate, plate_conf, seg_model=None,
                         img=None, feat_judge=True):
    def logic_judge(target_car_box, car_boxes, labels_list,score_list):
        if feat_judge:
            idx = car_boxes.index(target_car_box)
            print("车辆类型：", labels_list[idx])
            if labels_list[idx] in Filter_Vehicle_Type and score_list[idx] > Tricycle_Thresh and (
                    plate_conf < 0.98 or (plate_conf >= 0.98 and pred_plate[0] not in chars)):
                return "no_vio_100"
        print(f"src_plate:{src_plate} pred_plate:{pred_plate} plate_conf:{plate_conf}")
        # 前端相机识别车牌有误报废片
        if Plate_Num_Judge and pred_plate[0] in chars and pred_plate[1] not in chars and len(pred_plate) - len(src_plate) >= 1 and 7 <= len(pred_plate) <= 8:
            judge_res = "no_vio_101"
        elif pred_plate[0] not in chars and plate_conf > First_Plate_Thresh and len(pred_plate) >= 7:  # 0.8
            judge_res = "no_vio_102"
        elif pred_plate[0] in chars and pred_plate[0] != src_plate[
            0] and plate_conf > First_Plate_Thresh2:
            judge_res = "no_vio_103"
        else:
            judge_res = "vio_100"
        return judge_res
    try:
        if seg_model is not None and target_car_ps:
            print("add seg model detect!")
            det = BlendMaskDetect(img)
            predictions, kps = det.mask_predict(seg_model, show=False)
            # NMS
            nms_ls = det.nms_class_distinct(predictions["instances"].pred_boxes.tensor,
                                            predictions["instances"].scores,
                                            predictions["instances"].pred_classes,
                                            threshold=0.8,
                                            class_ditinct_threshold=0.85)
            predictions, kps_mask = det.prediction_nms_filter(predictions, nms_ls, kps)
            car_bbox_ps, labels_list, score_list = det.class_info2(predictions, Blend_Judge_Vehicle, 0.15)
            # IOU计算找同一辆车
            car_ps = Tools.filter_boxes_by_iou(target_car_ps, car_bbox_ps)
            if car_ps:
                judge_res = logic_judge(car_ps, car_bbox_ps, labels_list,score_list)

            else:
                judge_res = "vio_100"
        elif target_car_ps:
            judge_res = logic_judge(target_car_ps, car_box_list, car_label_list,car_score_list)
        else:
            judge_res = "vio_100"
        return judge_res
    except Exception as e:
        msg = catch_error()
        print(f"过滤老头乐模块异常!{e} \n{msg}")
        return "vio_100"
