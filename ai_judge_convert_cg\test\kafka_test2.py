import os
import time
import uuid

from kafka import KafkaProducer
import sys

sys.path.append("..")
import json
import datetime as dt
import urllib.parse
from conf.kafka_config import KafkaConf
from typing import Dict, List, Union
from src.utils.time_util import TimeUtil


def get_vio_data(img_name: str, module_name: str, vio_code: Union[str, int], plate: str = None, split_mode: int = None,
                 merge_rect: str = None) -> Dict:
    if plate is None:
        plate = img_name.split("_")[0]
    pic_root = f"/data/ai_judge_pic/modules_test/{module_name}/{img_name}"
    url = "http://10.184.48.200:8080" + pic_root
    vio_data = {
        "iieaUUID": str(uuid.uuid4()),
        "sendTime": TimeUtil.now_hikstr(),
        "vehicleAlarmResult": [{
            "target": [{
                "vehicle": {
                    "plateColor": {
                        "value": "blue"
                    },
                    "plateNo": {
                        "confidence": 0.0,
                        "value": plate
                    },
                }
            }],
            "targetAttrs": {
                "alarmType": str(vio_code),
                "cameraIndexCode": "805c3549bea2489a89be33cc0c87df56",
                "crossingName":"测试路口",
                "crossingIndexCode": "a3b82285b4b348e5acf03034b4d5c7f4",
                "directionIndex": "northSouth",#"eastWest",
                "platePicUrl": "http://35.26.210.25:6120/pic?5E0FB0D80AA0AB01C7B7*hcs87e8cc71bdd24f34b2023/cljj/27600;1725332044790429225254?pic*1052775206*7348*2058*5E0FB0D80AA0AB01C7B7-2*1725332154",
            },
            "targetPicUrl": urllib.parse.quote(url, safe=":/=?#")
        }],
        "iieaMergeType": split_mode,
        "iieaMergeRect": merge_rect,
    }

    return vio_data


if __name__ == '__main__':
    # 模型结果生产者
    producer = KafkaProducer(bootstrap_servers=KafkaConf.KAFKA_BROKERS,
                             value_serializer=lambda m: json.dumps(m).encode())

    test_data_list = [
        ["鲁GB2T71_崇德大街与昌安大道_16250_202502271700151.jpg","light",1625,None,411,"0,0,1,0.9478"],
        # ["鄂A814T5_20240415163502981.jpg","driver_vio",1120,None,411,None],
        # ["鄂DD66212_20240415165601155.jpg", "driver_vio", 1120,None,411,None],
        # ["鄂A8Y8N2_20240528163910000.jpg", "driver_vio", 1120,None,411,None],
        # ["鲁UKA935_省道220土王路_1223_202503130943031.jpg","phone",1223,None,111,None],
        # ["鲁G2EQ16_百脉湖大街夷安大道_1223_202503130933291.jpg", "phone", 1223, None, 111, None],
        # ["鲁U7583G_S219省道与疏港大道交叉口_1223_202503130905291.jpg", "phone", 1223, None, 111, None],

        # ["闽AD96338_split_img.jpg", "bkg_fusion", 1345, None, 211,None], #TODO：分割模型漏检禁行线
        # ["闽E1Y395_20241016031310156_1.jpg","cover_line", 1345, None, 211, None],
        # ["鲁VRE759_压线耗时长.jpg", "cover_line", 1345, None, 411, None],
        # ["闽E1Z886_20241016071021573_1.jpg", "cover_line", 1345, None, 211, None],

        # ["鲁GB2T71_崇德大街与昌安大道_16250_202502271700151.jpg", "light", 1625, None, 411, "0,0,1,0.9466"],
        # ["蒙AE7N09_大学东街与展览馆西路交叉路口_1373_202408071400021.jpg", "wrong_dir", 1301, None, 211, None],#青城/宁德数据
        # ["赣EB8753_30.jpg", "wrong_dir", 1301, None, 411, "0,0,1,0.924"],
        # ["闽A5CT81_38.jpg", "wrong_dir", 1301, None, 312, None],
        # ["蒙A236NC_生态路与盐站西路交叉路口_1373_202408071420291.jpg", "wrong_dir", 1301, None, 211, None]

    ]
    # 文件夹测试
    # for name in os.listdir(r"E:\AI审片项目和资料汇总\高密\2025-02-27\merge_data"):
    #     plate = name.split("_")[0]
    #
    #     if plate in ["鲁G8C083","鲁GRP918"]:
    #         mergerect = None
    #     else:
    #         mergerect = "0,0,1,0.9466"
    #     test_data_list.append([name,"light",1625,None,411,mergerect])
    # for i in range(50):
    #     data = test_data_list[0]
    for data in test_data_list:
        print("测试数据：",data)
        img_name,module_name,vio_code,plate,split_mode,merge_rect = data
        producer.send(KafkaConf.VIO_DATA_TOPIC, get_vio_data(img_name,module_name,vio_code,plate,split_mode,merge_rect))
    producer.flush()
    producer.close()
