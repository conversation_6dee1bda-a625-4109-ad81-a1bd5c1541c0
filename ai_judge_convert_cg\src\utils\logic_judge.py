import numpy as np


def plate_fuzzy_match(pred_plate_num, target_plate_num, thresh_list):
    if thresh_list == [None, None]:
        return False
    offset_judge = False
    if len(target_plate_num) == len(pred_plate_num):
        offset_judge = True
        error_num = 0
        for i in range(len(pred_plate_num)):
            if i != 0 and pred_plate_num[i] != target_plate_num[i]:
                # 排除易出错字符
                if (pred_plate_num[i] in ["Z", "2"] and target_plate_num[i] in ["Z", "2"]) or (
                        pred_plate_num[i] in ["D", "0"] and target_plate_num[i] in ["D", "0"]) or (
                        pred_plate_num[i] in ["S", "5"] and target_plate_num[i] in ["S", "5"]) or (
                        pred_plate_num[i] in ["8", "B"] and target_plate_num[i] in ["8", "B"]):
                    continue
                error_num += 1
        if error_num <= thresh_list[1]:
            return True
    if offset_judge:
        pred_plate_num = pred_plate_num[2:]
    target_plate_num = target_plate_num[2:]
    n = len(pred_plate_num)
    if n > 2:
        num = thresh_list[0]
        random_5 = n - num
        index = [i for i in range(random_5)]
        for i in range(int(len(index) + 1)):
            temp_plate = pred_plate_num[i:i + num]
            # 允许连续三位匹配车牌的起始索引相差小于2
            if temp_plate in target_plate_num and (
                    not offset_judge or (abs(target_plate_num.index(temp_plate) - i) < 2 and offset_judge)):
                return True
    return False


def compute_iou(boxes1, boxes2):
    """[Compute pairwise IOU matrix for given two sets of boxes]

    Args:
        boxes1 ([numpy ndarray with shape N,4]): [representing bounding boxes with format (xmin,ymin,xmax,ymax)]
        boxes2 ([numpy ndarray with shape M,4]): [representing bounding boxes with format (xmin,ymin,xmax,ymax)]
    Returns:
        pairwise IOU maxtrix with shape (N,M)，where the value at ith row jth column hold the iou between ith
        box and jth box from box1 and box2 respectively.
    """
    boxes1 = np.asarray(boxes1)
    boxes2 = np.asarray(boxes2)

    lu = np.maximum(boxes1[:, None, :2],
                    boxes2[:,
                    :2])  # lu with shape N,M,2 ; boxes1[:,None,:2] with shape (N,1,2) boxes2 with shape(M,2)
    rd = np.minimum(boxes1[:, None, 2:4], boxes2[:, 2:4])  # rd same to lu
    intersection_wh = np.maximum(0.0, rd - lu)
    intersection_area = intersection_wh[:, :, 0] * intersection_wh[:, :, 1]  # with shape (N,M)
    boxes1_wh = np.maximum(0.0, boxes1[:, 2:4] - boxes1[:, :2])
    boxes1_area = boxes1_wh[:, 0] * boxes1_wh[:, 1]  # with shape (N,)
    boxes2_wh = np.maximum(0.0, boxes2[:, 2:4] - boxes2[:, :2])
    boxes2_area = boxes2_wh[:, 0] * boxes2_wh[:, 1]  # with shape (M,)
    union_area = np.maximum(boxes1_area[:, None] + boxes2_area - intersection_area, 1e-8)  # with shape (N,M)
    ious = np.clip(intersection_area / union_area, 0.0, 1.0)
    return ious
if __name__ == '__main__':
    b1 = [[2111, 107, 2131, 128], [1970, 107, 1989, 127], [2112, 108, 2132, 128],
                          [1969, 108, 1989, 127], [2111, 214, 2131, 234], [1969, 213, 1989, 232]]
    b2 = [[2111, 107, 2131, 128], [1970, 107, 1989, 127], [2112, 108, 2132, 128],
                          [1969, 108, 1989, 127], [2111, 214, 2131, 234], [1969, 213, 1989, 232]]
    res = compute_iou(b1,b2)
    x,y = np.where(res>0.9)
    print(len(x),len(y))
    print(x,y)