import sys
import urllib.parse
import uuid

import cv2

from src.utils import tools

sys.path.append("..")
import pytest
from conf.config import DriverVioJudActConf, ImageJudActConf, RequestJudActConf, BkgFusionJudActConf
from src.module.image_model import ModelFactory
from src.module.judge_act import DriverVioJudAct, JudgeResult, ImageJudAct, RequestJudAct, BkgFusionJudAct
from src.module.judge_rule import CoverLineRule, \
    WrongDrvDirRule, NoVehRule, VehMoveRule, VehMarklineRule, DrvdirLightRule,DrvdirArrowRule
from src.module.request_parser import RequestCtx
from typing import Dict, Union, Optional
from src.utils.time_util import TimeUtil
from src.utils.tools import ObjectProcess

logger = tools.init_logger("main")

Request = RequestCtx()
# 请求判断
request_act = RequestJudAct("request", RequestJudActConf, {},{})
# 获取图片动作
image_act = ImageJudAct("image", ImageJudActConf, {},{})
model_dict = ModelFactory().init_models()
root = r"C:\Users\<USER>\Desktop\AIJudge_Unit_Test"


def init_judgeres(vio_data: Dict, img_num: int = None) -> JudgeResult:
    Request.parse(vio_data)  # 解析违法数据
    judgeres = JudgeResult(Request, logger)
    request_act.run(judgeres)
    image_act.run(judgeres)

    if img_num:
        img_list = judgeres.act_details.get("split_img")
        img_list[:] = img_list[:img_num]
    return judgeres


def init_judgeres2(vio_code="1625") -> JudgeResult:
    simple_data = {
        "iieaUUID": str(uuid.uuid4()),
        "sendTime": TimeUtil.now_hikstr(),
        "vehicleAlarmResult": [{
            "target": [{
                "vehicle": {
                    "plateColor": {
                        "value": "blue"
                    },
                    "plateNo": {
                        "confidence": 1.0,
                        "value": "川A88888"
                    },
                }
            }],
            "targetAttrs": {
                "alarmType": vio_code,
                "cameraIndexCode": "805c3549bea2489a89be33cc0c87df56",

                "crossingIndexCode": "a3b82285b4b348e5acf03034b4d5c7f4",
                "platePicUrl": "http://35.26.210.25:6120/pic?5E0FB0D80AA0AB01C7B7*hcs87e8cc71bdd24f34b2023/cljj/27600;1725332044790429225254?pic*1052775206*7348*2058*5E0FB0D80AA0AB01C7B7-2*1725332154",
            },
            "targetPicUrl": "www.baidu.com"
        }],
    }
    Request.parse(simple_data)  # 解析违法数据
    judgeres = JudgeResult(Request, logger)
    return judgeres


def get_vio_data(img_name: str, module_name: str, vio_code: Union[str, int], plate: str = None, split_mode: int = None,
                 merge_rect: str = None) -> Dict:
    if plate is None:
        plate = img_name.split("_")[0]
    pic_root = f"/data/ai_judge_pic/modules_test/{module_name}/{img_name}"
    url = "http://10.184.48.200:8080" + pic_root
    vio_data = {
        "iieaUUID": str(uuid.uuid4()),
        "sendTime": TimeUtil.now_hikstr(),
        "vehicleAlarmResult": [{
            "target": [{
                "vehicle": {
                    "plateColor": {
                        "value": "blue"
                    },
                    "plateNo": {
                        "confidence": 0.0,
                        "value": plate
                    },
                }
            }],
            "targetAttrs": {
                "alarmType": str(vio_code),
                "cameraIndexCode": "805c3549bea2489a89be33cc0c87df56",

                "crossingIndexCode": "a3b82285b4b348e5acf03034b4d5c7f4",
                "platePicUrl": "http://35.26.210.25:6120/pic?5E0FB0D80AA0AB01C7B7*hcs87e8cc71bdd24f34b2023/cljj/27600;1725332044790429225254?pic*1052775206*7348*2058*5E0FB0D80AA0AB01C7B7-2*1725332154",
            },
            "targetPicUrl": urllib.parse.quote(url, safe=":/=?#")
        }],
        "iieaMergeType": split_mode,
        "iieaMergeRect": merge_rect,
    }

    return vio_data


def add_details(judgeres: JudgeResult, details: Optional[Dict], name="pytest"):
    judgeres.add_act_details(name, details)


class TestActModules:
    drvier_vio_params = [
        [{'cls_thresh': 0.35},
         get_vio_data("鄂A814T5_20240415163502981.jpg", "driver_vio", 1120, None, 411, None), {
             "target_veh_box": {'img1': [1365, 605, 1896, 1154], 'img2': [1301, 685, 1879, 1286],
                                'img3': [1269, 727, 1873, 1350]},
             'person_box': {'img1': [[1518, 733, 1628, 831], [1654, 708, 1793, 827]],
                            'img2': [[1617, 801, 1763, 933], [1474, 823, 1591, 930]],
                            'img3': [[1450, 869, 1572, 981], [1597, 844, 1748, 979]]}}, 1],
        [{'cls_thresh': 0.35},
         get_vio_data("鄂DD66212_20240415165601155.jpg", "driver_vio", 1120, None, 411, None), {
             'target_veh_box': {'img1': [2515, 536, 3695, 1755], 'img2': [2559, 688, 3806, 1988],
                                'img3': [2598, 822, 3924, 2157]},
             'person_box': {'img1': [[3090, 805, 3388, 1062]], 'img2': [[3174, 1019, 3473, 1243]],
                            'img3': [[3238, 1204, 3565, 1425], [2827, 20, 2985, 157], [1239, 0, 1370, 77]]}}, 2],
        [{'cls_thresh': 0.35},
         get_vio_data("鄂A8Y8N2_20240528163910000.jpg", "driver_vio", 1120, None, 411, None), {
             'target_veh_box': {'img1': [602, 319, 1917, 1151], 'img3': [599, 323, 1934, 1153],
                                'img2': [561, 210, 747, 333]},
             'person_box': {'img1': [], 'img2': [[1062, 185, 1116, 264], [901, 165, 928, 206]], 'img3': []}}, 2],
        # 无驾驶人
    ]

    @pytest.fixture(params=drvier_vio_params, ids=["鄂A814T5_vio", "鄂DD66212_waste", "鄂A8Y8N2_no_person"])
    def add_driver_params(self, request):
        res_list = ["driver_vio", DriverVioJudActConf]
        res_list.extend(request.param)
        current_test_id = request.node.callspec.id.encode('utf-8').decode('unicode-escape')
        res_list.append(current_test_id)
        return res_list

    def test_driver_vio(self, add_driver_params):
        name, conf, params, vio_data, details, status, test_id = add_driver_params
        judgeres = init_judgeres(vio_data)
        add_details(judgeres, details, "driver_vio_test")
        act = DriverVioJudAct(name, conf, params, model_dict)
        act.run(judgeres)
        res_core = judgeres.get_judge_res_core()
        judgeres.print_details()
        judgeres.logger.info(f"res_core:{res_core}")
        judgeres.logger.info(f"{test_id}测试完成\n\n")

        assert res_core["judgeStatus"] == status, judgeres.logger.error("车内人员违法判定结果异常！")
    # 打电话
    # params,pkl_data,result_code
    phone_params = [
        [{"phone_thresh": 0.95, "add_phone_logic": True, "ph_filter_thresh": [0.2, 0.25], "ph_logic_mode": 2},
         "鲁G2EQ16_phone_details.pkl", "no_vio"],
        [{"phone_thresh": 0.95, "add_phone_logic": False, "ph_filter_thresh": [0.2, 0.25], "ph_logic_mode": 2},
         "鲁U7583G_phone_details.pkl", "phone_vio"],
        [{"phone_thresh": 1, "add_phone_logic": True, "ph_filter_thresh": [0.2, 0.25], "ph_logic_mode": 1},
         "鲁UKA935_phone_details.pkl", "vio_recall_mode"],
        [{"phone_thresh": 1, "add_phone_logic": True, "ph_filter_thresh": [0.2, 0.25], "ph_logic_mode": 2},
         "鲁UKA935_phone_details.pkl", "vio_static_mode"],
        [{"phone_thresh": 1, "add_phone_logic": True, "ph_filter_thresh": [0.2, 0.25], "ph_logic_mode": 3},
         "鲁UKA935_phone_details.pkl", "vio_filter_mode"],
    ]
    ids = ["抓耳朵-分类模型+检测模型作废","耳边打电话-分类模型判定正片","手持手机-检出模式","手持手机-均衡模式","手持手机-强过滤模式"]
    @pytest.fixture(params=phone_params, ids=ids)
    def add_phone_params(self, request):
        res_list = ["phone_vio", DriverVioJudActConf]
        res_list.extend(request.param)
        current_test_id = request.node.callspec.id.encode('utf-8').decode('unicode-escape')
        res_list.append(current_test_id)
        return res_list

    def test_phone_vio(self, add_phone_params):
        name, conf, params, pkl_name, result_code, test_id = add_phone_params
        judgeres = init_judgeres2(vio_code="1223")
        details = ObjectProcess.load_object(f"{root}/pkl_data/{pkl_name}")
        judgeres.act_details = details
        act = DriverVioJudAct(name, conf, params, model_dict)
        act.run(judgeres)
        res_core = judgeres.get_judge_res_core()
        judgeres.print_details()
        judgeres.logger.info(f"res_core:{res_core}")
        judgeres.logger.info(f"{test_id}测试完成\n\n")

        assert res_core["resultCode"] == result_code, judgeres.logger.error("打电话违法判定结果异常！")
    # 背景图融合
    bkg_fusion_params = [
        [
            {"sort_merge": True, 'det_cls': ["lane1", "stop_line", "arrow_s"]},
            get_vio_data("闽AD96338_split_img.jpg", "bkg_fusion", 1345, None, 211, None), ["tfc_element"]],
        [
            {"sort_merge": False, "bkg_det": False,
             'det_cls': ["lane1", "lane2", "arrow_l", "arrow_s", "arrow_r", "stop_line"]},
            get_vio_data("闽AD96338_split_img.jpg", "bkg_fusion", 1345, None, 211, None),
            ["lane", "arrow", "stop_line"]],
        [
            {"sort_merge": False, "bkg_det": False, 'det_cls': ["lane1", "arrow_s", "zebra_line"]},
            get_vio_data("闽AD96338_split_img.jpg", "bkg_fusion", 1345, None, 211, None), None]

    ]
    ids = ["sort_merge", "superclass_merge", "all_merge"]

    @pytest.fixture(params=bkg_fusion_params, ids=ids)
    def add_bg_fusion_params(self, request):
        res_list = ["bg_fusion", BkgFusionJudActConf]
        res_list.extend(request.param)
        current_test_id = request.node.callspec.id.encode('utf-8').decode('unicode-escape')
        res_list.append(current_test_id)
        return res_list

    def test_bg_fusion(self, add_bg_fusion_params):
        name, conf, params, vio_data, superclasses, test_id = add_bg_fusion_params
        judgeres: JudgeResult = init_judgeres(vio_data, 2)
        # 添加elements
        tfc_elements = ObjectProcess.load_object(f"{root}/pkl_data/bg_fusion_tfc_elemets.pkl")
        judgeres.tfc_elements = tfc_elements
        # print(tfc_elements["img1"].get_results(attr="subclass"))
        act = BkgFusionJudAct(name, conf, params, model_dict)
        act.run(judgeres)
        merge_tfc_element = judgeres.get_act_detail("merge_tfc_element")
        judgeres.logger.info(f"{test_id}-tfc_element:{merge_tfc_element.keys()}")

        bkg_img = judgeres.get_act_detail("bkg_img")
        if test_id == "sort_merge":
            cv2.imwrite(f"{root}/vis_img/bkg_fusion_{test_id}.jpg", bkg_img)
            assert list(merge_tfc_element.keys()) == params.get("det_cls"), print("分类融合字典键异常！")
        elif test_id == "superclass_merge":
            assert list(merge_tfc_element.keys()) == superclasses, print("超类融合字典键异常！")
        else:
            assert list(merge_tfc_element.keys()) == ["all_element"], print("全融合字典键异常！")

        for k, v in merge_tfc_element.items():
            for model_res in v:
                judgeres.logger.debug(f"{k}:{model_res.subclass}-{model_res.box}")

        judgeres.logger.info(f"{test_id}测试完成\n\n")

    # 压线
    cover_line_params = [
        [
            {'dist_thresh': 3, 'bias_rate': 1.5, 'min_pixel': 50},
            get_vio_data("闽E1Y395_20241016031310156_1.jpg", "cover_line", 1345, None, 211, None), 1],
        [
            {'dist_thresh': 3, 'bias_rate': 1.5, 'min_pixel': 50},
            get_vio_data("鲁VRE759_压线耗时长.jpg", "cover_line", 1345, None, 411, None), 1],
        [
            {'dist_thresh': 3, 'bias_rate': 1.5, 'min_pixel': 50},
            get_vio_data("闽E1Z886_20241016071021573_1.jpg", "cover_line", 1345, None, 211, None), 2],
    ]
    ids = ["闽E1Y395", "鲁VRE759", "闽E1Z886"]

    @pytest.fixture(params=cover_line_params, ids=ids)
    def add_cover_line_params(self, request):
        res_list = ["cover_line"]
        res_list.extend(request.param)
        current_test_id = request.node.callspec.id.encode('utf-8').decode('unicode-escape')
        res_list.append(current_test_id)
        return res_list

    def test_cover_line(self, add_cover_line_params):
        name, params, vio_data, status, test_id = add_cover_line_params
        judgeres: JudgeResult = init_judgeres(vio_data)
        # 添加details
        details = ObjectProcess.load_object(f"{root}/pkl_data/{test_id}_cover_line_details.pkl")
        judgeres.act_details = details
        act = CoverLineRule(name, params, model_dict)
        act.run(judgeres)
        res_core = judgeres.get_judge_res_core()
        judgeres.print_details()
        judgeres.logger.info(f"res_core:{res_core}")

        assert res_core["judgeStatus"] == status, judgeres.logger.error("压线违法判定结果异常！")

        judgeres.logger.info(f"{test_id}测试完成\n\n")

    # 逆行规则
    wrong_dir_params = [
        [{"dist_rate": 0.45}, 2],
        [{"dist_rate": 0.45}, 1],
        [{"dist_rate": 0.45}, 1],
        [{"dist_rate": 0.45}, 1]
    ]
    ids = ["蒙AE7N09", "赣EB8753", "闽A5CT81", "蒙A236NC"]

    @pytest.fixture(params=wrong_dir_params, ids=ids)
    def add_wrong_dir_params(self, request):
        res_list = ["wrong_dir"]
        res_list.extend(request.param)
        current_test_id = request.node.callspec.id.encode('utf-8').decode('unicode-escape')
        res_list.append(current_test_id)
        return res_list

    def test_wrong_dir(self, add_wrong_dir_params):
        name, params, status, test_id = add_wrong_dir_params
        # 添加details
        judgeres = ObjectProcess.load_object(f"{root}/pkl_data/{test_id}_wrong_dir_judgeres.pkl")
        act = WrongDrvDirRule(name, params, model_dict)
        act.run(judgeres)
        res_core = judgeres.get_judge_res_core()
        judgeres.print_details()
        logger.info(f"res_core:{res_core}")

        assert res_core["judgeStatus"] == status, logger.error("逆行违法判定结果异常！")

        judgeres.logger.info(f"{test_id}测试完成\n\n")

    # 车辆匹配判定规则
    no_veh_params = [
        [{"match_index": [0, 1, 2]}, {"img_keys": ["img1", "img2", "img3", "img4"],
                                      "target_veh_plate": {"img1": "plate", "img3": "similar_match", "img4": "plate"}},
         2],
        [{"plate_match_num": 2}, {"img_keys": ["img1", "img2", "img3", "img4"],
                                  "target_veh_plate": {"img1": "plate", "img2": "similar_match",
                                                       "img3": "similar_match", "img4": "similar_match"}}, 2],
        [{"match_index": [], "plate_match_num": 1},
         {"img_keys": ["img1", "img2", "img3", "img4"], "target_veh_plate": {}}, 2],
        [{"match_index": [], "match_num": 2},
         {"img_keys": ["img1", "img2", "img3", "img4"], "target_veh_plate": {"img1": "plate"}}, 2],
    ]
    ids = ["图片序号判定", "车牌匹配数量判定", "无车辆判定", "车辆匹配数量判定"]

    @pytest.fixture(params=no_veh_params, ids=ids)
    def add_no_veh_params(self, request):
        res_list = ["no_veh"]
        res_list.extend(request.param)
        current_test_id = request.node.callspec.id.encode('utf-8').decode('unicode-escape')
        res_list.append(current_test_id)
        return res_list

    def test_veh_match(self, add_no_veh_params):
        name, params, details, status, test_id = add_no_veh_params
        # 添加details
        judgeres = init_judgeres2()
        judgeres.add_act_details("test_no_veh", details)
        act = NoVehRule(name, params, model_dict)
        act.run(judgeres)
        res_core = judgeres.get_judge_res_core()
        judgeres.print_details()
        logger.info(f"res_core:{res_core}")

        assert res_core["judgeStatus"] == status, logger.error("车辆匹配判定结果异常！")

        judgeres.logger.info(f"{test_id}测试完成\n\n")

    # 位移判定规则
    veh_move_params = [
        [{'iou_thresh': 0.95, 'yiou_thresh': 0.98}, {
            "target_veh_box": {'img1': [958, 644, 1183, 874], 'img2': [979, 469, 1141, 621],
                               'img3': [979, 287, 1075, 371]}}, ""],
        [{'iou_thresh': 0.93, 'yiou_thresh': 0.98}, {
            "target_veh_box": {'img1': [958, 644, 1183, 874], 'img2': [960, 640, 1183, 865],
                               'img3': [979, 287, 1075, 371]}}, "no_move_001"],
        [{'iou_thresh': 0.95, 'yiou_thresh': 0.96}, {
            "target_veh_box": {'img1': [958, 644, 1183, 874], 'img2': [1200, 640, 1450, 870],
                               'img3': [979, 287, 1075, 371]}}, "no_move_002"],
        [{'iou_thresh': 0.95, 'yiou_thresh': 0.98}, {
            "target_veh_box": {'img1': [958, 644, 1183, 874], 'img2': [979, 469, 1141, 621],
                               'img3': [979, 468, 1141, 618]}}, "no_move_001"],
        [{'iou_thresh': 0.95, 'yiou_thresh': 0.98}, {
            "target_veh_box": {'img1': [958, 644, 1183, 874], 'img2': [979, 469, 1141, 621],
                               'img3': [1142, 468, 1350, 620]}}, "no_move_002"]
    ]
    ids = ["位移正常", "一二图无明显位移-iou", "一二图无明显位移-yiou", "二三图无明显位移-iou", "二三图无明显位移-yiou"]

    @pytest.fixture(params=veh_move_params, ids=ids)
    def add_veh_move_params(self, request):
        res_list = ["no_move"]
        res_list.extend(request.param)
        current_test_id = request.node.callspec.id.encode('utf-8').decode('unicode-escape')
        res_list.append(current_test_id)
        return res_list

    def test_veh_move(self, add_veh_move_params):
        name, params, details, result_code, test_id = add_veh_move_params
        # 添加details
        judgeres = init_judgeres2()
        judgeres.add_act_details("test_veh_move", details)
        act = VehMoveRule(name, params, model_dict)
        act.run(judgeres)
        res_core = judgeres.get_judge_res_core()
        judgeres.print_details()
        logger.info(f"res_core:{res_core}")

        assert res_core["resultCode"] == result_code, logger.error("车辆位移判定结果异常！")

        logger.info(f"{test_id}测试完成\n\n")

    # 地标线判定规则测试
    mark_line_params = [
        [{"stop_line_rule": True, "cover_line_rule": False},
         {
             'target_veh_box': {'img1': [958, 200, 1183, 360], 'img2': [979, 600, 1141, 751],
                                'img3': [979, 287, 1075, 371]}
         }, "stop_line_rule"],
        [{"stop_line_rule": True, "cover_line_rule": False},
         {
             'target_veh_box': {'img1': [958, 664, 1183, 894], 'img2': [979, 805, 1141, 1024],
                                'img3': [979, 287, 1075, 371]}
         }, "stop_line_rule"],
        [{"stop_line_rule": True, "cover_line_rule": False},
         {
             'target_veh_box': {'img1': [958, 450, 1183, 674], 'img2': [979, 269, 1141, 421],
                                'img3': [979, 87, 1075, 171]}
         }, "stop_line_veh1"],
        [{"stop_line_rule": True, "cover_line_rule": False},
         {
             'target_veh_box': {'img1': [958, 664, 1183, 894], 'img2': [979, 680, 1141, 842],
                                'img3': [979, 287, 1075, 371]}
         }, "stop_line_veh2"],
        [{"stop_line_rule": False, "cover_line_rule": True},
         {
             'target_veh_box': {'img1': [720, 644, 1083, 874], 'img2': [979, 469, 1141, 621],
                                'img3': [979, 287, 1075, 371]}
         }, "mark_line_cover"],
        [{"stop_line_rule": False, "cover_line_rule": False},
         {
             'target_veh_box': {'img1': [100, 644, 500, 874], 'img2': [979, 469, 1141, 621],
                                'img3': [979, 287, 1075, 371]}
         }, "no_arrow_waste"],
    ]
    ids = ["一图简易停止线判定", "二图简易停止线判定", "一图超过停止线", "二图未越过停止线", "横跨车道判定","无导向箭头"]

    @pytest.fixture(params=mark_line_params, ids=ids)
    def add_mark_line_params(self, request):
        res_list = ["mark_line"]
        res_list.extend(request.param)
        current_test_id = request.node.callspec.id.encode('utf-8').decode('unicode-escape')
        res_list.append(current_test_id)
        return res_list

    def test_mark_line(self, add_mark_line_params):
        name, params, details, result_code, test_id = add_mark_line_params
        # 添加details
        judgeres = ObjectProcess.load_object(
            r"C:\Users\<USER>\Desktop\AIJudge_Unit_Test\pkl_data\鲁GB2T71_veh_markline.pkl")
        judgeres.add_act_details("test_mark_line", details)
        rule = VehMarklineRule(name, params, model_dict)
        rule.run(judgeres)
        res_core = judgeres.get_judge_res_core()
        judgeres.print_details()
        logger.info(f"res_core:{res_core}")

        assert res_core["resultCode"] == result_code, logger.error("车辆地标线判定结果异常！")

        logger.info(f"{test_id}测试完成\n\n")

    # 行驶方向和红绿灯状态规则
    drvdir_light_params = [
        [{'drv_direction': 'straight', 'status': ['red_l', 'red_s']}, "vio_straight"],
        [{'drv_direction': 'straight', 'status': ['red_l', 'green_s']}, "no_vio"],
        [{'drv_direction': 'left', 'status': ['red_l', 'red_s']}, "vio_left"],
        [{'drv_direction': 'left', 'status': ['red_s']}, "vio_left2"],
        [{'drv_direction': 'left', 'status': ['green_l', 'red_s']}, "no_vio"],
        [{'drv_direction': 'left', 'status': ['green_s']}, "no_vio"],
        [{'drv_direction': 'right', 'status': ['red_l', 'red_s', 'red_r']}, "vio_right"],
        [{'drv_direction': 'right', 'status': ['red_l', 'red_s', 'green_r']}, "no_vio"],
        [{'drv_direction': 'right', 'status': ['red_l', 'red_s']}, "no_vio"],

    ]
    ids = ["直行红灯违法", "直行绿灯废片", "左转+左转红灯违法", "左转+直行红灯违法", "左转+左转绿灯废片",
           "左转+直行绿灯废片", "右转红灯违法", "右转绿灯废片", "右转无灯废片"]

    @pytest.fixture(params=drvdir_light_params, ids=ids)
    def add_drvdir_light_params(self, request):
        res_list = ["drvdir_light"]
        res_list.extend(request.param)
        current_test_id = request.node.callspec.id.encode('utf-8').decode('unicode-escape')
        res_list.append(current_test_id)
        return res_list

    def test_drvdir_light(self, add_drvdir_light_params):
        name, details, result_code, test_id = add_drvdir_light_params
        judgeres = init_judgeres2("1625")
        # 添加details
        judgeres.add_act_details("test_drvdir_light", details)
        rule = DrvdirLightRule(name, {}, model_dict)
        rule.run(judgeres)
        res_core = judgeres.get_judge_res_core()
        judgeres.print_details()
        logger.info(f"res_core:{res_core}")

        assert res_core["resultCode"] == result_code, logger.error("行驶方向和信号灯状态规则异常！")

        logger.info(f"{test_id}测试完成\n\n")

    # 行驶方向和导向箭头规则
    drvdir_arrow_params = [
        [{'drv_direction': None, 'target_arrow': ['arrow_l', 'arrow_s']}, "no_drvdir_vio"],
        [{'drv_direction': 'straight', 'target_arrow': ['arrow_l']}, "straight_vio"],
        [{'drv_direction': 'straight', 'target_arrow': ['arrow_l', 'arrow_s']}, "straight_waste"],
        [{'drv_direction': 'left', 'target_arrow': ['arrow_s']}, "left_vio"],
        [{'drv_direction': 'left', 'target_arrow': ['arrow_l', 'arrow_s']}, "left_waste"],
        [{'drv_direction': 'right', 'target_arrow': ['arrow_s']}, "right_vio"],
        [{'drv_direction': 'right', 'target_arrow': ['arrow_r']}, "right_waste"],
    ]
    ids = ["无行驶方向","直行违法", "直行废片", "左转违法", "左转废片", "右转违法", "右转废片"]

    @pytest.fixture(params=drvdir_arrow_params, ids=ids)
    def add_drvdir_arrow_params(self, request):
        res_list = ["drvdir_light"]
        res_list.extend(request.param)
        current_test_id = request.node.callspec.id.encode('utf-8').decode('unicode-escape')
        res_list.append(current_test_id)
        return res_list

    def test_drvdir_arrow(self, add_drvdir_arrow_params):
        name, details, result_code, test_id = add_drvdir_arrow_params
        judgeres = init_judgeres2("1208")
        # 添加details
        judgeres.add_act_details("test_drvdir_arrow", details)
        rule = DrvdirArrowRule(name, {}, model_dict)
        rule.run(judgeres)
        res_core = judgeres.get_judge_res_core()
        judgeres.print_details()
        logger.info(f"res_core:{res_core}")

        assert res_core["resultCode"] == result_code, logger.error("行驶方向和导向箭头规则异常！")

        logger.info(f"{test_id}测试完成\n\n")


if __name__ == '__main__':
    pytest.main()
