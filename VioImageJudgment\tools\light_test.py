import sys
import os

sys.path.append('..')

import warnings

warnings.filterwarnings('ignore')
from tqdm import tqdm
import cv2
import numpy as np
from ai_judge_code_gy.light_detect import LightJudge, judge_light_status, dn
from ai_judge_code_gy.judge_tools import Tools
import xml.etree.ElementTree as ET
import torch
import shutil
from yolov7.detect import load_lp, detect_lp

# IOU计算
def get_iou(box1, box2):
    data1 = np.array(box1)
    data2 = np.array(box2)
    tool = Tools()
    return tool.IOU(data1[:2], data1[2:], data2[:2], data2[2:])


# [('[[657, 49], [696, 72]]', ['red_l']), ('[[895, 48], [932, 70]]', ['red_s'])]
# 红绿灯识别结果正确判定
def check_result(xml_path, name, light_res, total_right):
    all_right = True
    with open(f'{xml_path}/{name}.xml', mode='r', encoding='utf-8') as f:
        tree = ET.parse(f)
        root = tree.getroot()
        label_list = []
        for obj in root.iter('object'):
            cls = obj.find('name').text
            if cls.startswith('police'):
                continue
            xmlbox = obj.find('bndbox')
            pos = [float(xmlbox.find('xmin').text), float(xmlbox.find('ymin').text), float(xmlbox.find('xmax').text),
                   float(xmlbox.find('ymax').text), cls]
            label_list.append(pos)
    if len(light_res) != len(label_list):
        all_right = False
    for item in light_res:
        if not label_list:
            all_right = False
        for idx, label in enumerate(label_list):
            label_pos = label[:4]
            pred_pos = eval(item[0])[0] + eval(item[0])[1]
            iou = get_iou(label_pos, pred_pos)
            # print("pred:", item[1], "label:", label[-1], iou)
            if iou > 0.2:
                if item[1].split("-")[0] == label[-1]:
                    total_right[label[-1]] += 1
                else:
                    all_right = False
                label_list.remove(label)
                break
            elif idx == len(label_list) - 1:
                all_right = False
    if all_right:
        total_right["all"] += 1
    return all_right


# 填充黑框
def add_img_to_half(img):
    h, w, _ = img.shape

    if h >= w // 2:
        add_black = np.zeros((h, 2 * h - w, 3), dtype=np.uint8)
        new_img = np.concatenate((img, add_black), 1)
    else:
        add_black = np.zeros((w // 2 - h, w, 3), dtype=np.uint8)
        new_img = np.concatenate((img, add_black), 0)
    return new_img


# 弃用
def light_detect(save_name, file_list, weights_list, weights_name, light_threshold=0.5, resize_scale=0.7, show=False):
    lp_net = weights_list[0]
    lp_meta = weights_list[1]
    judge_infos = {'lamp': []}

    try:
        img1_np, img2_np, img3_np = file_list[:3]
    except Exception as e:
        print(e)
        return judge_infos
    # try:
    #     img1, img2, img3 = Tools().resize_imgs(img1_np, img2_np, img3_np)
    # except:
    #     import traceback
    #     traceback.print_exc()
    #     return judge_infos
    #
    # if img1.shape[0] > 4000 or img1.shape[1] > 4000:
    #     resize_scale = resize_scale
    #     if float(resize_scale) < 1:
    #         img1 = cv2.resize(img1, None, fx=resize_scale, fy=resize_scale)
    #         img2 = cv2.resize(img2, None, fx=resize_scale, fy=resize_scale)
    #         img3 = cv2.resize(img3, None, fx=resize_scale, fy=resize_scale)
    # img_list = [img1, img2, img3]

    img1_light_res = LightJudge().confirm_light(add_img_to_half(img1), lp_net, lp_meta, light_threshold, show=False)
    # print(img1_light_res)
    img2_light_res = LightJudge().confirm_light(add_img_to_half(img2), lp_net, lp_meta, light_threshold, show=False)
    # print(img2_light_res)
    img3_light_res = LightJudge().confirm_light(add_img_to_half(img3), lp_net, lp_meta, light_threshold, show=False)
    # print(img3_light_res)
    status = judge_light_status(img1, img2, img3, img1_light_res, img2_light_res, img3_light_res, show=False)
    print('light_status：', status)
    judge_infos['lamp'] = img1_light_res + img2_light_res + img3_light_res
    print(img1_light_res, "\n", img2_light_res, "\n", img3_light_res)

    # if not status:
    #     return judge_infos
    # else:
    #     judge_infos['drive_direction_light'] = status

    # color = [(255, 0, 0), (0, 255, 0), (0, 0, 255), (0, 255, 255), (255, 255, 0), (255, 0, 255)]

    fontsize = 1.5
    thickness = 2
    # y_add = int(20 * fontsize)
    if show:
        # drv_img = img1.copy()
        # 画红绿灯位置信息和车辆位置信息
        for idx, item in enumerate(img1_light_res):
            point = eval(item[0])
            labels = item[1]
            pt_tx = int(point[0][0])
            pt_ty = int(point[0][1])
            pt_bx = int(point[1][0])
            pt_by = int(point[1][1])
            color, org = get_color_org((pt_tx, pt_ty), (pt_bx, pt_by), labels[0])
            cv2.rectangle(img1, (pt_tx, pt_ty), (pt_bx, pt_by), color, 1)
            cv2.putText(img1, str(labels[0]), org, cv2.FONT_HERSHEY_COMPLEX,
                        fontsize, color, thickness)
        for idx, item in enumerate(img2_light_res):
            point = eval(item[0])
            labels = item[1]
            pt_tx = int(point[0][0])
            pt_ty = int(point[0][1])
            pt_bx = int(point[1][0])
            pt_by = int(point[1][1])
            color, org = get_color_org((pt_tx, pt_ty), (pt_bx, pt_by), labels[0])
            cv2.rectangle(img2, (pt_tx, pt_ty), (pt_bx, pt_by), color, 1)
            cv2.putText(img2, str(labels[0]), org, cv2.FONT_HERSHEY_COMPLEX,
                        fontsize, color, thickness)
        for idx, item in enumerate(img3_light_res):
            point = eval(item[0])
            labels = item[1]
            pt_tx = int(point[0][0])
            pt_ty = int(point[0][1])
            pt_bx = int(point[1][0])
            pt_by = int(point[1][1])
            color, org = get_color_org((pt_tx, pt_ty), (pt_bx, pt_by), labels[0])
            cv2.rectangle(img3, (pt_tx, pt_ty), (pt_bx, pt_by), color, 1)
            cv2.putText(img3, str(labels[0]), org, cv2.FONT_HERSHEY_COMPLEX,
                        fontsize, color, thickness)
        if status:
            light_result = ""
            for i in status:
                light_result = light_result + i + " "
            cv2.putText(img3, light_result, (100, 100), cv2.FONT_HERSHEY_COMPLEX, fontsize, (0, 0, 255), thickness)
        w = weights_name.split(".")[0].split("_")[-1]
        save_dir = f'./light_results_{w}'
        if not os.path.exists(save_dir):
            os.makedirs(save_dir)
        cv2.imwrite(save_dir + f'/{save_name}_img1.jpg', img1)
        cv2.imwrite(save_dir + f'/{save_name}_img2.jpg', img2)
        cv2.imwrite(save_dir + f'/{save_name}_img3.jpg', img3)

    return judge_infos


# 单图红绿灯识别测试
def single_detect(save_name, img, weights_list, weights_name, total_right, add_black=False, light_threshold=0.3,
                  show=False, data=""):
    lp_net = weights_list[0]
    imgsz, stride, device = weights_list[1]
    judge_infos = {'lamp': []}
    # if add_black:
    #     img = add_img_to_half(img)
    # img_light_res = LightJudge().confirm_light(img, lp_net, lp_meta, light_threshold, show=False)
    img_light_res = detect_lp(img, lp_net, imgsz, stride, device, conf_thres=0.25, iou_thres=0.45, test_lp=True)
    name = save_name.split(".")[0]
    w = weights_name.split(".")[0]
    if data == "train":
        all_right = check_result("../voc_data/Annotations", name, img_light_res, total_right)
        # all_right = True
        save_dir = f'./light_train_results/{w}'
    else:
        all_right = check_result("../light_test/Annotations", name, img_light_res, total_right)
        save_dir = f'./light_vis_results/{w}'

    judge_infos['lamp'] = img_light_res

    fontsize = 1.5
    thickness = 2
    if show:
        # drv_img = img1.copy()
        # 画红绿灯位置信息和车辆位置信息
        for idx, item in enumerate(img_light_res):
            point = eval(item[0])
            labels = item[1]

            pt_tx = int(point[0][0])
            pt_ty = int(point[0][1])
            pt_bx = int(point[1][0])
            pt_by = int(point[1][1])
            color, org = get_color_org_new((pt_tx, pt_ty), (pt_bx, pt_by), labels)
            cls = labels.split("-")[0][-1]
            cv2.putText(img, cls, org, cv2.FONT_HERSHEY_COMPLEX,
                        fontsize, color, thickness)
            cv2.rectangle(img, (pt_tx, pt_ty), (pt_bx, pt_by), color, 1)

        if not os.path.exists(save_dir):
            os.makedirs(save_dir)
        right_img = f"{save_dir}/right"
        error_img = f"{save_dir}/error"
        os.makedirs(right_img, exist_ok=True)
        os.makedirs(error_img, exist_ok=True)
        if all_right:
            cv2.imwrite(f"{right_img}/{save_name}", img)
        else:
            cv2.imwrite(f"{error_img}/{save_name}", img)
    torch.cuda.empty_cache()
    return judge_infos, all_right


# 颜色位置修正
def get_color_org(pt1, pt2, cls):
    if cls.endswith('l'):
        org = (pt1[0] - 100, pt2[1] + 50)
    elif cls.endswith('r'):
        org = (pt1[0] + 100, pt2[1] + 50)
    else:
        org = (pt1[0], pt2[1] + 50)
    if cls.startswith('red'):
        color = (0, 0, 255)
    else:
        color = (0, 255, 0)
    return color, org


# 颜色位置修正
def get_color_org_new(pt1, pt2, cls):
    org = (pt1[0], pt2[1] + 50)
    if cls.startswith('red'):
        color = (0, 0, 255)
    else:
        color = (0, 255, 0)
    return color, org


# 统计数据集中各类别数量
def get_all_label_num(label_dir="../light_test/Annotations"):
    temp = os.listdir(label_dir)
    num_dict = {"red_l": 0, "red_s": 0, "red_r": 0, "green_l": 0, "green_s": 0, "green_r": 0, "all": len(temp)}
    for i in temp:
        xml = f"{label_dir}/{i}"
        with open(xml, mode='r', encoding='utf-8') as f:
            tree = ET.parse(f)
            root = tree.getroot()
            for obj in root.iter('object'):
                cls = obj.find('name').text
                if cls in num_dict.keys():
                    num_dict[cls] += 1
    return num_dict


# 排序
def sort_weigts(x: str):
    try:
        return int(x.split(".")[0].split("_")[-1])
    except Exception as e:
        print(e)
        return 0


if __name__ == '__main__':
    import keras.backend.tensorflow_backend as KTF
    import tensorflow as tf
    import keras

    os.environ["CUDA_VISIBLE_DEVICES"] = "1"  # 注意在import keras/tensorflow之前
    config = tf.ConfigProto()
    config.gpu_options.per_process_gpu_memory_fraction = 0.4
    session = tf.Session(config=config)

    KTF.set_session(session)
    keras.backend.clear_session()
    # 测试训练集或者测试集
    data = "t"
    # weights = "light_police_25002.weights"
    # weight_dir = "../weight_0608"
    if data == "train":
        weight_dir = "../test_weight"
    else:
        weight_dir = "../test_weight"
    # weight_dir = "../models/light_police_cp"
    w_list = os.listdir(weight_dir)
    temp = []
    if "light_police_train_last.weights" in w_list:
        w_list.remove("light_police_train_last.weights")
        temp.append("light_police_train_last.weights")
    if "light_police_train_final.weights" in w_list:
        w_list.remove("light_police_train_final.weights")
        temp.append("light_police_train_final.weights")
    w_list = sorted(w_list, key=sort_weigts) + temp
    # weight_dir = "../addblack_weight_0518"
    if data == "train":
        label_dir = "../voc_data/train_label"
    else:
        label_dir = "../light_test/Annotations"
    num_dict = get_all_label_num(label_dir=label_dir)
    # 保存各权重识别存在错误的图片名称和各类别准确率
    test_results = open("./light_test_results.txt", "w")
    cls_scores = open("./cls_scores.txt", "w")
    score_list = []
    cls_name = ["red_l", "red_s", "red_r", "green_l", "green_s", "green_r", "all"]
    # w_n = 0
    for weights in w_list:
        # w_name = weights.split(".")[0].split("_")[-1]
        w_name = weights.split(".")[0]

        if data == "train":
            judge_path = f"./light_train_results/{w_name}"
        else:
            judge_path = f"./light_vis_results/{w_name}"
        # if w_name == "last" and os.path.exists(judge_path):
        #     shutil.rmtree(judge_path)
        # elif os.path.exists(judge_path):
        #     print(f"{judge_path}已经存在")
        #     continue
        # w_n += 1
        # if w_n > 10:
        #     break
        total_right = {"red_l": 0, "red_s": 0, "red_r": 0, "green_l": 0, "green_s": 0, "green_r": 0, "all": 0}

        lp_net, imgsz, stride, device = load_lp(f"{weight_dir}/{weights}", trace=True)
        lp_meta = (imgsz, stride, device)

        weights_list = [lp_net, lp_meta]
        # img_dir = './light_data'
        # img_dir = './split_test'
        if data == "train":
            img_dir = '../voc_data/train_img'
            # img_dir = '../nj_test'
        else:
            img_dir = '../light_test/JPEGImages'
        # img_dir = './split_test'
        file_name = sorted(os.listdir(img_dir))
        # print('==', file_name)
        sum_num = len(file_name)
        # test_results.write(f"{w_name} 识别存在错误的图片：\n")
        # test_results.flush()
        import datetime

        tt_time = 0

        from ai_judge_code_gy.judge_tools import CUTIMG, SIMPLE_CUT

        for file in tqdm(file_name):
            img_path = os.path.join(img_dir, file)
            img = cv2.imread(img_path)
            print(file, "\n")
            # h, w, _ = img.shape
            # cx = w // 2
            # cy = (h - 140) // 2
            # img1 = img[:cy, :cx]
            # img2 = img[:cy, cx:]
            # img3 = img[cy:2 * cy, :cx]
            # file_list = [img1, img2, img3]
            # s_time = datetime.datetime.now()
            # add = file.split("_")[-2]
            # print(file,"===")
            # judge_infos = \
            #     light_detect(add, file_list, weights_list, weights_name=weights, light_threshold=0.3, resize_scale=0.7,
            #                  show=True,
            #                  )

            add = file
            s_time = datetime.datetime.now()
            judge_infos, all_right = \
                single_detect(add, img, weights_list, weights_name=weights, add_black=False, light_threshold=0.3,
                              show=True,
                              total_right=total_right, data=data)
            e_time = datetime.datetime.now()
            time_delta = round((e_time - s_time).seconds, 2)
            tt_time += time_delta
            print(total_right)
            # print("judge_infos:", judge_infos, 'use_ time:', time_delta)
            # if not all_right:
            #     test_results.write(f"{file}\n")
            #     test_results.flush()
            print("\n")
        # test_results.write("\n")
        right_list = list(total_right.values())
        num_list = list(num_dict.values())
        line = f"{w_name}"
        for idx, n in enumerate(right_list):
            score = round(n / num_list[idx], 3)
            if idx == len(right_list) - 1:
                score_list.append([weights, score])
            line += f"\t\t{cls_name[idx]}:{score}"
        test_results.write(line + "\n")
        cls_scores.write(line + "\n")
        test_results.flush()
        cls_scores.flush()
    score_list = sorted(score_list, key=lambda x: x[-1], reverse=True)
    test_results.write("\n")
    for i in score_list:
        test_results.write(f"{i[0]}:{i[1]}\n")
    test_results.close()
    cls_scores.close()
    # sum += 1
    #     score = round(right_num / sum_num, 3)
    #     if w_name.startswith("last") or w_name.startswith("final"):
    #         temp_list.append([w_name, score])
    #     else:
    #         score_list.append([int(w_name), score])
    #     print(f"{weights}的准确率：", score)
    #     error_file.close()
    # score_list = sorted(score_list) + temp_list
    # for i in score_list:
    #     score_file.write(f"{i[0]}" + ": " + f"{i[1]}" + "\n")
    #     score_file.flush()
    # score_file.close()

    # print('avg_prec:', n / sum)
    # print('avg_use_time:', tt_time / sum)
