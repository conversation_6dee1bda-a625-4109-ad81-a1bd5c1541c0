from multiprocessing import Process, Manager
import os
import time

def worker(shared_list):
    # 向共享列表添加元素
    print(shared_list)
    shared_list.append('Hello')
    print(shared_list)

    time.sleep(5)
if __name__ == '__main__':
    manager = Manager()
    shared_list = manager.list()  # 创建共享列表对象
    processes = []
    for i in range(5):
        p = Process(target=worker, args=(shared_list,))
        processes.append(p)
        p.start()

    for process in processes:
        process.join()

    # print("Shared List:", shared_list)