import os

import cv2
from init_models import *


def slice_det_plate(weights_list, img4):
    device = "cuda:0" if torch.cuda.is_available() else "cpu"
    plate_det_model = weights_list[0][0]
    plate_rec_model = weights_list[0][1]
    yolov7_model = weights_list[3][0]
    yolov7_meta = weights_list[3][1]
    plate_list = []
    # 检测车辆
    crop_img_list, car_bbox_ps, _ = yolov7_detect(img4, yolov7_model, yolov7_meta[0], yolov7_meta[1], yolov7_meta[2])
    for car in crop_img_list:
        dict_list = detect_Recognition_plate(plate_det_model, car, device, plate_rec_model, 640)
        for idx, res in enumerate(dict_list):
            plate_num = res["plate_no"]
            if len(plate_num) > 2:
                confidence = np.min(res["score"][2:])
            elif len(plate_num):
                confidence = np.min(res["score"])
            else:
                confidence = 0
            if plate_list == [] or (len(plate_list[0]) <= len(plate_num) and plate_list[1] <= confidence):
                plate_list = [plate_num, confidence]
    if not plate_list or (plate_list[0][0] not in chars and len(plate_list[0]) < 5):
        # 四图切分车辆识别车牌模糊匹配
        for num in [2, 3, 4, 5]:
            # 四图整图切分识别车牌模糊匹配
            h4 = img4.shape[0]
            slice = int(h4 // num)
            for j in range(num // 2, num):
                x_img = img4[j * slice:(j + 1) * slice]
                plate_num2, conf_arrary = get_plate_result(x_img, device, plate_rec_model)
                if len(plate_num2) > 2:
                    confidence2 = np.min(conf_arrary[2:])
                elif len(plate_num2):
                    confidence2 = np.min(conf_arrary)
                else:
                    confidence2 = 0
                if plate_list == [] or (len(plate_list[0]) <= len(plate_num2) and plate_list[1] <= confidence2):
                    plate_list = [plate_num2, confidence2]
    if plate_list == []:
        plate_list = ["", 0]
    return plate_list


if __name__ == '__main__':
    weights_list = init_models(traffic=False, lp=False, yolov7=True, trace=False, vio_model=False, cnn=False)
    root = "./add_plate"
    num = 1
    for type in os.listdir(root):
        type_dir = f"{root}/{type}"
        code = type.split("_")[-1]
        for name in tqdm(os.listdir(type_dir)):
            file_path = f"{type_dir}/{name}"
            img = cv2.imread(file_path)
            h, w, _ = img.shape
            cx = w // 2
            cy = h // 2
            img4 = img[cy:, cx:]
            print(name)
            plate, conf = slice_det_plate(weights_list, img4)
            os.rename(file_path, f"{type_dir}/{num}_{plate}_{code}.jpg")
