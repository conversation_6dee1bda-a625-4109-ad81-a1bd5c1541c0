2023-05-18 14:07:00,084 INFO    MainThread:200192 [wandb_setup.py:_flush():76] Current SDK version is 0.15.3
2023-05-18 14:07:00,084 INFO    MainThread:200192 [wandb_setup.py:_flush():76] Configure stats pid to 200192
2023-05-18 14:07:00,084 INFO    MainThread:200192 [wandb_setup.py:_flush():76] Loading settings from /root/.config/wandb/settings
2023-05-18 14:07:00,084 INFO    MainThread:200192 [wandb_setup.py:_flush():76] Loading settings from /home/<USER>/projects/ai_judge_simple/yolov7/wandb/settings
2023-05-18 14:07:00,084 INFO    MainThread:200192 [wandb_setup.py:_flush():76] Loading settings from environment variables: {}
2023-05-18 14:07:00,084 INFO    MainThread:200192 [wandb_setup.py:_flush():76] Applying setup settings: {'_disable_service': False}
2023-05-18 14:07:00,084 INFO    MainThread:200192 [wandb_setup.py:_flush():76] Inferring run settings from compute environment: {'program_relpath': 'train_aux.py', 'program': 'train_aux.py'}
2023-05-18 14:07:00,084 INFO    MainThread:200192 [wandb_setup.py:_flush():76] Applying login settings: {'api_key': '***REDACTED***'}
2023-05-18 14:07:00,085 INFO    MainThread:200192 [wandb_init.py:_log_setup():507] Logging user logs to /home/<USER>/projects/ai_judge_simple/yolov7/wandb/run-20230518_140700-g8e8ru0v/logs/debug.log
2023-05-18 14:07:00,085 INFO    MainThread:200192 [wandb_init.py:_log_setup():508] Logging internal logs to /home/<USER>/projects/ai_judge_simple/yolov7/wandb/run-20230518_140700-g8e8ru0v/logs/debug-internal.log
2023-05-18 14:07:00,086 INFO    MainThread:200192 [wandb_init.py:init():547] calling init triggers
2023-05-18 14:07:00,086 INFO    MainThread:200192 [wandb_init.py:init():555] wandb.init called with sweep_config: {}
config: {'weights': '../models/yolov7-e6e.pt', 'cfg': 'cfg/training/yolov7e6e.yaml', 'data': 'data/motor_helmet.yaml', 'hyp': {'lr0': 0.001, 'lrf': 0.2, 'momentum': 0.937, 'weight_decay': 0.0005, 'warmup_epochs': 3.0, 'warmup_momentum': 0.8, 'warmup_bias_lr': 1e-05, 'box': 0.05, 'cls': 0.3, 'cls_pw': 1.0, 'obj': 0.7, 'obj_pw': 1.0, 'iou_t': 0.2, 'anchor_t': 4.0, 'fl_gamma': 0.0, 'hsv_h': 0.5, 'hsv_s': 0.7, 'hsv_v': 0.4, 'degrees': 5.0, 'translate': 0.2, 'scale': 0.9, 'shear': 0.0, 'perspective': 0.001, 'flipud': 0.0, 'fliplr': 0.3, 'mosaic': 1.0, 'mixup': 0.5, 'copy_paste': 1.0, 'paste_in': 1.0}, 'epochs': 1000, 'batch_size': 15, 'img_size': [1280, 1280], 'rect': False, 'resume': False, 'nosave': False, 'notest': False, 'noautoanchor': False, 'evolve': False, 'bucket': '', 'cache_images': False, 'image_weights': True, 'device': '1,2,3', 'multi_scale': True, 'single_cls': False, 'adam': True, 'sync_bn': True, 'local_rank': -1, 'workers': 8, 'project': 'runs/train', 'entity': None, 'name': 'exp', 'exist_ok': False, 'quad': False, 'linear_lr': False, 'label_smoothing': 0.0, 'upload_dataset': False, 'bbox_interval': -1, 'save_period': -1, 'artifact_alias': 'latest', 'world_size': 1, 'global_rank': -1, 'save_dir': 'runs/train/exp8', 'total_batch_size': 15}
2023-05-18 14:07:00,086 INFO    MainThread:200192 [wandb_init.py:init():596] starting backend
2023-05-18 14:07:00,087 INFO    MainThread:200192 [wandb_init.py:init():600] setting up manager
2023-05-18 14:07:00,090 INFO    MainThread:200192 [backend.py:_multiprocessing_setup():108] multiprocessing start_methods=fork,spawn,forkserver, using: spawn
2023-05-18 14:07:00,093 INFO    MainThread:200192 [wandb_init.py:init():606] backend started and connected
2023-05-18 14:07:00,102 INFO    MainThread:200192 [wandb_init.py:init():700] updated telemetry
2023-05-18 14:07:00,103 INFO    MainThread:200192 [wandb_init.py:init():737] communicating run to backend with 60.0 second timeout
2023-05-18 14:07:28,713 INFO    Thread-2  :200192 [retry.py:__call__():172] Retry attempt failed:
Traceback (most recent call last):
  File "/opt/anaconda3/envs/adet_train/lib/python3.7/site-packages/urllib3/connection.py", line 175, in _new_conn
    (self._dns_host, self.port), self.timeout, **extra_kw
  File "/opt/anaconda3/envs/adet_train/lib/python3.7/site-packages/urllib3/util/connection.py", line 95, in create_connection
    raise err
  File "/opt/anaconda3/envs/adet_train/lib/python3.7/site-packages/urllib3/util/connection.py", line 85, in create_connection
    sock.connect(sa)
socket.timeout: timed out

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/opt/anaconda3/envs/adet_train/lib/python3.7/site-packages/urllib3/connectionpool.py", line 710, in urlopen
    chunked=chunked,
  File "/opt/anaconda3/envs/adet_train/lib/python3.7/site-packages/urllib3/connectionpool.py", line 386, in _make_request
    self._validate_conn(conn)
  File "/opt/anaconda3/envs/adet_train/lib/python3.7/site-packages/urllib3/connectionpool.py", line 1042, in _validate_conn
    conn.connect()
  File "/opt/anaconda3/envs/adet_train/lib/python3.7/site-packages/urllib3/connection.py", line 363, in connect
    self.sock = conn = self._new_conn()
  File "/opt/anaconda3/envs/adet_train/lib/python3.7/site-packages/urllib3/connection.py", line 182, in _new_conn
    % (self.host, self.timeout),
urllib3.exceptions.ConnectTimeoutError: (<urllib3.connection.HTTPSConnection object at 0x7f5dd5592750>, 'Connection to api.wandb.ai timed out. (connect timeout=10)')

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/opt/anaconda3/envs/adet_train/lib/python3.7/site-packages/requests/adapters.py", line 497, in send
    chunked=chunked,
  File "/opt/anaconda3/envs/adet_train/lib/python3.7/site-packages/urllib3/connectionpool.py", line 788, in urlopen
    method, url, error=e, _pool=self, _stacktrace=sys.exc_info()[2]
  File "/opt/anaconda3/envs/adet_train/lib/python3.7/site-packages/urllib3/util/retry.py", line 592, in increment
    raise MaxRetryError(_pool, url, error or ResponseError(cause))
urllib3.exceptions.MaxRetryError: HTTPSConnectionPool(host='api.wandb.ai', port=443): Max retries exceeded with url: /graphql (Caused by ConnectTimeoutError(<urllib3.connection.HTTPSConnection object at 0x7f5dd5592750>, 'Connection to api.wandb.ai timed out. (connect timeout=10)'))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/opt/anaconda3/envs/adet_train/lib/python3.7/site-packages/wandb/sdk/lib/retry.py", line 131, in __call__
    result = self._call_fn(*args, **kwargs)
  File "/opt/anaconda3/envs/adet_train/lib/python3.7/site-packages/wandb/sdk/internal/internal_api.py", line 285, in execute
    return self.client.execute(*args, **kwargs)  # type: ignore
  File "/opt/anaconda3/envs/adet_train/lib/python3.7/site-packages/wandb/vendor/gql-0.2.0/wandb_gql/client.py", line 52, in execute
    result = self._get_result(document, *args, **kwargs)
  File "/opt/anaconda3/envs/adet_train/lib/python3.7/site-packages/wandb/vendor/gql-0.2.0/wandb_gql/client.py", line 60, in _get_result
    return self.transport.execute(document, *args, **kwargs)
  File "/opt/anaconda3/envs/adet_train/lib/python3.7/site-packages/wandb/sdk/lib/gql_request.py", line 55, in execute
    request = self.session.post(self.url, **post_args)
  File "/opt/anaconda3/envs/adet_train/lib/python3.7/site-packages/requests/sessions.py", line 635, in post
    return self.request("POST", url, data=data, json=json, **kwargs)
  File "/opt/anaconda3/envs/adet_train/lib/python3.7/site-packages/requests/sessions.py", line 587, in request
    resp = self.send(prep, **send_kwargs)
  File "/opt/anaconda3/envs/adet_train/lib/python3.7/site-packages/requests/sessions.py", line 701, in send
    r = adapter.send(request, **kwargs)
  File "/opt/anaconda3/envs/adet_train/lib/python3.7/site-packages/requests/adapters.py", line 507, in send
    raise ConnectTimeout(e, request=request)
requests.exceptions.ConnectTimeout: HTTPSConnectionPool(host='api.wandb.ai', port=443): Max retries exceeded with url: /graphql (Caused by ConnectTimeoutError(<urllib3.connection.HTTPSConnection object at 0x7f5dd5592750>, 'Connection to api.wandb.ai timed out. (connect timeout=10)'))
2023-05-18 14:07:48,732 WARNING MainThread:200192 [wandb_init.py:init():1172] interrupted
Traceback (most recent call last):
  File "/opt/anaconda3/envs/adet_train/lib/python3.7/site-packages/wandb/sdk/wandb_init.py", line 1150, in init
    run = wi.init()
  File "/opt/anaconda3/envs/adet_train/lib/python3.7/site-packages/wandb/sdk/wandb_init.py", line 743, in init
    cancel=True,
  File "/opt/anaconda3/envs/adet_train/lib/python3.7/site-packages/wandb/sdk/lib/mailbox.py", line 283, in wait
    found, abandoned = self._slot._get_and_clear(timeout=wait_timeout)
  File "/opt/anaconda3/envs/adet_train/lib/python3.7/site-packages/wandb/sdk/lib/mailbox.py", line 130, in _get_and_clear
    if self._wait(timeout=timeout):
  File "/opt/anaconda3/envs/adet_train/lib/python3.7/site-packages/wandb/sdk/lib/mailbox.py", line 126, in _wait
    return self._event.wait(timeout=timeout)
  File "/opt/anaconda3/envs/adet_train/lib/python3.7/threading.py", line 552, in wait
    signaled = self._cond.wait(timeout)
  File "/opt/anaconda3/envs/adet_train/lib/python3.7/threading.py", line 300, in wait
    gotit = waiter.acquire(True, timeout)
KeyboardInterrupt
