import os
import shutil
from tqdm import tqdm

if os.path.exists("../test_dir/pred_result"):
    shutil.rmtree("../test_dir/pred_result")
    print("pred_result已经存在，已初始化目录！")
split_num = 3
root = "../test_dir/test_data"
all_data = os.listdir(root)
all_num = len(all_data)
one_split = all_num // split_num
for i in range(split_num):
    if i < split_num - 1:
        data = all_data[one_split * i:one_split * (i + 1)]
    else:
        data = all_data[one_split * i:]

    to_dir = f"../test_dir/split_data{i + 1}"
    if os.path.exists(to_dir):
        shutil.rmtree(to_dir)
        print(f"{to_dir}已经存在，已初始化目录！")
    os.makedirs(to_dir, exist_ok=True)
    for j in tqdm(data):
        shutil.copy(f"{root}/{j}", to_dir)
print("finished!")
