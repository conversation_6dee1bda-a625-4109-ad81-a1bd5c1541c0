import importlib.util
import os


def load_config_from_file(file_path):
    """
    从指定路径加载 Python 配置文件并返回其参数字典。
    """
    if not os.path.exists(file_path):
        raise FileNotFoundError(f"配置文件不存在: {file_path}")

    # 动态加载模块
    module_name = os.path.splitext(os.path.basename(file_path))[0]
    spec = importlib.util.spec_from_file_location(module_name, file_path)
    module = importlib.util.module_from_spec(spec)
    spec.loader.exec_module(module)

    # 提取模块中的所有变量
    config = {
        key: value
        for key, value in vars(module).items()
        if not key.startswith("__")  # 忽略特殊属性（如 __name__, __doc__）
    }
    return config


def compare_configs(base_config, target_config):
    """
    比较两个配置文件的参数差异。
    """
    differences = {
        "missing_keys": [],  # 目标配置中缺少的键
        "extra_keys": [],  # 目标配置中多余的键
        "value_differences": []  # 值不同的键
    }

    # 检查缺失的键
    for key in base_config:
        if key not in target_config:
            differences["missing_keys"].append(key)

    # 检查多余的键
    for key in target_config:
        if key not in base_config:
            differences["extra_keys"].append(key)

    # 检查值不同的键
    for key in base_config:
        if key in target_config and base_config[key] != target_config[key]:
            differences["value_differences"].append((key, base_config[key], target_config[key]))

    return differences


def main(target_config_path,base_config_path="../main_config/config.py"):
    # 加载配置
    base_config = load_config_from_file(base_config_path)
    target_config = load_config_from_file(target_config_path)

    # 比较配置
    differences = compare_configs(base_config, target_config)

    # 输出差异
    if any(differences.values()):
        print("发现以下差异：")
        if differences["missing_keys"]:
            print("\n目标配置中缺失的键：")
            for key in differences["missing_keys"]:
                print(f"  - {key}")

        if differences["extra_keys"]:
            print("\n目标配置中多余的键：")
            for key in differences["extra_keys"]:
                print(f"  - {key}")

        if differences["value_differences"]:
            print("\n值不同的键：")
            for key, base_value, target_value in differences["value_differences"]:
                print(f"  - 键: {key}, 基准值: {base_value}, 待同步值: {target_value}")
    else:
        print("配置文件一致，无需同步。")


if __name__ == "__main__":
    main("../main_config/config_cl.py")