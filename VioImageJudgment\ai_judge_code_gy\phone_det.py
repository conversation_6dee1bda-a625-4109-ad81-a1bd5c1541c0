import os

import cv2
from tqdm import tqdm

from init_models import init_models
from yolov7.yolo_interface import yolov7_detect

save_path = "../test_results/phone"
os.makedirs(save_path,exist_ok=True)
img_root = "../dev_pics/1223"
yolo_model,yolo_meta = init_models(False,False)[3]
img_size, stride, device = yolo_meta
for name in tqdm(os.listdir(img_root)):
    path = f"{img_root}/{name}"
    img = cv2.imread(path)
    # 识别车
    veh_list, veh_boxes, _ = yolov7_detect(img,yolo_model,img_size, stride, device,conf_thres=0.2,classes= ["car", "truck", "bus"])
    for idx,veh in enumerate(veh_list):
        crop_list, box_list, label_list = yolov7_detect(veh,yolo_model,img_size, stride, device,conf_thres=0.1,classes=["cell phone"])
        print("res:",box_list)
        for box in box_list:
            pt1 = (box[0]+veh_boxes[idx][0],box[1]+veh_boxes[idx][1])
            pt2 = (box[2]+veh_boxes[idx][0],box[3]+veh_boxes[idx][1])

            cv2.rectangle(img,pt1,pt2,(0,255,0),2)
    cv2.imwrite(f"{save_path}/{name}",img)

