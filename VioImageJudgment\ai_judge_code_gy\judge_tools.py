import cv2
import numpy as np
import torch
import torchvision.transforms as transforms
from PIL import Image, ImageDraw, ImageFont
from main_config.config import spec_cars_list, Police_Plate_Thresh, BLACK_HEIGHT, Special_Type
import math


# 转换模型检测结果的坐标到原始图片
def convert_src_coord(box, single_img, img_id, split_mode, black_height_rate, black_pos):
    h, w, _ = single_img.shape
    x1 = box[0]
    y1 = box[1]
    x2 = box[2]
    y2 = box[3]

    if split_mode == 111:
        src_h = h
        src_w = w
    elif split_mode in [211, 311]:
        src_h = h
        src_w = w * 2 if split_mode == 211 else w * 3
        x1 = x1 + (img_id - 1) * w
        x2 = x2 + (img_id - 1) * w
    elif split_mode in [221, 321]:
        src_h = h * 2 if split_mode == 221 else h * 3
        src_w = w
        y1 = y1 + (img_id - 1) * h
        y2 = y2 + (img_id - 1) * h
    elif split_mode == 312:
        src_h = h * 2
        src_w = w if img_id == 3 else w * 2
        if img_id == 2:
            x1 = x1 + w
            x2 = x2 + w
        elif img_id == 3:
            y1 = y1 + h
            y2 = y2 + h
    elif split_mode == 313:
        src_h = h * 2
        src_w = w if img_id == 1 else w * 2
        if img_id == 2:
            y1 = y1 + h
            y2 = y2 + h
        elif img_id == 3:
            x1 = x1 + w
            x2 = x2 + w
            y1 = y1 + h
            y2 = y2 + h
    elif split_mode == 322:
        src_h = h if img_id == 3 else h * 2
        src_w = w * 2
        if img_id == 2:
            y1 = y1 + h
            y2 = y2 + h
        elif img_id == 3:
            x1 = x1 + w
            x2 = x2 + w
    elif split_mode == 323:
        src_h = h if img_id == 1 else h * 2
        src_w = w * 2
        if img_id == 2:
            x1 = x1 + w
            x2 = x2 + w
        elif img_id == 3:
            x1 = x1 + w
            x2 = x2 + w
            y1 = y1 + h
            y2 = y2 + h
    elif split_mode in [411, 611, 621]:
        try:
            src_h = h * 3 / (1 - black_height_rate) if split_mode == 621 else h * 2 / (1 - black_height_rate)
        except:
            src_h = h * 3 if split_mode == 621 else h * 2
        src_w = w * 3 if split_mode == 611 else w * 2
        if black_pos == "up":
            black_area_height = src_h * black_height_rate
            if img_id == 1:
                y1 = y1 + black_area_height
                y2 = y2 + black_area_height
            elif img_id == 2:
                x1 = x1 + w
                x2 = x2 + w
                y1 = y1 + black_area_height
                y2 = y2 + black_area_height
            elif img_id == 3:
                y1 = y1 + h + black_area_height
                y2 = y2 + h + black_area_height
            elif img_id == 4:
                x1 = x1 + w
                x2 = x2 + w
                y1 = y1 + h + black_area_height
                y2 = y2 + h + black_area_height
            else:
                x1 = x2 = y1 = y2 = 0
        # 黑框在下或无黑框
        else:
            if img_id == 1:
                pass
            elif img_id == 2:
                x1 = x1 + w
                x2 = x2 + w
            elif img_id == 3:
                y1 = y1 + h
                y2 = y2 + h
            elif img_id == 4:
                x1 = x1 + w
                x2 = x2 + w
                y1 = y1 + h
                y2 = y2 + h
            else:
                x1 = x2 = y1 = y2 = 0
    else:
        print("Error！拼接方式未知:", split_mode)
        return []
    src_coord_percent = [x1 / src_w, y1 / src_h, x2 / src_w, y2 / src_h]
    return [round(i, 3) for i in src_coord_percent]


class VIO_JUDGE_TOOL():
    def __init__(self):
        pass

    # 特殊车牌判定
    def spe_no_vio_judge(self, src_plate_num, iieaFilterVehicle):
        # iiea警车配置
        try:
            police_filter = iieaFilterVehicle[0]
        except:
            police_filter = None
        # iiea消防车配置
        try:
            fire_filter = iieaFilterVehicle[2]
        except:
            fire_filter = None
        vio_judge = 'no_judge'
        if src_plate_num.startswith('无') or src_plate_num.startswith('未'):
            vio_judge = 'no_vio_009'
        if src_plate_num.endswith('警') and ((Police_Plate_Thresh >= 0 and police_filter is None) or police_filter):
            vio_judge = 'no_vio_010'
        if src_plate_num.endswith('应急') and len(src_plate_num) >= 2 and src_plate_num[1] in ["X", "S"] and (
                ("fire_engine_b" in Special_Type or "fire_engine_h" in Special_Type) or fire_filter):
            vio_judge = 'no_vio_010'

        #  src_plate_num.endswith('港')

        return vio_judge


class Tools():
    def __init__(self):
        pass

    # ***自动↓切分***
    def find_bounding(self, img, row_or_col):  # img为灰度图；row_or_col 值为0或1；取值为0代表计算水平拼接图像数,取值为1代表计算竖直拼接图像数
        if row_or_col == 0:
            sub = np.sum(np.abs(img[:, :-1] / 255 - img[:, 1:] / 255), row_or_col)
            length = img.shape[1]
        if row_or_col == 1:
            sub = np.sum(np.abs(img[:-1, :] / 255 - img[1:, :] / 255), row_or_col)
            length = img.shape[0]
        sub_norm = (sub - np.mean(sub)) / np.std(sub)
        peak_max = np.max(sub_norm)
        thresh = 5.5
        if peak_max < thresh:
            return [], 0
        else:
            peak_loc = np.where(sub_norm >= thresh)[0]
            peak_arr = np.array(peak_loc + 1)
            return peak_arr, length

    def cut_loc(self, p_l, length, mode=0):
        idx = np.zeros(len(p_l))
        p_l_norm = [x / length for x in p_l]  # /length

        for i in range(len(p_l)):
            if 0.03 < p_l_norm[i] <= 0.12:
                if mode == 1:
                    idx[i] = -1
            elif 0.97 > p_l_norm[i] >= 0.88:
                if mode == 1:
                    idx[i] = -2
            elif (p_l_norm[i] > 0.2) and (p_l_norm[i] < 0.4167):
                idx[i] = 1
            elif (p_l_norm[i] >= 0.4167) and (p_l_norm[i] < 0.583):
                idx[i] = 2
            elif (p_l_norm[i] >= 0.583) and (p_l_norm[i] < 0.8):
                idx[i] = 3
        cut_loc_l = []
        idx_list = [np.where(idx == 1)[0], np.where(idx == 2)[0], np.where(idx == 3)[0]]
        num_list = [len(i) for i in idx_list]
        idx[idx_list[np.argmin(num_list)]] = 0

        cut_1 = np.where(idx == -1)[0]
        cut_2 = np.where(idx == -2)[0]
        cut_loc1 = 0
        cut_loc2 = length
        if len(cut_1) > 0:
            cut_loc1 = np.max(p_l[cut_1])
        if len(cut_2) > 0:
            cut_loc2 = np.min(p_l[cut_2])
        if cut_loc1 > length - cut_loc2:
            cut_loc_l.append([cut_loc1, -1])
        if cut_loc1 < length - cut_loc2 and cut_loc2 > 0:
            cut_loc_l.append([cut_loc2, -2])

        for i in [2, 1, 3]:
            cut = np.where(idx == i)[0]
            if len(cut) > 0:
                status = i
                cut_loc_ = np.max(p_l[cut])
                cut_loc_l.append([cut_loc_, status])
                if i == 2:
                    break

        return cut_loc_l

    def cut(self, img_np, vio_code=None, show=False):
        img = img_np
        img_gray = cv2.cvtColor(img_np, cv2.COLOR_BGR2GRAY)
        # 检测水平拼接数目
        p_l_h, w = self.find_bounding(img_gray, 0)
        cut_loc_hor = np.array(self.cut_loc(p_l_h, w))

        # 检测竖直拼接数目
        p_l_v, h = self.find_bounding(img_gray, 1)
        cut_loc_ver = np.array(self.cut_loc(p_l_v, h, 1))
        if len(cut_loc_hor) == 0:
            cut_loc_hor = np.array([[0, 0]])
        if len(cut_loc_ver) == 0:
            cut_loc_ver = np.array([[0, 0]])
        # print("\n", cut_loc_hor, "\n", img.shape, cut_loc_ver, "\n")
        H, W, _ = img.shape
        if show:
            for hor in cut_loc_hor:
                cv2.line(img, (hor[0], 0), (hor[0], H), (0, 0, 255), 2)
                cv2.putText(img, str(hor[1]), (hor[0] + 10, H // 2), cv2.FONT_HERSHEY_SIMPLEX, 2, (0, 255, 0, 2), 2)

            for ver in cut_loc_ver:
                cv2.line(img, (0, ver[0]), (W, ver[0]), (0, 0, 255), 2)
                cv2.putText(img, str(ver[1]), (W // 2, ver[0]), cv2.FONT_HERSHEY_SIMPLEX, 2, (0, 255, 0, 2), 2)

        ver_status = cut_loc_ver[:, 1]
        ver_value = cut_loc_ver[:, 0]

        hor_status = cut_loc_hor[:, 1]

        top_black = ver_value[np.where(ver_status == -1)[0]].tolist()
        bottom_black = ver_value[np.where(ver_status == -2)[0]].tolist()
        split_height = ver_value[np.where(ver_status == 2)[0]].tolist()
        res_black_height = 0
        split_mode = 111
        error_judge = False
        black_pos = None
        if 2 in ver_status:
            split_mode = 221
            if top_black and bottom_black:
                res_black_height = 0
            elif top_black:
                black_pos = "up"
                split_mode = 411
                # 按黑框的结果得出的单图高度
                single_img_height = (H - top_black[0]) / 2
                # 按切分计算结果得出的上下两图高度
                single_up_img_height = split_height[0] - top_black[0]
                single_down_img_height = H - split_height[0]
                # 黑框高度
                black_height = top_black[0]
                # 按切分计算结果得出的上下两图高度
                split_black_height = 2 * split_height[0] - H
                if split_black_height < 0:
                    black_pos = None
                    error_judge = True
                # 黑框差值比
                black_dis_rate = (black_height - split_black_height) / H

                res_black_height = black_height
                print(black_height, split_black_height, round(black_dis_rate, 4))
                if 0 < split_black_height < 0.01 * H:
                    print("上黑框已修正：", black_height, "->", split_black_height)
                    res_black_height = split_black_height
                else:
                    # 上黑框包含了每张图片都带的黑框
                    # if split_height[0] > 0.5 * H and (
                    # single_img_height < single_down_img_height or single_up_img_height < single_down_img_height):
                    if split_height[
                        0] > 0.5 * H and black_dis_rate > 0.008 and 1.1 < black_height / split_black_height < 1.9:
                        print("上黑框高度:", black_height)
                        res_black_height = split_black_height
                        print("修正上黑框高度", split_black_height)
            elif bottom_black:
                black_pos = "down"
                split_mode = 411
                # 按黑框的结果得出的单图高度
                # single_img_height = bottom_black[0] / 2
                # 黑框高度
                black_height = H - bottom_black[0]
                split_black_height = H - 2 * split_height[0]
                if split_black_height < 0:
                    black_pos = None
                    error_judge = True

                # 黑框差值比
                black_dis_rate = (black_height - split_black_height) / H
                res_black_height = black_height
                print(black_height, split_black_height, round(black_dis_rate, 4))
                if black_dis_rate > 0.008 and 0 < split_black_height < 0.01 * H:
                    print("下黑框已修正：", black_height, "->", split_black_height)

                    res_black_height = split_black_height
                else:
                    # 下黑框包含了每张图片都带的黑框
                    if split_height[
                        0] < 0.5 * H and black_dis_rate > 0.008 and 1.1 < black_height / split_black_height < 1.9:
                        print("下黑框高度:", black_height)
                        res_black_height = split_black_height
                        print("修正下黑框高度:", split_black_height)
            # 6图横拼
            if error_judge:
                pass
            elif 1 in hor_status and 3 in hor_status:
                split_mode = 611
                if top_black:
                    black_pos = "up"
                else:
                    black_pos = "down"


        elif 1 in ver_status:
            split_mode = 321
            # 上2下1
            if 2 in hor_status:
                split_mode = 312
            if 1 in hor_status or 3 in hor_status:
                split_mode = 111

        elif 3 in ver_status:
            split_mode = 321
            if 1 in hor_status or 3 in hor_status:
                split_mode = 111
        # 二图横拼
        elif 2 in hor_status:
            split_mode = 211
        # 三图横拼
        elif 1 in hor_status and 3 in hor_status:
            split_mode = 311
        elif W > H * 4:
            split_mode = 211

        # 根据违法类型修正拼接方式
        if vio_code in ["1625", "1208"] and split_mode not in [411, 611]:
            split_mode = 411
        return split_mode, res_black_height / H, black_pos

    # ***自动↑切分***

    # 交并比计算，可用于计算车辆重叠度与检测框的重叠度
    def IOU(self, tl1, br1, tl2, br2, is_min_union=True):
        wh1, wh2 = br1 - tl1, br2 - tl2
        assert ((wh1 >= .0).all() and (wh2 >= .0).all())
        intersection_wh = np.maximum(np.minimum(br1, br2) - np.maximum(tl1, tl2), 0.)
        intersection_area = np.prod(intersection_wh)
        area1, area2 = (np.prod(wh1), np.prod(wh2))
        # if intersection_area > 0:
        # 大包小判断
        # x_range1 = set([i for i in range(int(tl1[0]), int(br1[0]))])
        # x_range2 = set([i for i in range(int(tl2[0]), int(br2[0]))])
        # y_range1 = set([i for i in range(int(tl1[1]), int(br1[1]))])
        # y_range2 = set([i for i in range(int(tl2[1]), int(br2[1]))])
        # big_cover_small = False
        # if (x_range1.issuperset(x_range2) and y_range1.issuperset(y_range2)) or (
        #         x_range2.issuperset(x_range1) and y_range2.issuperset(y_range1)):
        #     big_cover_small = True
        # union_area = np.min(area1, area2) if big_cover_small else area1 + area2 - intersection_area
        #     return intersection_area / union_area
        # else:
        #     return 0
        if is_min_union:
            union_area = max(min(area1, area2), 1e-8)
        else:
            union_area = max(area1 + area2 - intersection_area, 1e-8)
        return intersection_area / union_area

    def compute_iou(self, boxes1, boxes2):
        """[Compute pairwise IOU matrix for given two sets of boxes]

        Args:
            boxes1 ([numpy ndarray with shape N,4]): [representing bounding boxes with format (xmin,ymin,xmax,ymax)]
            boxes2 ([numpy ndarray with shape M,4]): [representing bounding boxes with format (xmin,ymin,xmax,ymax)]
        Returns:
            pairwise IOU maxtrix with shape (N,M)，where the value at ith row jth column hold the iou between ith
            box and jth box from box1 and box2 respectively.
        """
        boxes1 = np.asarray(boxes1)
        boxes2 = np.asarray(boxes2)

        lu = np.maximum(boxes1[:, None, :2],
                        boxes2[:,
                        :2])  # lu with shape N,M,2 ; boxes1[:,None,:2] with shape (N,1,2) boxes2 with shape(M,2)
        rd = np.minimum(boxes1[:, None, 2:4], boxes2[:, 2:4])  # rd same to lu
        intersection_wh = np.maximum(0.0, rd - lu)
        intersection_area = intersection_wh[:, :, 0] * intersection_wh[:, :, 1]  # with shape (N,M)
        boxes1_wh = np.maximum(0.0, boxes1[:, 2:4] - boxes1[:, :2])
        boxes1_area = boxes1_wh[:, 0] * boxes1_wh[:, 1]  # with shape (N,)
        boxes2_wh = np.maximum(0.0, boxes2[:, 2:4] - boxes2[:, :2])
        boxes2_area = boxes2_wh[:, 0] * boxes2_wh[:, 1]  # with shape (M,)
        union_area = np.maximum(boxes1_area[:, None] + boxes2_area - intersection_area, 1e-8)  # with shape (N,M)
        ious = np.clip(intersection_area / union_area, 0.0, 1.0)
        return ious

    @staticmethod
    def filter_boxes_by_iou(boxA, boxesB, iou_threshold=0.75) -> list:
        # boxA: [x1, y1, x2, y2] 单个边界框
        # boxesB: shape (n, 4) 多个边界框，格式为 [x1, y1, x2, y2]
        # 扩展 boxA 以匹配 boxesB 的形状
        boxA = np.asarray(boxA)
        boxesB = np.asarray(boxesB)

        # 扩展 boxA 以匹配 boxesB 的形状
        boxA = np.expand_dims(boxA, axis=0)

        # 计算交集的坐标
        interSec = np.maximum(np.minimum(boxA[:, 2:], boxesB[:, 2:]) - np.maximum(boxA[:, :2], boxesB[:, :2]) + 1, 0)
        interArea = interSec[:, 0] * interSec[:, 1]

        # 计算两个边界框的面积
        areaA = (boxA[:, 2] - boxA[:, 0] + 1) * (boxA[:, 3] - boxA[:, 1] + 1)
        areaB = (boxesB[:, 2] - boxesB[:, 0] + 1) * (boxesB[:, 3] - boxesB[:, 1] + 1)

        # 计算并集的面积
        unionArea = areaA + areaB - interArea

        # 避免除以零的情况
        unionArea = np.where(unionArea == 0, 1e-9, unionArea)

        # 计算IoU
        ious = interArea / unionArea

        # 找到IoU大于阈值的索引
        valid_indices = np.where(ious > iou_threshold)[0]

        if len(valid_indices) == 0:
            return []

        # 获取满足条件的边界框及其对应的IoU值
        valid_ious = ious[valid_indices]

        # 找到最大IoU值的索引
        max_iou_index = valid_indices[np.argmax(valid_ious)]

        # 返回具有最大IoU的边界框
        return boxesB[max_iou_index].tolist()

    # 如果图片存在形状不一致则进行裁剪保持一样
    def resize_imgs(self, img1, img2, img3=None):
        try:
            # print('====', img1.shape, img2.shape, img3.shape)
            while not img1.shape == img2.shape == img3.shape:
                if int(img1.shape[0]) > int(img2.shape[0]) * 1.5:
                    img2 = cv2.resize(img2, (img1.shape[1], img1.shape[0]))
                    img3 = cv2.resize(img3, (img1.shape[1], img1.shape[0]))
                else:
                    img1_h, img1_w = int(img1.shape[0]), int(img1.shape[1])
                    img2_h, img2_w = int(img2.shape[0]), int(img2.shape[1])
                    img3_h, img3_w = int(img3.shape[0]), int(img3.shape[1])
                    h_list = [img1_h, img2_h, img3_h]
                    w_list = [img1_w, img2_w, img3_w]
                    min_h = min(h_list)
                    min_w = min(w_list)
                    img1 = img1[:min_h, :min_w, :]
                    img2 = img2[:min_h, :min_w, :]
                    img3 = img3[:min_h, :min_w, :]
            # print('====', img1.shape, img2.shape, img3.shape)
        except:
            while not img1.shape == img2.shape:
                if int(img1.shape[0]) > int(img2.shape[0]) * 1.5:
                    img2 = cv2.resize(img2, (img1.shape[1], img1.shape[0]))
                else:
                    img1_h, img1_w = int(img1.shape[0]), int(img1.shape[1])
                    img2_h, img2_w = int(img2.shape[0]), int(img2.shape[1])
                    min_h = min([img1_h, img2_h])
                    min_w = min([img1_w, img2_w])
                    img1 = img1[:min_h, :min_w, :]
                    img2 = img2[:min_h, :min_w, :]
        return img1, img2, img3

    # 将图片转换为hist格式
    def create_rgb_hist(self, image):
        h, w, ch = image.shape
        rgbHist = np.zeros([16 * 16 * 16, 1], np.float32)
        bsize = 16
        for row in range(h):
            for col in range(w):
                b = image[row, col, 0]
                g = image[row, col, 1]
                r = image[row, col, 2]
                index = np.int(b / bsize) * 16 * 16 + np.int(g / bsize) * 16 + np.int(r / bsize)
                # 下降成16个bin，由256*256*256降维4096
                # print('index:',index)
                rgbHist[np.int(index), 0] += 1
        return rgbHist

    # 随机选取匹配车牌，模糊匹配
    def random_choice(self, plate_num, src_plate_num, random_list):
        if random_list == [None, None]:
            return False
        offset_judge = False
        if len(src_plate_num) == len(plate_num):
            offset_judge = True
            error_num = 0
            for i in range(len(plate_num)):
                if i != 0 and plate_num[i] != src_plate_num[i]:
                    # 排除易出错字符
                    if (plate_num[i] in ["Z", "2"] and src_plate_num[i] in ["Z", "2"]) or (
                            plate_num[i] in ["D", "0"] and src_plate_num[i] in ["D", "0"]) or (
                            plate_num[i] in ["S", "5"] and src_plate_num[i] in ["S", "5"]) or (
                            plate_num[i] in ["8", "B"] and src_plate_num[i] in ["8", "B"]):
                        continue
                    error_num += 1
            if error_num <= random_list[1]:
                return True
        if offset_judge:
            plate_num = plate_num[2:]
        src_plate_num = src_plate_num[2:]
        n = len(plate_num)
        if n > 2:
            num = random_list[0]
            random_5 = n - num
            index = [i for i in range(random_5)]
            for i in range(int(len(index) + 1)):
                temp_plate = plate_num[i:i + num]
                # 允许连续三位匹配车牌的起始索引相差小于2
                if temp_plate in src_plate_num and (
                        not offset_judge or (abs(src_plate_num.index(temp_plate) - i) < 2 and offset_judge)):
                    return True
        return False

    # 未使用
    def confidence_plate_match(self, plate_num, src_plate_num):
        n = min(len(plate_num), len(src_plate_num))
        error_num = 0
        for i in range(n):
            if plate_num[i] != src_plate_num[i]:
                error_num += 1
        return round(error_num / len(plate_num), 3)

    # 图片融合
    def merge_imgs(self, img_list):
        merge_img = np.zeros((200, 200 * len(img_list), 3))
        for i, img in enumerate(img_list):
            img_temp = cv2.resize(img, (200, 200))
            merge_img[:, 200 * i:200 * (i + 1), :] = img_temp
        return merge_img

    # 分割拼接图为横向4分图（默认为横向1234） 横向1234拼接或者竖向1234根据情况进行排序与删除
    def break_img_4(self, img_np):
        img = img_np
        img_h, img_w = img.shape[0], img.shape[1]
        split_x, split_y = int(img_w / 2), int(img_h / 2) - 30
        ps1_img = img[0:split_y, 0:split_x, :]
        ps2_img = img[0:split_y, split_x:, :]
        ps3_img = img[split_y:, 0:split_x, :]
        ps4_img = img[split_y:, split_x:, :]
        img_np_list = [ps1_img, ps2_img, ps3_img, ps4_img]
        return img_np_list, float(split_x), float(split_y)

    # 合并blendmask detect出的mask信息
    def merge_masks(self, roi_cor, img_list, label=0, img_shape=None):
        try:
            temp_img = np.zeros(img_list[0].shape)
        except:
            temp_img = np.zeros(img_shape)
        ###            print('cannot get any shape from input and applied set shape')
        # cv2.imshow('orign_img', cv2.resize(temp_img, None, fx=0.3, fy=0.3))
        for i, img in enumerate(img_list):
            if not temp_img.shape == img.shape:
                img1_w = int(temp_img.shape[1])
                img2_w = int(img.shape[1])
                img1_h = int(temp_img.shape[0])
                img2_h = int(img.shape[0])
                w_list = [img1_w, img2_w]
                h_list = [img1_h, img2_h]
                min_w = min(w_list)
                min_h = min(h_list)
                img = img[:min_h, :min_w]
                temp_img = temp_img[:min_h, :min_w]
            temp_img = np.where(temp_img < img, img, temp_img)

        if label:
            x1, y1, x2, y2 = roi_cor
            temp_img = temp_img[y1:y2, x1:x2]
        # =======================================================
        # if save:
        #     skimage.io.imsave(pro_lane_path, temp_img)
        # =======================================================

        temp_img = np.where(temp_img == 0, 0, 255)
        return temp_img

    def show_Car(self, img_path, car_ps):
        img = cv2.imread(img_path)
        x1, y1, x2, y2 = car_ps
        temp_img = img[(y1):(y2), (x1):(x2)]
        cv2.imshow('a', temp_img)
        cv2.waitKey()
        cv2.destroyAllWindows()

    def find_contours(self, mask_pic, epsilon=0.7):
        gray = mask_pic
        ret, thresh = cv2.threshold(gray, 0, 200, 0)
        contours, _ = cv2.findContours(thresh, cv2.RETR_TREE,
                                       cv2.CHAIN_APPROX_SIMPLE)
        contour = np.array([])
        if len(contours):
            # 轮廓面积筛选
            area_list = []
            for i in contours:
                area_list.append(cv2.contourArea(i))
            idx = np.argmax(area_list)
            # 轮廓近似
            approx = cv2.approxPolyDP(contours[idx], epsilon, True)
            # print(contours[idx].shape, approx.shape)
            contour = np.squeeze(approx)

        return contour.tolist()

    # 从mask转换为位置坐标信息
    def get_polygon_from_mask(self, demo, mask, label=0):
        # x,y = np.where(mask>0)
        # print("mask",len(x),len(y),mask.shape)
        polygons, has_hole = demo.mask_to_polygons(mask, mask.shape[0], mask.shape[1])
        # print("old:",polygons[0].shape)
        # mask_pic = np.array(mask, dtype=np.uint8) * 255
        # contour = self.find_contours(mask_pic,0.2)
        # print("new:",len(contour))
        ps_list = []
        if label == 0:
            for polygon in polygons:
                for ps_num in range(int(len(polygon) / 2)):
                    x, y = polygon[ps_num * 2], polygon[ps_num * 2 + 1]
                    ps_list.append((int(x), int(y)))
        else:
            for polygon in polygons:
                temp_list = []
                for ps_num in range(int(len(polygon) / 2)):
                    x, y = polygon[ps_num * 2], polygon[ps_num * 2 + 1]
                    temp_list.append((int(x), int(y)))
                ps_list.append(temp_list)
        # bg = np.zeros((3000,3000,3),dtype=np.uint8)
        # for point in ps_list:
        #     cv2.circle(bg,point,1,(255,255,255),2)
        # if ps_list:
        #     cv2.imwrite("bg.jpg",bg)
        #     exit()
        return ps_list

    # 获取位置坐标信息
    def get_polygon(self, polygons):
        ps_list = []
        n = 0
        for polygon in polygons:
            temp_list = []
            for ps_num in range(int(len(polygon) / 2)):
                x, y = polygon[ps_num * 2], polygon[ps_num * 2 + 1]
                temp_list.append((int(x), int(y)))
            n += 1
            ps_list.append(temp_list)
        # ps_list = ps_list[0]

        # bg = np.zeros((3000, 3000, 3), dtype=np.uint8)
        # for idx,temp_list in enumerate(ps_list):
        #     bg1 = bg.copy()
        #     for point in temp_list:
        #         cv2.circle(bg1, point, 1, (255, 255, 255), 2)
        #     if temp_list:
        #         cv2.imwrite(f"bg{idx}.jpg", bg1)
        # exit()
        return ps_list

    # 获取目标车辆周围区域
    def area_pro(self, img_np, car1_ps, car2_ps, car3_ps=None):
        # img = cv2.imread(img_path)
        img = img_np
        if car3_ps:
            car1_ps, car2_ps, car3_ps = sorted([car1_ps, car2_ps, car3_ps], reverse=True)
        else:
            car1_ps, car2_ps = sorted([car1_ps, car2_ps], reverse=True)
        if car1_ps and len(car2_ps) == 0:
            car1_width = int(car1_ps[2] - car1_ps[0])
            car1_height = int(car1_ps[3] - car1_ps[1])
            x1 = max(0, car1_ps[0] - car1_width)
            y1 = max(0, car1_ps[1] - car1_height)
            x2 = min(img.shape[1], car1_ps[2] + car1_width)
            y2 = min(img.shape[0], car1_ps[3] + car1_height)
        else:
            car1_width = int(car1_ps[2] - car1_ps[0])
            car1_height = int(car1_ps[3] - car1_ps[1])
            car2_width = int(car2_ps[2] - car2_ps[0])
            car2_height = int(car2_ps[3] - car2_ps[1])
            car_width = int((car1_width + car2_width) / 2)
            car_height = int((car1_height + car2_height) / 2)

            car_ps_x1 = min(car1_ps[0], car2_ps[0])
            car_ps_y1 = min(car1_ps[1], car2_ps[1])
            car_ps_x2 = max(car1_ps[2], car2_ps[2])
            car_ps_y2 = max(car1_ps[3], car2_ps[3])

            if car3_ps:
                car3_width = int(car3_ps[2] - car3_ps[0])
                car3_height = int(car3_ps[3] - car3_ps[1])
                car_width = int((car1_width + car2_width + car3_width) / 3)
                car_height = int((car1_height + car2_height + car3_height) / 3)
                car_ps_x1 = min(car1_ps[0], car2_ps[0], car3_ps[0])
                car_ps_y1 = min(car1_ps[1], car2_ps[1], car3_ps[1])
                car_ps_x2 = max(car1_ps[2], car2_ps[2], car3_ps[2])
                car_ps_y2 = max(car1_ps[3], car2_ps[3], car3_ps[3])

            x1 = max(0, car_ps_x1 - car_width)
            y1 = max(0, car_ps_y1 - car_height)
            x2 = min(img.shape[1], car_ps_x2 + car_width)
            y2 = min(img.shape[0], car_ps_y2 + car_height)

        return x1, y1, x2, y2

    def get_foot_point(self, point, line):
        p3 = point
        p1 = line[0]
        p2 = line[1]

        if p2[0] - p1[0] == 0:
            x = p1[0]
            y = p3[1]
        elif p2[1] - p1[1] == 0:
            x = p3[0]
            y = p1[1]
        else:
            slope = (p2[1] - p1[1]) / (p2[0] - p1[0])
            perslope = -1 / slope
            intercept = p3[1] - p3[0] * perslope
            x = (p3[1] - p1[1] + slope * p1[0] - perslope * p3[0]) / (slope - perslope)
            y = slope * (x - p1[0]) + p1[1]

        return [x, y]

    def get_point_line_distance(self, point, line):
        point_x = point[0]
        point_y = point[1]
        line_s_x = line[0][0]
        line_s_y = line[0][1]
        line_e_x = line[1][0]
        line_e_y = line[1][1]
        # 若直线与y轴平行，则距离为点的x坐标与直线上任意一点的x坐标差值的绝对值
        if line_e_x - line_s_x == 0:
            return math.fabs(point_x - line_s_x)
        # 若直线与x轴平行，则距离为点的y坐标与直线上任意一点的y坐标差值的绝对值
        if line_e_y - line_s_y == 0:
            return math.fabs(point_y - line_s_y)
        # 斜率
        k = (line_e_y - line_s_y) / (line_e_x - line_s_x)
        # 截距
        b = line_s_y - k * line_s_x
        # 带入公式得到距离dis
        dis = math.fabs(k * point_x - point_y + b) / math.pow(k * k + 1, 0.5)

        return dis

    #### 考虑大图包小图情况，返回参数包括，交并比与intersection/area
    def IOU_full(self, tl1, br1, tl2, br2):
        wh1, wh2 = br1 - tl1, br2 - tl2
        intersection_wh = np.maximum(np.minimum(br1, br2) - np.maximum(tl1, tl2), 0.)
        intersection_area = np.prod(intersection_wh)
        area1, area2 = (np.prod(wh1), np.prod(wh2))
        union_area = area1 + area2 - intersection_area
        return [intersection_area / union_area, intersection_area / area1, intersection_area / area2]

    def w_h_calc(self, car_ps):
        '''
        用于比较同一个的长宽比，返回结果为h/w 部分场景中取卡车等特殊车辆时使用
        '''
        h = abs(car_ps[1] - car_ps[3])
        w = abs(car_ps[0] - car_ps[2])

        if w == 0:
            return 0
        else:
            return h / w


# 自动切分拼接图（存在局限性）
class CUTIMG():
    def __init__(self):
        pass

    def find_bounding(self, img, row_or_col):  # img为灰度图；row_or_col 值为0或1；取值为0代表计算水平拼接图像数,取值为1代表计算竖直拼接图像数
        if row_or_col == 0:
            sub = np.sum(np.abs(img[:, :-1] / 255 - img[:, 1:] / 255), row_or_col)
            length = img.shape[1]
        if row_or_col == 1:
            sub = np.sum(np.abs(img[:-1, :] / 255 - img[1:, :] / 255), row_or_col)
            length = img.shape[0]

        #     peak_mean = np.mean(sub)
        #     peak_std = np.std(sub)
        sub_norm = (sub - np.mean(sub)) / np.std(sub)
        peak_max = np.max(sub_norm)
        if peak_max < 7:
            return [], 0
        else:
            peak_loc = np.where(sub_norm > 7)[0]
            #         print(peak_mean,sub[peak_loc],peak_loc)
            #     peak_idx = np.where((peak_loc>length/3-100) & (peak_loc<length*0.67+100))
            #     peak_list = peak_loc[peak_idx]
            peak_arr = np.array(peak_loc + 1)
            return peak_arr, length

    def cut_loc(self, p_l, length):
        idx = np.zeros(len(p_l))
        p_l_norm = [x / length for x in p_l]  # /length
        for i in range(len(p_l)):
            if (p_l_norm[i] > 0.167) and (p_l_norm[i] < 0.4167):
                idx[i] = 1
            elif (p_l_norm[i] >= 0.4167) and (p_l_norm[i] < 0.583):
                idx[i] = 2
            elif (p_l_norm[i] >= 0.583) and (p_l_norm[i] < 0.833):
                idx[i] = 3
        cut_loc_l = []
        for i in [2, 1, 3]:
            cut = np.where(idx == i)[0]
            if len(cut) > 0:
                status = i
                cut_loc_ = np.max(p_l[cut])
                cut_loc_l.append([cut_loc_, status])
                if i == 2:
                    break

        return cut_loc_l

    def cut_black(self, img):
        # print('===========', img.shape)
        try:
            img_gray = (cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)).astype(np.int16)
        except:
            return None
        #     img_gray[img_gray != 0] = 1
        #     sub = np.sum(np.abs(img_gray[:,:-1]-img_gray[:,1:]),1)
        #     sub=sub/np.mean(sub)
        #     img_gray[img_gray <16] = 0
        #     img_gray[img_gray >=16] = 1
        #     sub = np.sum(np.abs(img_gray[:,:-1]-img_gray[:,1:]),1)
        sub = np.sum(img_gray, 1) / img_gray.shape[1]
        #     sub=sub/np.mean(sub)
        peak_loc = np.where(sub[:np.int(len(sub) / 6)] < 40)[0]
        if len(peak_loc) == 0:
            left = -1
        else:
            left = max(peak_loc)
        sub[:np.int(len(sub) * 0.833)] = 50
        peak_loc = np.where(sub < 40)[0]
        # print(peak_loc)
        if len(peak_loc) == 0:
            return img[left + 1:, :, :]
        else:
            right = min(peak_loc)
            return img[left + 1:right - 1, :, :]

    def cut(self, img_np):
        # img = cv.imread(img_path)
        # img_gray = cv.imread(img_path, 0)
        img = img_np
        img_gray = cv2.cvtColor(img_np, cv2.COLOR_BGR2GRAY)
        img_list = []
        # 检测水平拼接数目
        p_l_h, w = self.find_bounding(img_gray, 0)
        cut_loc_hor = np.array(self.cut_loc(p_l_h, w))
        # 检测竖直拼接数目
        p_l_v, h = self.find_bounding(img_gray, 1)
        cut_loc_ver = np.array(self.cut_loc(p_l_v, h))
        # print(cut_loc_hor, cut_loc_ver)
        if len(cut_loc_hor) == 0:
            cut_loc_hor = np.array([[0, 0]])
        if len(cut_loc_ver) == 0:
            cut_loc_ver = np.array([[0, 0]])
        # print(cut_loc_hor, cut_loc_ver)
        ######################不切黑边
        #     if 1 in cut_loc_hor[:,1]:
        #     #三图横向拼接
        #         if 3 in cut_loc_hor[:,1]:
        #             img_list.append(img[:,:cut_loc_hor[0][0],:])
        #             img_list.append(img[:,cut_loc_hor[0][0]+1:cut_loc_hor[1][0],:])
        #             img_list.append(img[:,cut_loc_hor[1][0]+1:,:])
        #     #三图竖向拼接2
        #         else:
        #             img_list.append(img[:cut_loc_ver[0][0],:cut_loc_hor[0][0],:])
        #             img_list.append(img[cut_loc_ver[0][0]+1:,:cut_loc_hor[0][0],:])
        #             img_list.append(img[:,cut_loc_hor[0][0]+1:,:])
        #     #三图横向拼接1
        #     elif 1 in cut_loc_ver[:,1]:
        #         img_list.append(img[:cut_loc_ver[0][0],:cut_loc_hor[0][0],:])
        #         img_list.append(img[:cut_loc_ver[0][0],cut_loc_hor[0][0]+1:,:])
        #         img_list.append(img[cut_loc_ver[0][0]+1:,:,:])
        #     #三图横向拼接2
        #     elif 3 in cut_loc_ver[:,1]:
        #         img_list.append(img[:cut_loc_ver[0][0],:,:])
        #         img_list.append(img[cut_loc_ver[0][0]+1:,:cut_loc_hor[0][0],:])
        #         img_list.append(img[cut_loc_ver[0][0]+1:,cut_loc_hor[0][0]+1:,:])
        #     elif 2 in cut_loc_hor[:,1]:
        #         #四图拼接
        #         if 2 in cut_loc_ver[:,1]:
        #             img_list.append(img[:cut_loc_ver[0][0],:cut_loc_hor[0][0],:])
        #             img_list.append(img[:cut_loc_ver[0][0],cut_loc_hor[0][0]+1:,:])
        #             img_list.append(img[cut_loc_ver[0][0]+1:,:cut_loc_hor[0][0],:])
        #             img_list.append(img[cut_loc_ver[0][0]+1:,cut_loc_hor[0][0]+1:,:])
        #          #二图横向拼接
        #         else:
        #             img_list.append(img[:,:cut_loc_hor[0][0],:])
        #             img_list.append(img[:,cut_loc_hor[0][0]+1:,:])
        #     #二图竖向拼接
        #     elif 2 in cut_loc_ver[:,1]:
        #         img_list.append(img[:cut_loc_ver[0][0],:,:])
        #         img_list.append(img[cut_loc_ver[0][0]+1:,:,:])
        #     #单图
        #     else:
        #         img_list.append(img)
        ###########################切黑边
        if 1 in cut_loc_hor[:, 1]:
            # 三图横向拼接
            if 3 in cut_loc_hor[:, 1]:
                img_list.append(self.cut_black(img[:, :cut_loc_hor[0][0], :]))
                img_list.append(self.cut_black(img[:, cut_loc_hor[0][0] + 1:cut_loc_hor[1][0], :]))
                img_list.append(self.cut_black(img[:, cut_loc_hor[1][0] + 1:, :]))
            # 三图竖向拼接2
            else:
                img_list.append(self.cut_black(img[:cut_loc_ver[0][0], :cut_loc_hor[0][0], :]))
                img_list.append(self.cut_black(img[cut_loc_ver[0][0] + 1:, :cut_loc_hor[0][0], :]))
                img_list.append(self.cut_black(img[:, cut_loc_hor[0][0] + 1:, :]))
        # 三图横向拼接1
        elif 1 in cut_loc_ver[:, 1]:
            img_list.append(self.cut_black(img[:cut_loc_ver[0][0], :cut_loc_hor[0][0], :]))
            img_list.append(self.cut_black(img[:cut_loc_ver[0][0], cut_loc_hor[0][0] + 1:, :]))
            img_list.append(self.cut_black(img[cut_loc_ver[0][0] + 1:, :, :]))
        # 三图横向拼接2
        elif 3 in cut_loc_ver[:, 1]:
            # print('----------------------')
            img_list.append(self.cut_black(img[:cut_loc_ver[0][0], :, :]))
            img_list.append(self.cut_black(img[cut_loc_ver[0][0] + 1:, :int(img_np.shape[1] / 2), :]))
            img_list.append(self.cut_black(img[cut_loc_ver[0][0] + 1:, int(img_np.shape[1] / 2):, :]))
        elif 2 in cut_loc_hor[:, 1]:
            # 四图拼接
            if 2 in cut_loc_ver[:, 1]:
                img_list.append(self.cut_black(img[:cut_loc_ver[0][0], :cut_loc_hor[0][0], :]))
                img_list.append(self.cut_black(img[:cut_loc_ver[0][0], cut_loc_hor[0][0] + 1:, :]))
                img_list.append(self.cut_black(img[cut_loc_ver[0][0] + 1:, :cut_loc_hor[0][0], :]))
                img_list.append(self.cut_black(img[cut_loc_ver[0][0] + 1:, cut_loc_hor[0][0] + 1:, :]))
            # 二图横向拼接
            else:
                img_list.append(self.cut_black(img[:, :cut_loc_hor[0][0], :]))
                img_list.append(self.cut_black(img[:, cut_loc_hor[0][0] + 1:, :]))
        # 二图竖向拼接
        elif 2 in cut_loc_ver[:, 1]:
            img_list.append(self.cut_black(img[:cut_loc_ver[0][0], :, :]))
            img_list.append(self.cut_black(img[cut_loc_ver[0][0] + 1:, :, :]))
        # 单图
        else:
            img_list.append(self.cut_black(img))
        ##################################################################################################
        # print(cut_loc_ver, cut_loc_hor, '====')
        # print(cut_loc_ver.shape, cut_loc_hor.shape, '==++==')
        if cut_loc_hor.shape[0] == 2:
            split_x = [cut_loc_hor[0][0], cut_loc_hor[1][0]]
        elif cut_loc_hor.shape[0] == 1:
            split_x = [cut_loc_hor[0][0]]
        else:
            split_x = []

        if cut_loc_ver.shape[0] == 1:
            split_y = [cut_loc_ver[0][0]]
        else:
            split_y = []
        return img_list, split_x, split_y


class Cor_Change():
    def __init__(self):
        pass

    # 未使用
    def simple_cor_change(self, ps_list):
        res_list = []
        for i, value in enumerate(ps_list):
            try:
                value['car_ps'] = [int(x) for x in value['car_ps']]
            except:
                # traceback.print_exc()
                pass
            res_list.append(value)
        return res_list

    # 未使用
    def cor_change_41(self, ps_list, split_x, split_y, label=1):
        # label=1代表第4张放大图，label=2代表第1张放大图
        split_x = split_x[0]
        split_y = split_y[0]
        res_list = []
        for i, value in enumerate(ps_list):
            try:
                if label == 1:
                    if i == 1:
                        temp_ps_1 = np.array(value['car_ps'])
                        value['car_ps'] = list(np.array(temp_ps_1) + np.array([split_x, 0, split_x, 0]))
                    elif i == 2:
                        temp_ps_1 = np.array(value['car_ps'])
                        value['car_ps'] = list(np.array(temp_ps_1) + np.array([0, split_y, 0, split_y]))
                    elif i == 3:
                        temp_ps_1 = np.array(value['car_ps'])
                        value['car_ps'] = list(np.array(temp_ps_1) + np.array([split_x, split_y, split_x, split_y]))
                    else:
                        pass
                    value['car_ps'] = [int(x) for x in value['car_ps']]
                else:
                    if len(ps_list) == 3:
                        if i == 0:
                            temp_ps_1 = np.array(value['car_ps'])
                            value['car_ps'] = list(np.array(temp_ps_1) + np.array([split_x, 0, split_x, 0]))
                        elif i == 1:
                            temp_ps_1 = np.array(value['car_ps'])
                            value['car_ps'] = list(np.array(temp_ps_1) + np.array([0, split_y, 0, split_y]))
                        else:
                            temp_ps_1 = np.array(value['car_ps'])
                            value['car_ps'] = list(np.array(temp_ps_1) + np.array([split_x, split_y, split_x, split_y]))
                        value['car_ps'] = [int(x) for x in value['car_ps']]
                    elif len(ps_list) == 4:
                        if i == 1:
                            temp_ps_1 = np.array(value['car_ps'])
                            value['car_ps'] = list(np.array(temp_ps_1) + np.array([split_x, 0, split_x, 0]))
                        elif i == 2:
                            temp_ps_1 = np.array(value['car_ps'])
                            value['car_ps'] = list(np.array(temp_ps_1) + np.array([0, split_y, 0, split_y]))
                        elif i == 3:
                            temp_ps_1 = np.array(value['car_ps'])
                            value['car_ps'] = list(np.array(temp_ps_1) + np.array([split_x, split_y, split_x, split_y]))
                        else:
                            pass
                        value['car_ps'] = [int(x) for x in value['car_ps']]
            except:
                # traceback.print_exc()
                pass
            res_list.append(value)
        return res_list

    # 子图坐标回归至整图坐标  part cor --> global cor
    def cor_change_21(self, ps_list, split_x, split_y, label=0):
        split_x = split_x[0]
        split_y = split_y[0]
        res_list = []
        for i, value in enumerate(ps_list):
            try:
                if i == 1:
                    temp_ps_1 = np.array(value['car_ps'])
                    value['car_ps'] = list(np.array(temp_ps_1) + np.array([split_x, 0, split_x, 0]))
                else:
                    pass
                if label == 1:
                    if i == 1:
                        temp_ps_1 = np.array(value['car_ps'])
                        value['car_ps'] = list(np.array(temp_ps_1) + np.array([0, split_y, 0, split_y]))
                    else:
                        pass
                value['car_ps'] = [int(x) for x in value['car_ps']]
            except:
                # import traceback
                # traceback.print_exc()
                pass
            res_list.append(value)
        return res_list

    # 未使用
    def cor_change_31(self, ps_list, split_x, split_y):
        split_x1, split_x2 = split_x
        res_list = []
        for i, value in enumerate(ps_list):
            try:
                if i == 1:
                    temp_ps = np.array(value['car_ps'])
                    value['car_ps'] = list(np.array(temp_ps) + np.array([split_x1, 0, split_x1, 0]))
                elif i == 2:
                    temp_ps = np.array(value['car_ps'])
                    value['car_ps'] = list(np.array(temp_ps) + np.array([split_x2, 0, split_x2, 0]))
                else:
                    pass
                value['car_ps'] = [int(x) for x in value['car_ps']]
            except:
                # import traceback
                # traceback.print_exc()
                pass
            res_list.append(value)
        return res_list

    # 未使用
    def cor_change_211(self, ps_list, split_x, split_y):
        split_x = split_x[0]
        split_y = split_y[0]
        res_list = []
        for i, value in enumerate(ps_list):
            try:
                if i == 1:
                    temp_ps = np.array(value['car_ps'])
                    value['car_ps'] = list(np.array(temp_ps) + np.array([0, split_y, 0, split_y]))
                elif i == 2:
                    temp_ps = np.array(value['car_ps'])
                    value['car_ps'] = list(np.array(temp_ps) + np.array([split_x, split_y, split_x, split_y]))
                else:
                    pass
                value['car_ps'] = [int(x) for x in value['car_ps']]
            except:
                pass
            res_list.append(value)
        return res_list


class SIMPLE_CUT():
    def __init__(self):
        pass

    # 分割拼接图为横向4分图（默认为横向1234）
    def break_img_4(self, img_path):
        try:
            img = cv2.imread(img_path)
        except:
            img = img_path
        img_h, img_w = img.shape[0], img.shape[1]
        split_x, split_y = int(img_w / 2), int(img_h / 2) - 30
        ps1_img = img[0:split_y, 0:split_x, :]
        ps2_img = img[0:split_y, split_x:, :]
        ps3_img = img[split_y:, 0:split_x, :]
        ps4_img = img[split_y:, split_x:, :]
        img_np_list = [ps1_img, ps2_img, ps3_img, ps4_img]
        return img_np_list, [float(split_x)], [float(split_y)]

    # 分割图片为左右两个子图, 当lable=1时分为上下两个子图
    def break_img_2(self, img_path, label=0):
        try:
            img = cv2.imread(img_path)
        except:
            img = img_path
        img_h, img_w = img.shape[0], img.shape[1]
        split_x, split_y = int(img_w / 2), 0
        ps1_img = img[:, 0:split_x, :]
        ps2_img = img[:, split_x:, :]
        # print(ps1_img.shape, ps2_img.shape)
        img_np_list = [ps1_img, ps2_img]
        if label == 1:
            split_x, split_y = 0, int(img_h / 2)
            ps1_img = img[:split_y, :, :]
            ps2_img = img[split_y:, :, :]
            img_np_list = [ps1_img, ps2_img]
        return img_np_list, [float(split_x)], [float(split_y)]

    # 　　分割成横向2张图, 第２张是拼接图
    def break_img_211(self, img_path):
        try:
            img = cv2.imread(img_path)
        except:
            img = img_path
        img_h, img_w = img.shape[0], img.shape[1]
        split_y = int(img_h / 3) * 2
        split_x = int(img_w / 2)
        ps1_img = img[:split_y, :, :]
        ps2_img = img[split_y:, :split_x, :]
        ps3_img = img[split_y:, split_x:, :]
        img_np_list = [ps1_img, ps2_img, ps3_img]
        return img_np_list, [float(split_x)], [float(split_y)]

    # 　分割成横向3张图, 均匀分布
    def break_img_3(self, img_path):
        try:
            img = cv2.imread(img_path)
        except:
            img = img_path
        img_h, img_w = img.shape[0], img.shape[1]
        split_y = 0
        split_x1 = int(img_w / 3)
        split_x2 = int((img_w / 3) * 2)
        ps1_img = img[:, :split_x1, :]
        ps2_img = img[:, split_x1:split_x2, :]
        ps3_img = img[:, split_x2:, :]
        img_np_list = [ps1_img, ps2_img, ps3_img]
        return img_np_list, [float(split_x1), float(split_x2)], [float(split_y)]


def crop_driver_area(img, veh_box):
    w = veh_box[2] - veh_box[0]
    h = veh_box[3] - veh_box[1]
    x1 = int(veh_box[0] + 0.45 * w)
    y1 = int(veh_box[1] + 0.2 * h)
    x2 = int(veh_box[2] - 0.15 * w)
    y2 = int(veh_box[3] - 0.35 * h)
    return img[y1:y2, x1:x2], x1, y1


class IOU:
    @staticmethod
    def get_xiou(box1, box2):
        x1_min, _, x1_max, _ = box1
        x2_min, _, x2_max, _ = box2

        intersection_width = max(0, min(x1_max, x2_max) - max(x1_min, x2_min))
        union_width = max(x1_max, x2_max) - min(x1_min, x2_min)

        return intersection_width / union_width if union_width != 0 else 0.0

    @staticmethod
    def compute_iou(boxes1, boxes2):
        """[Compute pairwise IOU matrix for given two sets of boxes]

        Args:
            boxes1 ([numpy ndarray with shape N,4]): [representing bounding boxes with format (xmin,ymin,xmax,ymax)]
            boxes2 ([numpy ndarray with shape M,4]): [representing bounding boxes with format (xmin,ymin,xmax,ymax)]
        Returns:
            pairwise IOU maxtrix with shape (N,M)，where the value at ith row jth column hold the iou between ith
            box and jth box from box1 and box2 respectively.
        """
        boxes1 = np.asarray(boxes1)
        boxes2 = np.asarray(boxes2)

        lu = np.maximum(boxes1[:, None, :2],
                        boxes2[:,
                        :2])  # lu with shape N,M,2 ; boxes1[:,None,:2] with shape (N,1,2) boxes2 with shape(M,2)
        rd = np.minimum(boxes1[:, None, 2:4], boxes2[:, 2:4])  # rd same to lu
        intersection_wh = np.maximum(0.0, rd - lu)
        intersection_area = intersection_wh[:, :, 0] * intersection_wh[:, :, 1]  # with shape (N,M)
        boxes1_wh = np.maximum(0.0, boxes1[:, 2:4] - boxes1[:, :2])
        boxes1_area = boxes1_wh[:, 0] * boxes1_wh[:, 1]  # with shape (N,)
        boxes2_wh = np.maximum(0.0, boxes2[:, 2:4] - boxes2[:, :2])
        boxes2_area = boxes2_wh[:, 0] * boxes2_wh[:, 1]  # with shape (M,)
        union_area = np.maximum(boxes1_area[:, None] + boxes2_area - intersection_area, 1e-8)  # with shape (N,M)
        ious = np.clip(intersection_area / union_area, 0.0, 1.0)
        return ious

    @staticmethod
    def single_iou(box1, box2):
        """
        计算两个边界框的IoU。

        参数:
        box1, box2: list or tuple, 四个元素的列表或元组 [x1, y1, x2, y2]
                    其中 (x1, y1) 是左上角坐标，(x2, y2) 是右下角坐标。

        返回:
        iou: float, 两个边界框的交并比(IoU)。
        """
        try:
            # 解析边界框
            b1_x1, b1_y1, b1_x2, b1_y2 = box1
            b2_x1, b2_y1, b2_x2, b2_y2 = box2

            # 计算交集的左上角和右下角坐标
            inter_x1 = max(b1_x1, b2_x1)
            inter_y1 = max(b1_y1, b2_y1)
            inter_x2 = min(b1_x2, b2_x2)
            inter_y2 = min(b1_y2, b2_y2)

            # 计算交集面积
            inter_area = max(0, inter_x2 - inter_x1 + 1) * max(0, inter_y2 - inter_y1 + 1)

            # 计算每个框的面积
            b1_area = (b1_x2 - b1_x1 + 1) * (b1_y2 - b1_y1 + 1)
            b2_area = (b2_x2 - b2_x1 + 1) * (b2_y2 - b2_y1 + 1)

            # 计算并集面积
            union_area = b1_area + b2_area - inter_area

            # 计算IoU
            iou = round(inter_area / union_area, 2)
        except:
            iou = 0
        return iou

    @staticmethod
    def filter_boxes_by_iou(boxA, boxesB, iou_threshold=0.75) -> list:
        # boxA: [x1, y1, x2, y2] 单个边界框
        # boxesB: shape (n, 4) 多个边界框，格式为 [x1, y1, x2, y2]
        # 扩展 boxA 以匹配 boxesB 的形状
        boxA = np.asarray(boxA)
        boxesB = np.asarray(boxesB)

        # 扩展 boxA 以匹配 boxesB 的形状
        boxA = np.expand_dims(boxA, axis=0)

        # 计算交集的坐标
        interSec = np.maximum(np.minimum(boxA[:, 2:], boxesB[:, 2:]) - np.maximum(boxA[:, :2], boxesB[:, :2]) + 1, 0)
        interArea = interSec[:, 0] * interSec[:, 1]

        # 计算两个边界框的面积
        areaA = (boxA[:, 2] - boxA[:, 0] + 1) * (boxA[:, 3] - boxA[:, 1] + 1)
        areaB = (boxesB[:, 2] - boxesB[:, 0] + 1) * (boxesB[:, 3] - boxesB[:, 1] + 1)

        # 计算并集的面积
        unionArea = areaA + areaB - interArea

        # 避免除以零的情况
        unionArea = np.where(unionArea == 0, 1e-9, unionArea)

        # 计算IoU
        ious = interArea / unionArea

        # 找到IoU大于阈值的索引
        valid_indices = np.where(ious > iou_threshold)[0]

        if len(valid_indices) == 0:
            return []

        # 获取满足条件的边界框及其对应的IoU值
        valid_ious = ious[valid_indices]

        # 找到最大IoU值的索引
        max_iou_index = valid_indices[np.argmax(valid_ious)]

        # 返回具有最大IoU的边界框
        return boxesB[max_iou_index].tolist()


def show_chinese(orgimg, text,extra=""):
    IMG = Image.fromarray(cv2.cvtColor(orgimg, cv2.COLOR_BGR2RGB))
    draw = ImageDraw.Draw(IMG)
    path = "../yolov7/fonts/platech.ttf"
    try:
        height = 30
        font = ImageFont.truetype(path, height, encoding="utf-8")
        h, w, _ = orgimg.shape

        draw.text((int(w / 3 * 2), height), text+extra, (255, 0, 0), font)
    except:
        pass
    orgimg = cv2.cvtColor(np.asarray(IMG, dtype=np.uint8), cv2.COLOR_RGB2BGR)
    return orgimg
