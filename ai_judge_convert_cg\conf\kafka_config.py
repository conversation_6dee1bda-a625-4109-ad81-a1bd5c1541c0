from pydantic_settings import BaseSettings, SettingsConfigDict
from typing import List, Callable
import json
from conf.config import DeployConf as dp


class KafkaConf:
    KAFKA_BROKERS = dp.KAFKA_BROKERS
    VIO_DATA_TOPIC = 'BAYONET_VEHICLEALARM_TO_JUDGE_JSON_TOPIC'  # 需要审片消费审核的违法数据topic，基线应用无需修改
    JUDGE_RES_TOPIC = 'BAYONET_VEHICLEALARM_JUDGE_JSON_TOPIC'  # 审片判罚结果推送的topic，基线应用无需修改
    GROUP_ID = 'ai_judge_convert'
    CLINE_ID = 'admin'
    AUTO_OFFSET_RESET = "latest"  # earliest/latest
    Max_Poll_Interval_Ms = 60000  # 消费最大拉取时间，单位毫秒
    Max_Poll_Records = 1  # 一次拉取的数据量
    Poll_Interval_Ms = 2000  # 消费拉取等待消息的最大时间，单位毫秒
    # 配置kafka心跳
    Heart_Interval = 20  # 心跳机制间隔时间
    Heart_Error_Num = -1  # 心跳机制连续异常次数达到该值模型停止运行


class KafkaConsumerConfig(BaseSettings):
    # 必要配置项
    bootstrap_servers: List[str] = KafkaConf.KAFKA_BROKERS
    group_id: str = KafkaConf.GROUP_ID
    enable_auto_commit: bool = False
    auto_offset_reset: str = KafkaConf.AUTO_OFFSET_RESET
    value_deserializer: Callable = lambda x: json.loads(x.decode('utf-8'))

    # 其他可选配置项
    client_id: str = KafkaConf.CLINE_ID
    max_poll_interval_ms: int = KafkaConf.Max_Poll_Interval_Ms
    session_timeout_ms: int = int(KafkaConf.Max_Poll_Interval_Ms * 1.5)
    heartbeat_interval_ms: int = session_timeout_ms // 3
    max_poll_records: int = KafkaConf.Max_Poll_Records
    request_timeout_ms: int = int(KafkaConf.Max_Poll_Interval_Ms * 1.6) + 1000
    connections_max_idle_ms: int = int(KafkaConf.Max_Poll_Interval_Ms * 1.6) + 5000
    fetch_max_bytes: int = 52428800  # 50MB
    fetch_min_bytes: int = 1024
    fetch_max_wait_ms: int = 1000
    consumer_timeout_ms: int = -1
    retry_backoff_ms: int = 10
    model_config = SettingsConfigDict(case_sensitive=False)  # 确保字段名不区分大小写

    @classmethod
    def get_config(cls) -> dict:
        """
        获取所有配置项作为字典，用于传递给 KafkaConsumer 构造函数。
        """
        return cls().model_dump()


class KafkaProducerConfig(BaseSettings):
    # 必要配置项
    kafka_brokers: List[str] = KafkaConf.KAFKA_BROKERS
    value_serializer: Callable = lambda x: json.dumps(x).encode('utf-8')

    # 其他可选配置项
    client_id: str = KafkaConf.CLINE_ID
    retries: int = 5
    retry_backoff_ms: int = 500
    model_config = SettingsConfigDict(case_sensitive=False)  # 确保字段名不区分大小写

    @classmethod
    def get_config(cls) -> dict:
        """
        获取所有配置项作为字典，用于传递给 KafkaConsumer 构造函数。
        """
        config_dict = cls().model_dump()
        required_params = {
            'bootstrap_servers': config_dict.pop('kafka_brokers'),
            'value_serializer': config_dict.pop('value_serializer')
        }
        return {**required_params, **config_dict}


# 创建配置实例
consumer_config = KafkaConsumerConfig.get_config()
producer_config = KafkaProducerConfig.get_config()
