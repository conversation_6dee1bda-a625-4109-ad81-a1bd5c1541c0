#!/usr/bin/python
# -*- coding: UTF-8 -*-
import time
import warnings

warnings.filterwarnings('ignore')
import sys

sys.path.append('..')
import cv2
from ai_judge_code_gy.judge_tools import Tools, convert_src_coord
from ai_judge_code_gy.similar_match import SIMILAR_JUDGE
from yolov7.yolo_interface import detect_Recognition_plate, draw_result2
from yolov7.plate_recognition.plate_rec import get_plate_result
from main_config.config import Blend_Judge_Vehicle, Blend_Conf, Special_Type, Ambulance_Head_Thresh, Special_Car_Thresh, \
    Police_Plate_Thresh, SPLIT_DICT, BLACK_HEIGHT
import numpy as np
import os
import torch
import pandas as pd

# ==========================================================================================================
chars = [u"京", u"沪", u"津", u"渝", u"冀", u"晋", u"蒙", u"辽", u"吉", u"黑", u"苏", u"浙", u"皖", u"闽", u"赣", u"鲁",
         u"豫", u"鄂",
         u"湘", u"粤", u"桂",
         u"琼", u"川", u"贵", u"云", u"藏", u"陕", u"甘", u"青", u"宁", u"新", u"0", u"1", u"2", u"3", u"4", u"5", u"6",
         u"7",
         u"8", u"9", u"A",
         u"B", u"C", u"D", u"E", u"F", u"G", u"H", u"J", u"K", u"L", u"M", u"N", u"P", u"Q", u"R", u"S", u"T", u"U",
         u"V", u"W", u"X",
         u"Y", u"Z", u"港", u"学", u"使", u"警", u"澳", u"挂", u"军", u"北", u"南", u"广", u"沈", u"兰", u"成", u"济",
         u"海", u"民",
         u"航", u"空"
         ]

from skimage.metrics import structural_similarity as compare_ssim


# ssim相似度对比
def compare_image(imageA, imageB):
    # print(imageA.shape,imageB.shape)
    height, width = imageA.shape[:2]
    # 缩小图片
    size = (int(width), int(height))
    # h,w=imageA[0],imageA[1]
    # size = imageA[0:2]
    imageB = cv2.resize(imageB, size, cv2.INTER_NEAREST)
    # imageB=cv2.resize(np.array(imageB).astype(np.uint8),(h,w))
    # print(imageB.shape,imageA.shape)
    grayA = cv2.cvtColor(imageA, cv2.COLOR_BGR2GRAY)
    grayB = cv2.cvtColor(imageB, cv2.COLOR_BGR2GRAY)

    (score, diff) = compare_ssim(grayA, grayB, full=True)
    # print("SSIM: {}".format(score))
    return score


# phash计算
def pHash(img, leng=32, wid=32):
    img = cv2.resize(img, (leng, wid))
    gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
    dct = cv2.dct(np.float32(gray))
    dct_roi = dct[0:8, 0:8]
    avreage = np.mean(dct_roi)
    phash_01 = (dct_roi > avreage) + 0
    phash_list = phash_01.reshape(1, -1)[0].tolist()
    hash = ''.join([str(x) for x in phash_list])
    return hash


# 未使用
def dHash(img, leng=9, wid=8):
    img = cv2.resize(img, (leng, wid))
    image = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
    # 每行前一个像素大于后一个像素为1，相反为0，生成哈希
    hash = []
    for i in range(wid):
        for j in range(wid):
            if image[i, j] > image[i, j + 1]:
                hash.append(1)
            else:
                hash.append(0)
    return hash


# hash相似度计算
def Sim_hamming_distance(hash1, hash2):
    num = 0
    for index in range(len(hash1)):
        if hash1[index] != hash2[index]:
            num += 1
    sim = 1 - num * 1.0 / 64
    return sim


class PlateDetect:
    def __init__(self):
        pass

    # wpod模型车牌检测
    # def detect_plate_wpod(self, img_np, weights_list):
    #     plate = 'NODETECT'
    #     confidence = 0
    #     plate_cor = []
    #     lp_threshold = .6
    #     lpr_model, wpod_net = weights_list
    #     Ivehicle = img_np
    #     try:
    #         ratio = float(max(Ivehicle.shape[:2])) / min(Ivehicle.shape[:2])
    #         side = int(ratio * 288.)
    #         bound_dim = min(side + (side % (2 ** 4)), 608)
    #         Llp, LlpImgs, _ = detect_lp(wpod_net, im2single(Ivehicle), bound_dim, 2 ** 4, (240, 80), lp_threshold)
    #         if len(LlpImgs):
    #             Ilp = LlpImgs[0] * 255.
    #             tl_x, tl_y = Llp[0].tl()
    #             br_x, br_y = Llp[0].br()
    #             wx, hy = Llp[0].wh()
    #             x1 = int(Ivehicle.shape[1] * tl_x)
    #             y1 = int(Ivehicle.shape[0] * tl_y)
    #             w = int(Ivehicle.shape[1] * wx)
    #             h = int(Ivehicle.shape[0] * hy)
    #             plate_cor = [x1, y1, w, h]
    #             plate, confidence = lpr_model.recognizeOne(Ilp)
    #     except:
    #         traceback.print_exc()
    #         pass
    #     return plate, confidence, plate_cor

    # 使用LPR模型识别车牌
    # def detect_plate_lpr(self, img_np, weights_list):
    #     plate = 'NODETECT'
    #     confidence = 0
    #     plate_cor = []
    #     lpr_model = weights_list[0]
    #     Ivehicle = img_np
    #     try:
    #         res_list = lpr_model.RecognizePlate(Ivehicle)
    #         for res in res_list:
    #             plate = res[0]
    #             confidence = round(res[1], 4)
    #             plate_cor = [int(i) for i in res[2]]
    #     except:
    #         traceback.print_exc()
    #         pass
    #     return plate, confidence, plate_cor


# 车辆车牌识别定位与目标车辆位置返回
class CarJudge():
    def __init__(self, device=None, plate_match_thresh=None, need_first_plate=False, iiea_cfg=None):
        if device is None:
            self.device = "cuda:0" if torch.cuda.is_available() else "cpu"
        else:
            self.device = device
        if not isinstance(plate_match_thresh, list) or len(plate_match_thresh) != 2:
            self.plate_match_thresh = [3, 2]
        else:
            self.plate_match_thresh = plate_match_thresh
        self.need_first_plate = need_first_plate
        try:
            self.iiea_filter_police = iiea_cfg[0]
        except:
            self.iiea_filter_police = None

    def show_plate(self, img, car_ps, conf, plate_num, add_str='', position=None):
        # img = Image.fromarray(cv2.cvtColor(img, cv2.COLOR_BGR2RGB))
        # draw = ImageDraw.Draw(img)  # 图片上打印
        # font = ImageFont.truetype('simsun.ttc',20, encoding='utf-8')
        # draw.text((car_ps[0], (car_ps[1] + car_ps[3]) // 2), f"{round(conf,3)} {plate_num}",(0,255,0), font=font)
        # img = cv2.cvtColor(np.array(img), cv2.COLOR_RGB2BGR)  # PIL图片转cv2 图片
        cv2.rectangle(img, (car_ps[0], car_ps[1]), (car_ps[2], car_ps[3]), (255, 0, 0), 2)
        if position is None:
            cv2.putText(img, f"{conf:.2f} {plate_num[1:]}{add_str}", (car_ps[2], (car_ps[1] + car_ps[3]) // 2),
                        cv2.FONT_HERSHEY_COMPLEX, 1.5,
                        (0, 0, 255), 2)
        else:
            cv2.putText(img, f"{conf:.2f} {plate_num[1:]}{add_str}",
                        (car_ps[0], min(car_ps[1] + position, img.shape[0])),
                        cv2.FONT_HERSHEY_COMPLEX, 1.2,
                        (0, 0, 255), 2)

    # 车牌识别匹配
    def confrim_img1_plate(self, img_np, car_ps_list, src_plate_num, plate_det_model, plate_rec_model, device=None,
                           show=False, save_path="."):
        if device is None:
            device = "cuda:0" if torch.cuda.is_available() else "cpu"
        save = f"{save_path}/plate_num_show"
        Iorig = img_np.copy()
        img_car_ps = []
        plate_num = "NO_DETECT"
        n = 0
        img_plate_dict = {}
        del_car = []
        save_name = 0
        del_thresh = 0.9
        del_thresh2 = 0.93
        temp = sorted(car_ps_list, key=lambda x: x[1], reverse=True)
        for car_ps in temp:
            # 车牌坐标初始化
            img_plate_dict[str(car_ps)] = []
            save_name += car_ps[1]
            n += 1
            # 车牌识别去除车辆上三分之一部分
            h_bias = (car_ps[3] - car_ps[1]) // 3
            temp_img = img_np[car_ps[1] + h_bias:car_ps[3], car_ps[0]:car_ps[2]]
            # 车牌检测+识别
            with torch.no_grad():
                dict_list = detect_Recognition_plate(plate_det_model, temp_img, device, plate_rec_model, 640)

            if show:
                temp_car_ps = [car_ps[0], car_ps[1] + h_bias, car_ps[2], car_ps[3]]
                Iorig = draw_result2(Iorig, dict_list, temp_car_ps)

            # 车牌结果排序
            try:
                dict_list = sorted(dict_list, key=lambda x: np.min(x['score']), reverse=True)
                tmp_plate = dict_list[0]["plate_no"]
                if len(tmp_plate) >= 7 and tmp_plate[0] in chars:
                    dict_list = [dict_list[0]]  # 可能影响其它场景低要求的模糊匹配
            except:
                pass
            for idx, res in enumerate(dict_list):
                plate_num = res["plate_no"]
                label = res["label"] # 0单层车牌 1双层车牌
                plate_whole = False
                if plate_num and plate_num[-1] == "警" and (res["score"][
                                                                -1] < Police_Plate_Thresh or self.iiea_filter_police is False):
                    plate_num = plate_num[:-1]
                if len(plate_num) >= 7:
                    plate_whole = True
                if len(plate_num) > 2:
                    confidence = np.min(res["score"][2:])
                elif len(plate_num):
                    confidence = np.min(res["score"])
                else:
                    confidence = 0
                tmp_cor = res["rect"]
                plate_cor = [tmp_cor[0] + car_ps[0], tmp_cor[1] + car_ps[1] + h_bias, tmp_cor[2] + car_ps[0],
                             tmp_cor[3] + car_ps[1] + h_bias]

                img_plate_dict[str(car_ps)] = plate_cor
                img_plate_dict[str(car_ps) + "conf"] = confidence
                # 车牌模糊匹配
                if plate_num == src_plate_num or Tools().random_choice(plate_num, src_plate_num,
                                                                       random_list=self.plate_match_thresh):
                    save_name = "target" + str(img_np.shape[0] - car_ps[3])
                    img_car_ps.extend(car_ps)
                    if show:
                        os.makedirs(save, exist_ok=True)
                        cv2.imwrite(f"{save}/{src_plate_num}_{save_name}.jpg", Iorig)
                    if self.need_first_plate:
                        if len(res["score"]):
                            conf = res["score"][0]
                        else:
                            conf = 0
                        return img_car_ps, plate_num, img_plate_dict, src_plate_num, conf
                    else:
                        return img_car_ps, plate_num, img_plate_dict, src_plate_num
                # 为后续车辆相似度对比模块去除置信度大的非目标车辆
                if plate_whole and ((confidence >= del_thresh and 0.31 * Iorig.shape[
                    0] < (car_ps[1] + car_ps[3]) / 2 <= 0.5 * Iorig.shape[
                                         0]) or (
                                            confidence >= del_thresh2 and (car_ps[1] + car_ps[3]) / 2 > 0.5 *
                                            Iorig.shape[
                                                0]) or confidence == 1) and car_ps not in del_car:
                    del_car.append(car_ps)
        if plate_num == "NO_DETECT":
            # 如果仍无法检测到对应车牌,进行车辆图片切片检测
            # =====================================================================
            print('slice detecting !')
            # =====================================================================
            for car_ps in temp:
                save_name += car_ps[1]
                tmp_img_h = car_ps[3] - car_ps[1]
                if tmp_img_h < 150:
                    continue
                # 随机切成3、4、5份
                for num in [2, 3, 4, 5]:
                    slice = int(tmp_img_h // num)
                    for i in range(num // 2, num):
                        try:
                            x_img = img_np[int(car_ps[1] + i * slice):int(car_ps[1] + (i + 1) * slice),
                                    car_ps[0]:car_ps[2]]
                            plate_num2, conf_arry = get_plate_result(x_img, device, plate_rec_model)
                            plate_whole = False
                            if plate_num2 and plate_num2[-1] == "警" and (
                                    conf_arry[-1] < Police_Plate_Thresh or self.iiea_filter_police is False):
                                plate_num2 = plate_num2[:-1]

                            if len(plate_num2) >= 7:
                                plate_whole = True
                            if len(plate_num2) > 2:
                                confidence = np.min(conf_arry[2:])
                            elif len(plate_num2):
                                confidence = np.min(conf_arry)
                            else:
                                confidence = 0
                            if plate_num2 == src_plate_num or Tools().random_choice(plate_num2, src_plate_num,
                                                                                    random_list=self.plate_match_thresh):
                                img_car_ps.extend(car_ps)
                                print(f"slice plate rec:{car_ps}--{plate_num2}")

                                if show:
                                    os.makedirs(save, exist_ok=True)
                                    cv2.imwrite(f"{save}/{src_plate_num}_{save_name}.jpg", Iorig)
                                if self.need_first_plate:
                                    return img_car_ps, plate_num2, img_plate_dict, src_plate_num, 0
                                else:
                                    return img_car_ps, plate_num2, img_plate_dict, src_plate_num
                            # 为后续车辆相似度对比模块去除置信度大的非目标车辆
                            if plate_whole and ((confidence >= del_thresh and 0.35 * Iorig.shape[
                                0] < (car_ps[1] + car_ps[3]) / 2 <= 0.6 * Iorig.shape[
                                                     0]) or (
                                                        confidence >= del_thresh2 and (
                                                        car_ps[1] + car_ps[3]) / 2 > 0.6 * Iorig.shape[
                                                            0])) and car_ps not in del_car:
                                del_car.append(car_ps)
                                break
                        except:
                            pass

        # 将置信度高且车牌模糊匹配失败的车删除
        for i in del_car:
            if show:
                cv2.rectangle(Iorig, (i[0], i[1]), (i[2], i[3]), (0, 0, 255), 2)
            if i in car_ps_list:
                car_ps_list.remove(i)
        if show:
            os.makedirs(save, exist_ok=True)
            cv2.imwrite(f"{save}/{src_plate_num}_{save_name}.jpg", Iorig)
        if self.need_first_plate:
            return img_car_ps, "NO_DETECT", img_plate_dict, src_plate_num, 0
        else:
            return img_car_ps, "NO_DETECT", img_plate_dict, src_plate_num

    # 确认图片中的目标车辆位置
    def confirm_img1_car_ps(self, img_np, src_plate_num, plate_det_model, plate_rec_model, car_mask_ps, device=None,
                            label=0,
                            show=False, save_path=".", is_motorbike=False):
        if device is None:
            device = "cuda:0" if torch.cuda.is_available() else "cpu"
        car_ps_list = []
        img_car_ps = []
        plate_num = 'NO_DETECT'
        img_plate_dict = {}
        n = 0
        R1 = car_mask_ps
        if len(R1):
            for i, r in enumerate(R1):
                x1, y1, x2, y2 = r
                w = abs(x2 - x1)
                h = abs(y2 - y1)
                n += 1
                # 去除过小的车辆
                if w < 40 and h < 30:
                    if not is_motorbike:
                        continue
                car_ps_list.append([x1, y1, x2, y2])
            if self.need_first_plate:
                img_car_ps, plate_num, img_plate_dict, src_plate_num, first_plate = \
                    self.confrim_img1_plate(img_np, car_ps_list, src_plate_num, plate_det_model, plate_rec_model,
                                            device,
                                            show=show, save_path=save_path)
                return img_car_ps, plate_num, img_plate_dict, car_ps_list, first_plate

            else:
                img_car_ps, plate_num, img_plate_dict, src_plate_num = \
                    self.confrim_img1_plate(img_np, car_ps_list, src_plate_num, plate_det_model, plate_rec_model,
                                            device,
                                            show=show, save_path=save_path)
                return img_car_ps, plate_num, img_plate_dict, car_ps_list
        if self.need_first_plate:
            return img_car_ps, plate_num, img_plate_dict, car_ps_list, 0
        else:
            return img_car_ps, plate_num, img_plate_dict, car_ps_list

    def calculate(self, image1, image2):
        # 灰度直方图算法
        # 计算单通道的直方图的相似值
        hist1 = cv2.calcHist([image1], [0], None, [256], [0.0, 255.0])
        hist2 = cv2.calcHist([image2], [0], None, [256], [0.0, 255.0])
        # 计算直方图的重合度
        degree = 0
        for i in range(len(hist1)):
            if hist1[i] != hist2[i]:
                degree = degree + \
                         (1 - abs(hist1[i] - hist2[i]) / max(hist1[i], hist2[i]))
            else:
                degree = degree + 1
        degree = degree / len(hist1)
        return degree

    def classify_hist_with_split(self, image1, image2, size=(256, 256)):
        # RGB每个通道的直方图相似度
        # 将图像resize后，分离为RGB三个通道，再计算每个通道的相似值

        image1 = cv2.resize(image1, size)
        image2 = cv2.resize(image2, size)
        sub_image1 = cv2.split(image1)
        sub_image2 = cv2.split(image2)
        sub_data = 0
        for im1, im2 in zip(sub_image1, sub_image2):
            sub_data += self.calculate(im1, im2)
        sub_data = float(sub_data / 3)
        return sub_data

    # 车辆相似度匹配模块
    def confrim_img23_plate(self, last_car_ps, last_img_np, next_img_np, car_ps_list, src_plate_num, wf_label=False,
                            show=False, save_path=".", save_name=""):
        confidence = None
        plate_num = 'SIMILAR_COMPARE'
        Iorig = next_img_np
        show_img = next_img_np.copy()
        last_img = last_img_np
        last_car = last_img[last_car_ps[1]:last_car_ps[3], last_car_ps[0]:last_car_ps[2]]

        # hist1 = Tools().create_rgb_hist(last_car)
        match_dict = {}
        # 相似度匹配范围
        similar_range_thresh = 0.32
        if save_name == "23":
            similar_range_thresh = 0.13  # 0.2
        last_cx = (last_car_ps[0] + last_car_ps[2]) / 2

        source_info = [last_car_ps, last_car]
        cars_list = []
        hist_tmp = []
        for car_ps in car_ps_list:
            x1, y1, x2, y2 = car_ps
            next_cx = (x1 + x2) / 2
            dist_y = y2 - last_car_ps[3] if wf_label else last_car_ps[3] - y2
            dist_x = max(abs(last_cx - next_cx), 1)
            # 特殊情况以及不满足相似度匹配范围阈值不计算
            if save_name != "41" and not save_name.startswith("cl") and dist_y / dist_x < similar_range_thresh:
                continue
            temp_img = Iorig[y1:y2, x1:x2]
            # 车辆相似度匹配方法计算

            # hist2 = Tools().create_rgb_hist(temp_img)
            # match1 = round(cv2.compareHist(hist1, hist2, cv2.HISTCMP_BHATTACHARYYA), 6)
            hist_dis = 1 - self.classify_hist_with_split(last_car, temp_img)
            hist_tmp.append(hist_dis)
            match_dict[hist_dis] = car_ps
            # 可视化相似度对比结果
            if show:
                pt1 = (car_ps[0], car_ps[1])
                pt2 = (car_ps[2], car_ps[3])
                org = (car_ps[0], car_ps[1])
                cv2.rectangle(show_img, pt1, pt2, (255, 0, 0), 1)
                cv2.putText(show_img, f"M2-{hist_dis:.2f}", org, cv2.FONT_HERSHEY_COMPLEX, 1,
                            (0, 0, 255), 2)
            cars_list.append([car_ps, temp_img])
        if cars_list == []:
            plate_num = "NO_SUIT_CAR"
        # squeeze相似度匹配
        dis, tar_car_cor, tar_img = SIMILAR_JUDGE().similar_judge(source_info, cars_list, device=self.device)
        img_car_ps = []
        # plate_num = 'NO_DETECT'
        # 车辆相似度匹配逻辑模块
        score = dis_match = 0
        text = ""
        hist_tmp = sorted(hist_tmp)
        if len(match_dict) and tar_car_cor:
            score = min(match_dict.keys())
            for k, v in match_dict.items():
                if v == tar_car_cor:
                    dis_match = k
                    break
            diff = dis_match - score  # 0.08
            judge = dis - score + 2 * diff  # 0.13
            if len(cars_list) == 1:
                if dis < 0.5:
                    img_car_ps = tar_car_cor
            elif tar_car_cor == match_dict[score] and (dis < 0.5 or abs(hist_tmp[0] - hist_tmp[1]) > 0.1):
                img_car_ps = tar_car_cor
            elif abs(hist_tmp[0] - hist_tmp[1]) > 0.2 and hist_tmp[0] < 0.45:
                if show: cv2.putText(show_img, f"Sim1", (int(show_img.shape[1] * 0.8), 120), cv2.FONT_HERSHEY_COMPLEX,
                                     1,
                                     (0, 0, 255), 2)
                img_car_ps = match_dict[score]
            elif dis < 0.31 or diff < 0.052 or judge <= 0:
                if show: cv2.putText(show_img, f"Sim2", (int(show_img.shape[1] * 0.8), 120), cv2.FONT_HERSHEY_COMPLEX,
                                     1,
                                     (0, 0, 255), 2)
                img_car_ps = tar_car_cor
            elif score < 0.315 or (judge > 0.26 and abs(hist_tmp[0] - hist_tmp[1]) > 0.1) or diff > 0.3:
                if show: cv2.putText(show_img, f"Sim3", (int(show_img.shape[1] * 0.8), 120), cv2.FONT_HERSHEY_COMPLEX,
                                     1,
                                     (0, 0, 255), 2)
                img_car_ps = match_dict[score]
            elif dis < 0.41 and judge > 0.206:
                if show: cv2.putText(show_img, f"Sim4", (int(show_img.shape[1] * 0.8), 120), cv2.FONT_HERSHEY_COMPLEX,
                                     1,
                                     (0, 0, 255), 2)
                img_car_ps = tar_car_cor
            elif score < 0.4 and dis > 0.4 and abs(hist_tmp[0] - hist_tmp[1]) > 0.05:
                if show: cv2.putText(show_img, f"Sim5", (int(show_img.shape[1] * 0.8), 120), cv2.FONT_HERSHEY_COMPLEX,
                                     1,
                                     (0, 0, 255), 2)
                img_car_ps = match_dict[score]
            elif dis < 0.41 or (abs(hist_tmp[0] - hist_tmp[1]) < 0.05 and judge < 0.4):
                if show: cv2.putText(show_img, f"Sim6", (int(show_img.shape[1] * 0.8), 120), cv2.FONT_HERSHEY_COMPLEX,
                                     1,
                                     (0, 0, 255), 2)
                img_car_ps = tar_car_cor
            elif score < 0.35 and dis > 0.4:
                if show: cv2.putText(show_img, f"Sim7", (int(show_img.shape[1] * 0.8), 120), cv2.FONT_HERSHEY_COMPLEX,
                                     1,
                                     (0, 0, 255), 2)
                img_car_ps = match_dict[score]

            # if tar_car_cor != match_dict[score] and show:
            #     # 统计车辆相似度主要指标
            #     text += f"diff:{diff:.3f} judge:{judge:.3f}"
            #     excel_path = "../similar_different.xlsx"
            #     if os.path.exists(excel_path):
            #         df_main = pd.read_excel(excel_path)
            #         temp = df_main.append({'dis': dis,
            #                                'dis_match': dis_match,
            #                                'match': score,
            #                                'diff': diff,
            #                                'plate': src_plate_num}, ignore_index=True)
            #         temp.to_excel(excel_path, index=False)

        if not wf_label and img_car_ps:
            last_car_h = abs(last_car_ps[3] - last_car_ps[1])
            img_car_h = abs(img_car_ps[3] - img_car_ps[1])
            if img_car_h > last_car_h * 1.05:
                img_car_ps = []
                text += "--car_h_error"
                print("目标车辆长度变化异常,已初始化相似度匹配结果！")
        if show:
            # 画相似度匹配范围
            x1, y1, x2, y2 = last_car_ps
            point1 = ((x1 + x2) // 2, y2)
            tmp_x = int(y2 / similar_range_thresh)
            if wf_label:
                point_y = y2 * 2
            else:
                point_y = 0
            left_x = point1[0] - tmp_x
            right_x = point1[0] + tmp_x
            cv2.line(show_img, point1, (left_x, point_y), (0, 255, 0), 2)
            cv2.line(show_img, point1, (right_x, point_y), (0, 255, 0), 2)
            # 可视化相似度对比结果
            if tar_car_cor:
                cv2.putText(show_img, f"M1-{dis:.2f}", (tar_car_cor[0], tar_car_cor[1] - 50), cv2.FONT_HERSHEY_COMPLEX,
                            1,
                            (0, 255, 0), 2)
            if len(match_dict):
                car_ps = match_dict[score]
                cv2.putText(show_img, f"M2-{score:.2f}", (car_ps[0], car_ps[1]),
                            cv2.FONT_HERSHEY_COMPLEX, 1, (0, 255, 0), 2)
            if img_car_ps:
                cv2.rectangle(show_img, (img_car_ps[0], img_car_ps[1]), (img_car_ps[2], img_car_ps[3]), (0, 255, 0), 2)
            cv2.putText(show_img, text, (show_img.shape[1] // 2, 50), cv2.FONT_HERSHEY_COMPLEX,
                        1,
                        (0, 255, 0), 2)
            save = f"{save_path}/similar_show"
            os.makedirs(save, exist_ok=True)
            cv2.imwrite(f"{save}/{src_plate_num}_{save_name}.jpg", show_img)
        # 保存车辆图片
        # os.makedirs("./sim",exist_ok=True)
        # cv2.imwrite(f"./sim/target_{save_name}.jpg",last_car)
        # for idx,car_ps in enumerate(car_ps_list):
        #     x1, y1, x2, y2 = car_ps
        #     car = Iorig[y1:y2, x1:x2]
        #
        #     cv2.imwrite(f"./sim/{save_name}_{idx}.jpg",car)
        return img_car_ps, plate_num, confidence, src_plate_num

    # 定位第二三张车辆位置信息，采用车牌模糊匹配与相似度匹配原则
    def confirm_img23_car_ps(self, last_car_ps, last_img_np, next_img_np, src_plate_num,
                             car_mask_ps, wf_label=True, is_13=False, show=False, save_path=".", save_name=""):
        n = 0
        confidence = 0
        car_ps_list = []
        img_car_ps = []
        plate_num = 'SIMILAR_COMPARE'
        if len(last_car_ps):
            R2 = car_mask_ps
            if len(R2):
                for i, r in enumerate(R2):
                    x1, y1, x2, y2 = r
                    if car_ps_list:
                        iou_list = []
                        for i in car_ps_list:
                            iou = Tools().IOU(np.array([x1, y1]), np.array([x2, y2]), np.array(i[:2]), np.array(i[2:]))
                            iou_list.append(iou)
                        if np.sum(np.where(np.array(iou_list) > 0.7, 1, 0)) >= 1:
                            continue
                    n += 1
                    car_ps_list.append([x1, y1, x2, y2])
                img_car_ps, plate_num, confidence, src_plate_num = \
                    self.confrim_img23_plate(last_car_ps, last_img_np, next_img_np, car_ps_list,
                                             src_plate_num, wf_label, show, save_path, save_name)
            # 如果相似度对比匹配失败，但是存在一辆车
            # if not img_car_ps and len(car_ps_list) == 1:
            # img_car_ps.extend(car_ps_list[0])

        return img_car_ps, plate_num, confidence


# 未使用部分
def judge_car_info(src_plate_num, img1_np, img2_np, img3_np, img1_car_ps, img2_car_ps, img3_car_ps, show=False):
    img = img1_np.copy()
    img1 = img1_np
    img2 = img2_np
    img3 = img3_np
    img_W = img1.shape[1]
    img_H = img1.shape[0]
    try:
        img1_car_cor = [int((img1_car_ps[0] + img1_car_ps[2]) / 2), int((img1_car_ps[1] + img1_car_ps[3]) / 2)]
        img1_lx = round((img1_car_cor[0] / img_W), 4)
        img1_ly = round((img1_car_cor[1] / img_H), 4)
    except:
        img1_car_cor = []
        img1_lx = 0
        img1_ly = 0
    try:
        img2_car_cor = [int((img2_car_ps[0] + img2_car_ps[2]) / 2), int((img2_car_ps[1] + img2_car_ps[3]) / 2)]
        img2_lx = round((img2_car_cor[0] / img_W), 4)
        img2_ly = round((img2_car_cor[1] / img_H), 4)
    except:
        img2_car_cor = []
        img2_lx = 0
        img2_ly = 0
    try:
        img3_car_cor = [int((img3_car_ps[0] + img3_car_ps[2]) / 2), int((img3_car_ps[1] + img3_car_ps[3]) / 2)]
        img3_lx = round((img3_car_cor[0] / img_W), 4)
        img3_ly = round((img3_car_cor[1] / img_H), 4)
    except:
        img3_car_cor = []
        img3_lx = 0
        img3_ly = 0
    angle1 = None
    angle2 = None
    angle3 = None
    iou23 = 0

    if len(img1_car_ps) and len(img2_car_ps):
        if abs(img2_car_cor[1] - img1_car_cor[1]) < 5:
            angle1 = 1000
        else:
            h1 = img2_car_cor[1] - img1_car_cor[1] * 0.99
            w1 = img2_car_cor[0] - img1_car_cor[0] * 0.99
            tan_theta = w1 / h1
            angle1 = round(float(np.degrees(np.arctan(tan_theta))), 3)
        x11, y11, x12, y12 = img1_car_ps[0], img1_car_ps[1], img1_car_ps[2], img1_car_ps[3]
        x21, y21, x22, y22 = img2_car_ps[0], img2_car_ps[1], img2_car_ps[2], img2_car_ps[3]
        iou12 = Tools().IOU(np.array([x11, y11]), np.array([x12, y12]), np.array([x21, y21]), np.array([x22, y22]))

    if len(img2_car_ps) and len(img3_car_ps):
        if abs(img3_car_cor[1] - img2_car_cor[1]) < 5:
            angle2 = 1000
        else:
            h2 = (img3_car_cor[1] - img2_car_cor[1] * 0.99)
            w2 = (img3_car_cor[0] - img2_car_cor[0] * 0.99)
            tan_theta2 = w2 / h2
            angle2 = round(float(np.degrees(np.arctan(tan_theta2))), 3)
        x21, y21, x22, y22 = img2_car_ps[0], img2_car_ps[1], img2_car_ps[2], img2_car_ps[3]
        x31, y31, x32, y32 = img3_car_ps[0], img3_car_ps[1], img3_car_ps[2], img3_car_ps[3]
        iou23 = Tools().IOU(np.array([x21, y21]), np.array([x22, y22]), np.array([x31, y31]), np.array([x32, y32]))

    if len(img1_car_ps) and len(img3_car_ps):
        if abs(img3_car_cor[1] - img1_car_cor[1]) < 5:
            angle3 = 1000
        else:
            h3 = img3_car_cor[1] - img1_car_cor[1] * 0.99
            w3 = img3_car_cor[0] - img1_car_cor[0] * 0.99
            tan_theta = w3 / h3
            angle3 = round(float(np.degrees(np.arctan(tan_theta))), 3)

    # print(angle1, angle2, angle3)
    direction = 'straight'
    # 三张均存在车
    if angle2 and angle1 and angle3:
        delta1 = angle2 - angle1
        delta2 = angle3 - angle2
        if angle2 < 0 and (abs(delta1) > 35 or abs(delta2) > 50 or angle2 < -50):
            if img3_car_cor[0] < img1_car_cor[0] and img3_car_cor[0] < img2_car_cor[0]:
                if angle1 < -30 and angle1 > -80:
                    direction = 'right'
                elif angle1 > 30 and angle1 < 80:
                    direction = 'left'
                elif angle1 > -30 and angle1 < 30:
                    direction = 'straight'
            else:
                direction = 'right'
        elif angle2 > 0 and (abs(delta1) > 35 or abs(delta2) > 40 or angle2 > 50):
            if img3_car_cor[0] > img1_car_cor[0] and img3_car_cor[0] > img2_car_cor[0]:
                if angle1 < -30 and angle1 > -80:
                    direction = 'right'
                elif angle1 > 30 and angle1 < 80:
                    direction = 'left'
                elif angle1 > -30 and angle1 < 30:
                    direction = 'straight'
            else:
                direction = 'left'
        elif angle2 > 0 and (img2_lx < 0.3 or img3_lx < 0.2):
            direction = 'left'
        elif angle2 < 0 and (img2_lx > 0.7 or img3_lx > 0.8):
            direction = 'right'
        else:
            direction = 'straight'
    # 二三张均存在车
    elif angle2:
        if angle2 < -40:
            direction = 'right'
        elif angle2 > 40:
            direction = 'left'
        else:
            direction = 'straight'
    # 一三张均存在车
    elif angle3:
        if angle3 < -35:
            direction = 'right'
        elif angle3 > 35:
            direction = 'left'
        else:
            direction = 'straight'
    # 一二张均存在车
    elif angle1:
        if angle1 < -30:
            direction = 'right'
        elif angle1 > 30:
            direction = 'left'
        elif angle1 > -30 and angle1 < 30:
            direction = 'straight'

    # print('+++++++++++++', iou23)
    if iou23 > 0.7:
        direction = 'stop'

    if show:
        if len(img1_car_ps):
            cv2.rectangle(img1, (img1_car_ps[0], img1_car_ps[1]), (img1_car_ps[2], img1_car_ps[3]), color=(0, 0, 255),
                          thickness=2)
        if len(img2_car_ps):
            cv2.rectangle(img2, (img2_car_ps[0], img2_car_ps[1]), (img2_car_ps[2], img2_car_ps[3]), color=(0, 0, 255),
                          thickness=2)
        if len(img3_car_ps):
            cv2.rectangle(img3, (img3_car_ps[0], img3_car_ps[1]), (img3_car_ps[2], img3_car_ps[3]), color=(0, 0, 255),
                          thickness=2)
        # cv2.imshow(src_plate_num, cv2.resize(img1, None, fx=0.3, fy=0.3))
        # cv2.waitKey(0)
        # cv2.imshow(src_plate_num, cv2.resize(img2, None, fx=0.3, fy=0.3))
        # cv2.waitKey(0)
        # cv2.imshow(src_plate_num, cv2.resize(img3, None, fx=0.3, fy=0.3))
        # cv2.waitKey(0)
        red = (0, 0, 255)
        blue = (255, 0, 0)
        green = (0, 255, 0)
        if angle2 and angle1:
            cv2.line(img, (img1_car_cor[0], img1_car_cor[1]), (img2_car_cor[0], img2_car_cor[1]), red, 5)  # 5
            cv2.putText(img, str(angle1), (img1_car_cor[0], img1_car_cor[1]), cv2.FONT_HERSHEY_COMPLEX, 2, red, 3)
            cv2.line(img, (img2_car_cor[0], img2_car_cor[1]), (img3_car_cor[0], img3_car_cor[1]), red, 5)  # 5
            cv2.putText(img, str(angle2), (img2_car_cor[0], img2_car_cor[1]), cv2.FONT_HERSHEY_COMPLEX, 2, red, 3)
            cv2.line(img, (img1_car_cor[0], img1_car_cor[1]), (img3_car_cor[0], img3_car_cor[1]), blue, 5)  # 5
            cv2.putText(img, str(angle2), (img3_car_cor[0], img3_car_cor[1]), cv2.FONT_HERSHEY_COMPLEX, 2, blue, 3)
        elif angle1:
            cv2.line(img, (img1_car_cor[0], img1_car_cor[1]), (img2_car_cor[0], img2_car_cor[1]), red, 5)  # 5
            cv2.putText(img, str(angle1), (img1_car_cor[0], img1_car_cor[1]), cv2.FONT_HERSHEY_COMPLEX, 2, red, 3)
        elif angle2:
            cv2.line(img, (img2_car_cor[0], img2_car_cor[1]), (img3_car_cor[0], img3_car_cor[1]), red, 5)  # 5
            cv2.putText(img, str(angle2), (img2_car_cor[0], img2_car_cor[1]), cv2.FONT_HERSHEY_COMPLEX, 2, red, 3)
        elif angle3:
            cv2.line(img, (img1_car_cor[0], img1_car_cor[1]), (img3_car_cor[0], img3_car_cor[1]), red, 5)  # 5
            cv2.putText(img, str(angle3), (img3_car_cor[0], img3_car_cor[1]), cv2.FONT_HERSHEY_COMPLEX, 2, red, 3)
        cv2.putText(img, direction, (200, 200), cv2.FONT_HERSHEY_COMPLEX, 5, green, 5)
        # cv2.imshow('{}'.format(direction), cv2.resize(img, None, fx=0.3, fy=0.3))
        # cv2.waitKey(0)
        # cv2.destroyAllWindows()

    return direction, img


# 获取实例分割模型识别的车辆类型
def get_car_type(car_bbox_ps_with_label, img_car_ps):
    for key in car_bbox_ps_with_label.keys():
        for box in car_bbox_ps_with_label[key]:
            if img_car_ps == box:
                return key
    return None


# blendmask车辆识别+车牌识别匹配模块=目标车辆位置及类型(未使用)
# def get_car_box(img_np, src_plate_num, weights_list, device=None):
#     from ai_judge_code_gy.blendmask_detect import BlendMaskDetect
#     if device is None:
#         device = "cuda:0" if torch.cuda.is_available() else "cpu"
#     demo = weights_list[0]
#     plate_det_model = weights_list[1]
#     plate_rec_model = weights_list[2]
#     det = BlendMaskDetect(img_np)
#     predictions, kp_masks = det.mask_predict(demo, show=False)
#     car_bbox_ps_with_label, cars_mask_list = det.class_info(predictions, car_list, div_label=True)
#     car_bbox_ps = [box for key in car_bbox_ps_with_label.keys() for box in car_bbox_ps_with_label[key]]
#     img_car_ps, plate_num, _, _ = CarJudge().confirm_img1_car_ps(img_np, src_plate_num, plate_det_model,
#                                                                  plate_rec_model, car_bbox_ps, device, label=0)
#     car_type = get_car_type(car_bbox_ps_with_label, img_car_ps)
#
#     return img_car_ps, plate_num, car_type


def get_box_full(img_np, src_plate_num, weights_list, device=None, judge_infos=None):
    if device is None:
        device = "cuda:0" if torch.cuda.is_available() else "cpu"
    if judge_infos is None:
        judge_infos = {}
    from ai_judge_code_gy.blendmask_detect import BlendMaskDetect
    """
    与之前相比，只是多返回图中被识别到的所有车辆
    202308018 新增返参：lane2info 返回双实线信息[[[x1,y1],[x2,y2]]]
    """
    demo = weights_list[0]
    plate_det_model = weights_list[1]
    plate_rec_model = weights_list[2]
    det = BlendMaskDetect(img_np)
    predictions, kp_masks = det.mask_predict(demo, show=False)
    nms_ls = det.nms_class_distinct(predictions["instances"].pred_boxes.tensor,
                                    predictions["instances"].scores,
                                    predictions["instances"].pred_classes,
                                    threshold=0.8,
                                    class_ditinct_threshold=0.85)
    predictions, kp_masks = det.prediction_nms_filter(predictions, nms_ls, kp_masks)

    # 车辆相关信息
    car_bbox_ps_with_label, cars_mask_list = det.class_info(predictions, Blend_Judge_Vehicle, div_label=True)
    tmp_box, tmp_labels_list, tmp_score_list = det.class_info2(predictions, Blend_Judge_Vehicle, Blend_Conf)

    car_bbox_ps = [box for key in car_bbox_ps_with_label.keys() for box in car_bbox_ps_with_label[key]]
    img_car_ps, plate_num, img_plate_dict, _ = CarJudge(iiea_cfg=judge_infos["iieaFilterVehicle"]).confirm_img1_car_ps(
        img_np, src_plate_num, plate_det_model,
        plate_rec_model, car_bbox_ps, device,
        label=0)
    car_type = get_car_type(car_bbox_ps_with_label, img_car_ps)
    # iiea白名单配置
    try:
        _, ambulance_filter, fire_filter = judge_infos["iieaFilterVehicle"]
    except:
        ambulance_filter = None
        fire_filter = None
    special_type_list = []
    if ambulance_filter:
        special_type_list.extend(['ambulance_h', 'ambulance_b'])
    if fire_filter:
        special_type_list.extend(['fire_engine_h', 'fire_engine_b'])
    if ambulance_filter is None and fire_filter is None:
        special_type_list = Special_Type
    if img_car_ps:
        if plate_num.endswith("警"):
            judge_infos["vio_judge"] = "no_vio_010"
        tmp_idx = tmp_box.index(img_car_ps)
        tmp_label = tmp_labels_list[tmp_idx]
        tmp_score = tmp_score_list[tmp_idx]
        judge_infos['judgeConf'] = float(tmp_score)
        # 特殊车辆判定
        if tmp_label in special_type_list:
            if tmp_label == "ambulance_h":
                if tmp_score >= Ambulance_Head_Thresh:
                    judge_infos["vio_judge"] = "no_vio_010"
            elif tmp_score >= Special_Car_Thresh:
                judge_infos["vio_judge"] = "no_vio_010"
    # 返回检测框和置信度信息
    img_id = judge_infos["img_id"]

    if img_car_ps:
        conf = det.get_target_conf(predictions, img_car_ps)
        percent_coord = convert_src_coord(img_car_ps, img_np, img_id,
                                          judge_infos["split_mode"], judge_infos["black_height"],
                                          judge_infos["black_pos"])
        tmp_res = {
            "objId": img_id,
            "objType": "vehicle",
            "objConf": float(conf),
            "objCoord": percent_coord
        }
        judge_infos["modelDetectResults"].append(tmp_res)
    # 车牌位置信息
    plate_cor = img_plate_dict[str(img_car_ps)] if img_car_ps else []
    if plate_cor:
        target_idx = str(img_car_ps) + "conf"
        percent_coord = convert_src_coord(plate_cor, img_np, img_id,
                                          judge_infos["split_mode"], judge_infos["black_height"],
                                          judge_infos["black_pos"])
        tmp_res = {
            "objId": img_id,
            "objType": "plate",
            "objConf": float(img_plate_dict[target_idx]),
            "objCoord": percent_coord
        }
        judge_infos["modelDetectResults"].append(tmp_res)

    # lane2相关信息
    lane2_bbox_ps, lane2_mask_list = det.class_info(predictions, ['lane2'], div_label=False)
    point_ls = []
    if lane2_mask_list:
        w = lane2_mask_list[0].shape[1]
        h = lane2_mask_list[0].shape[0]
        for i in lane2_mask_list:
            point_temp = []
            i = i + 0
            idx = np.where(i > 0)
            idx_ls = list(zip(idx[1], idx[0]))
            idx_ls.sort(key=lambda i: (i[1], i[0]), reverse=False)
            point_temp.append(idx_ls[0][0])
            point_temp.append(idx_ls[0][1])
            idx_ls.sort(key=lambda i: (i[1], -i[0]), reverse=True)
            point_temp.append(idx_ls[0][0])
            point_temp.append(idx_ls[0][1])
            point_ls.append(point_temp)
        # 扔掉高度比图像0.6高的点
        point_ls = [i for i in point_ls if i[1] > h * 0.4]
        point_ls.sort(key=lambda i: (i[1], i[0]), reverse=False)
    lane2info = [point_ls[0]] if point_ls else point_ls

    return img_car_ps, plate_num, car_type, car_bbox_ps, lane2info