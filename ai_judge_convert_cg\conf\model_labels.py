# coco类别字典
COCO_DICT = {0: 'person', 1: 'bicycle', 2: 'car', 3: 'motorcycle', 4: 'airplane', 5: 'bus', 6: 'train', 7: 'truck',
             8: 'boat', 9: 'traffic light', 10: 'fire hydrant', 11: 'stop sign', 12: 'parking meter', 13: 'bench',
             14: 'bird',
             15: 'cat', 16: 'dog', 17: 'horse', 18: 'sheep', 19: 'cow', 20: 'elephant', 21: 'bear', 22: 'zebra',
             23: 'giraffe',
             24: 'backpack', 25: 'umbrella', 26: 'handbag', 27: 'tie', 28: 'suitcase', 29: 'frisbee', 30: 'skis',
             31: 'snowboard', 32: 'sports ball', 33: 'kite', 34: 'baseball bat', 35: 'baseball glove', 36: 'skateboard',
             37: 'surfboard', 38: 'tennis racket', 39: 'bottle', 40: 'wine glass', 41: 'cup', 42: 'fork', 43: 'knife',
             44: 'spoon', 45: 'bowl', 46: 'banana', 47: 'apple', 48: 'sandwich', 49: 'orange', 50: 'broccoli',
             51: 'carrot', 52: 'hot dog', 53: 'pizza', 54: 'donut', 55: 'cake', 56: 'chair', 57: 'couch',
             58: 'potted plant',
             59: 'bed', 60: 'dining table', 61: 'toilet', 62: 'tv', 63: 'laptop', 64: 'mouse', 65: 'remote',
             66: 'keyboard',
             67: 'cell phone', 68: 'microwave', 69: 'oven', 70: 'toaster', 71: 'sink', 72: 'refrigerator', 73: 'book',
             74: 'clock', 75: 'vase', 76: 'scissors', 77: 'teddy bear', 78: 'hair drier', 79: 'toothbrush'}
# 交通信号灯类别字典
LIGHT_DICT = {0: 'green_s', 1: 'green_r', 2: 'green_l', 3: 'red_s', 4: 'red_r', 5: 'red_l', 6: 'yellow_s',
              7: 'yellow_r', 8: 'yellow_l'}
Phone_Hand = {0:'hand',1:'phone',2:'phone_s'}
# 交通元素类别字典
TRAFFIC_DICT = {0: '_background_', 1: 'lane1', 2: 'lane2', 3: 'stop_area', 4: 'car_h', 5: 'car_b', 6: 'bus_h',
                7: 'bus_b', 8: 'truck_h', 9: 'truck_b', 10: 'ambulance_h', 11: 'ambulance_b', 12: 'fire_engine_h',
                13: 'fire_engine_b', 14: 'police_car_h', 15: 'police_car_b', 16: 'engineering_car_h',
                17: 'engineering_car_b', 18: 'motorcycle_h', 19: 'motorcycle_b', 20: 'arrow_l', 21: 'arrow_s',
                22: 'arrow_r', 23: 'arrow_t', 24: 'stop_line', 25: 'zebra_line', 26: 'no_entry', 27: 'arrow_s_t',
                28: 'arrow_l_t', 29: 'arrow_r_t', 30: 'aux_police', 31: 'no_parking_area'}
# 车牌类型字典
PLATE_TYPE_DICT = {0: "single_layer", 1: "double_layer"}
# 压线字典
COVER_LINE_DICT = {0: "waste", 1: "cover_line"}
# 安全带/打电话字典
BELT_PHONE_DICT = {-1:"belt_phone",0: "phone", 1: "belt", 2: "waste"}