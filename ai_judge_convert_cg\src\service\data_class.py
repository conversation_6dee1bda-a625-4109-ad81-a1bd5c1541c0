from logging import Logger
from typing import Dict, Any, List, Union
import numpy as np
from conf.config import OptConf
from src.module.image_model import ModelResults
from src.module.request_parser import RequestCtx
from dataclasses import dataclass


@dataclass
class JudgeResCore:
    status: int  # 0:未分析 1:正片 2:废片
    confid: float  # 判定置信度
    code: str  # 判定结果代码
    desc: str  # 判定结果描述

    def to_dict(self) -> Dict[str, Any]:
        return {
            'judgeStatus': self.status,
            'judgeConf': self.confid,
            'resultCode': self.code,
            'resultDesc': self.desc,
        }


class JudgeResult:
    """
    判定结果类，存放各种中间结果，连接各个模块
    """

    def __init__(self, request: RequestCtx, logger: Logger):
        self._request: RequestCtx = request  # 违法数据解析

        self._logger: Logger = logger
        self._valid: bool = True  # 为False时停止后续判定
        self._act_results: Dict[str, bool] = {}  # 判定动作的结果（成功与否） TODO:判罚代码整理
        self._act_details: Dict[str, Any] = {}  # 判定动作的细节
        self._tfc_elements: Dict[str, ModelResults] = {}  # 交通要素：人、车、灯、线等
        self._judge_res_core: JudgeResCore = JudgeResCore(0, 1.0, "", "")  # 默认值
        self._conf = OptConf
        self._judge_result: Dict[str, dict] = self._conf.JudgeResult  # 判罚结果描述字典

    @property
    def request(self) -> RequestCtx:
        return self._request

    @property
    def logger(self) -> Logger:
        return self._logger

    @property
    def valid(self) -> bool:
        return self._valid

    @property
    def tfc_elements(self) -> Dict[str, ModelResults]:
        return self._tfc_elements

    @tfc_elements.setter
    def tfc_elements(self, new_values: Dict) -> None:
        self._tfc_elements = new_values

    @property
    def act_results(self) -> dict:
        return self._act_results

    @property
    def act_details(self) -> dict:
        return self._act_details

    @act_details.setter
    def act_details(self, new_values: Dict) -> None:
        self._act_details = new_values

    def set_invalid(self) -> None:
        self._valid = False

    def init_tfc_elements(self, img_num: int) -> None:
        """
        根据图片数量初始化交通元素字典
        :param img_num: 图片切分后的数量
        """
        tfc_elements = {}
        for i in range(img_num):
            tfc_elements[f"img{i + 1}"] = ModelResults()
        self._tfc_elements = tfc_elements

    def set_tfc_elements(self, img_idx: int, res: ModelResults) -> None:
        """
        按图片存放交通元素推理结果
        :param img_idx: 切分图片的索引
        :param res: 功能模型的输出结果
        """
        self._tfc_elements[f"img{img_idx + 1}"].merge_results(res)

    def set_judres_core(self, judge_status: int = 0, judge_confid: float = 1.0, result_code: str = "",
                        result_desc: str = "") -> None:
        """
        设置最终判罚结果，调用后流程即将中止
        :param judge_status:判罚结果状态
        :param judge_confid:判罚置信度
        :param result_code:判罚结果代码
        :param result_desc:判罚结果描述
        """
        match_code = self.request.match_viocode
        if result_code and not result_desc and match_code in self._judge_result:
            result_desc = self._judge_result[match_code].get(result_code, f"作废,{result_code}")
        self._judge_res_core = JudgeResCore(judge_status, judge_confid, result_code, result_desc)
        if judge_status in [0, 2]:
            self.set_invalid()

    def add_act_details(self, act: str, detail: Dict[str, Any] = None) -> None:
        if detail:
            self._act_details.update(detail)
        self._act_results[act] = self._valid  # 记录动作执行的有效性

    def print_details(self) -> None:  # 展示所有细节，避免输出冗长的信息
        formatted_items = []
        for key, value in self._act_details.items():
            if isinstance(value, list) and all(isinstance(item, np.ndarray) for item in value):
                tmp = [f"shape={i.shape}, dtype={i.dtype}" for i in value]
                formatted_items.append(f"'{key}': {tmp}")
            elif key in ["veh_img", "merge_tfc_element"]:
                tmp = {k: len(v) for k, v in value.items()}
                formatted_items.append(f"'{key}': {tmp}")
            elif key == "bkg_img":
                tmp = value.shape
                formatted_items.append(f"'{key}': {tmp}")
            else:
                formatted_items.append(f"'{key}': {value}")
        msg = "{" + ", ".join(formatted_items) + "}"
        self._logger.info(f"act_details:{msg}")

    def get_judge_res_core(self) -> Dict[str, Any]:
        return self._judge_res_core.to_dict()

    def get_act_detail(self, name: str, default=None) -> Any:
        return self._act_details.get(name, default)

    def get_tfc_elements(self, img_key: str, superclass: str, attr: str, to_array=False) -> Union[List, np.ndarray]:
        res = self.tfc_elements.get(img_key, ModelResults()).get_results(superclass=superclass, attr=attr)
        if to_array:
            res = np.asarray(res)
        return res

    def add_judge_result(self):  # 添加红绿灯、目标车辆及车牌的坐标框、置信度等
        self.request.data["modelDetectResults"] = []  # TODO：模型检测结果
        self.request.data["modelJudgeResult"] = self.get_judge_res_core()
