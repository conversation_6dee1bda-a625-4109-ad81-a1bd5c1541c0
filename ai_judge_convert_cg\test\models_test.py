import sys

sys.path.append("..")
sys.path.append("../libs")

import pytest
from src.module.image_model import NvidiaArch, AscendArch, ModelResults,ModelFactory
from libs.plate_recognition.plate_rec import get_plate_rec_landmark
from conf.config import *
from src.utils import tools, logic_judge
import numpy as np
import cv2
from glob import glob
import os
import platform
from pathlib import Path
root = "./test_data"

skipping = pytest.mark.skip(reason='暂不测试')

model_arch = NvidiaArch() if BaseConf.arch else AscendArch()


class TestFuncModel:

    @pytest.fixture
    def add_model_params(self):
        model_dict = ModelFactory().init_models()
        # current_test_id = request.node.callspec.id.encode('utf-8').decode('unicode-escape')
        return model_dict

    def show_det_res(self, img, box, model_name, cls, save_name=None):
        tmp_img = img[box[1]:box[3], box[0]:box[2]]
        if platform.system() == "Linux":
            save_path = f"/home/<USER>/ai_judge_convert/vis_img/{model_name}/{cls}"
        else:
            save_path = rf"D:\plate_test\{model_name}\{cls}"

        os.makedirs(save_path, exist_ok=True)
        num = 1
        dst_path = f"{save_path}/{save_name}_{num}.jpg" if save_name else f"{save_path}/{num}.jpg"
        while os.path.exists(dst_path):
            num += 1
            dst_path = f"{save_path}/{save_name}_{num}.jpg" if save_name else f"{save_path}/{num}.jpg"
        cv2.imwrite(str(Path(dst_path)), tmp_img)


    def det_multi_img_eval(self, box_list, right_box_list, cls_list=None, right_cls_list=None):
        iou_array = logic_judge.compute_iou(box_list, right_box_list)
        pred_idx, right_idx = np.where(iou_array >= 0.9)
        right_num = 0
        finish_judge_idx1 = []
        finish_judge_idx2 = []
        print("pred_idx:", pred_idx)
        print("right_idx:", right_idx)
        assert len(pred_idx), print(f"检测的坐标框结果异常！")
        for idx in range(len(pred_idx)):
            if right_idx[idx] in finish_judge_idx1 or pred_idx[idx] in finish_judge_idx2:
                continue
            if cls_list and right_cls_list:
                assert cls_list[pred_idx[idx]] == right_cls_list[right_idx[idx]], print(
                    f"类别结果对应错误！{cls_list[pred_idx[idx]]} {right_cls_list[right_idx[idx]]}")
            finish_judge_idx1.append(right_idx[idx])
            finish_judge_idx2.append(pred_idx[idx])
            right_num += 1
        assert right_num == len(right_box_list), print(f"检测结果不完全准确！{finish_judge_idx1} {finish_judge_idx2}")

    def test_match_car(self, add_model_params):
        img_path = f"{root}/1625"

        path_list = glob(f"{img_path}/*.jpg")
        img_list = [tools.cv_imread(path) for path in path_list]
        model_dict = add_model_params
        #
        target_plate = "鄂A9A23F"

        ignore_judge_idx = []

        src_img = img_list[0]
        veh_person_det = model_dict["VehPersonModel"]
        plate_det = model_dict["PlateDetModel"]
        plate_rec = model_dict["PlateRecModel"]
        res_list = []

        veh_box_list = []
        plate_box_list = []

        veh_label_list = []
        plate_label_list = []

        # 图片切分
        img_list = tools.split_img(src_img, 111)

        for img_idx, img in enumerate(img_list):
            if img_idx in ignore_judge_idx:
                continue
            veh_results = veh_person_det.run(img,det_cls=["car", "truck", "bus", "motorcycle", "bicycle"])
            print(f"{img_idx + 1} 人车模型输出:{veh_results}")
            for res in veh_results.get_results():
                car_ps = res.box
                veh_box_list.append(car_ps)
                veh_label_list.append(res.subclass)
                # 车牌识别去除车辆上三分之一部分
                h_bias = (car_ps[3] - car_ps[1]) // 3
                veh_img = img[car_ps[1] + h_bias:car_ps[3], car_ps[0]:car_ps[2]]
                plate_det_results = plate_det.run(veh_img)  # 车牌检测
                print(f"{img_idx + 1} 车辆坐标：{car_ps} 车牌检测模型输出:{plate_det_results}")
                # self.show_det_res(img, car_ps, "match_car", res.subclass)
                for res2 in plate_det_results.get_results():
                    assert res2.box, print("车牌检测结果异常!")
                    plate_box = res2.box
                    plate_type = res2.subclass
                    plate_key_points = res2.get_ext_value("key_points")
                    # 修正车牌坐标
                    tmp_cor = plate_box
                    car_plate = [tmp_cor[0] + car_ps[0], tmp_cor[1] + car_ps[1] + h_bias, tmp_cor[2] + car_ps[0],
                                 tmp_cor[3] + car_ps[1] + h_bias]
                    plate_box_list.append(car_plate)
                    plate_label_list.append(res2.subclass)
                    # self.show_det_res(img, car_plate, "plate_det", "single_layer")

                    # 车牌抠图加透视变换
                    plate_img = get_plate_rec_landmark(veh_img, plate_key_points, plate_type)
                    plate_rec_results = plate_rec.run(plate_img)  # 车牌识别
                    print(f"{img_idx + 1} 车牌识别模型输出:{plate_rec_results}")
                    plate_rec_results = plate_rec_results.get_results()[0]

                    assert plate_rec_results is not None, print("车牌识别结果异常!")  # 为空字符串
                    plate = plate_rec_results.text

                    res_list.append((img_idx, plate))
                    # if plate:
                    #     h,w,_ = plate_img.shape
                    #可视化
                    # cv2.rectangle(img,(car_ps[0],car_ps[1]),(car_ps[2],car_ps[3]),(255,255,0),2)
                    # cv2.rectangle(img,(car_plate[0],car_plate[1]),(car_plate[2],car_plate[3]),(0,255,0),2)
                    # cv2.putText(img,plate,(car_plate[2],(car_plate[1]+car_plate[3])//2),cv2.FONT_HERSHEY_SIMPLEX,1.5,(0,0,255),2)
                    # self.show_det_res(img,[car_ps[0],car_ps[1],car_ps[2],car_ps[3]],"plate","plate",plate)

        right_car_box_list = [[2529, 1132, 3332, 1965], [1736, 1250, 2216, 1724], [3070, 823, 3262, 989],
                              [3306, 788, 3391, 917], [2407, 555, 2628, 728], [3196, 626, 3279, 704],
                              [1924, 480, 2081, 606], [11, 521, 100, 593], [61, 518, 155, 590], [3341, 528, 3389, 589],
                              [142, 511, 175, 574], [3322, 509, 3390, 572], [3093, 462, 3344, 567],
                              [544, 478, 603, 567], [202, 497, 236, 563], [2179, 418, 2301, 561], [249, 474, 306, 545],
                              [2554, 464, 2687, 536], [324, 477, 379, 532], [336, 468, 413, 528],
                              [1911, 417, 2043, 526], [371, 466, 426, 526], [2099, 385, 2192, 487],
                              [1448, 340, 1574, 451], [1610, 360, 1719, 449], [1982, 341, 2066, 422],
                              [1536, 342, 1603, 411], [2061, 339, 2131, 401], [1966, 284, 2021, 327],
                              [2014, 226, 2057, 275], [1934, 240, 1972, 272], [1988, 214, 2019, 244],
                              [1947, 200, 1977, 233], [2501, 1069, 3237, 1821], [1801, 995, 2171, 1336],
                              [3039, 836, 3244, 989], [3186, 800, 3271, 949], [3305, 789, 3392, 913],
                              [3258, 631, 3356, 719], [2374, 518, 2578, 689], [13, 520, 102, 594], [46, 516, 153, 591],
                              [3344, 526, 3389, 590], [1939, 461, 2091, 578], [144, 510, 175, 576],
                              [3325, 504, 3389, 572], [543, 461, 603, 570], [3075, 460, 3327, 566],
                              [203, 495, 236, 563], [247, 473, 309, 547], [2181, 401, 2297, 536],
                              [2554, 459, 2691, 536], [326, 479, 374, 535], [349, 471, 402, 532], [370, 466, 429, 528],
                              [1921, 405, 2046, 506], [2096, 375, 2183, 473], [1449, 342, 1574, 451],
                              [1612, 359, 1719, 449], [1980, 337, 2062, 413], [1526, 342, 1603, 413],
                              [2057, 334, 2128, 395], [1968, 280, 2022, 322], [2015, 223, 2058, 271],
                              [1934, 237, 1974, 270], [2450, 972, 3101, 1621], [2955, 831, 3170, 988],
                              [3177, 803, 3275, 945], [3281, 786, 3380, 907], [1898, 664, 2126, 855],
                              [2301, 468, 2471, 601], [12, 522, 102, 593], [63, 514, 152, 590], [3310, 487, 3391, 576],
                              [143, 508, 174, 574], [545, 458, 606, 567], [3008, 459, 3256, 564], [202, 496, 236, 563],
                              [249, 474, 308, 547], [3251, 482, 3315, 540], [2555, 465, 2671, 535],
                              [324, 479, 374, 535], [338, 472, 408, 532], [371, 466, 428, 527], [1991, 420, 2121, 517],
                              [2192, 363, 2286, 475], [1949, 380, 2054, 469], [1448, 341, 1574, 451],
                              [1610, 359, 1718, 449], [2088, 349, 2163, 435], [1543, 343, 1604, 411],
                              [1973, 320, 2043, 379], [2044, 320, 2111, 376], [1969, 269, 2018, 309],
                              [2016, 219, 2055, 263], [1938, 232, 1972, 262], [1956, 185, 1986, 217],
                              [2818, 276, 3391, 1968], [1238, 533, 2189, 1534]]

        right_plate_box_list = [[2978, 1854, 3107, 1901], [1920, 1573, 2043, 1614], [2527, 640, 2570, 657],
                                [1983, 534, 2017, 550], [1958, 464, 1984, 475], [2138, 438, 2161, 448],
                                [1646, 423, 1670, 432], [2907, 1718, 3028, 1761], [1940, 1207, 2032, 1238],
                                [2485, 607, 2525, 623], [1991, 513, 2024, 525], [2195, 481, 2233, 494],
                                [1969, 449, 1997, 461], [2131, 424, 2158, 433], [1484, 429, 1504, 436],
                                [1647, 424, 1667, 432], [2806, 1530, 2915, 1567], [1983, 765, 2039, 786],
                                [2390, 529, 2430, 543], [2033, 460, 2065, 472], [1991, 416, 2016, 424],
                                [1480, 429, 1503, 436], [1601, 1211, 1844, 1298]]

        right_car_label_list = ['truck', 'car', 'motorcycle', 'motorcycle', 'car', 'motorcycle', 'car', 'motorcycle',
                                'motorcycle', 'car', 'motorcycle', 'car', 'car', 'motorcycle', 'motorcycle', 'truck',
                                'motorcycle', 'motorcycle', 'motorcycle', 'motorcycle', 'car', 'motorcycle', 'truck',
                                'truck', 'car', 'car', 'truck', 'car', 'car', 'car', 'car', 'car', 'car', 'truck',
                                'car', 'motorcycle', 'motorcycle', 'motorcycle', 'motorcycle', 'car', 'motorcycle',
                                'motorcycle', 'car', 'car', 'motorcycle', 'car', 'motorcycle', 'car', 'motorcycle',
                                'motorcycle', 'truck', 'motorcycle', 'motorcycle', 'motorcycle', 'motorcycle', 'car',
                                'truck', 'truck', 'car', 'car', 'truck', 'car', 'car', 'car', 'car', 'truck',
                                'motorcycle', 'motorcycle', 'motorcycle', 'car', 'car', 'motorcycle', 'motorcycle',
                                'car', 'motorcycle', 'motorcycle', 'car', 'motorcycle', 'motorcycle', 'motorcycle',
                                'motorcycle', 'motorcycle', 'motorcycle', 'motorcycle', 'car', 'truck', 'car', 'truck',
                                'car', 'truck', 'truck', 'car', 'car', 'car', 'car', 'car', 'car', 'truck', 'car']

        right_plate_label_list = ['single_layer', 'single_layer', 'single_layer', 'single_layer', 'single_layer',
                                  'single_layer', 'single_layer', 'single_layer', 'single_layer', 'single_layer',
                                  'single_layer', 'single_layer', 'single_layer', 'single_layer', 'single_layer',
                                  'single_layer', 'single_layer', 'single_layer', 'single_layer', 'single_layer',
                                  'single_layer', 'single_layer', 'single_layer']

        self.det_multi_img_eval(veh_box_list, right_car_box_list, veh_label_list, right_car_label_list)  # 车辆检测模型测试判定
        self.det_multi_img_eval(plate_box_list, right_plate_box_list, plate_label_list,
                                right_plate_label_list)  # 车牌检测模型测试判定

        match_idx_list = []
        for res in res_list:
            img_idx, plate = res
            if img_idx in match_idx_list:
                continue
            if plate == target_plate or logic_judge.plate_fuzzy_match(plate, target_plate, [3, 2]):
                match_idx_list.append(img_idx)
        assert len(match_idx_list) == (len(img_list) - len(ignore_judge_idx)), print(
            f"车牌匹配失败，匹配情况：{match_idx_list}")

    def test_light(self, add_model_params):
        img_path = f"{root}/1625"

        path_list = glob(f"{img_path}/*.jpg")
        img_list = [tools.cv_imread(path) for path in path_list]
        model_dict = add_model_params
        src_img = img_list[0]
        light_det = model_dict["LightModel"]
        # 图片切分
        img_list = tools.split_img(src_img, 411)
        model_results = ModelResults()
        for img_idx, img in enumerate(img_list):
            if img_idx > 2:
                continue
            results = light_det.run(img, thresh=0.25)
            print(f"第{img_idx + 1}张图识别结果：{results}")
            model_results.merge_results(results)
        box_list = model_results.get_results(attr="box")
        cls_list = model_results.get_results(attr="subclass")
        tmp = model_results.get_results(superclass="red_s", attr="box")
        print("tmp:", tmp)
        print("box_list", box_list)
        print("cls_list", cls_list)
        right_box_list = [[2111, 107, 2131, 128], [1970, 107, 1989, 127], [2112, 108, 2132, 128],
                          [1969, 108, 1989, 127], [2111, 107, 2131, 128], [1969, 106, 1989, 125]]
        right_cls_list = ['red_s', 'red_l', 'red_s', 'red_l', 'red_s', 'red_l']
        self.det_multi_img_eval(box_list, right_box_list, cls_list, right_cls_list)

    def test_traffic_det(self, add_model_params):
        img_path = f"{root}/1625"

        path_list = glob(f"{img_path}/*.jpg")
        img_list = [tools.cv_imread(path) for path in path_list]
        model_dict = add_model_params
        src_img = img_list[0]
        traffic_det = model_dict["SegModel"]
        # 图片切分
        img_list = tools.split_img(src_img, 411)
        model_results = ModelResults()
        mask_list = []
        for img_idx, img in enumerate(img_list):
            if img_idx > 2:
                continue
            # results = traffic_det.run(img,
            #                           det_cls=["car_b", "car_h", "truck_b", "truck_h", "bus_b", "bus_h", "motorcycle_h",
            #                                    "motorcycle_b", "lane1", "lane2", "stop_line", 'arrow_l', 'arrow_s',
            #                                    'arrow_r', "ambulance_b", "ambulance_h", "engineering_car_h",
            #                                    "engineering_car_b", "fire_engine_b", "fire_engine_h"])
            results = traffic_det.run(img, det_cls=["car_h", "car_b", "bus_h", "bus_b", "truck_h", "truck_b", "engineering_car_h",
                       "engineering_car_b", 'motorcycle_h', 'motorcycle_b', 'ambulance_h', 'ambulance_b', 'fire_engine_h',
                       'fire_engine_b', "police_car_h", "police_car_b"])
            print(f"第{img_idx + 1}张图识别结果：{results}")
            exit()
            # print(f"{img_idx + 1} {results}")
            # for res in results.get_results():
            #     box = res.box
            #     cls = res.subclass
            #     box_list.append(box)
            #     cls_list.append(cls)
            # self.show_det_res(img,box,"ElementModel",cls)
            model_results.merge_results(results)

        #     masks = results.get_results(superclass="vehicle2",attr="mask")
        #     mask_list.append(masks)
        #     cv2.imwrite(f"./imgs/{img_idx}.jpg",img)
        # np.save("./mask_list.npy",mask_list)

        box_list = model_results.get_results(attr="box")
        cls_list = model_results.get_results(attr="subclass")
        right_box_list = [[1583, 1308, 1756, 2009], [2351, 1296, 2613, 2008], [1972, 1726, 2209, 2008],
                          [569, 1310, 1105, 2007], [1022, 1568, 1422, 2006], [2531, 1135, 3331, 1960],
                          [1742, 1253, 2214, 1724], [1073, 1284, 1795, 1312], [2220, 1273, 2526, 1297],
                          [3076, 1260, 3388, 1280], [522, 879, 2898, 1136], [2782, 702, 3079, 938], [0, 625, 466, 803],
                          [2409, 555, 2628, 727], [3194, 578, 3283, 702], [1925, 481, 2084, 606], [545, 465, 602, 569],
                          [3093, 464, 3345, 565], [2178, 419, 2308, 565], [2557, 422, 2688, 535],
                          [2397, 450, 2543, 532], [1914, 418, 2042, 525], [1123, 467, 1926, 501],
                          [2096, 385, 2196, 486], [1188, 457, 1820, 478], [1215, 383, 1433, 470],
                          [1273, 430, 1408, 470], [1730, 356, 1821, 467], [1386, 433, 1452, 467],
                          [1554, 425, 1612, 467], [1631, 447, 1688, 463], [1827, 442, 1860, 460],
                          [1438, 336, 1570, 450], [1612, 360, 1719, 446], [1982, 342, 2067, 423],
                          [1553, 345, 1606, 414], [2057, 341, 2133, 400], [1695, 349, 1717, 364],
                          [1967, 284, 2022, 326], [1884, 295, 1924, 325], [2016, 225, 2058, 275],
                          [1933, 239, 1973, 272], [1988, 218, 2017, 243], [1949, 203, 1974, 230],
                          [1989, 195, 2012, 217], [2353, 1297, 2614, 2008], [1586, 1307, 1762, 2008],
                          [572, 1312, 1100, 2008], [1970, 1592, 2208, 2006], [1025, 1568, 1421, 2006],
                          [2500, 1070, 3234, 1812], [1804, 997, 2169, 1337], [1069, 1285, 1785, 1313],
                          [2169, 1273, 2549, 1297], [3100, 1261, 3390, 1279], [2780, 699, 3058, 929],
                          [1, 626, 472, 805], [3254, 584, 3355, 716], [2377, 521, 2578, 687], [1941, 462, 2090, 577],
                          [545, 464, 602, 571], [3074, 462, 3328, 564], [2560, 425, 2687, 537], [2176, 402, 2302, 537],
                          [569, 426, 610, 524], [2399, 452, 2517, 522], [1923, 407, 2049, 507], [1118, 467, 1934, 503],
                          [1159, 462, 1893, 486], [2093, 375, 2184, 474], [1214, 385, 1436, 471],
                          [1270, 428, 1410, 471], [1555, 425, 1615, 468], [1900, 423, 1926, 468],
                          [1732, 356, 1822, 466], [1386, 433, 1453, 466], [1826, 443, 1861, 460],
                          [1439, 334, 1574, 450], [1613, 361, 1720, 447], [1560, 346, 1606, 415],
                          [1980, 336, 2064, 414], [2054, 336, 2129, 389], [1696, 349, 1719, 365],
                          [1969, 281, 2022, 321], [2017, 223, 2058, 272], [1935, 238, 1973, 269],
                          [1988, 220, 2017, 244], [1950, 208, 1975, 231], [2352, 1296, 2613, 2009],
                          [1583, 1307, 1762, 2009], [1972, 1591, 2208, 2007], [2824, 1630, 3102, 2007],
                          [571, 1310, 1108, 2007], [1023, 1562, 1424, 2006], [3100, 1480, 3383, 1837],
                          [2456, 977, 3101, 1614], [1039, 1272, 2474, 1313], [1905, 1275, 2125, 1299],
                          [2209, 1274, 2491, 1296], [3029, 1261, 3383, 1282], [2955, 787, 3175, 987],
                          [3286, 728, 3389, 904], [2778, 693, 2996, 885], [1899, 662, 2128, 857], [0, 626, 464, 803],
                          [2305, 466, 2472, 599], [547, 463, 604, 569], [3008, 458, 3261, 564], [3253, 460, 3315, 536],
                          [2558, 420, 2680, 535], [1993, 417, 2123, 518], [1116, 468, 1989, 502],
                          [1212, 461, 1901, 487], [1531, 436, 1624, 481], [2187, 364, 2288, 477],
                          [1215, 384, 1434, 470], [1260, 418, 1415, 470], [1953, 376, 2058, 467],
                          [1385, 432, 1453, 467], [1555, 425, 1611, 467], [1731, 356, 1821, 466],
                          [1633, 447, 1686, 463], [1438, 335, 1571, 450], [1612, 360, 1719, 446],
                          [2090, 349, 2170, 436], [1553, 345, 1606, 414], [1970, 320, 2046, 381],
                          [2042, 321, 2111, 376], [1695, 349, 1717, 365], [1886, 296, 1919, 328],
                          [1970, 272, 2019, 310], [2017, 218, 2055, 264], [1938, 234, 1973, 263],
                          [1989, 216, 2017, 239], [1949, 206, 1973, 225]]
        right_cls_list = ['lane1', 'lane1', 'arrow_s', 'lane2', 'arrow_l', 'truck_b', 'car_b', 'stop_line', 'stop_line',
                          'stop_line', 'stop_line', 'lane1', 'lane1', 'car_b', 'motorcycle_b', 'car_b', 'motorcycle_h',
                          'car_b', 'car_b', 'motorcycle_b', 'lane1', 'car_b', 'stop_line', 'car_b', 'stop_line',
                          'lane1', 'lane1', 'lane1', 'lane1', 'lane1', 'car_h', 'arrow_s', 'car_h', 'car_h', 'car_b',
                          'car_h', 'car_b', 'lane1', 'car_b', 'lane1', 'truck_b', 'car_b', 'car_b', 'car_b', 'car_b',
                          'lane1', 'lane1', 'lane2', 'arrow_s', 'arrow_l', 'truck_b', 'car_b', 'stop_line', 'stop_line',
                          'stop_line', 'lane1', 'lane1', 'motorcycle_h', 'car_b', 'car_b', 'motorcycle_h', 'car_b',
                          'motorcycle_b', 'car_b', 'motorcycle_h', 'lane1', 'car_b', 'stop_line', 'stop_line', 'car_b',
                          'lane1', 'lane1', 'lane1', 'lane2', 'lane1', 'lane1', 'arrow_s', 'car_h', 'car_h', 'car_h',
                          'car_b', 'car_b', 'lane1', 'car_b', 'truck_b', 'car_b', 'car_b', 'car_b', 'lane1', 'lane1',
                          'arrow_s', 'arrow_s', 'lane2', 'arrow_l', 'lane1', 'truck_b', 'stop_line', 'stop_line',
                          'stop_line', 'stop_line', 'motorcycle_b', 'motorcycle_b', 'lane1', 'car_b', 'lane1', 'car_b',
                          'motorcycle_h', 'car_b', 'motorcycle_b', 'motorcycle_b', 'car_b', 'stop_line', 'stop_line',
                          'lane1', 'motorcycle_b', 'lane1', 'lane1', 'car_b', 'lane1', 'lane1', 'lane1', 'car_h',
                          'car_h', 'car_h', 'car_b', 'car_h', 'car_b', 'car_b', 'lane1', 'lane1', 'car_b', 'truck_b',
                          'car_b', 'car_b', 'car_b']

        self.det_multi_img_eval(box_list, right_box_list, cls_list, right_cls_list)
    @skipping
    def test_cover_Line(self, add_model_params):
        img_path = f"{root}/1345"

        path_list = glob(f"{img_path}/*.jpg")
        img_list = [tools.cv_imread(path) for path in path_list]
        model_dict = add_model_params
        src_img = img_list[0]
        cls_model = model_dict["CoverLineModel"]
        cls_results = cls_model.run(src_img)
        print(f"压线分类模型输出:{cls_results}")
        res = cls_results.get_results()[0]
        assert res.subclass == "压线", print(f"模型判定结果不符:{res.subclass}")

    def test_phone_belt(self, add_model_params):
        img_path = f"{root}/1120"

        path_list = glob(f"{img_path}/*.jpg")
        img_list = [tools.cv_imread(path) for path in path_list]
        model_dict = add_model_params
        src_img = img_list[0]
        cls_model = model_dict["BeltPhoneModel"]
        det_model = model_dict["VehPersonModel"]
        box_list = []
        cls_list = []
        # 图片切分
        img_list = tools.split_img(src_img, 411)
        for img_idx, img in enumerate(img_list):
            person_results = det_model.run(img, det_cls=["person"])
            print(f"{img_idx + 1} 人车模型输出:{person_results}")
            for res in person_results.get_results():
                p_box = res.box
                p_img = img[p_box[1]:p_box[3], p_box[0]:p_box[2]]
                box_list.append(p_box)
                cls_results = cls_model.run(p_img,thresh=0.4)
                print(f"{img_idx + 1} 安全带分类模型输出:{cls_results}")
                res2 = cls_results.get_results()[0]
                cls_list.append(res2.subclass)
        right_box_list = [[1846, 2055, 2101, 2158], [3073, 743, 3410, 946], [703, 131, 1030, 436], [425, 151, 704, 413],
                          [3185, 987, 3551, 1211], [693, 179, 1027, 490], [3314, 1291, 3732, 1541],
                          [675, 200, 1031, 527], [2438, 596, 2989, 947]]
        right_cls_list = ['belt', 'belt', 'waste', 'waste', 'belt', 'phone', 'belt', 'waste',
                          'belt']
        self.det_multi_img_eval(box_list, right_box_list, cls_list, right_cls_list)

    def test_sim_model(self, add_model_params):
        img_path = f"{root}/sim"

        path_list = glob(f"{img_path}/*.jpg")
        veh_img_list = [tools.cv_imread(path) for path in path_list]
        model_dict = add_model_params
        target_car = cv2.imread("../test_data/target_car.jpg")
        sim_model = model_dict["SimilarModel"]
        sim_dist_list = []
        for per_car in veh_img_list:
            results = sim_model.run([target_car, per_car])
            print(f"相似度匹配模型输出:{results}")
            res = results.get_results()[0]
            sim_dist = res.score
            sim_dist_list.append(sim_dist)
        print(sim_dist_list)
        assert np.argmin(sim_dist_list) == 0 and min(sim_dist_list) < 0.2, print("相似度匹配结果异常！")


if __name__ == '__main__':
    pytest.main()
