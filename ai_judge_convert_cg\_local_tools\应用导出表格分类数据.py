import os
import shutil
import pandas as pd
from tqdm import tqdm
root = r"E:\AI审片项目和资料汇总\高密\违法数据\违法数据_20250314173939_1_2000"
img_root = f"{root}/merge_data"
sheet_name = "tmp"
# sheet_name = "正片"
# sheet_name = "废片"

pd_data = pd.read_excel(f"{root}/违法数据_20250314173939_1_2000.xlsx",sheet_name=sheet_name)
url_list = pd_data['过车图片1'].tolist()
filter_path = f"{root}/{sheet_name}"
os.makedirs(filter_path,exist_ok=True)

for idx,name in enumerate(tqdm(url_list)):
    # print(name)
    shutil.copy(f"{img_root}/{name}",filter_path)