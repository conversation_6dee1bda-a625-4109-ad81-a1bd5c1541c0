import pandas as pd

# **********************************************************
# 模型仓库授权
IP = "************"
AppKey = "29884383"
AppSecret = "QUKdCSqDrjA4ZG3Uzs50"
# 违法图片拼接方式
BLACK_HEIGHT = 0.008  # 黑框高度的占比
# 1:四图横向拼接去除下黑框 2:四图横向拼接上下均分 3:两图横向拼接 4:三图横向拼接 5:4图横拼去除上黑框 6:6图横拼去除上黑框 7:上2下1  0:无拼接
SPLIT_DICT = {
    "1625": 6,  # 闯红灯
    "1208": 6,  # 不按导向行驶
    "1345": 3,  # 违反禁止标示线
    "1301": 3,  # 逆行
    "1357": 2,  # 不礼让行人
    "1352": 3,  # 超速
    "1344": 2,  # 违法停车
    "1101": 0,  # 开车未系安全带
    "1223": 0,  # 开车打电话
    "8013": 3,  # 闯限行
    "7102": 0,  # 占用公交车道
}
# 违法代码匹配
Vio_Type_Match = {
    "1625": ["1625", "16251", "16252", "1302"],  # 闯红灯
    "1208": ["1208"],  # 不按导向行驶
    "1345": ["1345", "13451", "1230", "13453"],  # 违反禁止标示线
    "1301": ["1301"],  # 逆行
    "1357": ["1357", "1358", "1356"],  # 不礼让行人
    "1352": ["1349", "1350", "1351", "1352", "7626", "4713", "4712", "4711", "4710", "4709", "4708", "4707", "4706",
             "4612", "4611", "4610", "4609", "43053", "43052", "1729", "1728", "1727", "1726", "1725", "1724", "1723",
             "1722", "1721", "1636", "1635", "1634", "1633", "1632", "1631", "1630", "1629", "1628", "60500", "6050",
             "6048", "1726A", "1726B", "1726C", "1726D", "1726E", "1721A", "1721B", "1721C", "1721D", "1721E"],  # 超速
    "1344": ["1344", "1039", "1039B", "1039C", "10390"],  # 违法停车
    "1101": ["1101", "1244", "1120", "4101", "6011", "3019", "1240"],  # 开车未系安全带
    "1223": ["1223", "1362"],  # 开车打电话
    "8013": ["8013", "1229"],  # 闯限行
    "7102": ["7102", "1019"],  # 占用公交车道
}
# 可用的违法场景
Is_Allowed_Type = {
    "1625": True,  # 闯红灯
    "1208": True,  # 不按导向行驶
    "1345": True,  # 违反禁止标示线
    "1301": True,  # 逆行
    "1357": False,  # 不礼让行人
    "1352": True,  # 超速
    "1344": False,  # 违法停车
    "1101": True,  # 开车未系安全带
    "1223": True,  # 开车打电话
    "8013": True,  # 闯限行
    "7102": False,  # 占用公交车道
}
# 闯红灯按相机编号(cameraIndexCode)预设红绿灯方向
Set_Light_Direct = {

}
# 闯红灯按相机编号(cameraIndexCode)过滤不处理的特殊数据
Light_Filter_Camera = [

]
# 闯红灯需测试的卡口
Test_Cross_1625 = "all"
# 闯红灯上线的卡口
Online_Cross_1625 = [

]
# kafka 集群多个ip逗号隔开
SERVER = "***********:29092"
# SERVER = "localhost:9092"
VIO_DATA_TOPIC = 'BAYONET_VEHICLEALARM_TO_JUDGE_JSON_TOPIC'
MODEL_RES_TOPIC = 'BAYONET_VEHICLEALARM_JUDGE_JSON_TOPIC'
CLIENT_ID = 'admin'
GROUP_ID = "ai_judge"
AUTO_OFFSET_RESET = "latest"
# 配置模型处理延时的最大值（单位秒）
Max_Delay_Time = 60 * 30
# 控制模型运行时间
SET_RUN_TIME = -1  # 值为负数则一直运行
# 各违法场景保存图片的最大值
Max_Save_Num = 500
# 闯红灯各相机最大处理数量
Max_Camera_Num = -1
# modelJudgeStatus推送结果的值
FILTER_STATUS = [0, 1, 2]
# kafka心跳接口地址
Heart_Address = "http://************:8213/vps-data/service/rs/violation/v1/beat-heart"
Heart_Interval = 10
# **********************************************************
# 模型运行方式
DEVICE = "gpu"
# 多进程数
Multi_Num = [1, 2]  # [使用显卡数,每张卡进程数]
SAVE_RES_DIR = "../test_results"  # 审核图片结果分类保存路径
IS_SAVE_VIS_PIC = True
# 去重列表初始化的时间
Init_Url_List_Time = -1
# 收集图片最大数量
Max_Get_Camera_Num = 5
# 日志最大值MB
LOG_MAX_SIZE = 150
# 交辅警废片判定
Police_Judge = False
# 特殊车辆类型（不违法）
Special_Type = ['ambulance_h', 'ambulance_b']
# 特殊车辆判定废片阈值
Special_Car_Thresh = 0.45
Ambulance_Head_Thresh = 0.56
Special_Car_Vis_Thresh = 0.2
# 车牌结尾为警字的置信度大于此值报废片
Police_Plate_Thresh = 0.85
# 停止线判定
Stop_Line_Judge = [1]
Car1_Stop_Line_Rate = 0.68  # 一图停止线判定高度倍数
# 开车副驾未系安全带是否违法(强判定) 为None时根据违法代码进行判定
Copilot_Belt_Is_Vio = False
Belt_Judge_Thresh = 0.75
# *************限行场景******************
# 实例分割模型禁行类别
stop_car_type_list = ['truck_b', 'truck_h']
# yolov7模型禁行类别
yolov7_stop_car_type_list = ['truck']
# 货车限行违法高检出率
Truck_Vio_Recall = False
# 配置限行违法阈值，提升检准率
Blend_Conf = 0.4
Yolo_Conf = 0.6
# 限行场景模型结果去重的IOU阈值
IOU_THRESH = 0.75
# *************限行场景******************
# 闯红灯三图未匹配到目标车辆是否判定废片
No_Car3_Is_Waste = False
# 红绿灯模型阈值
LIGHT_THRESH = 0.15
# 闯红灯场景高违法检出率
Light_High_Vio_Recall = False
# 违停数据处理比例
Vio_Park_Rate = 1  # 每多少条数据处理一次
#违停违法高检出率模式
Vio_Park_High_Recall = False
# **********************文件夹测试模块配置****************************
# imgs_dir_test.py
# 图片命名格式示例：同组违法图片编号_车牌号码_时间序号.jpg
SPLIT_STR = "_"
ID_INDEX = 0
PLATE_INDEX = 0
SINGLE_PIC_NUM = {
    "1625": 4,  # 闯红灯
    "1208": 4,  # 不按导向行驶
    "1345": 3,  # 违反禁止标示线
    "1301": 3,  # 逆行
    "1357": 4,  # 不礼让行人
    "1352": 1,  # 超速
    "1344": 4,  # 违法停车
    "1101": 1,  # 开车未系安全带
    "1223": 1,  # 开车打电话
    "8013": 1,  # 闯限行
    "7102": 3,  # 占用公交车道
}
# **********************文件夹测试模块配置****************************

# 车牌开头列表
chars = [u"京", u"沪", u"津", u"渝", u"冀", u"晋", u"蒙", u"辽", u"吉", u"黑", u"苏", u"浙", u"皖", u"闽", u"赣",
         u"鲁", u"豫", u"鄂", u"湘", u"粤", u"桂", u"琼", u"川", u"贵", u"云", u"藏", u"陕", u"甘", u"青", u"宁",
         u"新", u"港", u"学", u"使", u"警", u"澳", u"挂", u"军", u"北", u"南", u"广", u"沈", u"兰", u"成", u"济",
         u"海", u"民", u"航", u"空"]

# 特殊车辆（白名单车辆列表）
try:
    try:
        spec_cars_list = pd.read_csv('../data/spec_cars.csv')['plate'].to_list()
    except:
        spec_cars_list = pd.read_csv('../data/spec_cars.csv')['plate'].tolist()
except:
    spec_cars_list = []
# 实例分割模型类别
WINDOW_NAME = "COCO detections"
_LABEL = ['_background_', 'lane1', 'lane2', 'stop_area', 'car_h', 'car_b', 'bus_h', 'bus_b', 'truck_h', 'truck_b',
          'ambulance_h', 'ambulance_b', 'fire_engine_h', 'fire_engine_b', 'police_car_h', 'police_car_b',
          'engineering_car_h',
          'engineering_car_b', 'motorbike_h', 'motorbike_b', 'arrow_l', 'arrow_s', 'arrow_r', 'arrow_t', 'stop_line',
          'zebra_line', 'no_entry', 'arrow_s_t', 'arrow_l_t', 'arrow_r_t', 'aux_police', 'no_parking_area']

# 实例分割模型需要检测车辆种类列表
car_list = ["car_h", "car_b", "bus_h", "bus_b", "truck_h", "truck_b", "engineering_car_h", "engineering_car_b"]
Blend_Judge_Vehicle = ["car_h", "car_b", "bus_h", "bus_b", "truck_h", "truck_b", "engineering_car_h",
                       "engineering_car_b", 'motorbike_h', 'motorbike_b', 'ambulance_h', 'ambulance_b', 'fire_engine_h',
                       'fire_engine_b', "police_car_h", "police_car_b"]
# 车辆相似度过滤车头类
Filter_Vehicle_Head = ["car_h", "bus_h", "truck_h", "engineering_car_h", 'ambulance_h', 'fire_engine_h',
                       "police_car_h"]
Det_Head_Thresh = 0.42
Head_IOU = 0.65
# 交辅警置信度
police_threshold = 0.7
# 无效车牌开头列表
no_valid_plate_list = ['无', '未', '车']
# 特殊车辆结尾列表
spe_plate_list = ['警', '应急']
# yolov7类别列表
coco_names = ['person', 'bicycle', 'car', 'motorcycle', 'airplane', 'bus', 'train', 'truck', 'boat', 'traffic light',
              'fire hydrant', 'stop sign', 'parking meter', 'bench', 'bird', 'cat', 'dog', 'horse', 'sheep', 'cow',
              'elephant', 'bear', 'zebra', 'giraffe', 'backpack', 'umbrella', 'handbag', 'tie', 'suitcase', 'frisbee',
              'skis', 'snowboard', 'sports ball', 'kite', 'baseball bat', 'baseball glove', 'skateboard', 'surfboard',
              'tennis racket', 'bottle', 'wine glass', 'cup', 'fork', 'knife', 'spoon', 'bowl', 'banana', 'apple',
              'sandwich', 'orange', 'broccoli', 'carrot', 'hot dog', 'pizza', 'donut', 'cake', 'chair', 'couch',
              'potted plant', 'bed', 'dining table', 'toilet', 'tv', 'laptop', 'mouse', 'remote', 'keyboard',
              'cell phone',
              'microwave', 'oven', 'toaster', 'sink', 'refrigerator', 'book', 'clock', 'vase', 'scissors', 'teddy bear',
              'hair drier', 'toothbrush']
# 审片结果代码及描述
JudgeResult = {
    "1625": {
        "vio_straight": "目标车辆直行且直行交通信号灯为红灯",
        "vio_left": "目标车辆左转且左转交通信号灯为红灯",
        "vio_right": "目标车辆右转且右转交通信号灯为红灯",
        "vio_004": "第三图未找到目标车，根据特殊规则判定违法",
        "vio":"高违法检出率模式判定为废片",
        "no_vio": "目标车行驶方向与之对应的交通信号灯一三图交集不存在红灯",
        "no_vio_001": "一图与三图的交通信号灯识别结果无交集或无红灯",
        "no_vio_002": "抓拍相机识别的车牌首字符异常",
        "no_vio_003": "一图和二图都不能通过识别车牌匹配到目标车辆",
        "no_vio_004": "第三图未找到目标车",
        "no_vio_005": "一图到二图或二图到三图，目标车未产生明显位移",
        "no_vio_006": "停止线判定",
        "no_vio_009": "抓拍设备识别的车牌开头字符为：'无'、'未'",
        "no_vio_010": "抓拍设备识别的车牌结尾字符为：'警'、 '应急'、'港'或模型识别车牌结尾存在'警'字；模型识别目标车辆为救护车",
        "no_vio_011": "图片经切分后传入模型不足四张图",
        "no_vio_012": "前三张图片缩放错误",
    },  # 闯红灯
    "1208": {
        "vio_straight": "目标车辆直行且所在车道导向箭头的方向不存在直行",
        "vio_left": "目标车辆左转且所在车道导向箭头的方向不存在左转",
        "vio_right": "目标车辆右转且所在车道导向箭头的方向不存在右转",
        "vio_004": "第三图未找到目标车，根据特殊规则判定违法",
        "no_vio": "目标车所在车道识别的箭头线方向包含目标车行驶方向",
        "no_vio_001": "目标车辆所在车道未检测到导向箭头",
        "no_vio_002": "抓拍相机识别的车牌首字符异常",
        "no_vio_003": "一图和二图都不能通过识别车牌匹配到目标车辆",
        "no_vio_004": "第一图或第三图未找到目标车",
        "no_vio_005": "一图到二图或二图到三图，目标车未产生明显位移",
        "no_vio_006": "停止线判定",
        "no_vio_007": "压线判定",
        "no_vio_009": "抓拍设备识别的车牌开头字符为：'无'、'未'",
        "no_vio_010": "抓拍设备识别的车牌结尾字符为：'警'、 '应急'、'港'或模型识别车牌结尾存在'警'字；模型识别目标车辆为救护车",
        "no_vio_011": "图片经切分后传入模型不足四张图",
        "no_vio_012": "前三张图片缩放错误",
    },  # 不按导向行驶
    "1345": {
        "vio": "压线违法",
        "vio_002": "压线违法2",
        "vio_003": "压线违法3",
        "no_vio": "无压线违法",
        "no_vio_002": "抓拍相机识别的车牌首字符异常",
        "no_vio_003": "图片经切分后传入模型数量异常",
        "no_vio_005": "mask信息缺失",
        "no_vio_006": "无法通过车牌识别匹配目标车辆",
        "no_vio_009": "抓拍设备识别的车牌开头字符为：'无'、'未'",
        "no_vio_010": "抓拍设备识别的车牌结尾字符为：'警'、 '应急'、'港'",
    },  # 违反禁止标示线
    "1301": {
        "vio": "逆行违法",
        "no_vio": "无逆行违法",
        "no_vio_002": "抓拍相机识别的车牌首字符异常",
        "no_vio_003": "图片经切分后传入模型数量异常",
        "no_vio_005": "mask信息缺失",
        "no_vio_006": "无法通过车牌识别匹配目标车辆",
        "no_vio_009": "抓拍设备识别的车牌开头字符为：'无'、'未'",
        "no_vio_010": "抓拍设备识别的车牌结尾字符为：'警'、 '应急'、'港'",
    },  # 逆行
    "1357": {
        "vio": "不礼让行人违法",
        "no_vio": "无不礼让行人违法",
        "no_vio_001": "没有一张图片检测到行人",
        "no_vio_002": "抓拍相机识别的车牌首字符异常",
        "no_vio_006": "无法通过车牌识别匹配目标车辆",
        "no_vio_009": "抓拍设备识别的车牌开头字符为：'无'、'未'",
        "no_vio_010": "抓拍设备识别的车牌结尾字符为：'警'、 '应急'、'港'",
    },  # 不礼让行人
    "1352": {
        "vio": "超速违法",
        "no_vio": "无超速违法",
        "no_vio_002": "抓拍相机识别的车牌首字符异常",
        "no_vio_009": "抓拍设备识别的车牌开头字符为：'无'、'未'",
        "no_vio_010": "抓拍设备识别的车牌结尾字符为：'警'、 '应急'、'港'",
    },  # 超速
    "1344": {
        "vio_001":"每张图车牌均匹配成功且至少有一张图完成精确匹配",
        "vio_002": "每张图车牌均匹配成功",
        "vio_003": "第一张图和最后一张图车牌能匹配成功",
        "vio_004": "高检出率模式，不进行车牌匹配判定",
        "no_vio": "无违法停车",
        "no_vio_002": "抓拍相机识别的车牌首字符异常",
        "no_vio_009": "抓拍设备识别的车牌开头字符为：'无'、'未'",
        "no_vio_010": "抓拍设备识别的车牌结尾字符为：'警'、 '应急'、'港'或模型识别车牌结尾存在'警'字；模型识别目标车辆为救护车",
    },  # 违法停车
    "1101": {
        "vio": "主驾和副驾都未系安全带",
        "vio_003": "主驾未系安全带",
        "vio_004": "副驾未系安全带",
        "vio_005": "副驾未系安全带（副驾违法代码过滤）",
        "no_vio_001": "未检测到人",
        "no_vio_002": "抓拍相机识别的车牌首字符异常",
        "no_vio_006": "无法通过车牌识别匹配目标车辆",
        "no_vio_009": "抓拍设备识别的车牌开头字符为：'无'、'未'",
        "no_vio_010": "抓拍设备识别的车牌结尾字符为：'警'、 '应急'、'港'或模型识别车牌结尾存在'警'字；模型识别目标车辆为救护车",
    },  # 开车未系安全带
    "1223": {
        "vio": "主驾开车打电话",
        "no_vio_001": "未检测到人",
        "no_vio_002": "抓拍相机识别的车牌首字符异常",
        "no_vio_006": "无法通过车牌识别匹配目标车辆",
        "no_vio_009": "抓拍设备识别的车牌开头字符为：'无'、'未'",
        "no_vio_010": "抓拍设备识别的车牌结尾字符为：'警'、 '应急'、'港'或模型识别车牌结尾存在'警'字；模型识别目标车辆为救护车",
    },  # 开车打电话
    "8013": {
        "vio": "货车闯限行违法",
        "no_vio_002": "抓拍相机识别的车牌首字符异常",
        "no_vio_009": "抓拍设备识别的车牌开头字符为：'无'、'未'",
        "no_vio_010": "抓拍设备识别的车牌结尾字符为：'警'、 '应急'、'港'或模型识别车牌结尾存在'警'字；模型识别目标车辆为救护车",
    },  # 闯限行
    "7102": {
        "vio": "占用公交车道违法",
        "no_vio_002": "抓拍相机识别的车牌首字符异常",
        "no_vio_006": "无法通过车牌识别匹配目标车辆",
        "no_vio_009": "抓拍设备识别的车牌开头字符为：'无'、'未'",
        "no_vio_010": "抓拍设备识别的车牌结尾字符为：'警'、 '应急'、'港'",
    }  # 占用公交车道
}
