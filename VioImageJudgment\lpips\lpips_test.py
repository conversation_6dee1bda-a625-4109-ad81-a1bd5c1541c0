# import lpips
from lpips import lpips
import time
# loss_fn_alex = lpips.LPIPS(net='alex') # best forward scores
import cv2
import torchvision.transforms as transforms
from PIL import Image

transform = transforms.Compose([
    # transforms.RandomResizedCrop((227,227)),
    transforms.Resize([224, 224]),
    transforms.ToTensor(),
])

img1 = transform(Image.open("/home/<USER>/ai_judge_v2/test_13440/pro_imgs/贵C1E755/2_car.jpg")).unsqueeze(0)
img2 = transform(Image.open("/home/<USER>/ai_judge_v2/test_13440/pro_imgs/贵C1E755/3_0_car.jpg")).unsqueeze(0)
img3 = transform(Image.open("/home/<USER>/ai_judge_v2/test_13440/pro_imgs/贵C1E755/3_1_car.jpg")).unsqueeze(0)
img4 = transform(Image.open("/home/<USER>/ai_judge_v2/test_13440/pro_imgs/贵C1E755/3_2_car.jpg")).unsqueeze(0)
img5 = transform(Image.open("/home/<USER>/ai_judge_v2/test_13440/pro_imgs/贵C1E755/3_3_car.jpg")).unsqueeze(0)
img6 = transform(Image.open("/home/<USER>/ai_judge_v2/test_13440/pro_imgs/贵C1E755/3_4_car.jpg")).unsqueeze(0)
img7 = transform(Image.open("/home/<USER>/ai_judge_v2/test_13440/pro_imgs/贵C1E755/3_5_car.jpg")).unsqueeze(0)
loss_fn_vgg = lpips.LPIPS(net='vgg')  # closer to "traditional" perceptual loss, when used for optimization
start = time.time()
import torch

d1 = loss_fn_vgg(img1, img2).detach().numpy()[0][0][0][0]
d2 = loss_fn_vgg(img1, img3).detach().numpy()[0][0][0][0]
d3 = loss_fn_vgg(img1, img4).detach().numpy()[0][0][0][0]
d4 = loss_fn_vgg(img1, img5).detach().numpy()[0][0][0][0]
d5 = loss_fn_vgg(img1, img6).detach().numpy()[0][0][0][0]
d6 = loss_fn_vgg(img1, img7).detach().numpy()[0][0][0][0]
print(d1, d2, d3, d4, d5, d6)
end = time.time()
print(end - start)
