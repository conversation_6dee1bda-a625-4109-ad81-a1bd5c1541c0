import os
import shutil
from tqdm import tqdm

root = r"E:\AI审片项目和资料汇总\荆州\公安网\data\图"
iter_bool = True

merge_img_dst = f"{root}/merge_data"
if os.path.exists(merge_img_dst):
    shutil.rmtree(merge_img_dst)
os.makedirs(merge_img_dst,exist_ok=True)

dir_list = []
img_list = []
json_list = []
while iter_bool:
    if not dir_list:
        for name in os.listdir(root):
            if name.startswith("vis") or name.startswith("tmp"):
                continue
            tmp = f"{root}/{name}"
            if os.path.isdir(tmp):
                dir_list.append(tmp)
            elif name.endswith(".jpg"):
                img_list.append(tmp)
            elif name.endswith(".json"):
                json_list.append(tmp)
    for path in dir_list:
        for name in os.listdir(path):
            if name.startswith("vis") or name.startswith("tmp"):
                continue
            tmp = f"{path}/{name}"
            if os.path.isdir(tmp):
                dir_list.append(tmp)
            elif name.endswith(".jpg"):
                img_list.append(tmp)
            elif name.endswith(".json"):
                json_list.append(tmp)
        dir_list.remove(path)
    if not dir_list:
        iter_bool = False
for path in tqdm(img_list):
    try:
        shutil.copy(path,merge_img_dst)
    except Exception as e:
        print(e)
for path in tqdm(json_list):
    try:
        shutil.copy(path,merge_img_dst)
    except Exception as e:
        print(e)