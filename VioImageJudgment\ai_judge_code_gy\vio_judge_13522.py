# 超速场景
from init_models import *


# 超速逻辑主函数
def judge_vio(src_plate_num, file_list, weights_list, resize_scale=0.7, save=False, save_path=".", extra_msg=None):
    error_path = f"{save_path}/no_vio"
    vio_path = f"{save_path}/vio"
    if save:
        os.makedirs(error_path, exist_ok=True)
        os.makedirs(vio_path, exist_ok=True)
    # 读取模型
    plate_det_model = weights_list[0][0]
    plate_rec_model = weights_list[0][1]
    demo = weights_list[1]
    lp_model = weights_list[2][0]
    lp_meta = weights_list[2][1]
    yolov7_model = weights_list[3][0]
    yolov7_meta = weights_list[3][1]
    model_cnn = weights_list[-2]
    vio_model = weights_list[-1]

    vio_judge = 'no_vio'
    try:
        split_mode = extra_msg[0]
        black_height = extra_msg[1]
        black_pos = extra_msg[2]
        iieaAuditMode = extra_msg[3]
        iieaFilterVehicle = extra_msg[4]
    except:
        split_mode = 111
        black_height = 0
        black_pos = None
        iieaAuditMode = None
        iieaFilterVehicle = None

    judge_infos = {'plate_num': src_plate_num,
                   'vio_code': 1352,
                   'vio_judge': vio_judge,
                   'zebra_line': {},
                   'police': 0,
                   'has_crossline': 0,
                   'vehicle_location': {},
                   'drv_direction': 0,
                   'drive_direction_light': [],
                   'lamp': [],
                   'stop_line': 0,
                   'lane': [],
                   'target_car': [],
                   'judgeConf': 1.0,
                   'modelDetectResults': [], 'split_mode': split_mode, "black_height": black_height, "black_pos":black_pos,"iieaAuditMode":iieaAuditMode,"iieaFilterVehicle":iieaFilterVehicle}
    # 特殊车牌识别
    if not src_plate_num:
        vio_judge = "no_vio_009"
        judge_infos['vio_judge'] = vio_judge
        return vio_judge, judge_infos
    if src_plate_num[0] not in chars:
        vio_judge = 'no_vio_002'
        judge_infos['vio_judge'] = vio_judge
        return vio_judge, judge_infos

    vio_judge = VIO_JUDGE_TOOL().spe_no_vio_judge(src_plate_num,iieaFilterVehicle)
    if vio_judge.startswith('no_vio'):
        judge_infos['vio_judge'] = vio_judge
        return vio_judge, judge_infos

    target_car_list = []
    plate_list = []
    vio_judge = 'no_vio'
    try:
        h, w, _ = file_list[0].shape
    except:
        h = w = 0
    # iiea审核模式
    if iieaAuditMode is None:
        judge_mode = Precision_Judge_Mode[str(judge_infos['vio_code'])]
    else:
        judge_mode = iieaAuditMode
    judge_thresh = Judge_Thresh[str(judge_infos['vio_code'])][int(judge_mode)]
    # 取消对图片数量的要求
    for idx, img in enumerate(file_list):
        # yolov7车辆检测
        crop_img1_list, car1_bbox_ps, _, score_list = yolov7_detect(img, yolov7_model, yolov7_meta[0], yolov7_meta[1],
                                                                    yolov7_meta[2], need_conf=True)
        # 车牌识别匹配模块
        img1_car_ps, plate1_num, img1_plate_dict, new_car1_bbox_ps = CarJudge(iiea_cfg=judge_infos["iieaFilterVehicle"],plate_match_thresh=judge_thresh).confirm_img1_car_ps(img, src_plate_num,
                                                                                                    plate_det_model,
                                                                                                    plate_rec_model,
                                                                                                    car1_bbox_ps,
                                                                                                    yolov7_meta[2],
                                                                                                    label=0, show=save,
                                                                                                    save_path=save_path)

        # 返回检测框和置信度信息
        if img1_car_ps:
            target_idx = car1_bbox_ps.index(img1_car_ps)
            img_id = idx + 1
            percent_coord = convert_src_coord(img1_car_ps, img, img_id,
                                              judge_infos["split_mode"], judge_infos["black_height"],judge_infos["black_pos"])
            tmp_res = {
                "objId": img_id,
                "objType": "vehicle",
                "objConf": float(score_list[target_idx]),
                "objCoord": percent_coord
            }
            judge_infos["modelDetectResults"].append(tmp_res)
        # 车牌位置信息
        plate_cor = img1_plate_dict[str(img1_car_ps)] if img1_car_ps else []
        if plate_cor:
            target_idx = str(img1_car_ps) + "conf"
            img_id = idx + 1
            percent_coord = convert_src_coord(plate_cor, img, img_id,
                                              judge_infos["split_mode"], judge_infos["black_height"],judge_infos["black_pos"])
            tmp_res = {
                "objId": img_id,
                "objType": "plate",
                "objConf": float(img1_plate_dict[target_idx]),
                "objCoord": percent_coord
            }
            judge_infos["modelDetectResults"].append(tmp_res)

        plate_list.append(plate1_num)
        target_car_list.append(img1_car_ps)
        if img1_car_ps != []:
            vio_judge = "vio"
            break
        # iiea白名单车辆
        if plate1_num.endswith("警"):
            vio_judge = "no_vio_010"
            break
        if filter_special_vehicle(demo, img, img1_car_ps, vio_judge, save, judge_infos) == 'no_vio_010':
            vio_judge = 'no_vio_010'
            break
    for i, value in enumerate(plate_list):
        temp_tar_car = {'plate_num': '', 'car_ps': ''}
        try:
            temp_tar_car['plate_num'] = plate_list[i]
        except:
            pass
        try:
            temp_tar_car['car_ps'] = target_car_list[i]
        except:
            pass
        judge_infos['target_car'].append(temp_tar_car)
    try:
        x_ratio = ((target_car_list[0][0] + target_car_list[0][2]) / 2) / w
    except:
        x_ratio = 0
    if x_ratio <= 0.5:
        judge_infos['vehicle_location'] = 0
    else:
        judge_infos['vehicle_location'] = 1

    judge_infos['vio_judge'] = vio_judge
    if vio_judge.startswith('vio'):
        judge_infos['judgeConf'] = 0.98
    else:
        judge_infos['judgeConf'] = 0.85
    try:
        if save:
            show_plate2(file_list, target_car_list)
            result_path = vio_path if vio_judge.startswith("vio") else error_path
            for idx, i in enumerate(file_list):
                cv2.imwrite(result_path + f'/{vio_judge}_{src_plate_num}_img{idx + 1}.jpg', i)
    except:
        print("Error!超速可视化异常")
    return vio_judge, judge_infos
