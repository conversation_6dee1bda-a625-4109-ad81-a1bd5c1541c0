import argparse
import sys

sys.path.append('..')
sys.path.append('../ai_judge_code_gy')

from ai_judge_code_gy.init_models import *
from tqdm import tqdm

if __name__ == '__main__':
    parser = argparse.ArgumentParser()
    parser.add_argument('--image_path', type=str, default='test', help='source')  # file/folder, 0 for webcam
    opt = parser.parse_args()
    print(opt)
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    plate_det_model, plate_rec_model = load_plate_model("../models/plate_detect.pt",
                                                        "../models/plate_rec.pth", device=device)
    yolov7_model, imgsz, stride, device = load_lp("../models/yolov7-e6e.pt", device=device)
    yolov7_meta = (imgsz, stride, device)
    yolov7_module = [yolov7_model, yolov7_meta]

    save_path = "./add_plate"
    os.makedirs(save_path,exist_ok=True)
    if not os.path.isfile(opt.image_path):
        file_name = os.listdir(opt.image_path)

        for idx1,i in enumerate(tqdm(file_name)):
            path = f"{opt.image_path}/{i}"
            img = cv2.imread(path)

            crop_img_list, car_bbox_ps, labels_list, score_list = yolov7_detect(img, yolov7_model, yolov7_meta[0],
                                                                                    yolov7_meta[1],
                                                                                    yolov7_meta[2],need_conf=True)
            temp = sorted(car_bbox_ps, key=lambda x: x[1], reverse=True)
            car_ps = temp[0]
            temp_img = img[car_ps[1] :car_ps[3], car_ps[0]:car_ps[2]]
            # 车牌检测+识别
            dict_list = detect_Recognition_plate(plate_det_model, temp_img, device, plate_rec_model, 640)
            tmp_plate = None
            for idx, res in enumerate(dict_list):
                plate_num = res["plate_no"]
                if tmp_plate is None or len(plate_num)>tmp_plate:
                    tmp_plate = plate_num

            cv2.imwrite(f"{save_path}/{tmp_plate}_{idx1}.jpg", img)
