# 闯红灯场景
import os
import time

import cv2
import numpy as np

from init_models import *
from main_config.log_config import get_logging
import logging
import logging.config


def catch_error():
    exc_type, exc_value, exc_traceback = sys.exc_info()
    tb_info = traceback.extract_tb(exc_traceback)
    filename, line, func, text = tb_info[-1]
    error_str = f"error file:{filename}\nerror func:{func} line {line}\nerror text:{text} —— {exc_value}\n"
    return error_str


# 计算IOU
def get_iou(box1, box2):
    data1 = np.array(box1)
    data2 = np.array(box2)
    tool = Tools()
    return tool.IOU(data1[:2], data1[2:], data2[:2], data2[2:])


# 计算y轴单方向的重叠度（车辆检测误差影响IOU判定车辆静止状态）
def get_yiou(box1, box2):
    y1 = (box1[1], box1[3])
    y2 = (box2[1], box2[3])
    top = min(y1[1], y2[1]) - max(y1[0], y2[0])
    bottom = max(y1[1], y2[1]) - min(y1[0], y2[0])
    return top / bottom


# 无四图不启用
def slice_judge(ps_list, img4, plate_rec_model, src_plate_num):
    # 四图切分车辆识别车牌模糊匹配
    device = "cuda:0" if torch.cuda.is_available() else "cpu"
    for num in [2, 3, 4, 5]:
        for ps in ps_list:
            x1, y1, x2, y2 = ps
            i = img4[y1:y2, x1:x2]
            h4 = i.shape[0]
            slice = int(h4 // num)
            for j in range(num // 2, num):
                x_img = i[j * slice:(j + 1) * slice]
                plate_num, conf_array = get_plate_result(x_img, device, plate_rec_model)
                # print(f"car slice {num}:{res_list}")
                print(f"car slice {j}/{num}:plate_num-{plate_num}")
                if plate_num == src_plate_num or Tools().random_choice(plate_num, src_plate_num,
                                                                       random_list=[3, 2]):
                    print("img4 car slice:", plate_num)
                    return False, ps
        # 四图整图切分识别车牌模糊匹配
        if not ps_list or len(ps_list) > 1:
            h4 = img4.shape[0]
            slice = int(h4 // num)
            for j in range(num // 2, num):
                x_img = img4[j * slice:(j + 1) * slice]
                plate_num, conf_array = get_plate_result(x_img, device, plate_rec_model)
                print(f"img4 slice {j}/{num}:plate_num-{plate_num}")

                if plate_num == src_plate_num or Tools().random_choice(plate_num, src_plate_num,
                                                                       random_list=[3, 2]):
                    print("img4 slice:", plate_num)
                    return False, []
    return True, []


# 闯红灯场景主逻辑函数
def judge_vio(src_plate_num, file_list, weights_list, resize_scale=1, merge=False, save=False, save_path=".",
              extra_config=None, high_recall=None, extra_msg=None):
    if high_recall is None:
        from main_config.config import Precision_Judge_Mode
        if Precision_Judge_Mode["1625"]:
            recall_mode = False
        else:
            recall_mode = True
    elif high_recall:
        recall_mode = True
    else:
        recall_mode = False
    logging.config.dictConfig(get_logging("kafka_main"))
    logger = logging.getLogger("console_plain_file_logger")
    time_stamp = int(time.time()) // (24 * 60 * 60)
    # if time_stamp > 19297 + 105:
    #     print("模型已过期！")
    # exit()
    error_path = f"{save_path}/no_vio"
    vio_path = f"{save_path}/vio"
    if save:
        os.makedirs(error_path, exist_ok=True)
        os.makedirs(vio_path, exist_ok=True)
    # 读取模型
    plate_det_model = weights_list[0][0]
    plate_rec_model = weights_list[0][1]
    demo = weights_list[1]
    lp_model = weights_list[2][0]
    lp_meta = weights_list[2][1]
    yolov7_model = weights_list[3][0]
    yolov7_meta = weights_list[3][1]
    model_cnn = weights_list[-2]
    vio_model = weights_list[-1]

    try:
        split_mode = extra_msg[0]
        black_height = extra_msg[1]
        black_pos = extra_msg[2]
        iieaAuditMode = extra_msg[3]
        iieaFilterVehicle = extra_msg[4]
    except:
        split_mode = 111
        black_height = 0
        black_pos = None
        iieaAuditMode = None
        iieaFilterVehicle = None

    judge_infos = {'plate_num': src_plate_num,
                   'vio_code': 1625,
                   'vio_judge': 'vio',
                   'drv_direction': 0,
                   'drive_direction_light': [],
                   'lamp': [],
                   'target_car': [],
                   'stop_line': 0,
                   'police': 0,
                   'has_crossline': 0,
                   'vehicle_location': 0,
                   'lane': [],
                   'zebra_line': [],
                   'judgeConf': 1.0,
                   'modelDetectResults': [], 'split_mode': split_mode, "black_height": black_height,
                   "black_pos": black_pos, "iieaAuditMode": iieaAuditMode, "iieaFilterVehicle": iieaFilterVehicle}
    judge_mode = 0 if recall_mode else 1
    judge_thresh = Judge_Thresh[str(judge_infos['vio_code'])][int(judge_mode)]
    if judge_thresh is None:
        judge_thresh = [3, 2]
    # 特殊车牌判定
    vio_judge = VIO_JUDGE_TOOL().spe_no_vio_judge(src_plate_num, iieaFilterVehicle)
    if vio_judge.startswith('no_vio'):
        judge_infos['vio_judge'] = vio_judge
        return vio_judge, judge_infos

    vio_judge = "no_vio"

    if not src_plate_num:
        vio_judge = "no_vio_009"
        judge_infos['vio_judge'] = vio_judge
        return vio_judge, judge_infos
    print("chars",chars)
    if src_plate_num[0] not in chars:
        vio_judge = "no_vio_002"
        judge_infos['vio_judge'] = vio_judge
        print("车牌首字符错误!")
        return vio_judge, judge_infos
    try:
        img1_np, img2_np, img3_np = file_list[:3]
    except:
        vio_judge = 'no_vio_011'
        judge_infos['vio_judge'] = vio_judge
        return vio_judge, judge_infos
    try:
        img4 = file_list[3]
    except:
        img4 = None
    try:
        img1, img2, img3 = Tools().resize_imgs(img1_np, img2_np, img3_np)
    except:
        import traceback
        traceback.print_exc()
        vio_judge = 'no_vio_012'
        judge_infos['vio_judge'] = vio_judge

        return vio_judge, judge_infos

    if img1.shape[0] > 4000 or img1.shape[1] > 4000:
        if float(resize_scale) < 1:
            img1 = cv2.resize(img1, None, fx=resize_scale, fy=resize_scale)
            img2 = cv2.resize(img2, None, fx=resize_scale, fy=resize_scale)
            img3 = cv2.resize(img3, None, fx=resize_scale, fy=resize_scale)

    # 检测第四张图车牌是否匹配（无四图不启用）
    # crop_img4_list, car4_bbox_ps, _ = carinfo.get_goal_class_box(img4, ['car', 'truck', 'bus'])
    # car4_bbox_ps = set(tuple(i) for i in car4_bbox_ps)
    # car4_bbox_ps = [list(i) for i in car4_bbox_ps]
    # print("car4_bbox_ps", car4_bbox_ps)
    # img4_error = True
    # img4_car_ps = []
    #
    # img4_show = []
    # for ps in car4_bbox_ps:
    #     x1, y1, x2, y2 = ps
    #     h_bias = (y2 - y1) // 3
    #     car = img4[y1 + h_bias:y2, x1:x2]
    #     res_list = detect_one(yolov5_plate_model, lpr_model, car)
    #     
    #     for res in res_list:
    #         plate_num = res[0]
    #         confidence = res[1]
    #         plate_cor = res[2]
    #         plate_cor[1] += h_bias
    #         img4_show.append([ps, plate_cor])
    #         print(f"img4 detect_one -- ps:{ps},plate_num:{plate_num},confidence:{confidence}")
    #         if plate_num == src_plate_num or Tools().random_choice(plate_num, src_plate_num,
    #                                                                random_list=[3,2]):
    #             img4_error = False
    #             img4_car_ps = ps
    #             break
    #     if not img4_error:
    #         break
    # if img4_error:
    #     img4_error, img4_car_ps = slice_judge(car4_bbox_ps, img4, lpr_model, src_plate_num)
    # if img4_error:
    #     vio_judge = 'no_vio_002'
    #     judge_infos['vio_judge'] = vio_judge
    #     code = vio_judge.split("_")[-1]
    #     if save:
    #         show_cars(img1=img4, car1_bbox_ps=car4_bbox_ps)
    #         for i in img4_show:
    #             show_plate(img1=img4, img1_car_ps=i[0], plate1_cor=i[1])
    #         cv2.imwrite(f"{error_path}/{code}_{src_plate_num}_img4.jpg", img4)
    #     return vio_judge, judge_infos
    t1 = time.time()
    # 红绿灯和交辅警识别
    l_res = []
    p_res = []
    img_list = [img1, img2, img3]
    empty_p = 0
    for i in img_list:
        h = i.shape[0]
        l, p = detect_lp(i[:h // 2], lp_model, lp_meta[0], lp_meta[1], lp_meta[2], conf_thres=LIGHT_THRESH,
                         iou_thres=0.45)

        if not p:
            empty_p += 1
        p_res.append(p)
        l_res.append(l)
        # judge_infos[""]

    logger.info(f"{os.getpid()}-红绿灯模型识别耗时：{round(time.time() - t1, 2)}")
    # 返回信号灯坐标和置信度
    img_np_list = file_list
    for idx, obj in enumerate(l_res):
        for res in obj:
            if res:
                tmp_box = eval(res[0])
                box = tmp_box[0] + tmp_box[1]
                conf = res[1].split("-")[-1]
                img_id = idx + 1
                percent_coord = convert_src_coord(box, img_np_list[idx], img_id,
                                                  judge_infos["split_mode"], judge_infos["black_height"],
                                                  judge_infos["black_pos"])
                tmp_res = {
                    "objId": img_id,
                    "objType": "trafficLight",
                    "objConf": float(conf),
                    "objCoord": percent_coord
                }
                judge_infos["modelDetectResults"].append(tmp_res)

    img1_light_res, img2_light_res, img3_light_res = l_res
    judge_infos['lamp'] = l_res[0] + l_res[1] + l_res[2]

    # 逻辑整合三图红绿灯识别结果得到红绿灯判罚状态status
    status, tmp_vio_judge = judge_light_status(l_res[0], l_res[1], l_res[2], light_config=extra_config)
    tmp_status = [i.split("_")[0] for i in status]
    ### 得到红绿灯状态 样例：['green_s', 'red_l']
    if Police_Judge:
        print("empty_p:", empty_p)
        ### 有两张及以上的片中有交警的话就判断为不违法（交警正在指挥交通） 直接打断
        if empty_p < 2:
            vio_judge = 'no_vio_008'
            judge_infos['police'] = 1
            judge_infos['vio_judge'] = vio_judge
            code = vio_judge.split("_")[-1]
            if save:
                show_police(img_list, p_res)
                show_light(img1, img2, img3, img1_light_res, img2_light_res, img3_light_res, status)
                cv2.imwrite(f"{error_path}/{code}_{src_plate_num}_img1.jpg", img1)
                cv2.imwrite(f"{error_path}/{code}_{src_plate_num}_img2.jpg", img2)
                cv2.imwrite(f"{error_path}/{code}_{src_plate_num}_img3.jpg", img3)
            return vio_judge, judge_infos
        else:
            judge_infos['police'] = 0
    # print('light_status：', status)
    # time.sleep(10)
    if not status or "red" not in tmp_status:  #### 如果为空就 直接判断不违法
        if "yellow" in tmp_status:
            vio_judge = 'no_vio_008'
        else:
            vio_judge = 'no_vio_001'
        judge_infos['vio_judge'] = vio_judge
        judge_infos['drive_direction_light'] = []
        judge_infos['judgeConf'] = 0.95
        code = vio_judge.split("_")[-1]
        if save:
            # show_police(img_list, p_res)
            show_light(img1, img2, img3, img1_light_res, img2_light_res, img3_light_res, status)
            cv2.imwrite(f"{error_path}/{code}_{src_plate_num}_img1.jpg", img1)
            cv2.imwrite(f"{error_path}/{code}_{src_plate_num}_img2.jpg", img2)
            cv2.imwrite(f"{error_path}/{code}_{src_plate_num}_img3.jpg", img3)

        return vio_judge, judge_infos
    else:
        judge_infos['drive_direction_light'] = status
    # if tmp_vio_judge.startswith("no"):
    #     vio_judge = tmp_vio_judge
    #     judge_infos['vio_judge'] = vio_judge
    #     judge_infos['drive_direction_light'] = []
    #     judge_infos['judgeConf'] = 0.85
    #     code = vio_judge.split("_")[-1]
    #     if save:
    #         show_light(img1, img2, img3, img1_light_res, img2_light_res, img3_light_res, status)
    #         cv2.imwrite(f"{error_path}/{code}_{src_plate_num}_img1.jpg", img1)
    #         cv2.imwrite(f"{error_path}/{code}_{src_plate_num}_img2.jpg", img2)
    #         cv2.imwrite(f"{error_path}/{code}_{src_plate_num}_img3.jpg", img3)
    #
    #     return vio_judge, judge_infos

    # 交通要素识别
    t1 = time.time()


    # tmp  ="../test_results/1625/tmp"
    # os.makedirs(tmp,exist_ok=True)
    # for idx,i in enumerate(crop_img_list):
    #     cv2.imwrite(f"{tmp}/{src_plate_num}_{labels_list[idx]}_{conf_list[idx]}_{idx}.jpg",i)
    # 0417：切换为yolov7识别一图车辆，舍弃救护车过滤模块

    det1 = BlendMaskDetect(img1)
    predictions1, kps_mask1 = det1.mask_predict(demo, show=False)
    logger.info(f"{os.getpid()}-blend img1耗时：{round(time.time() - t1, 2)}")
    nms_ls1 = det1.nms_class_distinct(predictions1["instances"].pred_boxes.tensor,
                                      predictions1["instances"].scores,
                                      predictions1["instances"].pred_classes,
                                      threshold=0.8,
                                      class_ditinct_threshold=0.85)
    predictions1, kps_mask1 = det1.prediction_nms_filter(predictions1, nms_ls1, kps_mask1)

    # 筛选进行违法判定的车辆类型
    car1_bbox_ps, labels_list, score_list = det1.class_info2(predictions1, Blend_Judge_Vehicle, conf_thresh=0.1)

    print("img1 plate recognizing...")
    t1 = time.time()
    # 车牌匹配模块

    img1_car_ps, plate1_num, img1_plate_dict, new_car1_bbox_ps, first_plate_conf = CarJudge(
        iiea_cfg=judge_infos["iieaFilterVehicle"], need_first_plate=True,
        plate_match_thresh=judge_thresh).confirm_img1_car_ps(img1, src_plate_num,
                                                             plate_det_model,
                                                             plate_rec_model,
                                                             car1_bbox_ps,
                                                             yolov7_meta[2], label=0,
                                                             show=save,
                                                             save_path=save_path)

    logger.info(f"{os.getpid()}-车牌匹配1耗时：{round(time.time() - t1, 2)}")
    if plate1_num.endswith("警"):
        vio_judge = 'no_vio_010'
        judge_infos['vio_judge'] = vio_judge
        code = vio_judge.split("_")[-1]
        judge_infos['judgeConf'] = 0.85
        if save:
            show_police(img_list, p_res)
            show_light(img1, img2, img3, img1_light_res, img2_light_res, img3_light_res, status)
            show_cars(img1, img2, img3, car1_bbox_ps)
            cv2.imwrite(f"{error_path}/{code}_{src_plate_num}_img1.jpg", img1)
            cv2.imwrite(f"{error_path}/{code}_{src_plate_num}_img2.jpg", img2)
            cv2.imwrite(f"{error_path}/{code}_{src_plate_num}_img3.jpg", img3)
        return vio_judge, judge_infos
    # 减少相似度匹配的数量
    is_motorbike = False
    range_judge = False
    # 特殊车辆判废片
    try:
        if img1_car_ps:
            target_car_idx = car1_bbox_ps.index(img1_car_ps)
            print(labels_list[target_car_idx], score_list[target_car_idx], "############")
            if labels_list[target_car_idx] in ['motorbike_h', 'motorbike_b'] and score_list[target_car_idx] > 0.65:
                is_motorbike = True
            if labels_list[target_car_idx] in ['motorbike_b']:
                range_judge = True
            # iiea车辆白名单
            try:
                _, ambulance_filter, fire_filter = judge_infos["iieaFilterVehicle"]
            except:
                ambulance_filter = None
                fire_filter = None
            special_type_list = []
            if ambulance_filter:
                special_type_list.extend(['ambulance_h', 'ambulance_b'])
            if fire_filter:
                special_type_list.extend(['fire_engine_h', 'fire_engine_b'])
            if ambulance_filter is None and fire_filter is None:
                special_type_list = Special_Type
            if labels_list[target_car_idx] in special_type_list:
                if labels_list[target_car_idx] == "ambulance_h":
                    if score_list[target_car_idx] >= Ambulance_Head_Thresh:
                        vio_judge = 'no_vio_010'
                elif score_list[target_car_idx] >= Special_Car_Thresh:
                    vio_judge = 'no_vio_010'
                if vio_judge == "no_vio_010":
                    judge_infos['vio_judge'] = vio_judge
                    code = vio_judge.split("_")[-1]
                    # 返回目标框和置信度
                    percent_coord = convert_src_coord(img1_car_ps, img1, 1,
                                                      judge_infos["split_mode"], judge_infos["black_height"],
                                                      judge_infos["black_pos"])
                    tmp_res = {
                        "objId": 1,
                        "objType": "vehicle",
                        "objConf": float(score_list[target_car_idx]),
                        "objCoord": percent_coord
                    }
                    judge_infos["modelDetectResults"].append(tmp_res)
                    judge_infos["judgeConf"] = float(score_list[target_car_idx])
                    try:
                        if save:
                            show_light(img1, img2, img3, img1_light_res, img2_light_res, img3_light_res, status)
                            boxes = predictions1["instances"].pred_boxes.tensor
                            classes = predictions1["instances"].pred_classes
                            scores = predictions1["instances"].scores
                            from main_config.config import _LABEL
                            labels = [_LABEL[i] for i in classes if i is not None]
                            for i in range(len(boxes)):
                                box = boxes[i]
                                x1, y1, x2, y2 = [int(t) for t in box]
                                center_point = (x1, (y1 + y2) // 2)
                                cv2.putText(img1, labels[i] + f"-{scores[i] * 100:.0f}", center_point,
                                            cv2.FONT_HERSHEY_SIMPLEX,
                                            2, (0, 0, 255), 2)
                                cv2.rectangle(img1, (x1, y1), (x2, y2), (0, 255, 255), 2)
                            cv2.imwrite(f"{error_path}/{code}_{src_plate_num}_img1.jpg", img1)
                            cv2.imwrite(f"{error_path}/{code}_{src_plate_num}_img2.jpg", img2)
                            cv2.imwrite(f"{error_path}/{code}_{src_plate_num}_img3.jpg", img3)
                    except:
                        pass
                    return vio_judge, judge_infos
    except Exception as e:
        error_msg = catch_error()
        logger.error(error_msg)
    # t1 = time.time()
    # yolov7车辆检测
    # crop_img1_list, car1_bbox_ps, _ = yolov7_detect(img1, yolov7_model, yolov7_meta[0], yolov7_meta[1], yolov7_meta[2])
    # logger.info(f"{os.getpid()}-车辆检测1耗时：{round(time.time() - t1, 2)}")
    ### 得到 crop_img1_list 每辆车对应 numpy
    ### car1_bbox_ps 车框坐标 x1,y1,x2,y2
    # 特征图匹配车辆
    feat_veh_box = []
    feat_boxes = []
    feat_labels = []
    feat_scores = []
    if Feature_Match and img4 is not None:
        ft = time.time()
        _, feat_boxes, feat_labels,feat_scores = yolov7_detect(img4, yolov7_model, yolov7_meta[0],
                                                   yolov7_meta[1], yolov7_meta[2], conf_thres=0.1,need_conf=True)
        feat_veh_box, feat_plate, _, _, feat_plate_conf = CarJudge(
            iiea_cfg=judge_infos["iieaFilterVehicle"], plate_match_thresh=judge_thresh,
            need_first_plate=True).confirm_img1_car_ps(img4,
                                                       src_plate_num,
                                                       plate_det_model,
                                                       plate_rec_model,
                                                       feat_boxes,
                                                       yolov7_meta[
                                                           2],
                                                       label=0,
                                                       show=save,
                                                       save_path=save_path)
        logger.info(f"{os.getpid()} feat_plate:{feat_plate}-特征图匹配耗时：{round(time.time() - ft, 2)}")
        if feat_veh_box:  # 一图车牌识别误检，使用特征图的结果进行修正
            plate1_num = feat_plate
            first_plate_conf = feat_plate_conf

    # 过滤老头乐
    if img1_car_ps:
        vio_judge = filter_low_speed_veh(img1_car_ps, car1_bbox_ps, labels_list, score_list, src_plate_num, plate1_num,
                                         first_plate_conf)
    else:
        vio_judge = filter_low_speed_veh(feat_veh_box, feat_boxes, feat_labels,feat_scores, src_plate_num, plate1_num,
                                         first_plate_conf,feat_judge=False)
    if vio_judge.startswith("no"):
        judge_infos['vio_judge'] = vio_judge
        if save:
            code = vio_judge.split("_")[-1]
            img4 = show_chinese(img4, plate1_num,extra=f" {first_plate_conf:.3f}")
            cv2.imwrite(f"{error_path}/{code}_{src_plate_num}_feature.jpg", img4)
        return vio_judge, judge_infos
    vio_judge = "no_vio"
    t1 = time.time()
    det2 = BlendMaskDetect(img2)
    predictions2, kps_mask2 = det2.mask_predict(demo, show=False)
    nms_ls2 = det2.nms_class_distinct(predictions2["instances"].pred_boxes.tensor,
                                      predictions2["instances"].scores,
                                      predictions2["instances"].pred_classes,
                                      threshold=0.8,
                                      class_ditinct_threshold=0.85)
    ### nms 处理
    predictions2, kps_mask2 = det2.prediction_nms_filter(predictions2, nms_ls2, kps_mask2)
    seg_veh_boxes, seg_labels, seg_scores = det2.class_info2(predictions2, Blend_Judge_Vehicle, conf_thresh=0.1)

    logger.info(f"{os.getpid()}-blend img2耗时：{round(time.time() - t1, 2)}")
    t1 = time.time()

    if is_motorbike:
        detect_cls = ["motorcycle", "car", "truck"]
    elif img1_car_ps:
        detect_cls = ["car", "truck", "bus"]
    else:
        detect_cls = None
    crop_img2_list, car2_bbox_ps, labels_list2, score_list2 = yolov7_detect(img2, yolov7_model, yolov7_meta[0],
                                                                            yolov7_meta[1],
                                                                            yolov7_meta[2],conf_thres=0.1,
                                                                            classes=detect_cls, need_conf=True)
    # tmp = "../test_results/1625/tmp2"
    # os.makedirs(tmp, exist_ok=True)
    # for idx, i in enumerate(crop_img2_list):
    #     cv2.imwrite(f"{tmp}/{src_plate_num}_{labels_list2[idx]}_{score_list2[idx]}_{idx}.jpg", i)
    if not car2_bbox_ps:
        car2_bbox_ps = seg_veh_boxes
        labels_list2 = seg_labels
        score_list2 = seg_scores
    logger.info(f"{os.getpid()}-车辆检测2耗时：{round(time.time() - t1, 2)}")
    print("img2 plate recognizing...")
    t1 = time.time()
    img2_car_ps, plate2_num, img2_plate_dict, new_car2_bbox_ps = CarJudge(
        plate_match_thresh=judge_thresh).confirm_img1_car_ps(img2, src_plate_num,
                                                             plate_det_model,
                                                             plate_rec_model,
                                                             car2_bbox_ps,
                                                             yolov7_meta[2], label=0,
                                                             show=save,
                                                             save_path=save_path,
                                                             is_motorbike=is_motorbike)
    logger.info(f"{os.getpid()}-车牌匹配2耗时：{round(time.time() - t1, 2)}")

    if not img1_car_ps and img2_car_ps:
        idx2 = car2_bbox_ps.index(img2_car_ps)
        if labels_list2[idx2] == "motorcycle":
            is_motorbike = True
            range_judge = True

    # 一二图都无法通过车牌匹配模块找到目标车辆报废片
    if not img1_car_ps and not img2_car_ps:
        # 第四张图与第一张图进行相似度匹配找到目标车(无四图不启用)
        # if img4_car_ps:
        #     print("41")
        #     img1_car_ps, plate1_num, img1_conf, _ = \
        #         CarJudge().confrim_img23_plate(img4_car_ps, img4, img1, new_car1_bbox_ps, src_plate_num, wf_label=True,
        #                                        show=save, save_path=save_path, save_name="41")
        # if not img1_car_ps:

        if feat_veh_box:
            vio_judge = 'vio_feat'
            save_dst = vio_path
        else:
            vio_judge = 'no_vio_003'
            save_dst = error_path
        judge_infos['vio_judge'] = vio_judge
        code = vio_judge.split("_")[-1]
        if save:
            show_police(img_list, p_res)
            show_light(img1, img2, img3, img1_light_res, img2_light_res, img3_light_res, status)
            show_cars(img1, img2, img4, car1_bbox_ps, car2_bbox_ps, feat_boxes)
            cv2.imwrite(f"{save_dst}/{code}_{src_plate_num}_img1.jpg", img1)
            cv2.imwrite(f"{save_dst}/{code}_{src_plate_num}_img2.jpg", img2)
            cv2.imwrite(f"{save_dst}/{code}_{src_plate_num}_img3.jpg", img3)
            cv2.imwrite(f"{save_dst}/{code}_{src_plate_num}_feat.jpg", img4)
        return vio_judge, judge_infos
    t1 = time.time()
    det3 = BlendMaskDetect(img3)
    predictions3, kps_mask3 = det3.mask_predict(demo, show=False)
    logger.info(f"{os.getpid()}-blend img3耗时：{round(time.time() - t1, 2)}")
    t1 = time.time()
    if is_motorbike:
        detect_cls = ["motorcycle", "car", "truck"]
    else:
        detect_cls = ["car", "truck", "bus"]
    crop_img3_list, car3_bbox_ps, labels_list3, score_list3 = yolov7_detect(img3, yolov7_model, yolov7_meta[0],
                                                                            yolov7_meta[1], yolov7_meta[2],
                                                                            classes=detect_cls, conf_thres=0.1,
                                                                            need_conf=True)
    # tmp = "../test_results/1625/tmp3"
    # os.makedirs(tmp, exist_ok=True)
    # for idx, i in enumerate(crop_img3_list):
    #     cv2.imwrite(f"{tmp}/{src_plate_num}_{labels_list3[idx]}_{score_list3[idx]}_{idx}.jpg", i)
    logger.info(f"{os.getpid()}-车辆检测3耗时：{round(time.time() - t1, 2)}")
    t1 = time.time()
    print("img3 plate recognizing...")
    img3_car_ps, plate3_num, img3_plate_dict, new_car3_bbox_ps = CarJudge(
        plate_match_thresh=[3, 1]).confirm_img1_car_ps(img3, src_plate_num,
                                                       plate_det_model,
                                                       plate_rec_model,
                                                       car3_bbox_ps,
                                                       yolov7_meta[2], label=0,
                                                       show=save,
                                                       save_path=save_path,
                                                       is_motorbike=is_motorbike)

    logger.info(f"{os.getpid()}-车牌匹配3耗时：{round(time.time() - t1, 2)}")
    logger.info(f"三图车牌识别情况：{plate1_num} {plate2_num} {plate3_num}")

    # blendmask结果NMS处理
    nms_ls2 = det2.nms_class_distinct(predictions2["instances"].pred_boxes.tensor,
                                      predictions2["instances"].scores,
                                      predictions2["instances"].pred_classes,
                                      threshold=0.8,
                                      class_ditinct_threshold=0.85)
    nms_ls3 = det3.nms_class_distinct(predictions3["instances"].pred_boxes.tensor,
                                      predictions3["instances"].scores,
                                      predictions3["instances"].pred_classes,
                                      threshold=0.8,
                                      class_ditinct_threshold=0.85)
    ### nms 处理
    predictions2, kps_mask2 = det2.prediction_nms_filter(predictions2, nms_ls2, kps_mask2)
    predictions3, kps_mask3 = det3.prediction_nms_filter(predictions3, nms_ls3, kps_mask3)

    # 测试
    # car2_bbox_ps, labels_list2, score_list2 = det2.class_info2(predictions2, Blend_Judge_Vehicle)
    # tmp = "../test_results/1625/tmp_blend"
    # os.makedirs(tmp,exist_ok=True)
    # for idx,box in enumerate(car2_bbox_ps):
    #     cv2.rectangle(img2,(box[0],box[1]),(box[2],box[3]),(0,0,255),2)
    #     cv2.putText(img2,f"{labels_list2[idx]}_{score_list2[idx]:.3f}",(box[0],(box[1]+box[3])//2),cv2.FONT_HERSHEY_SIMPLEX,1,(0,0,255),2)
    # cv2.imwrite(f"{tmp}/{src_plate_num}_img2.jpg",img2)
    #
    # car3_bbox_ps, labels_list3, score_list3 = det3.class_info2(predictions3, Blend_Judge_Vehicle)
    # os.makedirs(tmp, exist_ok=True)
    # for idx,box in enumerate(car3_bbox_ps):
    #     cv2.rectangle(img3,(box[0],box[1]),(box[2],box[3]),(0,0,255),2)
    #     cv2.putText(img3,f"{labels_list3[idx]}_{score_list3[idx]:.3f}",(box[0],(box[1]+box[3])//2),cv2.FONT_HERSHEY_SIMPLEX,1,(0,0,255),2)
    # cv2.imwrite(f"{tmp}/{src_plate_num}_img3.jpg",img3)

    # 3图过滤车头
    if img3_car_ps == [] and not recall_mode:
        head_boxes_list, head_labels_list, head_score_list = det3.class_info2(predictions3, Filter_Vehicle_Head,
                                                                              conf_thresh=Det_Head_Thresh)
        # IOU比较删除相似度匹配中的车头
        if head_boxes_list and new_car3_bbox_ps:
            tmp_car3_boxes = np.asarray(new_car3_bbox_ps)
            compute_iou = Tools().compute_iou(head_boxes_list, new_car3_bbox_ps)
            _, del_head_idx = np.where(compute_iou >= Head_IOU)
            all_idx = np.arange(len(new_car3_bbox_ps))
            fiter_idx = np.setdiff1d(all_idx, del_head_idx)

            del_car3_bbox_ps = tmp_car3_boxes[del_head_idx]
            logger.info(f"三图删除车头数：{len(del_head_idx)}")
            if save:
                head_img = img3.copy()
                head_save_root = f"{save_path}/del_head"
                os.makedirs(head_save_root, exist_ok=True)
                for box in head_boxes_list:
                    x1, y1, x2, y2 = np.asarray(box)
                    cv2.rectangle(head_img, (x1, y1), (x2, y2), (0, 255, 255), 2)
                for box in del_car3_bbox_ps:
                    x1, y1, x2, y2 = box
                    cv2.rectangle(head_img, (x1, y1), (x2, y2), (0, 0, 255), 2)
                num = 1
                head_save_path = f"{head_save_root}/{src_plate_num}_{num}.jpg"
                while os.path.exists(head_save_path):
                    num += 1
                    head_save_path = f"{head_save_root}/{src_plate_num}_{num}.jpg"

                cv2.imwrite(head_save_path, head_img)
            new_car3_bbox_ps = tmp_car3_boxes[fiter_idx].tolist()
            if new_car3_bbox_ps == []:
                logger.info("车头去除后三图无车辆，初始化结果!")
                new_car3_bbox_ps = tmp_car3_boxes.tolist()
    # 车辆相似度对比模块
    # 车辆相似度对比模块
    if (not img1_car_ps) and img2_car_ps:  ### 第一张图为空 而 第二张图有准确识别到
        t1 = time.time()
        print("21")
        img1_car_ps, plate1_num, img1_conf, _ = \
            CarJudge(yolov7_meta[2]).confrim_img23_plate(img2_car_ps, img2, img1, new_car1_bbox_ps,
                                                         src_plate_num,
                                                         wf_label=True,
                                                         show=save, save_path=save_path, save_name="21")
        logger.info(f"{os.getpid()}-车辆相似度21匹配耗时：{round(time.time() - t1, 2)}")
    if not recall_mode:
        if img2_car_ps == [] and img1_car_ps:  ### 第二张图为空 而 第一张图有准确识别到
            t1 = time.time()
            print("12")
            img2_car_ps, plate2_num, img2_conf = \
                CarJudge(yolov7_meta[2]).confirm_img23_car_ps(img1_car_ps, img1, img2, src_plate_num,
                                                              new_car2_bbox_ps, wf_label=False, show=save,
                                                              save_path=save_path,
                                                              save_name="12")

            logger.info(f"{os.getpid()}-车辆相似度12匹配耗时：{round(time.time() - t1, 2)}")
        # 车辆相似度对比模块
        if img3_car_ps == [] and img2_car_ps:  ### 通过第二张 找 第三张
            t1 = time.time()
            print("23")

            img3_car_ps, plate3_num, img3_conf = CarJudge(yolov7_meta[2]).confirm_img23_car_ps(img2_car_ps, img2, img3,
                                                                                               src_plate_num,
                                                                                               new_car3_bbox_ps,
                                                                                               wf_label=False,
                                                                                               show=save,
                                                                                               save_path=save_path,
                                                                                               save_name="23")
            logger.info(f"{os.getpid()}-车辆相似度23匹配耗时：{round(time.time() - t1, 2)}")
        # 车辆相似度对比模块
        if img3_car_ps == [] and img2_car_ps == [] and img1_car_ps:  ### 通过第一张图找第三张图
            t1 = time.time()
            print("13")
            img3_car_ps, plate3_num, img3_conf = CarJudge(yolov7_meta[2]).confirm_img23_car_ps(img1_car_ps, img1, img3,
                                                                                               src_plate_num,
                                                                                               new_car3_bbox_ps,
                                                                                               wf_label=False,
                                                                                               is_13=True, show=save,
                                                                                               save_path=save_path,
                                                                                               save_name="13")
            logger.info(f"{os.getpid()}-车辆相似度13匹配耗时：{round(time.time() - t1, 2)}")
    # 车牌位置信息
    plate1_cor = img1_plate_dict[str(img1_car_ps)] if img1_car_ps else []
    plate2_cor = img2_plate_dict[str(img2_car_ps)] if img2_car_ps else []
    plate3_cor = img3_plate_dict[str(img3_car_ps)] if img3_car_ps else []
    # 返回检测框和置信度信息
    img_np_list = [img1, img2, img3]
    img_car_ps_list = [img1_car_ps, img2_car_ps, img3_car_ps]
    det_car_ps_list = [car1_bbox_ps, car2_bbox_ps, car3_bbox_ps]
    scores_list = [score_list, score_list2, score_list3]
    for idx, box in enumerate(img_car_ps_list):
        if box:
            target_idx = det_car_ps_list[idx].index(box)
            img_id = idx + 1
            percent_coord = convert_src_coord(box, img_np_list[idx], img_id, judge_infos["split_mode"],
                                              judge_infos["black_height"], judge_infos["black_pos"])
            tmp_res = {
                "objId": img_id,
                "objType": "vehicle",
                "objConf": float(scores_list[idx][target_idx]),
                "objCoord": percent_coord
            }
            judge_infos["modelDetectResults"].append(tmp_res)
    plate_box_list = [plate1_cor, plate2_cor, plate3_cor]
    plate_dict_list = [img1_plate_dict, img2_plate_dict, img3_plate_dict]
    for idx, box in enumerate(plate_box_list):
        if box:
            target_idx = str(img_car_ps_list[idx]) + "conf"
            img_id = idx + 1
            percent_coord = convert_src_coord(box, img_np_list[idx], img_id, judge_infos["split_mode"],
                                              judge_infos["black_height"], judge_infos["black_pos"])
            tmp_res = {
                "objId": img_id,
                "objType": "plate",
                "objConf": float(plate_dict_list[idx][target_idx]),
                "objCoord": percent_coord
            }
            judge_infos["modelDetectResults"].append(tmp_res)

    # 第三张图未找到目标车
    if not img3_car_ps:
        color_list = [i.split("_")[0] for i in status]
        if (not recall_mode and No_Car3_Is_Waste):# or plate3_num == "NO_SUIT_CAR":# or new_car3_bbox_ps == []
            vio_judge = 'no_vio_004'
            judge_infos["judgeConf"] = 0.8
        elif "red" in color_list and new_car3_bbox_ps:
            vio_judge = 'vio_004'
            judge_infos["judgeConf"] = 0.85
        if not recall_mode or vio_judge == 'no_vio_004':
            judge_infos['vio_judge'] = vio_judge
            judge_infos['target_car'] = [{'plate_num1': plate1_num, 'car_ps1': img1_car_ps},
                                         {'plate_num2': plate2_num, 'car_ps2': img2_car_ps},
                                         {'plate_num3': plate3_num, 'car_ps3': img3_car_ps}]
            code = vio_judge.split("_")[-1]
            tmp_path = vio_path if vio_judge.startswith("vio") else error_path
            if save:
                show_police(img_list, p_res)
                show_light(img1, img2, img3, img1_light_res, img2_light_res, img3_light_res, status)
                show_cars(img1, img2, img3, car1_bbox_ps, car2_bbox_ps, car3_bbox_ps)
                show_plate(img1, img2, img3, img1_car_ps, img2_car_ps, img3_car_ps, plate1_cor, plate2_cor, plate3_cor)
                cv2.imwrite(f"{tmp_path}/{code}_{src_plate_num}_img1.jpg", img1)
                cv2.imwrite(f"{tmp_path}/{code}_{src_plate_num}_img2.jpg", img2)
                cv2.imwrite(f"{tmp_path}/{code}_{src_plate_num}_img3.jpg", img3)
            return vio_judge, judge_infos

    # time.sleep(10)
    judge_infos['target_car'] = [{'plate_num1': plate1_num, 'car_ps1': img1_car_ps},
                                 {'plate_num2': plate2_num, 'car_ps2': img2_car_ps},
                                 {'plate_num3': plate3_num, 'car_ps3': img3_car_ps}]

    # 明显位移判定
    stop = False
    iou12 = 1
    iou23 = iou_y = 0
    img2_tmp = img2_car_ps
    if img2_car_ps and img3_car_ps:
        h2 = img2_car_ps[3] - img2_car_ps[1]
        stop_thresh = 0.05 * h2
        if img2_car_ps[3] - img3_car_ps[3] < stop_thresh:  ### 二三图判断没有移动
            if plate2_num == "SIMILAR_COMPARE" and plate3_num != "SIMILAR_COMPARE":  ### 二图是匹配出来的
                if img1_car_ps[3] - img3_car_ps[3] < stop_thresh * 2:  ### 如果顺序为 三图在最前方 且与一没有明显位移 then stop
                    stop = True
                else:  ### 一三判断有位移， 但是第二张图没有唯一，第二张图错误，洗掉错误值
                    # 二图目标车辆匹配有误进行初始化
                    img2_car_ps = []
            elif max(abs(img2_car_ps[0] - img3_car_ps[0]), abs(img2_car_ps[2] - img3_car_ps[2])) > 2.75 * abs(
                    img2_car_ps[3] - img3_car_ps[3]):
                # 大概率掉头或转向
                logger.info(f"{os.getpid()}-大概率掉头或转向")
            else:  ### 确认后 确实未移动
                stop = True
        else:  ### 二三图移动了的情况
            if img1_car_ps and img2_car_ps:
                iou12 = (img1_car_ps[3] - img2_car_ps[3]) / h2  # get_iou(img1_car_ps, img2_car_ps)  ### 12图重叠度
            iou23 = get_iou(img2_car_ps, img3_car_ps)  ### 23 图重叠度
            iou_y = get_yiou(img2_car_ps, img3_car_ps)  ### 只看 23图 y轴重叠度
            print("iou12:", iou12, "iou23:", iou23, "iou_y:", iou_y)

        if iou12 < 0.07 or iou23 > 0.95 or iou_y > 0.98 or stop:  ### 12 23 任意一个没有位移 直接打断 返回
            vio_judge = 'no_vio_005'
            judge_infos['vio_judge'] = vio_judge
            code = vio_judge.split("_")[-1]
            judge_infos["judgeConf"] = 0.9

            if save:
                show_police(img_list, p_res)
                show_light(img1, img2, img3, img1_light_res, img2_light_res, img3_light_res, status)
                show_cars(img1, img2, img3, car1_bbox_ps, car2_bbox_ps, car3_bbox_ps)
                show_plate(img1, img2, img3, img1_car_ps, img2_tmp, img3_car_ps, plate1_cor, plate2_cor, plate3_cor)
                cv2.imwrite(f"{error_path}/{code}_{src_plate_num}_img1.jpg", img1)
                cv2.imwrite(f"{error_path}/{code}_{src_plate_num}_img2.jpg", img2)
                cv2.imwrite(f"{error_path}/{code}_{src_plate_num}_img3.jpg", img3)
            return vio_judge, judge_infos

    img_car_ps_list = [img1_car_ps, img2_car_ps, img3_car_ps]  ### 三张图检测的结果

    # 检测车道线状态信息
    # print(predictions1,"############\n")

    ### 如果需要加入nms则可以考虑采用如下代码
    ### 统一做nms
    ## 对三张图进行nms处理 得到被留下的tensor索引
    # t1 = time.time()

    # logger.info(f"{os.getpid()}-blend nms耗时：{round(time.time() - t1, 2)}")

    ### 画图用整体
    lanes_img1, arrow_mask_info1 = det1.class_info(predictions1,
                                                   ['lane1', 'lane2', "arrow_l", "arrow_s", "arrow_r", 'stop_line'],
                                                   div_label=True)
    lanes_img2, arrow_mask_info2 = det2.class_info(predictions2,
                                                   ['lane1', 'lane2', "arrow_l", "arrow_s", "arrow_r", 'stop_line'],
                                                   div_label=True)
    lanes_img3, arrow_mask_info3 = det3.class_info(predictions3,
                                                   ['lane1', 'lane2', "arrow_l", "arrow_s", "arrow_r", 'stop_line'],
                                                   div_label=True)

    ### 具体识别

    lane_pred1, _ = det1.class_info(predictions1, ['lane1', 'lane2'], div_label=True)
    lane_pred2, _ = det2.class_info(predictions2, ['lane1', 'lane2'], div_label=True)
    lane_pred3, _ = det3.class_info(predictions3, ['lane1', 'lane2'], div_label=True)

    arrow_pred1, _ = det1.class_info(predictions1, ["arrow_l", "arrow_s", "arrow_r"], div_label=True)
    arrow_pred2, _ = det2.class_info(predictions2, ["arrow_l", "arrow_s", "arrow_r"], div_label=True)
    arrow_pred3, _ = det3.class_info(predictions3, ["arrow_l", "arrow_s", "arrow_r"], div_label=True)

    stop_line_pred1, _ = det1.class_info(predictions1, ['stop_line'], div_label=True)
    stop_line_pred2, _ = det2.class_info(predictions2, ['stop_line'], div_label=True)
    stop_line_pred3, _ = det3.class_info(predictions3, ['stop_line'], div_label=True)

    line_judge = 0
    # 存放确信的停止线
    stop_line_s1 = []
    stop_line_s2 = []
    # 筛选目标车相关的元素
    img1_arrow = {}
    img2_arrow = {}
    img3_arrow = {}

    lane_list = []
    arrow_list = [img1_arrow, img2_arrow, img3_arrow]
    stop_line_list = []
    # img1_car_by = max(img1_car_ps[1], img1_car_ps[3]) if img1_car_ps else img1.shape[0]
    car1_h = abs(img1_car_ps[1] - img1_car_ps[3]) if img1_car_ps else img1.shape[0]
    img1_car_by = min(img1_car_ps[1], img1_car_ps[3]) + Car1_Stop_Line_Rate * car1_h if img1_car_ps else img1.shape[0]
    img2_car_by = max(img2_car_ps[1], img2_car_ps[3]) if img2_car_ps else 0
    if img1_car_ps and img2_car_ps:
        img1_car_cy = (img1_car_ps[1] + img1_car_ps[3]) / 2
        img2_car_ty = min(img2_car_ps[1], img2_car_ps[3])

        t1 = img1_car_cy < 0.25 * img1.shape[0]
        t2 = img2_car_ty > 0.7 * img1.shape[0]
        if t1 or t2:
            line_judge = 1
            print("img1_line_judge:", t1, " ", "img2_line_judge:", t2)
            # 停止线判定
            if line_judge:
                vio_judge = 'no_vio_006'
                judge_infos['vio_judge'] = vio_judge
                judge_infos['stop_line'] = line_judge
                code = vio_judge.split("_")[-1]
                judge_infos["judgeConf"] = 0.99

                if save:
                    show_police(img_list, p_res)
                    show_light(img1, img2, img3, img1_light_res, img2_light_res, img3_light_res, status)
                    show_cars(img1, img2, img3, car1_bbox_ps, car2_bbox_ps, car3_bbox_ps)
                    show_plate(img1, img2, img3, img1_car_ps, img2_tmp, img3_car_ps, plate1_cor, plate2_cor,
                               plate3_cor)
                    show_lanes(img1, img2, img3, lanes_img1, lanes_img2, lanes_img3)
                    cv2.imwrite(f"{error_path}/{code}_{src_plate_num}_img1.jpg", img1)
                    cv2.imwrite(f"{error_path}/{code}_{src_plate_num}_img2.jpg", img2)
                    cv2.imwrite(f"{error_path}/{code}_{src_plate_num}_img3.jpg", img3)
                return vio_judge, judge_infos

    stop_by = img1.shape[0]
    if img1_car_ps:
        img1_car_cx = (img1_car_ps[0] + img1_car_ps[2]) / 2
        car_lx = img1_car_ps[0]
        car_rx = img1_car_ps[2]
        img1_car_w = (img1_car_ps[2] - img1_car_ps[0])
        img_half_h = [img1.shape[0] / 2, img2.shape[0] / 2, img3.shape[0] / 2]
        # 单双实线筛选
        last_cx = -img1_car_w
        arrow_range = range(0, img1.shape[1] + 1)
        lx = car_lx - img1_car_w * 0.5
        rx = car_rx + img1_car_w * 0.5
        for idx, lane_info in enumerate([lane_pred1, lane_pred2, lane_pred3]):
            min_y = img_half_h[idx]
            for key, value in lane_info.items():
                for ps in value:
                    ps_cx = (ps[0] + ps[2]) // 2
                    ps_cy = (ps[1] + ps[3]) / 2

                    ps_w = abs(ps[0] - ps[2])
                    add = [ps_cx, ps_w, ps[1]]
                    ps.extend(add)
                    # ps:[x1,y1,x2,y2,ps_cx,ps_w,ty]
                    # 过滤中心点位置过高的框/车道以外的框
                    if ps_cy < min_y * 1 or ps_cx > rx or ps_cx < lx:
                        continue
                    dist = abs(ps_cx - last_cx) / 1.5 if range_judge else abs(ps_cx - last_cx)
                    lane_iou = [get_iou(lane[:4], ps[:4]) for lane in lane_list] if lane_list else []
                    # 修正同一条单双实线的最小高度
                    for idx, lane in enumerate(lane_list):
                        # 新的线要在旧的线区域之外 iou比较
                        # if (lane[0] < ps_cx or ps_cx < lane[2]) and lane_iou[idx] >= 0.7:
                        #     lane[-1] = min((lane[-1], ps[1]))
                        # 实线被截断
                        lane[-1] = min((lane[-1], ps[1]))

                    # 修正arrow_range范围
                    if lx < ps_cx < rx and dist > img1_car_w:
                        last_cx = ps_cx
                        if len(lane_list) < 2:
                            lane_list.append(ps)
                            if len(lane_list) == 2:
                                lane_list = sorted(lane_list, reverse=False)
                                arrow_range = range(lane_list[0][4], lane_list[1][4])
                                lane_iou = [get_iou(lane[:4], ps[:4]) for lane in lane_list] if lane_list else []
                        # 修正实线最高点
                        if len(lane_list) == 2:
                            # 识别的实线比之前的判定实线高
                            if lane_iou[0] >= 0.7 and lane_list[0][1] > ps[1] and ps_cx < arrow_range[-1]:
                                lane_list[0] = ps
                                arrow_range = range(ps_cx, arrow_range[-1])
                            elif lane_iou[1] >= 0.7 and lane_list[1][1] > ps[1] and ps_cx > arrow_range[0]:
                                lane_list[1] = ps
                                arrow_range = range(arrow_range[0], ps_cx)
                            # if lane_iou[0]>=0.7 and lane_list[0][1]<ps[1]:
                            #     lane_list[0] = ps
                            # if lane_iou[1]>=0.7 and lane_list[1][1]<ps[1]:
                            #     lane_list[1] = ps
        if save:
            cv2.line(img1, (int(lx), 0), (int(lx), img1.shape[0]), (255, 0, 0), 2)
            cv2.line(img1, (int(rx), 0), (int(rx), img1.shape[0]), (255, 0, 0), 2)
            if arrow_range != range(0, img1.shape[1] + 1):
                cv2.line(img1, (arrow_range[0], 0), (arrow_range[0], img1.shape[0]),
                         (255, 255, 255), 2)
                cv2.line(img1, (arrow_range[-1], 0), (arrow_range[-1], img1.shape[0]),
                         (255, 255, 255), 2)
        # 箭头线筛选
        tmp_stop_by = img1.shape[0]
        lx = car_lx - img1_car_w * 0.25
        rx = car_rx + img1_car_w * 0.25
        if save:
            cv2.line(img1, (int(lx), 0), (int(lx), img1.shape[0]), (255, 0, 255), 2)
            cv2.line(img1, (int(rx), 0), (int(rx), img1.shape[0]), (255, 0, 255), 2)
            # test_range = 0.2
            # cv2.line(img1, (int(car_lx - img1_car_w * test_range), int(img1.shape[0] * 0.5)),
            #          (int(car_lx - img1_car_w * test_range), img1.shape[0]), (255, 0, 255), 2)
            # cv2.line(img1, (int(car_rx + img1_car_w * test_range), int(img1.shape[0] * 0.5)),
            #          (int(car_rx + img1_car_w * test_range), img1.shape[0]), (255, 0, 255), 2)
        if len(lane_list) == 2:
            judge_lx = max((lx, arrow_range[0]))
            judge_rx = min((rx, arrow_range[-1] + 1))
        else:
            judge_lx = lx
            judge_rx = rx

        for idx, lane_info in enumerate([arrow_pred1, arrow_pred2, arrow_pred3]):
            min_y = img_half_h[idx]
            for key, value in lane_info.items():
                for ps in value:
                    ps_cx = (ps[0] + ps[2]) / 2
                    ps_cy = (ps[1] + ps[3]) / 2
                    if ps_cy < min_y:
                        continue
                    # 添加箭头线的stop_by限制停止线区域
                    if judge_lx < ps_cx < judge_rx and ps[0] in arrow_range and ps[2] in arrow_range:
                        if key not in arrow_list[idx].keys():
                            arrow_list[idx][key] = [ps]
                        else:
                            arrow_list[idx][key].append(ps)
                        stop_by = min((ps[1], stop_by))
                    # 所在车道无导向箭头
                    tmp_stop_by = min((ps[1], stop_by))
        if stop_by == img1.shape[0]:
            stop_by = tmp_stop_by
        # 停止线筛选
        for idx, lane_info in enumerate([stop_line_pred1, stop_line_pred2, stop_line_pred3]):
            min_y = img_half_h[idx]
            for key, value in lane_info.items():
                for ps in value:
                    ps_cy = (ps[1] + ps[3]) / 2
                    ps_w = abs(ps[0] - ps[2])
                    if ps_cy < min_y or ps_cy > stop_by or (ps[0] + ps[2]) / 2 < img1.shape[1] * 0.3:
                        continue
                    if ps[0] < img1_car_cx < ps[2] or (ps[0] > judge_lx and ps[2] < judge_rx):
                        stop_line_s1.append(ps)
                        continue
                    # 停止线足够宽则认为识别准确
                    if ps_w > img1_car_w * 2:
                        stop_line_s2.append(ps)
                        continue
                    # 不确定停止线保存在列表中
                    if ps_w > img1_car_w:
                        stop_line_list.append(ps)

    # ('stop_line_list') [ps] ps:[x1,y1,x2,y2]
    # ('lane_list') 存为 [ps] ps:[x1,y1,x2,y2,ps_cx,ps_w,ty]
    # ('arrow_list')
    lane_ty = []
    # 单双实线位置信息推测停止线[x1,y1,x2,y2,ps_cx,ps_w,ty] TY就是y1
    if len(lane_list) == 2:  ### 推测停止线位置 [x1,y1,x2,y2,ps_cx,ps_w,ty]
        h1 = lane_list[0][3] - lane_list[0][1]
        h2 = lane_list[1][3] - lane_list[1][1]
        if abs(lane_list[0][-1] - lane_list[1][-1]) < h1:
            lane_ty.append(lane_list[0][-1])
        if abs(lane_list[0][-1] - lane_list[1][-1]) < h2:
            lane_ty.append(lane_list[1][-1])
    elif len(lane_list) == 1 and stop_by != img1.shape[0]:
        dist = abs(lane_list[0][-1] - stop_by)
        if dist < 0.2 * (img1_car_ps[3] - img1_car_ps[1]):
            lane_ty.append(np.average((lane_list[0][-1], stop_by)))
    line_judge1 = []
    line_judge2 = []
    if stop_line_s1:
        for i in stop_line_s1:
            line_cy = (i[1] + i[3]) / 2
            line_h = abs(i[1] - i[3])
            line_judge1.append(line_cy - line_h)
            line_judge2.append(line_cy + line_h)
    elif stop_line_s2:
        for i in stop_line_s2:
            line_cy = (i[1] + i[3]) / 2
            line_h = abs(i[1] - i[3])
            line_judge1.append(line_cy - line_h)
            line_judge2.append(line_cy + line_h)
    elif stop_line_list:
        for i in stop_line_list:
            line_cy = (i[1] + i[3]) / 2
            line_h = abs(i[1] - i[3])
            if lane_ty:
                line_cy = np.average((np.min(lane_ty), line_cy))
            line_judge1.append(line_cy - line_h)
            line_judge2.append(line_cy + line_h)
    elif lane_ty:
        line_cy = np.min(lane_ty)
        line_judge1.append(line_cy - 20)
        line_judge2.append(line_cy + 20)
    if line_judge1:
        line_judge1 = np.average(line_judge1)
        if img1_car_by < line_judge1 and 1 in Stop_Line_Judge:
            line_judge = 2

    if line_judge2:
        line_judge2 = np.average(line_judge2) + 0.2 * np.average(
            (img2_car_ps[1], img2_car_ps[3])) if img2_car_ps else np.average(line_judge2)
        if img2_car_by > line_judge2 and 2 in Stop_Line_Judge:
            line_judge = 3
    judge_infos['stop_line'] = line_judge

    if save:
        if line_judge1: cv2.line(img1, (0, int(line_judge1)), (img1.shape[1], int(line_judge1)), (0, 0, 255), 2)
        if line_judge2: cv2.line(img2, (0, int(line_judge2)), (img2.shape[1], int(line_judge2)), (0, 0, 255), 2)
        if img1_car_ps: cv2.line(img1, (int(img1_car_ps[0]), int(img1_car_by)), (int(img1_car_ps[2]), int(img1_car_by)),
                                 (0, 255, 0), 2)
        if img2_car_ps: cv2.line(img2, (int(img2_car_ps[0]), int(img2_car_by)), (int(img2_car_ps[2]), int(img2_car_by)),
                                 (0, 255, 0), 2)
    # 停止线判定
    if line_judge:
        vio_judge = 'no_vio_006'
        judge_infos['vio_judge'] = vio_judge
        judge_infos['stop_line'] = line_judge
        code = vio_judge.split("_")[-1]
        judge_infos["judgeConf"] = 0.9

        if save:
            show_police(img_list, p_res)
            show_light(img1, img2, img3, img1_light_res, img2_light_res, img3_light_res, status)
            show_cars(img1, img2, img3, car1_bbox_ps, car2_bbox_ps, car3_bbox_ps)
            show_plate(img1, img2, img3, img1_car_ps, img2_tmp, img3_car_ps, plate1_cor, plate2_cor,
                       plate3_cor)
            show_lanes(img1, img2, img3, lanes_img1, lanes_img2, lanes_img3)
            cv2.imwrite(f"{error_path}/{code}#{line_judge}_{src_plate_num}_img1.jpg", img1)
            cv2.imwrite(f"{error_path}/{code}#{line_judge}_{src_plate_num}_img2.jpg", img2)
            cv2.imwrite(f"{error_path}/{code}#{line_judge}_{src_plate_num}_img3.jpg", img3)
        return vio_judge, judge_infos

    # 三张图的箭头线求并集
    temp1 = set(img1_arrow.keys())
    temp2 = set(img2_arrow.keys())
    temp3 = set(img3_arrow.keys())
    arrow_result = list(temp1.union(temp2).union(temp3))
    a_l = 0
    a_s = 0
    a_r = 0
    if arrow_result:
        for arrow in arrow_result:
            if arrow == 'arrow_l':
                a_l = 1
            if arrow == 'arrow_s':
                a_s = 1
            if arrow == 'arrow_r':
                a_r = 1

    judge_infos['lane'] = [a_l, a_s, a_r]

    plate_list = [plate1_cor, plate2_cor, plate3_cor]
    lane_list = [a_l, a_s, a_r]
    direction = None
    if img3_car_ps:
        # 车辆行驶方向判定
        car_diretion = CarDriveDirectionJudge(img_list, img_car_ps_list, plate_list, lane_list,
                                              car_body_direct_judge=True, lane_pos_judge=True,
                                              need_three_car_body_data=False)
        direction, drv_img, sure = car_diretion.driv_dire_judge_16250()

    try:
        x_ratio = ((img1_car_ps[0] + img1_car_ps[2]) / 2) / img1.shape[1]
    except:
        x_ratio = 0
    if x_ratio <= 0.5:
        judge_infos['vehicle_location'] = 0
    else:
        judge_infos['vehicle_location'] = 1
    arrow_judge = False
    # if direction is None and sum((a_l,a_s,a_r)) == 1:
    #     if a_l == 1:
    #         direction = "left"
    #     elif a_s == 1:
    #         direction = "straight"
    #     elif a_r == 1:
    #         direction = "right"
    #     logger.info(f"根据导向箭头修正行驶方向为{direction}")
    #     vio_judge = ""
    #     arrow_judge = True
    judge_infos['drv_direction'] = direction

    # 行驶方向与status逻辑判定违法
    if direction == 'right' and 'red_r' in status:
        vio_judge = 'vio_right'
    elif direction == 'left':
        if 'red_l' in status:
            vio_judge = 'vio_left'
        elif 'green_l' not in status and 'red_s' in status:
            vio_judge = 'vio_left'
    elif direction == 'straight' and 'red_s' in status:
        vio_judge = 'vio_straight'
    elif direction == "vio_002":
        vio_judge = direction
    elif direction is None:
        vio_judge = 'vio'
    # if arrow_judge and not vio_judge.endswith("008"):
    #     vio_judge += "_008"

    judge_infos['vio_judge'] = vio_judge
    if vio_judge.startswith("no_vio"):
        if plate3_num == "SIMILAR_COMPARE":
            judge_infos["judgeConf"] = 0.6
        else:
            judge_infos["judgeConf"] = 0.9
    if save:
        # 画红绿灯位置信息和车辆位置信息
        show_police(img_list, p_res)
        show_light(img1, img2, img3, img1_light_res, img2_light_res, img3_light_res, status)
        show_cars(img1, img2, img3, car1_bbox_ps, car2_bbox_ps, car3_bbox_ps)
        show_plate(img1, img2, img3, img1_car_ps, img2_tmp, img3_car_ps, plate1_cor, plate2_cor, plate3_cor)
        show_lanes(img1, img2, img3, lanes_img1, lanes_img2, lanes_img3)

        cv2.putText(img3, direction, (300, 300), cv2.FONT_HERSHEY_COMPLEX, 1.5, (0, 0, 255), 3)
        vio_rect_w = img3.shape[1] // 5
        cv2.putText(img3, vio_judge, (vio_rect_w * 2 + vio_rect_w // 3, 70), cv2.FONT_HERSHEY_COMPLEX, 1.5, (0, 0, 255),
                    3)

        if vio_judge.startswith("no_vio"):
            cv2.imwrite(error_path + f'/{vio_judge}_{src_plate_num}_img1.jpg', img1)
            cv2.imwrite(error_path + f'/{vio_judge}_{src_plate_num}_img2.jpg', img2)
            cv2.imwrite(error_path + f'/{vio_judge}_{src_plate_num}_img3.jpg', img3)
        else:
            cv2.imwrite(vio_path + f'/{vio_judge}_{src_plate_num}_img1.jpg', img1)
            cv2.imwrite(vio_path + f'/{vio_judge}_{src_plate_num}_img2.jpg', img2)
            cv2.imwrite(vio_path + f'/{vio_judge}_{src_plate_num}_img3.jpg', img3)

    return vio_judge, judge_infos
