from kafka import <PERSON><PERSON><PERSON><PERSON>onsumer, TopicPartition, OffsetAndMetadata
import sys
sys.path.append("..")
from main_config.config import *
import time
import json
from multiprocessing import Process
import os


# 获取主题的所有分区
def get_topic_partitions():
    consumer = KafkaConsumer(bootstrap_servers=SERVER)
    partitions = consumer.partitions_for_topic(VIO_DATA_TOPIC)
    consumer.close()
    return [TopicPartition(VIO_DATA_TOPIC, p) for p in partitions]


# 消费者函数
def consume(partitions):
    num_partitions = len(get_topic_partitions())
    set_partition = [i.partition for i in partitions]
    PID = os.getpid()
    Max_Poll_Interval_Ms = 60000
    max_poll_records = 2
    GROUP_ID = "ai_judge_1106_16"
    while True:
        consumer = KafkaConsumer(
            bootstrap_servers=SERVER,
            group_id=GROUP_ID,
            auto_offset_reset=AUTO_OFFSET_RESET,
            client_id=f"{CLIENT_ID}",
            max_poll_interval_ms=Max_Poll_Interval_Ms,
            request_timeout_ms=int(Max_Poll_Interval_Ms * 1.2) + 1000,
            session_timeout_ms=Max_Poll_Interval_Ms,
            connections_max_idle_ms=int(Max_Poll_Interval_Ms * 1.2) + 5000,
            enable_auto_commit=False,
            max_poll_records=max_poll_records,
            metadata_max_age_ms=Max_Poll_Interval_Ms,
            heartbeat_interval_ms=Max_Poll_Interval_Ms,
            max_partition_fetch_bytes=5242880
        )
        break

    # 手动分配分区
    consumer.assign(partitions)
    print(f"{PID}消费已就绪！总分区数：{num_partitions}，消费分区：{set_partition}")
    while True:
        try:
            # 拉取消息
            records = consumer.poll(timeout_ms=1000)
        except Exception as e:
            records = {}
            print(f"拉取消息失败: {e}")

        poll_st = time.time()
        for tp, messages in records.items():
            for msg in messages:
                request_dict = json.loads(msg.value)
                s_id = request_dict["vehicleAlarmResult"][0]["taskID"]
                try:
                    topic_partition = TopicPartition(msg.topic, msg.partition)
                    offset = OffsetAndMetadata(msg.offset + 1, metadata="")
                    print(f"pid: {os.getpid()}, partition: {msg.partition}, offset: {msg.offset}, id: {s_id}")

                except Exception as e:
                    print(time.strftime("%Y-%m-%d %H:%M:%S", time.localtime()) +
                          f"{e}\nTopic分区和偏移量初始化异常!")
                    continue
                detect()
                try:
                    # 手动提交偏移量
                    consumer.commit({topic_partition: offset})
                    print(
                        f"pid: {os.getpid()}, partition: {msg.partition}, offset: {msg.offset}, id: {s_id}, 完成偏移量提交")
                except Exception as e:
                    print(time.strftime("%Y-%m-%d %H:%M:%S", time.localtime()) +
                          f"{e}\ntopic offset update error!")
                    retry_commit(consumer, {topic_partition: offset})
        poll_et = time.time()
        if records:
            print(f"处理拉取数据总耗时: {poll_et - poll_st:.2f}秒")


def detect():
    time.sleep(10)
    print("detect finished!")


def retry_commit(consumer, offsets, max_retries=3):
    retries = 0
    while retries < max_retries:
        try:
            consumer.commit(offsets)
            break
        except Exception as e:
            print(f"偏移量提交第{retries + 1}次重试失败! {e}")
            retries += 1
    if retries == max_retries:
        print(f"重试{max_retries}次提交偏移量失败！")


# 主函数
def main(num_consumers):
    partitions = get_topic_partitions()
    num_partitions = len(partitions)
    partitions_per_consumer = num_partitions // num_consumers

    processes = []
    for i in range(num_consumers):
        start = i * partitions_per_consumer
        end = (i + 1) * partitions_per_consumer if i < num_consumers - 1 else num_partitions
        process_partitions = partitions[start:end]
        p = Process(target=consume, args=(process_partitions,))
        p.start()
        processes.append(p)

    while True:
        is_alive = [f"{p.pid}:{p.is_alive()}" for p in processes]
        save_status = " ".join(is_alive)
        now = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())
        mode = "w" if os.path.exists("./logs/status.txt") and (
                os.path.getsize("./logs/status.txt") / 1024 / 1024) > 10 else "a"
        try:
            with open("./logs/status.txt", mode, encoding="utf-8") as status_log:
                status_log.write(f"{now} 进程状态：{save_status}\n")
        except:
            pass
        time.sleep(Heart_Interval)
if __name__ == '__main__':
    num_consumers = 3
    main(num_consumers)