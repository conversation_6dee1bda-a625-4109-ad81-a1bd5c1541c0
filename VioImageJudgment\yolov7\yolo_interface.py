import os
import shutil
import sys

sys.path.append('..')
import time
import copy
import cv2
import torch
from main_config.config import coco_names
from yolov7.models.experimental import attempt_load
from yolov7.utils.datasets import LoadStreams, LoadImages, letterbox
from yolov7.utils.general import check_img_size, check_requirements, check_imshow, non_max_suppression, \
    non_max_suppression_plate, apply_classifier, scale_coords, \
    scale_coords_plate, xyxy2xywh, strip_optimizer, set_logging, increment_path
from yolov7.utils.torch_utils import select_device, load_classifier, TracedModel
from yolov7.utils.cv_puttext import cv2ImgAddText
from yolov7.plate_recognition.plate_rec import get_plate_result, allFilePath, init_model, cv_imread
from yolov7.plate_recognition.double_plate_split_merge import get_split_merge
from PIL import Image, ImageDraw, ImageFont
import numpy as np

# 车牌识别模型
colors = [(255, 0, 0), (0, 255, 0), (0, 0, 255), (255, 255, 0), (0, 255, 255)]


def img_split(img):
    # 4图横向拼接均分
    h, w, _ = img.shape
    cx = w // 2
    cy = h // 2
    img1 = img[:cy, :cx]
    img2 = img[:cy, cx:]
    img3 = img[cy:2 * cy, :cx]
    img4 = img[cy:2 * cy, cx:]
    img_list = [img1, img2, img3, img4]
    return img_list


def order_points(pts):  # 关键点按照（左上，右上，右下，左下）排列
    rect = np.zeros((4, 2), dtype="float32")
    s = pts.sum(axis=1)
    rect[0] = pts[np.argmin(s)]
    rect[2] = pts[np.argmax(s)]
    diff = np.diff(pts, axis=1)
    rect[1] = pts[np.argmin(diff)]
    rect[3] = pts[np.argmax(diff)]
    return rect


def four_point_transform(image, pts):  # 透视变换
    rect = order_points(pts)
    (tl, tr, br, bl) = rect
    widthA = np.sqrt(((br[0] - bl[0]) ** 2) + ((br[1] - bl[1]) ** 2))
    widthB = np.sqrt(((tr[0] - tl[0]) ** 2) + ((tr[1] - tl[1]) ** 2))
    maxWidth = max(int(widthA), int(widthB))
    heightA = np.sqrt(((tr[0] - br[0]) ** 2) + ((tr[1] - br[1]) ** 2))
    heightB = np.sqrt(((tl[0] - bl[0]) ** 2) + ((tl[1] - bl[1]) ** 2))
    maxHeight = max(int(heightA), int(heightB))
    dst = np.array([
        [0, 0],
        [maxWidth - 1, 0],
        [maxWidth - 1, maxHeight - 1],
        [0, maxHeight - 1]], dtype="float32")
    M = cv2.getPerspectiveTransform(rect, dst)
    warped = cv2.warpPerspective(image, M, (maxWidth, maxHeight))
    return warped


def get_plate_rec_landmark(img, xyxy, conf, landmarks, class_num, device, plate_rec_model):
    h, w, c = img.shape
    result_dict = {}
    tl = 1 or round(0.002 * (h + w) / 2) + 1  # line/font thickness

    x1 = int(xyxy[0])
    y1 = int(xyxy[1])
    x2 = int(xyxy[2])
    y2 = int(xyxy[3])
    height = y2 - y1
    landmarks_np = np.zeros((4, 2))
    rect = [x1, y1, x2, y2]
    for i in range(4):
        point_x = int(landmarks[2 * i])
        point_y = int(landmarks[2 * i + 1])
        landmarks_np[i] = np.array([point_x, point_y])

    class_label = int(class_num)  # 车牌的的类型0代表单牌，1代表双层车牌
    roi_img = four_point_transform(img, landmarks_np)  # 透视变换得到车牌小图
    if class_label:  # 判断是否是双层车牌，是双牌的话进行分割后然后拼接
        roi_img = get_split_merge(roi_img)
    plate_number, prob = get_plate_result(roi_img, device, plate_rec_model)  # 对车牌小图进行识别
    result_dict['rect'] = rect
    result_dict['landmarks'] = landmarks_np.tolist()
    result_dict['plate_no'] = plate_number
    result_dict['roi_height'] = roi_img.shape[0]
    result_dict['score'] = prob
    result_dict['label'] = class_label
    return result_dict


def detect_Recognition_plate(model, orgimg, device, plate_rec_model, img_size):
    conf_thres = 0.15
    iou_thres = 0.45
    dict_list = []
    im0 = copy.deepcopy(orgimg)
    imgsz = (img_size, img_size)

    img = letterbox(im0, new_shape=imgsz,stride=32)[0]
    img = img[:, :, ::-1].transpose(2, 0, 1).copy()  # BGR to RGB, to 3x640X640
    img = torch.from_numpy(img).to(device)
    img = img.float()  # uint8 to fp16/32
    img /= 255.0  # 0 - 255 to 0.0 - 1.0
    if img.ndimension() == 3:
        img = img.unsqueeze(0)
    pred = model(img)[0]
    pred = non_max_suppression_plate(pred, conf_thres=conf_thres, iou_thres=iou_thres, kpt_label=4)
    for i, det in enumerate(pred):
        if len(det):
            # Rescale boxes from img_size to im0 size
            scale_coords_plate(img.shape[2:], det[:, :4], im0.shape, kpt_label=False)
            scale_coords_plate(img.shape[2:], det[:, 6:], im0.shape, kpt_label=4, step=3)
            for j in range(det.size()[0]):
                xyxy = det[j, :4].view(-1).tolist()
                conf = det[j, 4].cpu().numpy()
                landmarks = det[j, 6:].view(-1).tolist()
                landmarks = [landmarks[0], landmarks[1], landmarks[3], landmarks[4], landmarks[6], landmarks[7],
                             landmarks[9], landmarks[10]]
                class_num = det[j, 5].cpu().numpy()
                result_dict = get_plate_rec_landmark(orgimg, xyxy, conf, landmarks, class_num, device, plate_rec_model)
                dict_list.append(result_dict)
    return dict_list


def draw_result(orgimg, dict_list):
    result_str = ""
    for result in dict_list:
        rect_area = result['rect']
        height_area = result['roi_height']
        landmarks = result['landmarks']
        if len(result["plate_no"]) > 2:
            confidence = np.min(result["score"][2:])
        elif len(result["plate_no"]):
            confidence = np.min(result["score"])
        else:
            confidence = 0

        result = result['plate_no'] + " " + "{:.2f}".format(confidence)
        result_str += result + " "
        cv2.rectangle(orgimg, (rect_area[0], rect_area[1]), (rect_area[2], rect_area[3]), (0, 0, 255), 2)  # 画框
        if len(result) >= 7:
            for i in range(4):  # 关键点
                cv2.circle(orgimg, (int(landmarks[i][0]), int(landmarks[i][1])), 5, colors[i], -1)

            # orgimg = cv2ImgAddText(orgimg, result, rect_area[0] - height_area, rect_area[1] - height_area - 10,
            #                        (0, 255, 0), height_area)
            orgimg = cv2ImgAddText(orgimg, result, 0, rect_area[1] - height_area - 10,
                                   (0, 255, 0), height_area)
    print(result_str)
    return orgimg


def draw_result2(orgimg, dict_list, car_ps):
    result_str = ""
    for idx, result in enumerate(dict_list):
        rect_area = [result['rect'][0] + car_ps[0], result['rect'][1] + car_ps[1], result['rect'][2] + car_ps[0],
                     result['rect'][3] + car_ps[1]]
        height_area = result['roi_height']
        landmarks = result['landmarks']
        if len(result["plate_no"]) > 2:
            confidence = np.min(result["score"][2:])
        elif len(result["plate_no"]):
            confidence = np.min(result["score"])
        else:
            confidence = 0
        try:
            first_conf = result["score"][0]
        except:
            first_conf = 0
        result = result['plate_no'] + " " + "{:.2f}".format(confidence) + f" {first_conf:.2f}"
        result_str += result + " "
        h = orgimg.shape[0]
        cv2.rectangle(orgimg, (rect_area[0], rect_area[1]), (rect_area[2], rect_area[3]), (0, 0, 255), 2)  # 画框
        if len(result) >= 7:
            for i in range(4):  # 关键点
                cv2.circle(orgimg, (int(landmarks[i][0]) + car_ps[0], int(landmarks[i][1]) + car_ps[1]), 5, colors[i],
                           -1)
            IMG = Image.fromarray(cv2.cvtColor(orgimg, cv2.COLOR_BGR2RGB))
            draw = ImageDraw.Draw(IMG)
            path = "../yolov7/fonts/platech.ttf"
            try:
                font = ImageFont.truetype(path, height_area, encoding="utf-8")
                if car_ps == [0, 0, 0, 0]:
                    car_ps = rect_area
                y = min(car_ps[1] + idx * height_area, h)
                draw.text((car_ps[0], y), result, (255, 0, 0), font)
            except:
                pass
            orgimg = cv2.cvtColor(np.asarray(IMG, dtype=np.uint8), cv2.COLOR_RGB2BGR)
    return orgimg


def load_plate_model(det_model_path, rec_model_path, device='cuda' if torch.cuda.is_available() else 'cpu'):
    # 加载训练好的检测模型
    det_model = attempt_load(det_model_path, map_location=device)
    rec_model = init_model(device, rec_model_path)
    return det_model, rec_model


# 红绿灯识别、行人车辆检测模型

def load_lp(weights="best.pt", imgsz=1472, trace=False, device=None, half=None):
    # Directories
    if device is None:
        device = torch.device("cuda:0" if torch.cuda.is_available() else "cpu")
    else:
        device = torch.device(device)
    # device = select_device(device)
    if half is None:
        half = device.type != 'cpu'  # half precision only supported on CUDA
    # Load model
    model = attempt_load([weights], map_location=device)  # load FP32 model
    stride = int(model.stride.max())  # model stride
    imgsz = check_img_size(imgsz, s=stride)  # check img_size

    if trace:
        model = TracedModel(model, device, imgsz)

    if half:
        model.half()  # to FP16

    # Run inference
    if device.type != 'cpu':
        model(torch.zeros(1, 3, imgsz, imgsz).to(device).type_as(next(model.parameters())))  # run once
    return model, imgsz, stride, device


def detect_lp(img0, model, img_size=1472, stride=32, device=None, conf_thres=0.25, iou_thres=0.45,
              classes=None, agnostic_nms=True, augment=False, test_lp=False):

    t0 = time.time()
    if device is None:
        device = torch.device("cuda:0" if torch.cuda.is_available() else "cpu")
    # half = device.type != 'cpu'  # half precision only supported on CUDA
    # Padded resize
    img = letterbox(img0, img_size, stride=stride)[0]

    # Convert
    img = img[:, :, ::-1].transpose(2, 0, 1)  # BGR to RGB, to 3x416x416
    img = np.ascontiguousarray(img)

    img = torch.from_numpy(img).to(device)
    img = img.float()  # img.half() if half else img.float()  # uint8 to fp16/32
    img /= 255.0  # 0 - 255 to 0.0 - 1.0
    if img.ndimension() == 3:
        img = img.unsqueeze(0)
    # Inference
    with torch.no_grad():
        pred = model(img, augment=augment)[0]
    # Apply NMS
    pred = non_max_suppression(pred, conf_thres, iou_thres, classes=classes, agnostic=agnostic_nms)
    #### conf_thres 置信度过滤参数
    # print("time inference + NMS:", f'Done. ({t2 - t1:.3f}s)')
    # Process detections
    l_res = []
    p_res = []
    for idx, det in enumerate(pred):  # detections per image
        if len(det):
            # Rescale boxes from img_size to im0 size
            det[:, :4] = scale_coords(img.shape[2:], det[:, :4], img0.shape).round()
            # 转换格式
            det = det.cpu()
            conf = det[:, 4].data.numpy()
            a = np.asarray(det, dtype=np.int32)
            conf = [str(i)[:4] if len(str(i)) > 4 else str(i) for i in conf]
            # names = ['green_s', 'green_r', 'green_l', 'red_s', 'red_r', 'red_l', 'police_1', 'police_2']
            names = ['green_s', 'green_r', 'green_l', 'red_s', 'red_r', 'red_l', 'yellow_s', 'yellow_r', 'yellow_l']

            for idx, i in enumerate(a):
                box = [[i[0], i[1]], [i[2], i[3]]]
                cls = names[i[-1]]
                # if i[-1] > 5:
                #     if eval(conf[idx]) < 0.95:
                #         continue
                #     p_res.append([str(box), f"{cls}-{conf[idx]}"])
                # else:
                # if cls[0] == "y":
                #     print(cls, " ", conf[idx])#[x1,y1,x2,y2]
                l_res.append([str(box), f"{cls}-{conf[idx]}"])
    # print(f'Done. {time.time() - t0:.3f}s')
    if test_lp:
        return l_res
    else:
        return l_res, p_res


def yolov7_detect(img0, model, img_size=1472, stride=32, device=None, conf_thres=0.2, iou_thres=0.45,
                  classes=None, agnostic_nms=True, augment=False, is_split=False, need_conf=False):
    if device is None:
        device = torch.device("cuda:0" if torch.cuda.is_available() else "cpu")
    else:
        device = torch.device(device)
    if classes is None:
        classes = ["car", "truck", "bus", "motorcycle"]
    t0 = time.time()
    half = device.type != 'cpu'  # half precision only supported on CUDA
    # Padded resize
    img = letterbox(img0, img_size, stride=stride)[0]
    # Convert
    img = img[:, :, ::-1].transpose(2, 0, 1)  # BGR to RGB, to 3x416x416
    img = np.ascontiguousarray(img)

    img = torch.from_numpy(img).to(device)
    img = img.half() if half else img.float()  # uint8 to fp16/32
    img /= 255.0  # 0 - 255 to 0.0 - 1.0
    if img.ndimension() == 3:
        img = img.unsqueeze(0)

    # Inference
    with torch.no_grad():
        pred = model(img, augment=augment)[0]
    # cls filter
    idx_list = []
    if classes:
        for i in classes:
            if i in coco_names:
                idx_list.append(coco_names.index(i))
    filter_cls = idx_list if idx_list else None
    # Apply NMS
    pred = non_max_suppression(pred, conf_thres, iou_thres, classes=filter_cls, agnostic=agnostic_nms)
    # print("time inference + NMS:", f'Done. ({t2 - t1:.3f}s)')
    # Process detections
    box_list = []
    label_list = []
    crop_list = []
    conf = []
    res_dict = {"vehicle": [], "person": []}
    for idx, det in enumerate(pred):  # detections per image
        if len(det):
            # Rescale boxes from img_size to im0 size
            det[:, :4] = scale_coords(img.shape[2:], det[:, :4], img0.shape).round()
            # 转换格式
            det = det.cpu()
            conf = det[:, 4].data.numpy()
            a = np.asarray(det.detach(), dtype=np.int32).tolist()
            conf = [float(str(i)[:4]) if len(str(i)) > 4 else float(i) for i in conf]
            for idx, i in enumerate(a):
                box = [i[0], i[1], i[2], i[3]]
                cls = coco_names[i[-1]]
                if is_split:
                    if cls in ["car", "truck", "bus", "motorcycle"]:
                        res_dict["vehicle"].append(box)
                    elif cls == "person":
                        res_dict["person"].append(box)
                        crop_list.append(img0[box[1]:box[3], box[0]:box[2]])
                    if need_conf:
                        box_list.append(box)

                else:
                    crop_list.append(img0[box[1]:box[3], box[0]:box[2]])
                    box_list.append(box)
                    label_list.append(cls)
    if is_split:
        if need_conf:
            return crop_list, res_dict, box_list, conf
        else:
            return crop_list, res_dict
    elif need_conf:
        return crop_list, box_list, label_list, conf
    else:
        return crop_list, box_list, label_list
    # print(f'Done. {time.time() - t0:.3f}s')


def phone_detect(img0, model, x_bias, y_bias, img_size=640, stride=32, device=None, conf_thres=0.25, iou_thres=0.45,
                 classes=None, agnostic_nms=False, augment=False):

    t0 = time.time()
    if device is None:
        device = torch.device("cuda:0" if torch.cuda.is_available() else "cpu")
    half = device.type != 'cpu'  # half precision only supported on CUDA
    # Padded resize
    img = letterbox(img0, img_size, stride=stride)[0]

    # Convert
    img = img[:, :, ::-1].transpose(2, 0, 1)  # BGR to RGB, to 3x416x416
    img = np.ascontiguousarray(img)

    img = torch.from_numpy(img).to(device).float()
    # img = img.half() if half else img.float()  # uint8 to fp16/32
    img /= 255.0  # 0 - 255 to 0.0 - 1.0
    if img.ndimension() == 3:
        img = img.unsqueeze(0)
    # Inference
    with torch.no_grad():
        pred = model(img, augment=augment)[0]
    # Apply NMS
    pred = non_max_suppression(pred, conf_thres, iou_thres, classes=classes, agnostic=agnostic_nms)
    # print("time inference + NMS:", f'Done. ({t2 - t1:.3f}s)')
    # Process detections
    box_dict = {}
    box_list = []
    label_list = []
    conf = []
    for idx, det in enumerate(pred):  # detections per image
        if len(det):
            # Rescale boxes from img_size to im0 size
            det[:, :4] = scale_coords(img.shape[2:], det[:, :4], img0.shape).round()
            # 转换格式
            det = det.cpu()
            conf = det[:, 4].data.numpy()
            a = np.asarray(det, dtype=np.int32)
            conf = [float(str(i)[:4]) if len(str(i)) > 4 else float(str(i)) for i in conf]
            names = ['hand', 'phone', 'phone_s']

            for idx, i in enumerate(a):
                box = [i[0] + x_bias, i[1] + y_bias, i[2] + x_bias, i[3] + y_bias]
                cls = names[i[-1]]
                box_list.append(box)
                label_list.append(cls)
                if cls not in box_dict:
                    box_dict[cls] = [box]
                else:
                    box_dict[cls].append(box)
    # print(f'Done. {time.time() - t0:.3f}s')
    return box_dict, box_list, label_list, conf


if __name__ == '__main__':
    from glob import glob
    from tqdm import tqdm
    import time

    w_name_list = ["best1103.pt"]
    for w_name in w_name_list:
        print("w_name:", w_name)
        model, imgsz, stride, device = load_lp(f"../models/{w_name}")
        save_path = f"../test_results/{w_name[:-3]}"
        if os.path.exists(save_path):
            shutil.rmtree(save_path)
        os.makedirs(save_path, exist_ok=True)
        test_path = glob("../test_dir/1625JZ1" + "/*.jpg") + glob(
            "../test_dir/1625JZ2" + "/*.jpg")  # '../test_dir/test.jpg'
        for pic_idx, path in enumerate(tqdm(test_path)):
            print(path)
            name = path.split("/")[-1].split(".")[0]
            Img = cv2.imread(path)
            file_list = img_split(Img)
            for idx, img in enumerate(file_list):
                if idx == 3:
                    continue
                # _, boxs_list, labels_list = yolov7_detect(img, model)
                start_t = time.time()
                light, police = detect_lp(img, model, imgsz, stride, device, conf_thres=0.1)
                # print("use_time:", round(time.time() - start_t, 2))
                # print("tttttttttttttttttttttttttttttttttttttt",boxs_list, labels_list, stuffs_list)
                # 车辆行人模型
                # for n, box in enumerate(boxs_list):
                #     x1, y1, x2, y2 = box[:4]
                #     cv2.rectangle(img, (int(x1), int(y1)), (int(x2), int(y2)), (0, 255, 0), 2)
                #     cv2.putText(img, labels_list[n], (int(x1), int(y1 + 20)), cv2.FONT_HERSHEY_SIMPLEX, 1,
                #                 (0, 0, 255),2)

                # 红绿灯模型
                for n, res in enumerate(light):
                    box = eval(res[0])
                    x1, y1, x2, y2 = [j for i in box for j in i]
                    cls = res[1].split("-")[0].split("_")[1]
                    color = res[1].split("-")[0].split("_")[0]

                    if color == "red":
                        vis_color = (0, 0, 255)
                    elif color == "yellow":
                        vis_color = (0, 255, 255)
                    else:
                        vis_color = (0, 255, 0)
                    cv2.rectangle(img, (int(x1), int(y1)), (int(x2), int(y2)), vis_color, 2)
                    cv2.putText(img, cls, (int(x2), int(y1 + 20)), cv2.FONT_HERSHEY_SIMPLEX, 2,
                                vis_color, 2)
                cv2.imwrite(f'{save_path}/{name}_{idx + 1}.jpg', img)
            print("\n")
