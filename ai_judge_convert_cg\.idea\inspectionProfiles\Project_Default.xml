<component name="InspectionProjectProfileManager">
  <profile version="1.0">
    <option name="myName" value="Project Default" />
    <inspection_tool class="PyPackageRequirementsInspection" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="ignoredPackages">
        <value>
          <list size="217">
            <item index="0" class="java.lang.String" itemvalue="google-pasta" />
            <item index="1" class="java.lang.String" itemvalue="numba" />
            <item index="2" class="java.lang.String" itemvalue="tensorflow-estimator" />
            <item index="3" class="java.lang.String" itemvalue="pascal-voc-writer" />
            <item index="4" class="java.lang.String" itemvalue="scikit-learn" />
            <item index="5" class="java.lang.String" itemvalue="PyQt5-sip" />
            <item index="6" class="java.lang.String" itemvalue="gitdb" />
            <item index="7" class="java.lang.String" itemvalue="torchvision" />
            <item index="8" class="java.lang.String" itemvalue="lpips" />
            <item index="9" class="java.lang.String" itemvalue="pycuda" />
            <item index="10" class="java.lang.String" itemvalue="aliyun-python-sdk-core" />
            <item index="11" class="java.lang.String" itemvalue="starlette" />
            <item index="12" class="java.lang.String" itemvalue="bleach" />
            <item index="13" class="java.lang.String" itemvalue="soupsieve" />
            <item index="14" class="java.lang.String" itemvalue="gtrending" />
            <item index="15" class="java.lang.String" itemvalue="Werkzeug" />
            <item index="16" class="java.lang.String" itemvalue="tensorboard-data-server" />
            <item index="17" class="java.lang.String" itemvalue="pytest-benchmark" />
            <item index="18" class="java.lang.String" itemvalue="click" />
            <item index="19" class="java.lang.String" itemvalue="prettytable" />
            <item index="20" class="java.lang.String" itemvalue="viapi-utils" />
            <item index="21" class="java.lang.String" itemvalue="paramiko" />
            <item index="22" class="java.lang.String" itemvalue="regex" />
            <item index="23" class="java.lang.String" itemvalue="tensorboard" />
            <item index="24" class="java.lang.String" itemvalue="av" />
            <item index="25" class="java.lang.String" itemvalue="matplotlib" />
            <item index="26" class="java.lang.String" itemvalue="Keras" />
            <item index="27" class="java.lang.String" itemvalue="Mako" />
            <item index="28" class="java.lang.String" itemvalue="idna" />
            <item index="29" class="java.lang.String" itemvalue="rsa" />
            <item index="30" class="java.lang.String" itemvalue="google-colab" />
            <item index="31" class="java.lang.String" itemvalue="smmap" />
            <item index="32" class="java.lang.String" itemvalue="cffi" />
            <item index="33" class="java.lang.String" itemvalue="numpy" />
            <item index="34" class="java.lang.String" itemvalue="gdown" />
            <item index="35" class="java.lang.String" itemvalue="nightly" />
            <item index="36" class="java.lang.String" itemvalue="Deprecated" />
            <item index="37" class="java.lang.String" itemvalue="prompt-toolkit" />
            <item index="38" class="java.lang.String" itemvalue="nbnb" />
            <item index="39" class="java.lang.String" itemvalue="websockets" />
            <item index="40" class="java.lang.String" itemvalue="dataclasses" />
            <item index="41" class="java.lang.String" itemvalue="astor" />
            <item index="42" class="java.lang.String" itemvalue="jsonpointer" />
            <item index="43" class="java.lang.String" itemvalue="netron" />
            <item index="44" class="java.lang.String" itemvalue="docker-pycreds" />
            <item index="45" class="java.lang.String" itemvalue="importlib-resources" />
            <item index="46" class="java.lang.String" itemvalue="pathtools" />
            <item index="47" class="java.lang.String" itemvalue="debugpy" />
            <item index="48" class="java.lang.String" itemvalue="tensorboardX" />
            <item index="49" class="java.lang.String" itemvalue="progress" />
            <item index="50" class="java.lang.String" itemvalue="isort" />
            <item index="51" class="java.lang.String" itemvalue="pytz" />
            <item index="52" class="java.lang.String" itemvalue="efficientnet-pytorch" />
            <item index="53" class="java.lang.String" itemvalue="jsons" />
            <item index="54" class="java.lang.String" itemvalue="traitlets" />
            <item index="55" class="java.lang.String" itemvalue="absl-py" />
            <item index="56" class="java.lang.String" itemvalue="protobuf" />
            <item index="57" class="java.lang.String" itemvalue="filterpy" />
            <item index="58" class="java.lang.String" itemvalue="motmetrics" />
            <item index="59" class="java.lang.String" itemvalue="pygame" />
            <item index="60" class="java.lang.String" itemvalue="joblib" />
            <item index="61" class="java.lang.String" itemvalue="opt-einsum" />
            <item index="62" class="java.lang.String" itemvalue="h11" />
            <item index="63" class="java.lang.String" itemvalue="promise" />
            <item index="64" class="java.lang.String" itemvalue="gast" />
            <item index="65" class="java.lang.String" itemvalue="docopt" />
            <item index="66" class="java.lang.String" itemvalue="filelock" />
            <item index="67" class="java.lang.String" itemvalue="pyzmq" />
            <item index="68" class="java.lang.String" itemvalue="oauthlib" />
            <item index="69" class="java.lang.String" itemvalue="munch" />
            <item index="70" class="java.lang.String" itemvalue="coremltools" />
            <item index="71" class="java.lang.String" itemvalue="tensorflow-gpu" />
            <item index="72" class="java.lang.String" itemvalue="beautifulsoup4" />
            <item index="73" class="java.lang.String" itemvalue="image" />
            <item index="74" class="java.lang.String" itemvalue="yolox" />
            <item index="75" class="java.lang.String" itemvalue="tifffile" />
            <item index="76" class="java.lang.String" itemvalue="cryptography" />
            <item index="77" class="java.lang.String" itemvalue="omegaconf" />
            <item index="78" class="java.lang.String" itemvalue="ninja" />
            <item index="79" class="java.lang.String" itemvalue="pytools" />
            <item index="80" class="java.lang.String" itemvalue="deep" />
            <item index="81" class="java.lang.String" itemvalue="jupyter-core" />
            <item index="82" class="java.lang.String" itemvalue="pydot" />
            <item index="83" class="java.lang.String" itemvalue="matplotlib-inline" />
            <item index="84" class="java.lang.String" itemvalue="aliyun-python-sdk-viapiutils" />
            <item index="85" class="java.lang.String" itemvalue="sklearn" />
            <item index="86" class="java.lang.String" itemvalue="update" />
            <item index="87" class="java.lang.String" itemvalue="llvmlite" />
            <item index="88" class="java.lang.String" itemvalue="Jinja2" />
            <item index="89" class="java.lang.String" itemvalue="alfred-py" />
            <item index="90" class="java.lang.String" itemvalue="pretrainedmodels" />
            <item index="91" class="java.lang.String" itemvalue="Keras-Preprocessing" />
            <item index="92" class="java.lang.String" itemvalue="MultiScaleDeformableAttention" />
            <item index="93" class="java.lang.String" itemvalue="fvcore" />
            <item index="94" class="java.lang.String" itemvalue="wandb" />
            <item index="95" class="java.lang.String" itemvalue="onnxoptimizer" />
            <item index="96" class="java.lang.String" itemvalue="typish" />
            <item index="97" class="java.lang.String" itemvalue="faiss-gpu" />
            <item index="98" class="java.lang.String" itemvalue="timm" />
            <item index="99" class="java.lang.String" itemvalue="pyflakes" />
            <item index="100" class="java.lang.String" itemvalue="parso" />
            <item index="101" class="java.lang.String" itemvalue="blessed" />
            <item index="102" class="java.lang.String" itemvalue="ipython" />
            <item index="103" class="java.lang.String" itemvalue="packaging" />
            <item index="104" class="java.lang.String" itemvalue="chardet" />
            <item index="105" class="java.lang.String" itemvalue="jmespath" />
            <item index="106" class="java.lang.String" itemvalue="asyncio" />
            <item index="107" class="java.lang.String" itemvalue="json-tricks" />
            <item index="108" class="java.lang.String" itemvalue="xdg" />
            <item index="109" class="java.lang.String" itemvalue="tabulate" />
            <item index="110" class="java.lang.String" itemvalue="yarg" />
            <item index="111" class="java.lang.String" itemvalue="defusedxml" />
            <item index="112" class="java.lang.String" itemvalue="apex" />
            <item index="113" class="java.lang.String" itemvalue="Pygments" />
            <item index="114" class="java.lang.String" itemvalue="sentry-sdk" />
            <item index="115" class="java.lang.String" itemvalue="PyQt5" />
            <item index="116" class="java.lang.String" itemvalue="astunparse" />
            <item index="117" class="java.lang.String" itemvalue="xtcocotools" />
            <item index="118" class="java.lang.String" itemvalue="shortuuid" />
            <item index="119" class="java.lang.String" itemvalue="uvicorn" />
            <item index="120" class="java.lang.String" itemvalue="pyquaternion" />
            <item index="121" class="java.lang.String" itemvalue="qtconsole" />
            <item index="122" class="java.lang.String" itemvalue="libclang" />
            <item index="123" class="java.lang.String" itemvalue="terminado" />
            <item index="124" class="java.lang.String" itemvalue="GitPython" />
            <item index="125" class="java.lang.String" itemvalue="portalocker" />
            <item index="126" class="java.lang.String" itemvalue="pydantic" />
            <item index="127" class="java.lang.String" itemvalue="jupyter-client" />
            <item index="128" class="java.lang.String" itemvalue="loguru" />
            <item index="129" class="java.lang.String" itemvalue="ipykernel" />
            <item index="130" class="java.lang.String" itemvalue="attrs" />
            <item index="131" class="java.lang.String" itemvalue="psutil" />
            <item index="132" class="java.lang.String" itemvalue="flatbuffers" />
            <item index="133" class="java.lang.String" itemvalue="portpicker" />
            <item index="134" class="java.lang.String" itemvalue="install" />
            <item index="135" class="java.lang.String" itemvalue="oss2" />
            <item index="136" class="java.lang.String" itemvalue="style" />
            <item index="137" class="java.lang.String" itemvalue="decorator" />
            <item index="138" class="java.lang.String" itemvalue="tb-nightly" />
            <item index="139" class="java.lang.String" itemvalue="pandocfilters" />
            <item index="140" class="java.lang.String" itemvalue="py-cpuinfo" />
            <item index="141" class="java.lang.String" itemvalue="pycodestyle" />
            <item index="142" class="java.lang.String" itemvalue="requests" />
            <item index="143" class="java.lang.String" itemvalue="sniffio" />
            <item index="144" class="java.lang.String" itemvalue="websocket-client" />
            <item index="145" class="java.lang.String" itemvalue="flake8-import-order" />
            <item index="146" class="java.lang.String" itemvalue="tensorflow" />
            <item index="147" class="java.lang.String" itemvalue="tensorboard-plugin-wit" />
            <item index="148" class="java.lang.String" itemvalue="zipp" />
            <item index="149" class="java.lang.String" itemvalue="nest-asyncio" />
            <item index="150" class="java.lang.String" itemvalue="ipywidgets" />
            <item index="151" class="java.lang.String" itemvalue="scipy" />
            <item index="152" class="java.lang.String" itemvalue="google-auth-oauthlib" />
            <item index="153" class="java.lang.String" itemvalue="opencv-python" />
            <item index="154" class="java.lang.String" itemvalue="Shapely" />
            <item index="155" class="java.lang.String" itemvalue="gpustat" />
            <item index="156" class="java.lang.String" itemvalue="jupyterthemes" />
            <item index="157" class="java.lang.String" itemvalue="torch" />
            <item index="158" class="java.lang.String" itemvalue="tensorflow-io-gcs-filesystem" />
            <item index="159" class="java.lang.String" itemvalue="addict" />
            <item index="160" class="java.lang.String" itemvalue="pandas" />
            <item index="161" class="java.lang.String" itemvalue="hydra-core" />
            <item index="162" class="java.lang.String" itemvalue="Django" />
            <item index="163" class="java.lang.String" itemvalue="jupyter-console" />
            <item index="164" class="java.lang.String" itemvalue="lesscpy" />
            <item index="165" class="java.lang.String" itemvalue="typing_extensions" />
            <item index="166" class="java.lang.String" itemvalue="cachetools" />
            <item index="167" class="java.lang.String" itemvalue="pycryptodome" />
            <item index="168" class="java.lang.String" itemvalue="setproctitle" />
            <item index="169" class="java.lang.String" itemvalue="Pillow" />
            <item index="170" class="java.lang.String" itemvalue="torchreid" />
            <item index="171" class="java.lang.String" itemvalue="typed-ast" />
            <item index="172" class="java.lang.String" itemvalue="python-dateutil" />
            <item index="173" class="java.lang.String" itemvalue="nbclient" />
            <item index="174" class="java.lang.String" itemvalue="MarkupSafe" />
            <item index="175" class="java.lang.String" itemvalue="submitit" />
            <item index="176" class="java.lang.String" itemvalue="certifi" />
            <item index="177" class="java.lang.String" itemvalue="anyio" />
            <item index="178" class="java.lang.String" itemvalue="pit" />
            <item index="179" class="java.lang.String" itemvalue="ffmpeg" />
            <item index="180" class="java.lang.String" itemvalue="Markdown" />
            <item index="181" class="java.lang.String" itemvalue="notebook" />
            <item index="182" class="java.lang.String" itemvalue="opencv-contrib-python" />
            <item index="183" class="java.lang.String" itemvalue="coloredlogs" />
            <item index="184" class="java.lang.String" itemvalue="fonttools" />
            <item index="185" class="java.lang.String" itemvalue="configparser" />
            <item index="186" class="java.lang.String" itemvalue="charset-normalizer" />
            <item index="187" class="java.lang.String" itemvalue="mock" />
            <item index="188" class="java.lang.String" itemvalue="gym" />
            <item index="189" class="java.lang.String" itemvalue="torchsummary" />
            <item index="190" class="java.lang.String" itemvalue="scikit-image" />
            <item index="191" class="java.lang.String" itemvalue="ptyprocess" />
            <item index="192" class="java.lang.String" itemvalue="nvidia-ml-py" />
            <item index="193" class="java.lang.String" itemvalue="crcmod" />
            <item index="194" class="java.lang.String" itemvalue="importlib-metadata" />
            <item index="195" class="java.lang.String" itemvalue="flake8" />
            <item index="196" class="java.lang.String" itemvalue="sqlparse" />
            <item index="197" class="java.lang.String" itemvalue="onnxruntime" />
            <item index="198" class="java.lang.String" itemvalue="urllib3" />
            <item index="199" class="java.lang.String" itemvalue="attr" />
            <item index="200" class="java.lang.String" itemvalue="Cython" />
            <item index="201" class="java.lang.String" itemvalue="onnx" />
            <item index="202" class="java.lang.String" itemvalue="Flask" />
            <item index="203" class="java.lang.String" itemvalue="funcy" />
            <item index="204" class="java.lang.String" itemvalue="pytest" />
            <item index="205" class="java.lang.String" itemvalue="nbformat" />
            <item index="206" class="java.lang.String" itemvalue="pipreqs" />
            <item index="207" class="java.lang.String" itemvalue="onnx-simplifier" />
            <item index="208" class="java.lang.String" itemvalue="humanfriendly" />
            <item index="209" class="java.lang.String" itemvalue="Keras-Applications" />
            <item index="210" class="java.lang.String" itemvalue="prometheus-client" />
            <item index="211" class="java.lang.String" itemvalue="tqdm" />
            <item index="212" class="java.lang.String" itemvalue="fastapi" />
            <item index="213" class="java.lang.String" itemvalue="aliyun-python-sdk-kms" />
            <item index="214" class="java.lang.String" itemvalue="grpcio" />
            <item index="215" class="java.lang.String" itemvalue="google-auth" />
            <item index="216" class="java.lang.String" itemvalue="openpyxl" />
          </list>
        </value>
      </option>
    </inspection_tool>
  </profile>
</component>