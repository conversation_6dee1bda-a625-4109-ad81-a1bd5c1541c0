#!/usr/bin/python
# -*- coding: UTF-8 -*-
# import os
# #
# os.environ["CUDA_VISIBLE_DEVICES"] = "1"
from init_models import *

if Is_Allowed_Type["1625"]:
    from ai_judge_code_gy.vio_judge_16251 import judge_vio as judge_16251
if Is_Allowed_Type["1208"]:
    from ai_judge_code_gy.vio_judge_12080 import judge_vio as judge_12080
if Is_Allowed_Type["1345"]:
    from ai_judge_code_gy.vio_judge_13450 import judge_vio as judge_13450
if Is_Allowed_Type["1301"]:
    from ai_judge_code_gy.vio_judge_13010 import judge_vio as judge_13010
if Is_Allowed_Type["1352"]:
    from ai_judge_code_gy.vio_judge_13522 import judge_vio as judge_13522
if Is_Allowed_Type["1357"]:
    from ai_judge_code_gy.vio_judge_13570v2 import judge_vio as judge_13570
if Is_Allowed_Type["1223"] or Is_Allowed_Type["1101"]:
    from ai_judge_code_gy.vio_judge_12230_11010 import judge_vio as judge_12230_11010
if Is_Allowed_Type["7102"]:
    from ai_judge_code_gy.vio_judge_71020 import judge_vio as judge_71020
if Is_Allowed_Type["8013"]:
    from ai_judge_code_gy.vio_judge_80130 import judge_vio as judge_80130
if Is_Allowed_Type["1344"]:
    from ai_judge_code_gy.vio_judge_13440_v2 import judge_vio as judge_13440
import urllib.request
import socket
import base64
import traceback
import json
from main_config.log_config import get_logging
import logging
import logging.config
import time
import datetime as dt

logging.config.dictConfig(get_logging("res_producer1"))
logger = logging.getLogger("console_plain_file_logger")

socket.setdefaulttimeout(10)

# 初始化所有模型
weights_list = init_models(traffic=True, lp=True, yolov7=True, trace=False, vio_model=True, cnn=True)


def base2img(img_path, data):
    file = open(img_path, 'wb')
    file.write(data)
    file.close()


# 拼接方式
def img_split(img, n):
    # 4图横向拼接去除下黑框
    img_list = []
    if n == 1:
        h, w, _ = img.shape
        black_h = int(BLACK_HEIGHT * h)
        cx = w // 2
        cy = (h - black_h) // 2
        img1 = img[:cy, :cx]
        img2 = img[:cy, cx:]
        img3 = img[cy:2 * cy, :cx]
        img4 = img[cy:2 * cy, cx:]
        img_list = [img1, img2, img3, img4]
    # 4图横向拼接均分
    elif n == 2:
        h, w, _ = img.shape
        cx = w // 2
        cy = h // 2
        img1 = img[:cy, :cx]
        img2 = img[:cy, cx:]
        img3 = img[cy:2 * cy, :cx]
        img4 = img[cy:2 * cy, cx:]
        img_list = [img1, img2, img3, img4]
    # 2图横向拼接
    elif n == 3:
        h, w, _ = img.shape
        cx = w // 2
        img1 = img[:, :cx]
        img2 = img[:, cx:]
        img_list = [img1, img2]
    # 3图横向拼接
    elif n == 4:
        h, w, _ = img.shape

        split_w = w // 3
        img1 = img[:, :split_w]
        img2 = img[:, split_w:2 * split_w]
        img3 = img[:, 2 * split_w:]
        img_list = [img1, img2, img3]
    # 4图横拼去除上黑框
    elif n == 5:
        h, w, _ = img.shape
        black_h = int(BLACK_HEIGHT * h)
        cx = w // 2
        cy = (h - black_h) // 2
        img1 = img[black_h:cy + black_h, :cx]
        img2 = img[black_h:cy + black_h, cx:]
        img3 = img[cy + black_h:, :cx]
        img4 = img[cy + black_h:, cx:]
        img_list = [img1, img2, img3, img4]
    # 6图横拼去除上黑框（漳州）
    elif n == 6:
        h, w, _ = img.shape
        black_h = int(BLACK_HEIGHT * h)
        cx = w // 3
        cy = (h - black_h) // 2
        img1 = img[black_h:cy + black_h, :cx]
        img2 = img[black_h:cy + black_h, cx:2 * cx]
        img3 = img[cy + black_h:, :cx]
        img4 = img[cy + black_h:, cx:2 * cx]
        img_list = [img1, img2, img3, img4]

    return img_list


# 输入图片处理
def get_images(images, plate_num, vio_code):
    raw_img_dir = '../temp/raw_imgs_%s' % str(vio_code)
    if not os.path.exists(raw_img_dir):
        os.makedirs(raw_img_dir)
    if len(os.listdir(raw_img_dir)) > 1000:
        os.system('find {} -mmin +5 -name "*.*" -delete'.format(raw_img_dir))
    file_list = []
    if len(images) != 0:
        try:
            if "url" in images[0] and images[0]['url']:
                img_num = len(images)
                if img_num == 1:
                    try:
                        try:
                            img_path = raw_img_dir + '/%s.jpg' % plate_num
                            img_url = images[0]['url']
                            urllib.request.urlretrieve(img_url, filename=img_path)
                            # from ai_judge_code_gy.judge_tools import CUTIMG, SIMPLE_CUT
                            img = cv2.imread(img_path)
                            # file_list, split_x, split_y = CUTIMG().cut(img)
                            # if len(file_list) != 4:
                            #     file_list, split_x, split_y = SIMPLE_CUT().break_img_4(img)
                            # file_list = file_list[:3]
                            # if vio_code in ["1625", "1208"]:
                            #     file_list = img_split(img, n=4)
                            # elif vio_code in ["1344", "1357"]:
                            #     file_list = img_split(img, n=2)
                            # elif vio_code in ["1352"]:
                            #     file_list = img_split(img, n=3)
                            # else:
                            #     print("图片拼接方式未配置！")
                            file_list = img_split(img, n=SPLIT_DICT[str(vio_code)])

                        except:
                            traceback.print_exc()
                            pass
                        urllib.request.urlcleanup()
                    except socket.timeout:
                        traceback.print_exc()
                        pass
                elif img_num > 1:
                    try:
                        try:
                            img1_path = raw_img_dir + '/%s_1.jpg' % plate_num
                            img1_url = images[0]['url']
                            urllib.request.urlretrieve(img1_url, filename=img1_path)
                            file_list.append(cv2.imread(img1_path))
                        except:
                            traceback.print_exc()
                            pass
                        try:
                            img2_path = raw_img_dir + '/%s_2.jpg' % plate_num
                            img2_url = images[1]['url']
                            urllib.request.urlretrieve(img2_url, filename=img2_path)
                            file_list.append(cv2.imread(img2_path))
                        except:
                            traceback.print_exc()
                            pass
                        try:
                            img3_path = raw_img_dir + '/%s_3.jpg' % plate_num
                            img3_url = images[2]['url']
                            urllib.request.urlretrieve(img3_url, filename=img3_path)
                            file_list.append(cv2.imread(img3_path))
                        except:
                            traceback.print_exc()
                            pass
                        try:
                            img4_path = raw_img_dir + '/%s_4.jpg' % plate_num
                            img4_url = images[3]['url']
                            urllib.request.urlretrieve(img4_url, filename=img4_path)
                            file_list.append(cv2.imread(img4_path))
                        except:
                            traceback.print_exc()
                            pass
                        urllib.request.urlcleanup()
                    except socket.timeout:
                        traceback.print_exc()
                        pass
                else:
                    print("Input url-image error!")
            elif "file" in images[0] and images[0]['file']:
                img_num = len(images)
                if img_num == 1:
                    try:
                        img_path = raw_img_dir + '/%s.jpg' % plate_num
                        imgdata = base64.b64decode(images[0]['file'].encode())
                        base2img(img_path, imgdata)

                        # img = cv2.imread(img_path)
                        img_array = np.frombuffer(imgdata, np.uint8)  # 转换np序列
                        img = cv2.imdecode(img_array, cv2.COLOR_RGB2BGR)  # 转换Opencv格式

                        # if vio_code in ["1625", "1208"]:
                        #     file_list = img_split(img, n=4)
                        # elif vio_code in ["1344", "1357"]:
                        #     file_list = img_split(img, n=2)
                        # elif vio_code in ["1352"]:
                        #     file_list = img_split(img, n=3)
                        # else:
                        # print("图片拼接方式未配置！")
                        file_list = img_split(img, n=SPLIT_DICT[str(vio_code)])

                    except:
                        traceback.print_exc()
                        pass
                elif img_num > 1:
                    try:
                        img1_path = raw_img_dir + '/%s_1.jpg' % plate_num
                        imgdata = base64.b64decode(images[0]['file'])
                        base2img(img1_path, imgdata)
                        file_list.append(cv2.imread(img1_path))
                    except:
                        traceback.print_exc()
                        pass
                    try:
                        img2_path = raw_img_dir + '/%s_2.jpg' % plate_num
                        imgdata = base64.b64decode(images[1]['file'])
                        base2img(img2_path, imgdata)
                        file_list.append(cv2.imread(img2_path))
                    except:
                        traceback.print_exc()
                        pass
                    try:
                        img3_path = raw_img_dir + '/%s_3.jpg' % plate_num
                        imgdata = base64.b64decode(images[2]['file'])
                        base2img(img3_path, imgdata)
                        file_list.append(cv2.imread(img3_path))
                    except:
                        traceback.print_exc()
                        pass
                    try:
                        img4_path = raw_img_dir + '/%s_4.jpg' % plate_num
                        imgdata = base64.b64decode(images[3]['file'])
                        base2img(img4_path, imgdata)
                        file_list.append(cv2.imread(img4_path))
                    except:
                        traceback.print_exc()
                        pass
                else:
                    print("Input file-image error!")
            else:
                print("Input image error!")
        except:
            traceback.print_exc()
            pass

    return file_list


def calculate(request_data, save=False, save_path=""):
    global dev_desc

    # h = time.localtime()[3]
    # d = time.localtime()[2]
    # UTC时区转换为东八区
    # pro_time = time.strftime(f'%Y-%m-{d + 1 if h + 8 >= 24 else d} {(h + 8) % 24}:%M:%S', time.localtime())
    s_time = dt.datetime.now()
    session_id = request_data["session_id"]
    mode = request_data["mode"]
    violation_type = request_data["violation_type"]
    plate_num = request_data["plate_number"]
    images = request_data["images"]

    shoot_scene = request_data["shoot_scene"] if "shoot_scene" in request_data.keys() else ""
    extra = request_data["extra"] if "extra" in request_data.keys() else ""
    dev_desc = request_data["dev_desc"] if "dev_desc" in request_data.keys() else ""
    special_car_type = request_data["special_car_type"] if "special_car_type" in request_data.keys() else []
    place_type = request_data["place_type"] if "place_type" in request_data.keys() else ""

    logger.info(
        f"session_id:{session_id} mode:{mode} violation_type:{violation_type} plate_num:{plate_num} place_type:{place_type}")
    # print("images:", images, " shoot_scene:", shoot_scene, " extra:", extra, " dev_desc:", dev_desc,
    #       " special_car_type:", special_car_type)

    vio_judge = 'no_judge'

    data = {
        'session_id': session_id,
        'plate_number': plate_num,
        'is_valid': "false",
        'reason': 'judge_fail',
        'mode': mode,
        'extra': extra
    }

    res_infos = {
        'code': 0,
        'msg': None,
        'data': data
    }
    file_name = f"{session_id}_{plate_num}_{violation_type}"
    try:
        file_list = get_images(images, file_name, violation_type)
        scale = 0.7
        # print('=========', file_list)
        # 1

        if Is_Allowed_Type[str(violation_type)] and int(violation_type) == 1208:
            vio_judge, judge_infos = \
                judge_12080(plate_num, file_list, weights_list, resize_scale=scale, merge=False, save=save,
                            save_path=save_path)
        # 2
        if Is_Allowed_Type[str(violation_type)] and int(violation_type) == 1625:
            vio_judge, judge_infos = \
                judge_16251(plate_num, file_list, weights_list, resize_scale=scale, merge=False, save=save,
                            save_path=save_path)
        # 3
        if Is_Allowed_Type[str(violation_type)] and int(violation_type) == 1345:
            vio_judge, judge_infos = \
                judge_13450(plate_num, file_list, weights_list, resize_scale=scale, save=save,
                            save_path=save_path)
        # 4
        if Is_Allowed_Type[str(violation_type)] and int(violation_type) == 1301:
            is_highway = False
            if place_type in ["高速", "匝道"]:
                is_highway = True
            vio_judge, judge_infos = \
                judge_13010(plate_num, file_list, weights_list, resize_scale=scale, merge=True, save=save,
                            save_path=save_path, highway=is_highway)
        # 5
        if Is_Allowed_Type[str(violation_type)] and int(violation_type) == 1352:
            vio_judge, judge_infos = \
                judge_13522(plate_num, file_list, weights_list, resize_scale=scale, save=save,
                            save_path=save_path)
        # 6
        if Is_Allowed_Type[str(violation_type)] and int(violation_type) == 1357:
            vio_judge, judge_infos = \
                judge_13570(plate_num, file_list, weights_list, resize_scale=scale, save=save,
                            save_path=save_path)
        # 7
        if Is_Allowed_Type[str(violation_type)] and int(violation_type) == 7102:
            vio_judge, judge_infos = \
                judge_71020(plate_num, file_list, weights_list, resize_scale=scale, save=save,
                            save_path=save_path)
        # 8
        if Is_Allowed_Type[str(violation_type)] and int(violation_type) == 8013:
            vio_judge, judge_infos = \
                judge_80130(plate_num, file_list, weights_list, resize_scale=scale, save=save,
                            save_path=save_path)
        # 9,10
        if Is_Allowed_Type[str(violation_type)] and int(violation_type) in [1223, 1101]:
            vio_judge, judge_infos = \
                judge_12230_11010([violation_type, violation_type], plate_num, file_list, weights_list,
                                  resize_scale=scale, save=save,
                                  save_path=save_path)
        # 11
        if Is_Allowed_Type[str(violation_type)] and int(violation_type) == 1344:
            vio_judge, judge_infos, score_list = \
                judge_13440(plate_num, file_list, weights_list, save=save, save_path=save_path)
            data['extra'] += str(score_list)
        if vio_judge.startswith('vio'):
            data['is_valid'] = "true"

        data['reason'] = vio_judge
        data['session_id'] = session_id
        data['mode'] = mode

        res_infos['code'] = int(violation_type)
        res_infos['msg'] = 'success'
        res_infos['data'] = data
    except Exception as e:
        # print('=========Error!====={}=={}=========='.format(violation_type, plate_num), e,
        #       e.__traceback__.tb_frame.f_globals["__file__"], e.__traceback__.tb_lineno)
        tmp_name = e.__traceback__.tb_frame.f_globals["__file__"]
        logger.error(time.strftime("%Y-%m-%d %H:%M:%S", time.localtime()) +
                     f"id:{session_id} violation_type:{violation_type} plate_num:{plate_num} {e} {tmp_name} {e.__traceback__.tb_lineno}")
        traceback.print_exc()
        res_infos['code'] = 1
        res_infos['msg'] = 'fail'
        res_infos['data'] = data
        return res_infos
    e_time = dt.datetime.now()
    time_delta = round((e_time - s_time).seconds, 2)
    # tt_time += time_delta
    # print(time.strftime("%Y-%m-%d %H:%M:%S", time.localtime()), f' {session_id} use time', time_delta, '\n', res_infos,
    #       "\n\n", "-" * 50, "\n")
    logger.info(time.strftime("%Y-%m-%d %H:%M:%S",
                              time.localtime()) + f' id:{session_id} use time:{time_delta}\nres_infos:{res_infos}')
    # res_infos = json.dumps(res_infos)
    torch.cuda.empty_cache()
    # 保存判罚结果图片
    try:
        pred_result = f"{SAVE_RES_DIR}/{violation_type}/vio" if vio_judge.startswith(
            "vio") else f"{SAVE_RES_DIR}/{violation_type}/no_vio"
        for idx, img in enumerate(file_list):
            cv2.imwrite(f"{pred_result}/{session_id}_{plate_num}_{idx}.jpg", img)
    except Exception as e:
        # print('{}=========Error!====={}=={}=========='.format(session_id, violation_type, plate_num), e,
        #       e.__traceback__.tb_frame.f_globals["__file__"], e.__traceback__.tb_lineno)
        # print(time.strftime("%Y-%m-%d %H:%M:%S", time.localtime()), f" {session_id} 保存判罚结果图片失败!")
        tmp_name = e.__traceback__.tb_frame.f_globals["__file__"]
        logger.error(time.strftime("%Y-%m-%d %H:%M:%S", time.localtime()) +
                     f"id:{session_id} violation_type:{violation_type} plate_num:{plate_num} {e} {tmp_name} {e.__traceback__.tb_lineno} 保存图片失败！")

    return res_infos, file_name


if __name__ == '__main__':
    from kafka import KafkaConsumer
    from kafka import KafkaProducer

    # 初始化保存路径
    save = IS_SAVE_VIS_PIC
    # result_dir = '../test_dir/pred_result'
    # if os.path.exists(result_dir):
    #     tmp = result_dir.split("/")[-1]
    #     print(f"{tmp}目录已经存在，即将初始化目录...")
    #     shutil.rmtree(result_dir)

    # 请求消费者

    consumer = KafkaConsumer(REQ_TOPIC,
                             bootstrap_servers=SERVER,
                             group_id=GROUP_ID,
                             auto_offset_reset=AUTO_OFFSET_RESET, fetch_max_bytes=25000000,
                             max_partition_fetch_bytes=25000000)
    # 模型结果生产者
    producer = KafkaProducer(bootstrap_servers=SERVER, value_serializer=lambda m: json.dumps(m).encode())
    start_time = time.time()
    for msg in consumer:
        request_dict = eval(msg.value)
        try:
            s_id = request_dict["session_id"]
            plate = request_dict["plate_number"]
            violation_type = request_dict["violation_type"]
            os.makedirs(f"{SAVE_RES_DIR}/{violation_type}/vio", exist_ok=True)
            os.makedirs(f"{SAVE_RES_DIR}/{violation_type}/no_vio", exist_ok=True)
            logger.info(f"{s_id}_{plate}——正在调用模型，等待判罚结果...")
            res_infos, file_name = calculate(request_dict, save=save, save_path=f"{SAVE_RES_DIR}/{violation_type}/vis")
            producer.send(RES_TOPIC, res_infos)
            logger.info(f"id:{s_id} {file_name}的判罚结果已推送至kafka!\n")
        except Exception as e:
            tmp_name = e.__traceback__.tb_frame.f_globals["__file__"]
            logger.error(time.strftime("%Y-%m-%d %H:%M:%S", time.localtime()) +
                         f"{e} {tmp_name} {e.__traceback__.tb_lineno}\n{request_dict}模型调用过程出错!")
        producer.flush(3)
        if time.time() - start_time > SET_RUN_TIME:
            exit(f"{SET_RUN_TIME}S运行完成！")
