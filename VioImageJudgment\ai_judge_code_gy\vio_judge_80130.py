# 限行违法场景
import os.path

import cv2
import numpy as np

from init_models import *
import sys

sys.path.append('..')
from main_config.config import _LABEL


# 车牌匹配目标车辆，返回目标车辆位置、车牌、车辆类型和所有检测的车辆位置
class BlendMaskCarInfo(object):
    def __init__(self, img_np, car_num, weights_list, device, save=False):
        self.img_np = img_np
        self.car_num = car_num
        self.weights_list = weights_list
        self.save = save
        self.device = device

    # def get_goal_car_info(self):
    #     demo, plate_det_model, plate_rec_model, yolov7_model, yolov7_meta = self.weights_list
    #     det = BlendMaskDetect(self.img_np)
    #     predictions, kps = det.mask_predict(demo, show=False)
    #     car_bbox_ps_with_label, cars_mask_list = det.class_info(predictions, car_list, div_label=True)
    #     # car_bbox_ps_with_label, cars_mask_list = det.class_info(predictions, ['truck_b', 'truck_h', "engineering_car_h", "engineering_car_b"], div_label=True)
    #     car_bbox_ps = [box for key in car_bbox_ps_with_label.keys() for box in car_bbox_ps_with_label[key]]
    #     img_car_ps, plate_num, _, new_car_bbox_ps = \
    #         CarJudge().confirm_img1_car_ps(self.img_np, self.car_num, plate_det_model, plate_rec_model, car_bbox_ps,
    #                                        self.device, label=1)
    #     car_type = get_car_type(car_bbox_ps_with_label, img_car_ps)
    #     # print('====', img_car_ps, plate_num, car_type)
    #     return img_car_ps, plate_num, car_type, car_bbox_ps
    # return get_car_box(self.img_np, self.car_num, self.weights_list)

    def merge_bbox(self, bbox1: list, bbox2: list):
        for box1 in bbox1:
            for box2 in bbox2:
                # 交集
                w = min(box1[2], box2[2]) - max(box1[0], box2[0])
                h = min(box1[3], box2[3]) - max(box1[1], box2[1])
                if w > 0 and h > 0:
                    intersection = w * h
                else:
                    intersection = 0
                # 计算IOU
                IOU = intersection / max(((box1[2] - box1[0]) * (box1[3] - box1[1]) + (box2[2] - box2[0]) * (
                        box2[3] - box2[1]) - intersection), 1e-8)
                if IOU > IOU_THRESH:
                    bbox2.remove(box2)
                    # print("remove box:", box2)
                    break
        return bbox1 + bbox2

    def get_goal_car_info_full(self, vio_judge, judge_infos):
        iieaAuditMode = judge_infos["iieaAuditMode"]
        # iiea审核模式
        if iieaAuditMode is None:
            judge_mode = Precision_Judge_Mode[str(judge_infos['vio_code'])]
        else:
            judge_mode = iieaAuditMode
        judge_thresh = Judge_Thresh[str(judge_infos['vio_code'])][int(judge_mode)]
        if judge_thresh is None:
            judge_thresh = [0.45, 0.4] if judge_mode else [0.2, 0.2]
        demo, plate_det_model, plate_rec_model, yolov7_model, yolov7_meta = self.weights_list
        crop_img_list, car_bbox_ps, label_list, score_list = yolov7_detect(self.img_np, yolov7_model, yolov7_meta[0],
                                                                           yolov7_meta[1],
                                                                           yolov7_meta[2],conf_thres=judge_thresh[0], need_conf=True)
        # ↓测试yolov7识别车辆类型↓
        # img_car_ps, plate_num, img_plate_dict, new_car_bbox_ps = \
        #     CarJudge().confirm_img1_car_ps(self.img_np, self.car_num, plate_det_model, plate_rec_model, car_bbox_ps,
        #                                    yolov7_meta[2], label=1)
        #
        # print(label_list[car_bbox_ps.index(img_car_ps)],score_list[car_bbox_ps.index(img_car_ps)])
        #
        # return img_car_ps, plate_num, car_bbox_ps, vio_judge
        # ↑测试yolov7识别车辆类型↑

        car_bbox_ps, score_list, labels_list = self.filter_yolo_stop_info(car_bbox_ps, label_list, score_list,judge_thresh[0])
        print("Yolo:", car_bbox_ps, score_list, labels_list)
        # 加入Blend过滤低速车提高检准率
        det = BlendMaskDetect(self.img_np)
        predictions, kps = det.mask_predict(demo, show=False)
        # NMS
        nms_ls = det.nms_class_distinct(predictions["instances"].pred_boxes.tensor,
                                        predictions["instances"].scores,
                                        predictions["instances"].pred_classes,
                                        threshold=0.8,
                                        class_ditinct_threshold=0.85)
        predictions, kps_mask = det.prediction_nms_filter(predictions, nms_ls, kps)
        car_bbox_ps2, labels_list2, scores2 = det.class_info2(predictions, stop_car_type_list, judge_thresh[1])

        print("Blend:", car_bbox_ps2, scores2, "\n")
        inter_list = []
        for yolo_idx, yolo_box in enumerate(car_bbox_ps):
            tmp1 = np.asarray(yolo_box)
            for blend_idx, blend_box in enumerate(car_bbox_ps2):
                tmp2 = np.asarray(blend_box)
                tl1, br1, tl2, br2 = tmp1[:2], tmp1[2:], tmp2[:2], tmp2[2:]
                IOU = Tools().IOU(tl1, br1, tl2, br2, is_min_union=False)
                if IOU >= Head_IOU:
                    inter_list.append(blend_box)
                    break
        print("禁行车辆：", inter_list)

        # 加入Blend模型检测货车提高检出率
        if Truck_Vio_Recall:
            # det = BlendMaskDetect(self.img_np)
            # predictions, kps = det.mask_predict(demo, show=False)
            # car_bbox_ps2, scores2 = self.stop_car_class_info(predictions)
            # print("\nBlend:", car_bbox_ps2, scores2)

            car_bbox_ps = self.merge_bbox(car_bbox_ps, car_bbox_ps2)
            print("merge:", car_bbox_ps)


        img_car_ps, plate_num, img_plate_dict, new_car_bbox_ps,first_plate_conf = \
            CarJudge(iiea_cfg=judge_infos["iieaFilterVehicle"],need_first_plate=True).confirm_img1_car_ps(self.img_np, self.car_num,
                                                                                    plate_det_model, plate_rec_model,
                                                                                    inter_list,
                                                                                    yolov7_meta[2], label=1, show=IS_SAVE_VIS_PIC,
                                           save_path=f"{SAVE_RES_DIR}/8013/vis")



        # 过滤老头乐
        vio_judge = filter_low_speed_veh(img_car_ps, car_bbox_ps2, labels_list2,scores2, self.car_num, plate_num,
                                         first_plate_conf)
        # 返回检测框和置信度信息
        img_id = judge_infos["img_id"]

        if img_car_ps:
            target_idx = car_bbox_ps.index(img_car_ps)
            percent_coord = convert_src_coord(img_car_ps, self.img_np, img_id,
                                              judge_infos["split_mode"], judge_infos["black_height"],
                                              judge_infos["black_pos"])
            tmp_res = {
                "objId": img_id,
                "objType": "vehicle",
                "objConf": float(score_list[target_idx]),
                "objCoord": percent_coord
            }
            judge_infos["modelDetectResults"].append(tmp_res)
        # 车牌位置信息
        plate_cor = img_plate_dict[str(img_car_ps)] if img_car_ps else []
        if plate_cor:
            target_idx = str(img_car_ps) + "conf"
            percent_coord = convert_src_coord(plate_cor, self.img_np, img_id,
                                              judge_infos["split_mode"], judge_infos["black_height"],
                                              judge_infos["black_pos"])
            tmp_res = {
                "objId": img_id,
                "objType": "plate",
                "objConf": float(img_plate_dict[target_idx]),
                "objCoord": percent_coord
            }
            judge_infos["modelDetectResults"].append(tmp_res)
        # iiea车辆白名单
        try:
            _, ambulance_filter, fire_filter = judge_infos["iieaFilterVehicle"]
        except:
            ambulance_filter = None
            fire_filter = None
        special_type_list = []
        if ambulance_filter:
            special_type_list.extend(['ambulance_h', 'ambulance_b'])
        if fire_filter:
            special_type_list.extend(['fire_engine_h', 'fire_engine_b'])
        if ambulance_filter is None and fire_filter is None:
            special_type_list = Special_Type
        # 警车判定
        if plate_num.endswith("警"):
            vio_judge = 'no_vio_010'
            judge_infos['judgeConf'] = 0.9
        # 特殊车辆判定
        elif img_car_ps and special_type_list:
            # 筛选进行违法判定的车辆类型
            car_bbox_ps2, labels_list2, score_list2 = det.class_info2(predictions, special_type_list,
                                                                      Special_Car_Vis_Thresh)
            for target_idx, special_car in enumerate(car_bbox_ps2):
                target_label = labels_list2[target_idx]
                tmp1 = np.asarray(img_car_ps)
                tmp2 = np.asarray(special_car)
                tl1, br1, tl2, br2 = tmp1[:2], tmp1[2:], tmp2[:2], tmp2[2:]
                IOU = Tools().IOU(tl1, br1, tl2, br2, is_min_union=False)
                if IOU >= Head_IOU:
                    if target_label == "ambulance_h":
                        if score_list2[target_idx] >= Ambulance_Head_Thresh:
                            vio_judge = "no_vio_010"
                    elif score_list2[target_idx] >= Special_Car_Thresh:
                        vio_judge = "no_vio_010"

                    judge_infos['judgeConf'] = float(score_list2[target_idx])

                if self.save:
                    vis_area = (special_car[0], (2 * special_car[1] + special_car[3]) // 3)
                    cv2.putText(self.img_np, target_label + f"{score_list2[target_idx]:.3f}", vis_area,
                                cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 0, 255), 2)

        if self.save:
            for target_idx, box in enumerate(car_bbox_ps):
                vis_area = (box[0], (box[1] + box[3]) // 2)
                cv2.putText(self.img_np, f"{labels_list[target_idx]} {score_list[target_idx]:.3f}", vis_area,
                            cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 0, 255), 2)

        # print('====', img_car_ps, plate_num, car_type)
        return img_car_ps, plate_num, car_bbox_ps, vio_judge

    def stop_car_class_info(self, predictions):
        bbox_list = []
        score_list = []
        boxes = predictions["instances"].pred_boxes
        classes = predictions["instances"].pred_classes.cpu().data.numpy()
        scores = predictions["instances"].scores.cpu().data.numpy()
        for stop_cls in stop_car_type_list:
            label_index = _LABEL.index(stop_cls)
            if label_index not in classes:
                pass
            else:
                res_idxs = [i for i, cl in enumerate(classes) if cl == label_index]
                res_idxs = [idx for idx in res_idxs if scores[idx] >= Blend_Conf]
                boxes_info = [boxes[index] for index in res_idxs]
                tmp_score = [scores[index] for index in res_idxs]
                for box in boxes_info:
                    for i in box:
                        x1, y1, x2, y2 = i
                        bbox_list.append([int(x1), int(y1), int(x2), int(y2)])
                score_list += tmp_score

        return bbox_list, score_list

    def filter_yolo_stop_info(self, bboxes, labels, scores,judge_thresh):
        bbox_list = []
        score_list = []
        labels_list = []
        for stop_cls in yolov7_stop_car_type_list:
            if stop_cls not in labels:
                pass
            else:
                res_idxs = [i for i, cl in enumerate(labels) if cl == stop_cls]
                res_idxs = [idx for idx in res_idxs if float(scores[idx]) >= judge_thresh]
                boxes_info = [bboxes[index] for index in res_idxs]
                tmp_score = [float(scores[index]) for index in res_idxs]
                tmp_label = [labels[index] for index in res_idxs]
                for box in boxes_info:
                    box = [int(i) for i in box]
                    bbox_list.append(box)
                score_list += tmp_score
                labels_list += tmp_label
        return bbox_list, score_list, labels_list


# 限行场景主框架函数
def judge_vio(src_plate_num, file_list, weights_list, resize_scale=0.7, save=False,
              save_path=".", extra_msg=None):
    error_path = f"{save_path}/no_vio"
    vio_path = f"{save_path}/vio"
    if save:
        os.makedirs(error_path, exist_ok=True)
        os.makedirs(vio_path, exist_ok=True)
    # 读取模型
    plate_det_model = weights_list[0][0]
    plate_rec_model = weights_list[0][1]
    demo = weights_list[1]
    lp_model = weights_list[2][0]
    lp_meta = weights_list[2][1]
    yolov7_model = weights_list[3][0]
    yolov7_meta = weights_list[3][1]
    model_cnn = weights_list[-2]
    vio_model = weights_list[-1]

    vio_judge = 'no_vio'
    try:
        split_mode = extra_msg[0]
        black_height = extra_msg[1]
        black_pos = extra_msg[2]
        iieaAuditMode = extra_msg[3]
        iieaFilterVehicle = extra_msg[4]
    except:
        split_mode = 111
        black_height = 0
        black_pos = None
        iieaAuditMode = None
        iieaFilterVehicle = None
    judge_infos = {'plate_num': src_plate_num,
                   'vio_code': 8013,
                   'vio_judge': vio_judge,
                   'zebra_line': {},
                   'police': 0,
                   'has_crossline': 0,
                   'vehicle_location': {},
                   'drv_direction': 0,
                   'drive_direction_light': [],
                   'lamp': [],
                   'stop_line': 0,
                   'lane': [],
                   'target_car': [],
                   'judgeConf': 1.0,
                   'modelDetectResults': [], 'split_mode': split_mode, "black_height": black_height,
                   "black_pos": black_pos, "iieaAuditMode": iieaAuditMode, "iieaFilterVehicle": iieaFilterVehicle}

    if not src_plate_num:
        vio_judge = "no_vio_009"
        judge_infos['vio_judge'] = vio_judge
        return vio_judge, judge_infos
    if src_plate_num[0] not in chars:
        vio_judge = 'no_vio_002'
        judge_infos['vio_judge'] = vio_judge
        return vio_judge, judge_infos
    # 特殊车牌判定
    vio_judge = VIO_JUDGE_TOOL().spe_no_vio_judge(src_plate_num, iieaFilterVehicle)
    if vio_judge.startswith('no_vio'):
        judge_infos['vio_judge'] = vio_judge
        return vio_judge, judge_infos
    vio_judge = "no_vio"
    plate_list = []
    car_ps_list = []
    judge_label = []
    all_car_ps = []
    # 禁行的车辆类型

    for i, img in enumerate(file_list):
        if i == 3:
            continue
        label = 0
        temp_car = {}
        judge_weights_list = [demo, plate_det_model, plate_rec_model, yolov7_model, yolov7_meta]
        car_detect = BlendMaskCarInfo(img, src_plate_num, judge_weights_list, yolov7_meta[2], save)
        judge_infos['img_id'] = i + 1
        img_car_ps, plate_num, car_bbox_ps, vio_judge = car_detect.get_goal_car_info_full(vio_judge, judge_infos)

        all_car_ps.append(car_bbox_ps)

        if img_car_ps:
            label = 1

        temp_car['plate_num'] = plate_num
        temp_car['car_ps'] = img_car_ps
        judge_infos['target_car'].append(temp_car)
        try:
            x_ratio = ((img_car_ps[0] + img_car_ps[2]) / 2) / img.shape[1]
        except:
            x_ratio = 0
        # 0 代表左边， 1代表右边
        if x_ratio <= 0.5:
            judge_infos['vehicle_location'] = 0
        else:
            judge_infos['vehicle_location'] = 1
        plate_list.append(plate_num)
        car_ps_list.append(img_car_ps)
        if vio_judge.startswith("no"):
            break
        judge_label.append(label)
    # 存在一张图找不到目标车则为废片
    # num_ds = 0
    # for idx, plate in enumerate(plate_list):
    #     if plate == 'NO_DETECT' or not car_ps_list[idx]:
    #         num_ds += 1
    ##if num_ds:
    ##    vio_judge = 'no_vio_006'
    ##    judge_infos['vio_judge'] = vio_judge
    ##return vio_judge, judge_infos

    if 1 in judge_label:
        vio_judge = 'vio'
        judge_infos['judgeConf'] = 0.9

    else:
        vio_judge = 'no_vio'
        judge_infos['judgeConf'] = 0.8
    judge_infos['vio_judge'] = vio_judge
    try:
        if save:
            for i in range(len(all_car_ps)):
                if i == 3:
                    continue
                show_cars(file_list[i], None, None, all_car_ps[i])
                show_plate(file_list[i], None, None, car_ps_list[i])

                if vio_judge.startswith("vio"):
                    tmp_path = vio_path
                else:
                    tmp_path = error_path
                num = 1
                vis_path = tmp_path + f'/{vio_judge}_{src_plate_num}_{num}_img{i + 1}.jpg'
                while os.path.exists(vis_path):
                    num += 1
                    vis_path = tmp_path + f'/{vio_judge}_{src_plate_num}_{num}_img{i + 1}.jpg'

                cv2.imwrite(vis_path, file_list[i])
    except:
        print("Error!闯禁行场景可视化模块异常！")
    return vio_judge, judge_infos
