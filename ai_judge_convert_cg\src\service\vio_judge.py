from typing import Dict, Any, List, Union
from src.module.judge_act import create_judge_act, create_rule_module
from src.module.request_parser import RequestCtx
from conf.config import create_conf
from logging import Logger
from src.service.data_class import JudgeResult


class VioTypeJudger:
    def __init__(self, name, conf, model_dict):
        """
        违法场景类，串起违法场景中的动作和规则流程
        :param name:违法场景名称
        :param conf:违法场景配置类，各动作规则模块可灵活配置
        :param model_dict:功能模型字典
        """
        self._name = name
        self._conf = conf
        self._act_dict = self._conf.act_dict
        self._acts = self._conf.acts
        self._model_dict = model_dict
        self._actions = self.init_action()

    def init_action(self):
        actions = []
        for actinfo in self._acts:
            name = actinfo['act']
            param = actinfo['param']
            actclass, actconf = self._act_dict[name]
            actions.append(create_judge_act(actclass, name, actconf, param, self._model_dict))
        return actions

    def run(self, judgeres: JudgeResult):
        judgeres.logger.info(f"\n{self._name}违法场景开始运行...")
        for act_cls in self._actions:
            act_cls.run(judgeres)


class VioJudger:
    def __init__(self, request: RequestCtx, model_dict: Dict):
        self.request = request
        self.actions = [
            create_judge_act("RequestJudAct", "request", "RequestJudActConf", {}, {}),  # 请求判定动作
            create_judge_act("ImageJudAct", "image", "ImageJudActConf", {}, {}),  # 图片判定动作
        ]
        # 初始化违法场景判定器
        self.judger_dict = {
            "1101": VioTypeJudger("belt", create_conf("BeltPhoneJudgerConf"), model_dict),
            "1223": VioTypeJudger("phone", create_conf("BeltPhoneJudgerConf"), model_dict),
            "1625": VioTypeJudger("run_light", create_conf("LightJudgerConf"), model_dict),
            "1345": VioTypeJudger("cover_line", create_conf("CoverJudgerConf"), model_dict),
            "1301": VioTypeJudger("wrong_dir", create_conf("WrongDirJudgerConf"), model_dict),
        }

    def judge(self, msg, logger: Logger) -> JudgeResult:
        self.request.parse(msg)  # 解析违法数据
        self.request.print_parse_data()  # 打印所有解析字段
        vio_code = self.request.match_viocode
        if vio_code in self.judger_dict:
            self.actions.append(self.judger_dict[vio_code])
        judgeres = JudgeResult(self.request, logger)
        for action in self.actions:
            action.run(judgeres)
        judgeres.add_judge_result()
        self.actions = self.actions[:2]  # 动作初始化
        return judgeres
