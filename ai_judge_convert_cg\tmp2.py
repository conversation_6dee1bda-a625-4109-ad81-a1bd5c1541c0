class TrafficLightJudge:
    def __init__(self):
        # 初始化实例变量
        self.add_l = False  # 标记是否需要添加左转灯
        self.status = []  # 整合三图的红绿灯结果
        self.status_cx = []  # 每个红绿灯的中点横坐标
        self.status_thresh = []  # 每个红绿灯的置信度阈值
        self.left_ps = []  # 左转灯信息 [[类别, 中点横坐标],]

    def judge_light(self, judgeres: JudgeResult) -> None:
        camera_code = judgeres.request.camera_code
        img_keys = ["img1", "img2", "img3"]
        attr_keys = ["box", "subclass", "score"]

        # 构建图片识别结果字典
        res_dict = {
            img_key: {
                attr: judgeres.tfc_elements.get(img_key, ModelResults()).get_results(
                    superclass="light", attr=attr)
                for attr in attr_keys
            }
            for img_key in img_keys
        }

        # 处理三张图片的识别结果
        single_left = self._process_images(res_dict)

        # 处理同方向颜色冲突
        vio_judge = self._handle_conflicts(camera_code, judgeres)

        # 添加左转灯
        self._add_left_turn_light(single_left)

        # 应用预设的红绿灯信息
        self._apply_preset_light_info(camera_code)

        # 记录最终结果
        judgeres.add_act_details(f"{self._cls_name}.judge_light",
                                 {"light_judge": vio_judge, "status": self.status, "add_l": self.add_l,
                                  "single_left": single_left,
                                  "left_ps": self.left_ps})

    def _process_images(self, res_dict):
        """处理三张图片的识别结果"""
        single_left = None  # 判定当只有第一张图检测到某位置的红绿灯

        # 遍历第一张图的识别结果
        for idx1, box1 in enumerate(res_dict["img1"]["box"]):
            cls1 = res_dict["img1"]["subclass"][idx1]
            score1 = res_dict["img1"]["score"][idx1]
            cx1 = (box1[0] + box1[2]) // 2

            single_left = True  # 默认认为当前灯是单个左转灯

            # 处理第三张图的识别结果
            for idx3, box3 in enumerate(res_dict["img3"]["box"]):
                cls3 = res_dict["img3"]["subclass"][idx3]
                score3 = res_dict["img3"]["score"][idx3]
                cx3 = (box3[0] + box3[2]) // 2

                if cls3[-1] == 'l':
                    self.left_ps.append([cls3, cx3])

                iou13 = IOU.get_xiou(box1, box3)  # 横向 IoU 计算
                if iou13 > 0.5:
                    self._process_match(cls1, score1, cx1, cls3, score3, cx3, res_dict)
                    single_left = False

            if single_left and not self.add_l:
                self._update_single_left(cls1, cx1)

        return single_left

    def _process_match(self, cls1, score1, cx1, cls3, score3, cx3, res_dict):
        """处理两张图片匹配的情况"""
        thresh_list = [score1, score3]

        if cls1 == cls3:  # 全匹配
            self.status.append(cls1)
            self.status_cx.append(cx1)
            self.status_thresh.append(max(thresh_list))
        elif cls1[-1] == cls3[-1] == 'l':  # 方向匹配
            self.add_l = True
        elif cls1[0] == cls3[0]:  # 颜色匹配
            color = cls1.split("_")[0]
            direction_list = [cls1[-1], cls3[-1]]
            idx = np.argmax(thresh_list)
            new_thresh = thresh_list[idx]
            new_light = color + "_" + direction_list[idx]

            # 遍历第二张图的识别结果
            for idx2, box2 in enumerate(res_dict["img2"]["box"]):
                cls2 = res_dict["img2"]["subclass"][idx2]
                score2 = res_dict["img2"]["score"][idx2]
                cx2 = (box2[0] + box2[2]) // 2

                if cls2[-1] == 'l':
                    self.left_ps.append([cls2, cx2])

                iou23 = IOU.get_xiou(box2, box3)
                if iou23 > 0.5:
                    self._process_second_image_match(cls1, score1, cx1, cls2, score2, cx2, cls3, score3, direction_list,
                                                     thresh_list, res_dict)

    def _process_second_image_match(self, cls1, score1, cx1, cls2, score2, cx2, cls3, score3, direction_list, thresh_list,
                                    res_dict):
        """处理三张图片匹配的情况"""
        thresh_list = [score1, score2, score3]
        direction_list = [cls1[-1], cls2[-1], cls3[-1]]

        if direction_list.count("l") > 1:
            self.add_l = True

        idx = np.argmax(thresh_list)
        temp_dt = direction_list.copy()
        temp_dt.pop(idx)
        temp_thresh = thresh_list.copy()
        temp_thresh.pop(idx)
        dist_thresh = thresh_list[idx] - np.max(temp_thresh)
        new_thresh = thresh_list[idx]

        if direction_list.count(direction_list[idx]) == 2:
            new_light = color + "_" + direction_list[idx]
        elif temp_dt[0] == temp_dt[1]:
            if dist_thresh > 0.3:
                new_light = color + "_" + direction_list[idx]
            else:
                new_light = color + "_" + temp_dt[0]
                new_thresh = np.max(temp_thresh)
        elif dist_thresh > 0:
            new_light = color + "_" + direction_list[idx]

        res_dict["img2"]["subclass"][idx2] = new_light + "-" + str(new_thresh)

        self.status.append(new_light)
        self.status_cx.append(cx1)
        self.status_thresh.append(new_thresh)

        res_dict["img1"]["subclass"][idx1] = new_light + "-" + str(new_thresh)
        res_dict["img3"]["subclass"][idx3] = new_light + "-" + str(new_thresh)

    def _update_single_left(self, cls1, cx1):
        """更新单个左转灯的状态"""
        if cls1[-1] == 'l':
            self.add_l = True
        else:
            self.left_ps.append([cls1, cx1])

    def _handle_conflicts(self, camera_code, judgeres):
        """处理同方向颜色冲突"""
        vio_judge = None

        if "red_s" in self.status and "green_s" in self.status:
            vio_judge = self._resolve_red_green_conflict("s", camera_code, judgeres)

        if "red_l" in self.status and "green_l" in self.status:
            vio_judge = self._resolve_red_green_conflict("l", camera_code, judgeres)

        return vio_judge

    def _resolve_red_green_conflict(self, direction, camera_code, judgeres):
        """解决红绿灯冲突"""
        red_key = f"red_{direction}"
        green_key = f"green_{direction}"

        idx_r = self.status.index(red_key)
        idx_g = self.status.index(green_key)

        if abs(self.status_cx[idx_r] - self.status_cx[idx_g]) < 20:
            vio_judge = "light_logic_judge"
            judgeres.set_judres_core(judge_status=2, judge_confid=self.status_thresh[idx_r],
                                     result_code=vio_judge,
                                     result_desc=f"过滤误识别{direction}直行红灯")
            self.status.pop(idx_r)
            self.status_thresh.pop(idx_r)
            self.status_cx.pop(idx_r)
        elif self.status_cx[idx_r] > self.status_cx[idx_g]:
            if self.status_thresh[idx_r] < self.status_thresh[idx_g] and self._conf.Set_Light_Direct.get(camera_code) is None:
                self.status.pop(idx_r)
                self.status_thresh.pop(idx_r)
                self.status_cx.pop(idx_r)
            elif self.status_thresh[idx_r] > self.status_thresh[idx_g] and self.status_direct.count("l" if direction == "s" else "s") == 0:
                self.status[idx_g] = green_key
                self.status_thresh[idx_g] = 0
        else:
            if self.status_thresh[idx_r] < self.status_thresh[idx_g] and self.status_direct.count("l" if direction == "s" else "s") == 0:
                self.status[idx_r] = red_key
                self.status_thresh[idx_r] = 0

        return vio_judge

    def _add_left_turn_light(self, single_left):
        """添加左转灯"""
        status_direct = [i[-1] for i in self.status]

        if status_direct.count("l") > 0:
            self.add_l = False
        elif not self.add_l and status_direct.count("s") == 1:
            idx = status_direct.index('s')
            judge_cx = self.status_cx[idx]
            for i in self.left_ps:
                if i[-1] < judge_cx and i[0][-1] == "l":
                    self.add_l = True
                    break

        if self.add_l:
            self.status.append("green_l")
            self.status_cx.append(0)

    def _apply_preset_light_info(self, camera_code):
        """应用预设的红绿灯信息"""
        try:
            if self._conf.Set_Light_Direct.get(camera_code) is not None:
                self.status_cx, self.status = zip(*sorted(zip(self.status_cx, self.status), key=lambda x: x[0]))
                self.status = list(self.status)
                pred_color = [i.split("_")[0] for i in self.status]
                pred_direct = [i[-1] for i in self.status]

                if len(self._conf.Set_Light_Direct.get(camera_code)) == len(self.status):
                    self.status = [pred_color[i] + "_" + self._conf.Set_Light_Direct.get(camera_code)[i] for i in
                                   range(len(self.status))]
                elif len(self._conf.Set_Light_Direct.get(camera_code)) > len(self.status) > 0:
                    for i in self._conf.Set_Light_Direct.get(camera_code):
                        if i not in pred_direct:
                            self.status.append(f"green_{i}")
        except:
            pass