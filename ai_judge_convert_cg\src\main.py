import sys

sys.path.append("..")
sys.path.append("../libs")
from typing import List
from logging import Logger
import torch
import urllib3
import requests
import time
from kafka import KafkaConsumer, KafkaProducer, TopicPartition, OffsetAndMetadata
from src.service.vio_judge import VioJudger
from src.utils.tools import catch_error
from src.module.image_model import ModelFactory
from src.module.request_parser import RequestCtx
from src.utils import tools
from src.utils.time_util import TimeUtil
from conf.kafka_config import consumer_config, producer_config, KafkaConf
from conf.config import BaseConf, DeployConf
from multiprocessing import Process
import os
import warnings

warnings.filterwarnings('ignore', category=urllib3.exceptions.InsecureRequestWarning)


class KafkaClient:
    """ kafka消费发送数据类
    消费和发送优化前后数据，只实现消费、发送数据功能，不包含数据处理
    """

    def __init__(self, partition, logger):
        self._logger = logger
        self._consumer = self.create_consumer()  # 创建消费者
        self.assign_partition(partition)  # 手动分配消费者负责的分区
        self._producer = self.create_producer()  # 创建生产者

    # 获取topic分区
    @staticmethod
    def get_topic_partitions(logger: Logger) -> List[TopicPartition]:
        while True:
            try:
                consumer = KafkaConsumer(bootstrap_servers=KafkaConf.KAFKA_BROKERS, client_id=KafkaConf.CLINE_ID)
                partitions = consumer.partitions_for_topic(KafkaConf.VIO_DATA_TOPIC)
                consumer.close()
                if partitions:
                    break
                else:
                    logger.info(f"TOPIC不存在，请先创建{KafkaConf.VIO_DATA_TOPIC}")
                    time.sleep(KafkaConf.Heart_Interval)
            except:
                tools.log_error(logger, "获取TOPIC分区信息失败！")
                time.sleep(KafkaConf.Heart_Interval)
        return [TopicPartition(KafkaConf.VIO_DATA_TOPIC, p) for p in partitions]

    @staticmethod
    def create_consumer() -> KafkaConsumer:
        return KafkaConsumer(**consumer_config)

    @staticmethod
    def create_producer() -> KafkaProducer:
        return KafkaProducer(**producer_config)

    def assign_partition(self, partition):
        # 手动分配分区
        self._consumer.assign(partition)
        PID = os.getpid()
        set_partition = [i.partition for i in partition]
        self._logger.info(f"PID:{PID} 消费已就绪！消费分区：{set_partition}")

    def receive(self):
        try:
            # 拉取消息
            records = self._consumer.poll(timeout_ms=KafkaConf.Poll_Interval_Ms)
            msgs = records.values()
        except:
            tools.log_error(self._logger, "拉取消息异常!")
            msgs = []
        return msgs

    def send(self, msg):
        msg["modelSendTime"] = TimeUtil.now_hikstr()
        msg["modelJudgeTime"] = round(
            TimeUtil.hikstr2timestamp(msg["modelSendTime"]) - TimeUtil.hikstr2timestamp(msg["modelRecvTime"]), 2)

        self._producer.send(KafkaConf.JUDGE_RES_TOPIC, msg)
        self._logger.info(f"已推送数据：{msg}")

    def commit(self, partition: TopicPartition, offset: OffsetAndMetadata):
        offset_dict = {partition: offset}
        try:
            self._consumer.commit(offset_dict)
            self._logger.debug(f"偏移量提交成功，partition {partition.partition} offset {offset.offset + 1}")
        except:
            tools.log_error(self._logger, "topic偏移量更新失败！")
            self.retry_commit(offset_dict)

    def retry_commit(self, offset_dict, max_retries=3):
        retries = 0
        while retries < max_retries:
            try:
                self._consumer.commit(offset_dict)
                self._logger.info(f"偏移量重试更新第{retries + 1}次成功！")
                break
            except:
                tools.log_error(self._logger, f"偏移量提交第{retries + 1}次重试失败!")
                retries += 1
            time.sleep(0.5)
        if retries == max_retries:
            tools.log_error(self._logger, "Max retries reached. Offset commit failed.")

    def flush(self, time_out=None):
        self._producer.flush(time_out)


def heartbeat():
    logger = tools.init_logger(process_type="heart", show_in_console=False)
    while True:
        try:
            req = requests.get(f"https://{DeployConf.TPC_IP}/iiea-web/service/rs/heartbeat/v1/beat", verify=False,
                               timeout=3)
            logger.info(f"状态码：{req.status_code} 数据推送相应结果：{req.text}")
        except:
            tools.log_error(logger, "心跳发送异常！")
        time.sleep(KafkaConf.Heart_Interval)


def judge_vio(device: int, partitions: List[TopicPartition]) -> None:
    logger = tools.init_logger()
    # TODO:模型授权
    # 初始化模型
    model_dict = ModelFactory().init_models(device)
    kafka_client = KafkaClient(partitions, logger)
    # 心跳进程
    if device == 0:
        hp = Process(target=heartbeat)
        hp.daemon = True
        hp.start()
    # 请求解析模块
    request = RequestCtx()
    # 违法场景动作初始化
    vio_judger = VioJudger(request, model_dict)
    topic_partition = offset = None
    while True:
        try:
            # 拉取kafka消息
            msgs = kafka_client.receive()
            for msg in msgs:
                msg = msg[0]
                try:
                    topic_partition = TopicPartition(msg.topic, msg.partition)
                    offset = OffsetAndMetadata(msg.offset + 1, metadata="")
                except:
                    tools.log_error(logger, "Topic分区和偏移量获取异常!")
                    continue
                judgeres = vio_judger.judge(msg, logger)
                judgeres.print_details()
                logger.info(f"act_results:{judgeres.act_results}")
                # logger.info(f"\ntfc_elements:{judgeres.tfc_elements['img1'].get_results(superclass='arrow_s',attr='box')}")
                logger.info(f"ResCore:{judgeres.get_judge_res_core()}\n\n")
                kafka_client.send(request.data)
                # 手动提交偏移量
                kafka_client.commit(topic_partition, offset)
        except:
            tools.log_error(logger, "违法数据处理过程异常！")
            error_msg = catch_error()
            error_core = {
                'judgeStatus': 0,
                'judgeConf': 1,
                'resultCode': "judge_error",
                'resultDesc': error_msg,
            }
            request.data["modelJudgeResult"] = error_core
            kafka_client.send(request.data)
            kafka_client.commit(topic_partition, offset)
        kafka_client.flush()


if __name__ == "__main__":
    main_logger = tools.init_logger(process_type="main", show_in_console=False)
    arc_list = ["华为昇腾arm", "英伟达x86"]
    main_logger.info(f"配置运行的架构为：{arc_list[BaseConf.arch]}")
    # multiprocessing.set_start_method("spawn", force=True)
    partitions = KafkaClient.get_topic_partitions(main_logger)
    num_partitions = len(partitions)
    processes = []
    assert isinstance(BaseConf.Multi_Num, list) and len(BaseConf.Multi_Num) == 2, tools.log_error(main_logger,
                                                                                                  "BaseConf.Multi_Num必须是长度为2的列表！[使用的GPU数量,每个GPU启动的进程数]")
    Per_Multi = BaseConf.Multi_Num[1]
    if BaseConf.arch:
        GPU_Num = BaseConf.Multi_Num[0] if torch.cuda.device_count() >= BaseConf.Multi_Num[
            0] else torch.cuda.device_count()
        if GPU_Num == 0:
            Per_Multi = min(2, Per_Multi)  # CPU运行置为2
        GPU_Num = max(GPU_Num, 1)

    else:
        GPU_Num = BaseConf.Multi_Num[0]
    num_consumers = GPU_Num * Per_Multi
    partitions_per_consumer = num_partitions // num_consumers
    extra_partitions = num_partitions % num_consumers
    for i in range(GPU_Num):
        for j in range(Per_Multi):
            idx = i * Per_Multi + j
            if idx >= num_partitions:
                main_logger.info(f"总分区数：{num_partitions} 多余的进程：{idx + 1}")
                continue
            start = idx * partitions_per_consumer
            end = (idx + 1) * partitions_per_consumer
            process_partitions = partitions[start:end] + partitions[
                                                         num_consumers * partitions_per_consumer + idx:num_consumers * partitions_per_consumer + idx + 1] if idx < extra_partitions else partitions[
                                                                                                                                                                                         start:end]

            p = Process(target=judge_vio, args=(i, process_partitions))
            p.start()
            processes.append(p)

    # 监控子进程状态
    while True:
        try:
            is_alive = [f"{p.pid}:{p.is_alive()}" for p in processes]
            save_status = " ".join(is_alive)
            main_logger.info(f"进程状态 --> {save_status}")
        except:
            pass
        time.sleep(KafkaConf.Heart_Interval)
