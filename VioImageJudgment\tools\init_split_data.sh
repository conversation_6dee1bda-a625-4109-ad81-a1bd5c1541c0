#!/bin/bash
export LANG=C.UTF-8
rm -rf /home/<USER>/ai_judge/test_dir/split_data*
rm /home/<USER>/ai_judge/ai_judge_code_gy/split_out.txt
python /home/<USER>/ai_judge/ai_judge_code_gy/split_test_data.py
nohup python /home/<USER>/ai_judge/ai_judge_code_gy/1625_test_gpu2.py >> split_out.txt 2>&1 &
nohup python /home/<USER>/ai_judge/ai_judge_code_gy/1625_test_gpu3.py >> split_out.txt 2>&1 &
nohup python /home/<USER>/ai_judge/ai_judge_code_gy/1625_test_gpu4.py >> split_out.txt 2>&1 &
/bin/bash
