import os
import cv2
import shutil

import numpy as np
import torch.cuda


# 初始化目录
def init_show(save_path=""):
    if os.path.exists(save_path):
        shutil.rmtree(save_path)


def show_plate(img1=None, img2=None, img3=None, img1_car_ps=None, img2_car_ps=None, img3_car_ps=None, plate1_cor=None,
               plate2_cor=None, plate3_cor=None):
    # 画目标车和车牌的位置
    IMG_LIST = [img1, img2, img3]
    CAR_LIST = [img1_car_ps, img2_car_ps, img3_car_ps]
    PLATE_LIST = [plate1_cor, plate2_cor, plate3_cor]
    for idx, i in enumerate(PLATE_LIST):
        if CAR_LIST[idx]:
            h, w, _ = IMG_LIST[idx].shape
            pt1 = (CAR_LIST[idx][0], CAR_LIST[idx][1])
            pt2 = (CAR_LIST[idx][2], CAR_LIST[idx][3])
            cv2.rectangle(IMG_LIST[idx], pt1, pt2, color=(0, 255, 0), thickness=2)
            if PLATE_LIST[idx]:
                pt1 = (PLATE_LIST[idx][0], PLATE_LIST[idx][1])
                pt2 = (PLATE_LIST[idx][2], PLATE_LIST[idx][3])
                cv2.rectangle(IMG_LIST[idx], pt1, pt2, color=(0, 0, 255), thickness=2)
                car_top_center = ((CAR_LIST[idx][0] + CAR_LIST[idx][2]) // 2, CAR_LIST[idx][1])
                pt_center = ((pt1[0] + pt2[0]) // 2, (pt1[1] + pt2[1]) // 2)
                cv2.line(IMG_LIST[idx], car_top_center, pt_center, (0, 0, 255), 1)
                cv2.line(IMG_LIST[idx], (car_top_center[0], 0), (car_top_center[0], h), (0, 0, 255), 1)
                cv2.line(IMG_LIST[idx], (0, pt_center[1]), (w, pt_center[1]), (0, 0, 255), 1)


def show_plate2(img_list, CAR_LIST, PLATE_LIST=None):
    # 画目标车和车牌的位置
    for idx, i in enumerate(CAR_LIST):
        if CAR_LIST[idx]:
            h, w, _ = img_list[idx].shape
            pt1 = (CAR_LIST[idx][0], CAR_LIST[idx][1])
            pt2 = (CAR_LIST[idx][2], CAR_LIST[idx][3])
            cv2.rectangle(img_list[idx], pt1, pt2, color=(0, 255, 0), thickness=2)
            if PLATE_LIST:
                pt1 = (PLATE_LIST[idx][0], PLATE_LIST[idx][1])
                pt2 = (PLATE_LIST[idx][2], PLATE_LIST[idx][3])
                cv2.rectangle(img_list[idx], pt1, pt2, color=(0, 0, 255), thickness=2)
                car_top_center = ((CAR_LIST[idx][0] + CAR_LIST[idx][2]) // 2, CAR_LIST[idx][1])
                pt_center = ((pt1[0] + pt2[0]) // 2, (pt1[1] + pt2[1]) // 2)
                cv2.line(img_list[idx], car_top_center, pt_center, (0, 0, 255), 1)
                cv2.line(img_list[idx], (car_top_center[0], 0), (car_top_center[0], h), (0, 0, 255), 1)
                cv2.line(img_list[idx], (0, pt_center[1]), (w, pt_center[1]), (0, 0, 255), 1)


def show_light(img1=None, img2=None, img3=None, img1_light_res=None, img2_light_res=None, img3_light_res=None,
               status=None):
    # 画红绿灯位置信息
    if status is None:
        status = []
    fontsize = 1.5
    thickness = 2
    for item in img1_light_res:
        point = eval(item[0])
        labels = item[1].split("-")[0]
        pt_tx = int(point[0][0])
        pt_ty = int(point[0][1])
        pt_bx = int(point[1][0])
        pt_by = int(point[1][1])
        color, org = get_color_org((pt_tx, pt_ty), (pt_bx, pt_by), labels)
        cv2.rectangle(img1, (pt_tx, pt_ty), (pt_bx, pt_by), color, 1)
        cv2.putText(img1, str(labels[-1]), org, cv2.FONT_HERSHEY_COMPLEX,
                    fontsize, color, thickness)
    for item in img2_light_res:
        point = eval(item[0])
        labels = item[1].split("-")[0]
        pt_tx = int(point[0][0])
        pt_ty = int(point[0][1])
        pt_bx = int(point[1][0])
        pt_by = int(point[1][1])
        color, org = get_color_org((pt_tx, pt_ty), (pt_bx, pt_by), labels)
        cv2.rectangle(img2, (pt_tx, pt_ty), (pt_bx, pt_by), color, 1)
        cv2.putText(img2, str(labels[-1]), org, cv2.FONT_HERSHEY_COMPLEX,
                    fontsize, color, thickness)
    for item in img3_light_res:
        point = eval(item[0])
        labels = item[1].split("-")[0]
        pt_tx = int(point[0][0])
        pt_ty = int(point[0][1])
        pt_bx = int(point[1][0])
        pt_by = int(point[1][1])
        color, org = get_color_org((pt_tx, pt_ty), (pt_bx, pt_by), labels)
        cv2.rectangle(img3, (pt_tx, pt_ty), (pt_bx, pt_by), color, 1)
        cv2.putText(img3, str(labels[-1]), org, cv2.FONT_HERSHEY_COMPLEX,
                    fontsize, color, thickness)
    if status:
        light_result = ""
        for i in status:
            light_result = light_result + i + " "
        cv2.putText(img3, light_result, (100, 100), cv2.FONT_HERSHEY_COMPLEX, fontsize, (0, 0, 255), thickness)


def show_police(img_list, p_res):
    # 画交辅警位置
    fontsize = 1.5
    thickness = 2
    for idx, i in enumerate(p_res):
        for item in i:
            point = eval(item[0])
            labels = item[1]
            pt_tx = int(point[0][0])
            pt_ty = int(point[0][1])
            pt_bx = int(point[1][0])
            pt_by = int(point[1][1])
            color, org = get_color_org((pt_tx, pt_ty), (pt_bx, pt_by), labels)
            cv2.rectangle(img_list[idx], (pt_tx, pt_ty), (pt_bx, pt_by), color, 1)
            cv2.putText(img_list[idx], str(labels), org, cv2.FONT_HERSHEY_COMPLEX,
                        fontsize, color, thickness)


def show_cars(img1=None, img2=None, img3=None, car1_bbox_ps=None, car2_bbox_ps=None, car3_bbox_ps=None):
    # 画所有检测到的车辆位置
    if car1_bbox_ps:
        for i, ps in enumerate(car1_bbox_ps):
            cv2.rectangle(img1, (ps[0], ps[1]), (ps[2], ps[3]), color=(255, 255, 0),
                          thickness=2)
    if car2_bbox_ps:
        for i, ps in enumerate(car2_bbox_ps):
            cv2.rectangle(img2, (ps[0], ps[1]), (ps[2], ps[3]), color=(255, 255, 0),
                          thickness=2)
    if car3_bbox_ps:
        for i, ps in enumerate(car3_bbox_ps):
            cv2.rectangle(img3, (ps[0], ps[1]), (ps[2], ps[3]), color=(255, 255, 0),
                          thickness=2)


def show_cars2(img_list, box_ps_list):
    # 画所有检测到的车辆位置
    for idx, box in enumerate(box_ps_list):
        for ps in box:
            cv2.rectangle(img_list[idx], (ps[0], ps[1]), (ps[2], ps[3]), color=(255, 255, 0),
                          thickness=2)


def show_lanes(img1=None, img2=None, img3=None, lanes_img1=None, lanes_img2=None, lanes_img3=None):
    # 画实例分割模型识别的交通要素
    color_dict = {"stop_line": (0, 0, 255), "lane1": (255, 0, 0), "lane2": (0, 255, 255), "arrow_l": (255, 0, 255),
                  "arrow_s": (0, 255, 0), "arrow_r": (0, 0, 0), "zebra_line": (255, 255, 0)}
    lanes_list = [lanes_img1, lanes_img2, lanes_img3]
    for idx, img in enumerate([img1, img2, img3]):
        for k, v in lanes_list[idx].items():
            for i in v:
                pt1 = (i[0], i[1])
                pt2 = (i[2], i[3])
                center = (i[0], (i[1] + i[3]) // 2)
                color = color_dict[k] if k in color_dict.keys() else (255, 255, 255)
                cv2.rectangle(img, pt1, pt2, color, 2)
                cv2.putText(img, k, center, cv2.FONT_HERSHEY_COMPLEX, 1.5, color, 2)


def show_person(img_list, person_list):
    # 画检测到的所有人的位置
    for idx, box in enumerate(person_list):
        for ps in box:
            cv2.rectangle(img_list[idx], (ps[0], ps[1]), (ps[2], ps[3]), color=(255, 255, 0),
                          thickness=2)

def show_phone_hand(img_list, box_list, label_list, score_list):
    # 可视化
    for idx, boxes in enumerate(box_list):
        for idx2, box in enumerate(boxes):
            cls = label_list[idx][idx2]
            score = score_list[idx][idx2]
            if cls == "hand":
                color = (255, 0, 0)
            elif cls == "phone":
                color = (0, 255, 0)
            else:
                color = (0, 0, 255)
            cv2.rectangle(img_list[idx], (box[0], box[1]), (box[2], box[3]), color, 2)
            cv2.putText(img_list[idx], f"{score:.2f}"[2:], (box[2], box[1]), cv2.FONT_HERSHEY_SIMPLEX, 1, color, 2)


# 红绿灯显示位置颜色修正
def get_color_org(pt1, pt2, cls):
    org = (pt1[0], pt2[1] + 50)
    if cls.startswith('red'):
        color = (0, 0, 255)
    elif cls.startswith('green'):
        color = (0, 255, 0)
    else:
        color = (0, 255, 255)
    return color, org


def show_judge_area(img1=None, img2=None, img3=None, area_ps=None):
    if area_ps:
        # 画所有检测到的车辆位置
        cv2.polylines(img1, np.array([area_ps], dtype=np.int32), True, color=(123, 231, 66), thickness=2)
        cv2.polylines(img2, np.array([area_ps], dtype=np.int32), True, color=(123, 231, 66), thickness=2)
        cv2.polylines(img3, np.array([area_ps], dtype=np.int32), True, color=(123, 231, 66), thickness=2)


def show_lane2_top_point(img_list, point_list):
    # 只对于不礼让行人场景做可视化
    for i in range(len(img_list)):
        if point_list[i]:
            cv2.circle(img_list[i], (point_list[i][0][0], point_list[i][0][1]), 10, (255, 0, 255), -1)
            cv2.putText(img_list[i], 'lane2_point', (point_list[i][0][0] + 5, point_list[i][0][1] + 5),
                        cv2.FONT_HERSHEY_COMPLEX, 1.5, (255, 0, 255), 2)

            cv2.circle(img_list[i], (point_list[i][0][2], point_list[i][0][3]), 10, (255, 0, 255), -1)
            cv2.putText(img_list[i], 'lane2_point', (point_list[i][0][2] + 5, point_list[i][0][3] + 5),
                        cv2.FONT_HERSHEY_COMPLEX, 1.5, (255, 0, 255), 2)
