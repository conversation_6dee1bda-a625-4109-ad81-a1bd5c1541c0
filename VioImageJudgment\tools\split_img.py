import cv2
import numpy as np
import math
import os
import shutil
import random
from tqdm import tqdm


def cut_data(img_root, mode):
    file_list = sorted(os.listdir(img_root))
    save_path = "../../AdelaiDet/test_data/"
    os.makedirs(save_path, exist_ok=True)
    img_list = []
    for file in tqdm(file_list):
        name = file.split(".")[0]
        img_path = os.path.join(img_root, file)
        img = cv2.imread(img_path)
        h, w, _ = img.shape

        if mode == "nc":
            split_w = w // 3
            img1 = img[:, :split_w]
            img2 = img[:, split_w:2 * split_w]
            img3 = img[:, 2 * split_w:]
            img_list = [img1, img2, img3]
        elif mode == "nj":
            black_h = (h - 4530) + 210
            if black_h < 140:
                black_h = 140
            cx = w // 2
            cy = (h - black_h) // 2
            img1 = img[:cy, :cx]
            img2 = img[:cy, cx:]
            img3 = img[cy:2 * cy, :cx]
            img_list = [img1, img2, img3]
        for i in range(len(img_list)):
            cv2.imwrite(f"{save_path}/{name}_img{i + 1}.jpg", img_list[i])


def rename_file(path):
    num = 1
    for i in os.listdir(path):
        dir1 = f"{path}/{i}"
        print(i)
        new_name = i.split(".")[0]
        os.rename(dir1, f"{path}/{num}.jpg")
        num += 1
        # for j in os.listdir(dir1):
        #     dir2 = f"{dir1}/{j}"
        #     os.rename(dir2, f"{dir1}/add2_{num}.jpg")
        #     num += 1


def mv_src_img():
    dir1 = r"E:\add_light_data\split_img\cat"
    dir2 = r"E:\add_light_data\split_img\no_cat"
    for i in os.listdir(dir1):
        img_root = f"E:/voc_data_0704/JPEGImages/{i}"
        shutil.copy(img_root, dir2)


if __name__ == '__main__':
    cut_data("../../add_data", mode="nj")
