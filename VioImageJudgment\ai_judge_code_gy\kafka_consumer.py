from kafka import <PERSON><PERSON><PERSON>Consumer, KafkaProducer
import json
from main_config.config import *

VIO_DATA_TOPIC = "TEST_0729"
# SERVER = "11.75.1.12:31993,11.75.1.14:31993,11.75.1.16:31993"
SERVER = "10.184.48.200:9092"
CLIENT_ID = "admin"

consumer = KafkaConsumer(VIO_DATA_TOPIC,
                         bootstrap_servers=SERVER,
                         group_id=GROUP_ID,
                         auto_offset_reset=AUTO_OFFSET_RESET, client_id=CLIENT_ID,
                         max_poll_interval_ms=Max_Poll_Interval_Ms,
                         request_timeout_ms=int(Max_Poll_Interval_Ms * 1.2) + 1000,
                         session_timeout_ms=int(Max_Poll_Interval_Ms * 1.2),
                         connections_max_idle_ms=int(Max_Poll_Interval_Ms * 1.2) + 5000,
                         enable_auto_commit=False, max_poll_records=Max_Poll_Records,
                         metadata_max_age_ms=Max_Poll_Interval_Ms, heartbeat_interval_ms=Max_Poll_Interval_Ms
                         )

print("dddd")
for msg in consumer:
    request_dict = json.loads(msg.value)
    s_id = request_dict["vehicleAlarmResult"][0]["taskID"]
    plate = request_dict["vehicleAlarmResult"][0]["target"][0]["vehicle"]["plateNo"]["value"]
    print(s_id, plate)
    print("next")
