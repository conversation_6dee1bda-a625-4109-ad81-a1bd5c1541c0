from __future__ import annotations
import pandas as pd
from conf.model_labels import *


# ***部署AI审片时需要修改的配置***
class DeployConf:
    # **********************kafka配置***************************
    KAFKA_BROKERS = ["*************:9092"]  # kafka集群多个地址逗号隔开 ["broker1","broker2"]
    # **********************授权配置***************************
    # 授权模式 iiea/MR
    Sign_Mode = "iiea"
    # iiea授权配置TPC平台的IP、端口
    TPC_IP = "***********:1443"
    # 模型仓库MR授权
    MR_IP = ""
    AppKey = ""
    AppSecret = ""
    # **********************图片合成方式配置***************************
    # 违法图片合成方式取值及描述
    SPLIT_MODE_DESC = {111: "单图无拼接", 211: "2图横向拼接", 221: "2图竖向拼接", 311: "3图横向拼接", 312: "倒品字型",
                       313: "品字型", 321: "3图竖向拼接", 322: "品字型顺时针旋转90度", 323: "品字型逆时针旋转90度",
                       411: "4图横向拼接", 611: "6图横向拼接", 621: "6图竖向拼接", None: "自动切分"}
    # 根据违法类型配置默认的合成方式（以下为审片模型内部违法代码，无需修改）
    SPLIT_DICT = {
        "1625": 411,  # 闯红灯
        "1208": 411,  # 不按导向行驶
        "1345": 211,  # 违反禁止标示线
        "1301": None,  # 逆行
        "1357": 411,  # 不礼让行人
        "1352": None,  # 超速
        "1039": None,  # 违法停车
        "1101": None,  # 开车未系安全带
        "1223": None,  # 开车打电话
        "8013": None,  # 闯限行
        "7102": None,  # 占用公交车道
        "1018": 411,  # 占用非机动车道
        "1000": 411,  # 过滤指定车辆类型
    }
    BLACK_HEIGHT = 0.05  # 黑框高度占图片高度的比例,为None时通过自动切分计算高度
    # 根据违法类型配置黑框所在的位置：
    BLACK_POSITION_DICT = {
        # "1625":"up", （up:黑框在图片顶部 down:黑框在图片底部）
    }

    # 设定单独配置拼接方式生效的违法代码
    Split_Set_Type = ["1625", "1208"]
    # 单独配置卡口的拼接方式："卡口内码":611,
    Cross_Split_Set = {

    }
    # 单独配置相机的拼接方式："相机内码":611,
    Camera_Split_Set = {

    }

    # 修正图片处理序号
    PIC_JUDGE_INDEX = {"1625": [], "1208": [], "1357": [],
                       "1301": [], "1345": [], "1352": [],
                       "1039": [], "1101": [], "1223": [],
                       "8013": [], "7102": [], "1018": [],
                       "1000": []}


# 通用配置
class BaseConf(DeployConf):
    # 模型运行架构
    arch = 1  # 0:华为昇腾显卡，1:英伟达显卡
    # 多进程数
    Multi_Num = [1, 1]  # [使用显卡数,每张卡进程数]
    SAVE_RES_DIR = "../test_results"  # 审核图片结果分类保存路径
    IS_SAVE_VIS_PIC = True  # 是否保存可视化结果图片
    # 闯红灯测试的卡口
    Test_Cross_1625 = []
    # 闯红灯上线的卡口
    Online_Cross_1625 = "all"
    # 配置模型处理延时的最大时间（单位秒）
    Max_Delay_Seconds = 60 * 30
    # modelJudgeStatus推送结果的值
    FILTER_STATUS = [0, 1, 2]
    # 根据违法数据中url的数量判断是否为合成图(默认不开启)
    Judge_Url = False
    # 不适用Judge_Url判定的违法代码
    No_Judge_Url_Code = []
    # 各违法场景保存图片的最大值
    Max_Save_Num = 500
    # 收集图片最大数量
    Max_Get_Camera_Num = 5
    # 日志最大值MB
    LOG_MAX_SIZE = 150
    # 违停数据处理比例
    Vio_Park_Rate = 1  # 每多少条数据处理一次
    # 控制模型运行时间
    SET_RUN_TIME = -1  # 值为负数则一直运行
    # 闯红灯各相机最大处理数量
    Max_Camera_Num = -1
    # 车牌开头列表
    First_Plate_No = ["京", "沪", "津", "渝", "冀", "晋", "蒙", "辽", "吉", "黑", "苏", "浙", "皖", "闽", "赣",
                      "鲁", "豫", "鄂", "湘", "粤", "桂", "琼", "川", "贵", "云", "藏", "陕", "甘", "青", "宁",
                      "新", "港", "学", "使", "警", "澳", "挂", "军", "北", "南", "广", "沈", "兰", "成", "济",
                      "海", "民", "航", "空"]
    # 特殊车辆（白名单车辆列表）
    try:
        try:
            spec_cars_list = pd.read_csv('../data/spec_cars.csv')['plate'].to_list()
        except:
            spec_cars_list = pd.read_csv('../data/spec_cars.csv')['plate'].tolist()
    except:
        spec_cars_list = []
        # 实例分割模型需要检测车辆种类列表
    Blend_Judge_Vehicle = ["car_h", "car_b", "bus_h", "bus_b", "truck_h", "truck_b", "engineering_car_h",
                           "engineering_car_b", 'motorbike_h', 'motorbike_b', 'ambulance_h', 'ambulance_b',
                           'fire_engine_h',
                           'fire_engine_b', "police_car_h", "police_car_b"]
    # 车辆相似度过滤车头类
    Filter_Vehicle_Head = ["car_h", "bus_h", "truck_h", "engineering_car_h", 'ambulance_h', 'fire_engine_h',
                           "police_car_h"]
    Det_Head_Thresh = 0.42  # 车头检测阈值
    Head_IOU = 0.65  # 车头过滤IOU判定阈值

    # 违法代码匹配    审片内部代码:实际违法代码（取前四位）
    Vio_Type_Match = {
        '1625': ['1302', '1625'],  # 闯红灯
        '1208': ['1208', '6095'],  # 不按导向行驶
        '1345': ['1117', '1230', '1345'],  # 违反禁止标示线
        '1301': ['1301', '1373', '1734', '4702'],  # 逆行
        '1357': ['1357', '1358'],  # 不礼让行人
        '1352': ['1130', '1349', '1350', '1351', '1352', '1369', '1628', '1629', '1630', '1631', '1632', '1633', '1634',
                 '1635', '1636', '1650', '1721', '1722', '1723', '1724', '1725', '1726', '1727', '1728', '1729', '4305',
                 '4609', '4610', '4611', '4612', '4706', '4707', '4708', '4709', '4710', '4711', '4712', '4713', '6048',
                 '6050', '6093', '7626'],  # 超速
        '1039': ['1039'],  # 违法停车
        '1101': ['1101', '1120', '1240', '1244', '3019', '4101', '6011'],  # 开车未系安全带
        '1223': ['1223', '1362'],  # 开车打电话
        '8013': ['1116', '1229', '1344'],  # 闯限行
        '7102': ['1019'],  # 占用公交车道
        '1018': ['1018'],  # 占用非机动车道
        '1000': [],  # 过滤指定车辆类型
    }
    # 可用的违法场景
    Is_Allowed_Type = {
        "1625": True,  # 闯红灯
        "1208": True,  # 不按导向行驶
        "1345": True,  # 违反禁止标示线
        "1301": True,  # 逆行
        "1357": True,  # 不礼让行人
        "1352": True,  # 超速
        "1039": True,  # 违法停车
        "1101": True,  # 开车未系安全带
        "1223": True,  # 开车打电话
        "8013": True,  # 闯限行
        "7102": True,  # 占用公交车道
        "1018": True,  # 占用非机动车道
        '1000': True,  # 过滤指定车辆类型
    }
    # 审片结果代码及描述
    JudgeResult = {
        # 闯红灯
        "1625": {
            "vio_straight": "目标车辆直行且直行交通信号灯为红灯",
            "vio_left": "目标车辆左转且左转交通信号灯为红灯",
            "vio_right": "目标车辆右转且右转交通信号灯为红灯",
            "vio_004": "第三图未找到目标车，根据特殊规则判定违法",
            "vio": "正片高检出模式判定为正片",
            "vio_002": "行驶方向结果不可信置为正片",
            "no_vio": "目标车行驶方向与之对应的交通信号灯一三图交集不存在红灯",
            "no_vio_001": "一图与三图的交通信号灯识别结果无交集或无红灯",
            "no_vio_002": "抓拍相机识别的车牌首字符异常",
            "no_vio_003": "一图和二图都不能通过识别车牌匹配到目标车辆",
            "no_vio_004": "第三图未找到目标车",
            "no_vio_005": "一图到二图或二图到三图，目标车未产生明显位移",
            "no_vio_006": "停止线判定",
            "no_vio_007": "同一灯盘的红灯和绿灯或黄灯同亮",
            "no_vio_008": "红灯偏黄或抓拍为黄灯",
            "no_vio_009": "抓拍设备识别的车牌开头字符为：'无'、'未'或车牌为空",
            "no_vio_010": "特殊车辆（警车、应急车、救护车）过滤",
            "no_vio_011": "图片经切分后传入模型不足四张图",
            "no_vio_012": "前三张图片缩放错误",
        },
        # 不按导向行驶
        "1208": {
            "vio_straight": "目标车辆直行且所在车道导向箭头的方向不存在直行",
            "vio_left": "目标车辆左转且所在车道导向箭头的方向不存在左转",
            "vio_right": "目标车辆右转且所在车道导向箭头的方向不存在右转",
            "vio_004": "第三图未找到目标车，根据特殊规则判定违法",
            "vio_002": "行驶方向结果不可信置为正片",
            "vio": "正片高检出模式判定为正片",
            "no_vio": "目标车所在车道识别的箭头线方向包含目标车行驶方向",
            "no_vio_001": "目标车辆所在车道未检测到导向箭头",
            "no_vio_002": "抓拍相机识别的车牌首字符异常",
            "no_vio_003": "一图和二图都不能通过识别车牌匹配到目标车辆",
            "no_vio_004": "第一图或第三图未找到目标车",
            "no_vio_005": "一图到二图或二图到三图，目标车未产生明显位移",
            "no_vio_006": "停止线判定",
            "no_vio_007": "压线判定",
            "no_vio_009": "抓拍设备识别的车牌开头字符为：'无'、'未'或车牌为空",
            "no_vio_010": "特殊车辆（警车、应急车、救护车）过滤",
            "no_vio_011": "图片经切分后传入模型不足四张图",
            "no_vio_012": "前三张图片缩放错误",
        },
        # 违反禁止标示线
        "1345": {
            "vio": "压线违法",
            "vio_002": "压线违法2",
            "vio_003": "压线违法3",
            "vio_004": "图片经切分后数量不足，无法分析透传违法",
            "no_vio": "无压线违法",
            "no_vio_001": "未检测到有效地标信息",
            "no_vio_002": "抓拍相机识别的车牌首字符异常",
            "no_vio_006": "无法通过车牌识别匹配目标车辆",
            "no_vio_007": "模型识别的车牌首字符异常",
            "no_vio_009": "抓拍设备识别的车牌开头字符为：'无'、'未'或车牌为空",
            "no_vio_010": "特殊车辆（警车、应急车、救护车）过滤",
        },
        # 逆行
        "1301": {
            "vio": "逆行违法",
            "vio_003": "图片经切分后数量不足，无法分析透传违法",
            "no_vio": "无逆行违法",
            "no_vio_001": "模型未识别到辅助判定的交通标志",
            "no_vio_002": "抓拍相机识别的车牌首字符异常",
            "no_vio_003": "目标车辆行驶方向与导向箭头一致",
            "no_vio_004": "目标车辆行驶方向与双黄线相对位置判定为废片",
            "no_vio_005": "目标车辆行驶方向与双黄线中心点位置判定为废片",
            "no_vio_006": "无法通过车牌识别匹配目标车辆",
            "no_vio_007": "模型识别的车牌首字符异常",
            "no_vio_009": "抓拍设备识别的车牌开头字符为：'无'、'未'或车牌为空",
            "no_vio_010": "特殊车辆（警车、应急车、救护车）过滤",
        },
        # 不礼让行人
        "1357": {
            "vio": "不礼让行人违法",
            "no_vio": "行人和目标车辆的距离不在判定阈值范围内",
            "no_vio_001": "没有一张图片检测到行人",
            "no_vio_002": "抓拍相机识别的车牌首字符异常",
            "no_vio_006": "无法通过车牌识别匹配目标车辆",
            "no_vio_007": "第三图未能匹配到车辆",
            "no_vio_009": "抓拍设备识别的车牌开头字符为：'无'、'未'或车牌为空",
            "no_vio_010": "特殊车辆（警车、应急车、救护车）过滤",
        },
        # 超速
        "1352": {
            "vio": "超速违法",
            "no_vio": "未能通过车牌匹配到目标车辆",
            "no_vio_002": "抓拍相机识别的车牌首字符异常",
            "no_vio_009": "抓拍设备识别的车牌开头字符为：'无'、'未'或车牌为空",
            "no_vio_010": "特殊车辆（警车、应急车、救护车）过滤",
        },
        # 违法停车
        "1039": {
            "vio_001": "每张图车牌均匹配成功且至少有一张图完成精确匹配",
            "vio_002": "每张图车牌均匹配成功",
            "vio_003": "第一张图/第二张图和最后一张图车牌能匹配成功",
            "vio_004": "前三张图至少有一张图车牌能匹配成功",
            "vio_005": "高检出率模式，不进行车牌匹配判定",
            "no_vio": "无违法停车",
            "no_vio_002": "抓拍相机识别的车牌首字符异常",
            "no_vio_009": "抓拍设备识别的车牌开头字符为：'无'、'未'或车牌为空",
            "no_vio_010": "特殊车辆（警车、应急车、救护车）过滤",
        },
        # 开车未系安全带
        "1101": {
            "vio": "主驾和副驾都未系安全带",
            "no_vio": "无未系安全带违法行为",
            "vio_001": "未检测到人根据配置判定违法",
            "vio_003": "主驾未系安全带",
            "vio_004": "副驾未系安全带",
            "vio_005": "副驾未系安全带（副驾违法代码过滤）",
            "no_vio_001": "未检测到人",
            "no_vio_002": "抓拍相机识别的车牌首字符异常",
            "no_vio_006": "无法通过车牌识别匹配目标车辆",
            "no_vio_009": "抓拍设备识别的车牌开头字符为：'无'、'未'或车牌为空",
            "no_vio_010": "特殊车辆（警车、应急车、救护车）过滤",
        },
        # 开车打电话
        "1223": {
            "vio": "主驾开车打电话",
            "vio_001": "未检测到人根据配置判定违法",
            "no_vio": "未识别到开车打电话行为",
            "no_vio_001": "未检测到人",
            "no_vio_002": "抓拍相机识别的车牌首字符异常",
            "no_vio_006": "无法通过车牌识别匹配目标车辆",
            "no_vio_009": "抓拍设备识别的车牌开头字符为：'无'、'未'或车牌为空",
            "no_vio_010": "特殊车辆（警车、应急车、救护车）过滤",
        },
        # 闯限行
        "8013": {
            "vio": "货车闯限行违法",
            "no_vio": "目标车辆非货车",
            "no_vio_001": "目标车牌未匹配成功",
            "no_vio_002": "抓拍相机识别的车牌首字符异常",
            "no_vio_003": "抓拍相机识别车牌有误",
            "no_vio_004": "未检测到车辆",
            "no_vio_009": "抓拍设备识别的车牌开头字符为：'无'、'未'或车牌为空",
            "no_vio_010": "特殊车辆（警车、应急车、救护车）过滤",
        },
        # 占用公交车道
        "7102": {
            "vio": "占用公交车道违法",
            "no_vio": "目标车辆为公交车",
            "no_vio_002": "抓拍相机识别的车牌首字符异常",
            "no_vio_006": "无法通过车牌识别匹配目标车辆",
            "no_vio_009": "抓拍设备识别的车牌开头字符为：'无'、'未'或车牌为空",
            "no_vio_010": "特殊车辆（警车、应急车、救护车）过滤",
        },
        # 占用非机动车道
        "1018": {
            "vio": "非法占用非机动车道",
            "no_vio": "目标车辆为非机动车",
            "no_vio_001": "目标车牌未匹配成功",
            "no_vio_002": "抓拍相机识别的车牌首字符异常",
            "no_vio_003": "抓拍相机识别车牌有误",
            "no_vio_004": "模型识别的车牌首字符与抓拍相机不一致",
            "no_vio_005": "模型识别的车牌首字符异常",
            "no_vio_009": "抓拍设备识别的车牌开头字符为：'无'、'未'或车牌为空",
            "no_vio_010": "特殊车辆（警车、应急车、救护车）过滤",
        },
        # 过滤车辆类型场景
        "1000": {
            "vio": "非过滤车型",
            "no_vio": "目标车辆为过滤车型",
            "no_vio_001": "目标车牌未匹配成功",
            "no_vio_002": "抓拍相机识别的车牌首字符异常",
            "no_vio_003": "抓拍相机识别车牌有误",
            "no_vio_004": "未检测到车辆",
            "no_vio_005": "模型识别的车牌首字符异常",
            "no_vio_006": "模型识别的车牌首字符与抓拍相机不一致",
            "no_vio_009": "抓拍设备识别的车牌开头字符为：'无'、'未'或车牌为空",
            "no_vio_010": "特殊车辆（警车、应急车、救护车）过滤",
        }
    }


# 判罚规则配置
class JudgeConf(BaseConf):
    Ambulance_Thresh = 0.5  # 0.56 救护车判定废片阈值，置为-1则不作废救护车数据
    Police_Plate_Thresh = 0.85  # 车牌警字置信度阈值，置为-1则不作废警车数据
    Fire_Plate_Thresh = 0.85  # 车牌应急置信度阈值
    Police_Filter = True
    Ambulance_Filter = True
    Fire_Filter = True
    To_IIEAVeh_Cfg = [str(int(Police_Filter)), str(int(Ambulance_Filter)), str(int(Fire_Filter))]
    # 停止线判定
    Stop_Line_Height = [0.25, 0.7]  # 简单停止线规则
    Stop_Line_Judge = [1]  # 被参数替代
    Car1_Stop_Line_Rate = 0.64  # 一图停止线判定高度倍数
    No_Car3_Is_Waste = True  # 三图未匹配到目标车辆是否判定废片
    # *********************闯红灯*********************
    # 闯红灯按相机编号(cameraIndexCode)预设红绿灯方向 TODO
    Set_Light_Direct = {

    }
    # 红绿灯模型阈值
    LIGHT_THRESH = 0.05
    # *********************不按导向*********************
    Stop_Line_1208 = False  # 是否启用停止线判定
    Cover_Line_Rate = 1.25  # 压线模块左右箭头的距离比例
    Filter_No_Arrow = True  # 未检测到导向箭头是否过滤
    # *********************违停*********************
    Vio_Park_High_Recall = False  # 高检出率模式(不匹配车牌)
    # *********************安全带/打电话*********************

    # *********************货车闯限行*********************
    # 实例分割模型禁行类别
    stop_car_type_list = ['truck_b', 'truck_h']
    # 实例分割模型过滤类别
    Filter_Vehicle_Type = ['motorbike_b', 'motorbike_h']  # 低速车 参数中已替代
    # yolov7模型禁行类别
    yolov7_stop_car_type_list = ['truck']
    # 货车限行违法高检出率
    Truck_Vio_Recall = False
    # 限行场景模型结果去重的IOU阈值
    IOU_THRESH = 0.75
    # 闯禁行场景判断itrc数据
    Judge_Itrc = False
    # 闯禁行场景过滤非货车数据
    Filter_No_Truck = True
    # *********************机占非*********************
    First_Plate_Thresh = 0.8  # 机占非车牌首字符置信度阈值
    Plate_Fuzzy_Thresh = [None, None]  # 机占非车牌模糊匹配阈值
    # *********************过滤指定车辆类型*********************
    Filter_Vehicle_Type_Yolo = ["motorcycle"]
    Filter_Vehicle_Type_Blend = ['motorbike_b', 'motorbike_h']
    # *********************压线*********************
    # *********************逆行*********************
    # *********************不礼让行人*********************
    # *********************超速*********************
    # *********************占用公交车道*********************


# 效果调优配置
class OptConf(JudgeConf):
    # 各违法类型审核模式  0:普通模式（少遗漏正片）   1：强过滤模式（多过滤废片）
    Judge_Mode = {
        "1625": 0,  # 闯红灯
        "1208": 0,  # 不按导向行驶
        "1345": 0,  # 违反禁止标示线
        "1301": 0,  # 逆行
        "1357": 0,  # 不礼让行人
        "1352": 0,  # 超速
        "1039": 0,  # 违法停车
        "1101": 0,  # 开车未系安全带
        "1223": 0,  # 开车打电话
        "8013": 0,  # 闯限行
        "7102": 0,  # 占用公交车道
        "1018": 0,  # 占用非机动车道
        "1000": 0,  # 过滤指定车辆类型
    }
    # 各违法类型两种模式的判罚置信度阈值 [高检出模式阈值,强过滤模式阈值]
    Judge_Thresh = {
        "1625": [None, None],  # 闯红灯
        "1208": [None, None],  # 不按导向行驶
        "1345": [[0.2, 15], [0.55, 5]],  # 违反禁止标示线([[交通标志模型阈值,车辆与地标线的像素距离],])
        "1301": [None, None],  # 逆行
        "1357": [2, 1.5],  # 不礼让行人(车辆与行人横向距离小于车宽的n倍)
        "1352": [[3, 2], [4, 1]],  # 超速(车牌模糊匹配阈值)
        "1039": [[3, 2], [4, 1]],  # 违法停车(车牌模糊匹配阈值)
        "1101": [0.25, 0.92],  # 开车未系安全带(模型阈值)
        "1223": [0.3, 0.75],  # 开车打电话(模型阈值)
        "8013": [[0.2, 0.2], [0.35, 0.5]],  # 闯限行([[yolov7置信度阈值,blendMask置信度阈值],])
        "7102": [0.2, 0.45],  # 占用公交车道(yolov7置信度阈值)
        "1018": [0.2, 0.5],  # 占用非机动车道(blendMask置信度阈值)
        '1000': [[0.2, 0.2], [0.35, 0.45]],  # 过滤指定车辆类型([[yolov7置信度阈值,blendMask置信度阈值],])
    }
    # 单独配置审核策略生效的违法代码
    Judge_Mode_Set_Type = ["1625", "1208"]
    # 另外配置卡口的审核策略（0:普通模式 1:强过滤模式）
    Judge_Mode_Cross_Set = {
        # "卡口编号(crossingIndexCode)":1,
    }
    # 另外配置使用强过滤策略的违法场景和相机（0:普通模式 1:强过滤模式）
    Judge_Mode_Camera_Set = {
        # "相机编号(cameraIndexCode)":1,
    }

    # 按卡口配置不分析的特殊数据(status为0)
    No_Judge_Cross = {
        # "1625":["卡口编号(crossingIndexCode)"],
    }
    # 按相机配置不分析的特殊数据(status为0)
    No_Judge_Camera = {
        # "1625":["相机编号(cameraIndexCode)"],
    }

    # 按相机过滤废片率高的路口数据(透传为废片)
    Filter_Waste_Camera = {
        # "1625":["相机编号(cameraIndexCode)"],
    }
    # 逆行场景版本切换
    NX_VERSION = 1
    # 救护车辆判定废片阈值
    Ambulance_Head_Thresh = 0.587
    Special_Car_Vis_Thresh = 0.2


class DirTestConf:
    # **********************文件夹测试模块配置****************************
    # imgs_dir_test.py
    # 图片命名格式示例：同组违法图片编号_车牌号码_时间序号.jpg
    SPLIT_STR = "_"
    ID_INDEX = 0
    PLATE_INDEX = 0
    # 配置各违法类型一组违法图片的单图数量
    SINGLE_PIC_NUM = {
        "1625": 4,  # 闯红灯
        "1208": 4,  # 不按导向行驶
        "1345": 2,  # 违反禁止标示线
        "1301": 3,  # 逆行
        "1357": 4,  # 不礼让行人
        "1352": 1,  # 超速
        "1039": 4,  # 违法停车
        "1101": 1,  # 开车未系安全带
        "1223": 1,  # 开车打电话
        "8013": 1,  # 闯限行
        "7102": 3,  # 占用公交车道
        "1018": 3,  # 占用非机动车道
        '1000': 4,  # 过滤指定车辆类型
    }


# 功能模型基础配置
class ModelBaseConf(BaseConf):
    model_thresh = 0.2
    iou_thresh = 0.45
    stride = 32
    weight = ""
    prepare_shape = (640, 640)
    model_type = "det"  # class:分类, det:检测, seg：分割, ocr：识别
    label_dict = {}
    det_class = None
    superclass = {
        "vehicle": ["car", "truck", "bus", "motorcycle"],
        "vehicle2": ["car_b", "car_h", "truck_b", "truck_h", "bus_b", "bus_h", "police_car_h", "police_car_b",
                     "engineering_car_h", "engineering_car_b", "ambulance_h", "ambulance_b"],
        "merge_veh": ["car", "truck", "bus", "motorcycle", "car_b", "car_h", "truck_b", "truck_h", "bus_b", "bus_h",
                      "police_car_h", "police_car_b",
                      "engineering_car_h", "engineering_car_b", "ambulance_h", "ambulance_b"],
        "head_veh": ["car_h", "bus_h", "truck_h", "engineering_car_h", 'ambulance_h', 'fire_engine_h',
                     "police_car_h"],
        "ride": ["motorcycle", 'bicycle'],
        "light": ['green_s', 'green_r', 'green_l', 'red_s', 'red_r', 'red_l', 'yellow_s', 'yellow_r', 'yellow_l'],
        "lane": ["lane1", "lane2"],
        "arrow": ["arrow_l", "arrow_s", "arrow_r"],
        "stop_line": ["stop_line"],
        "no_cover": ["lane1", "lane2", "stop_area"],  # 禁行类别
        "special_vehicle": ["ambulance_h", "ambulance_b"],
        "bkg_fusion": ["car_b", "car_h", "truck_b", "truck_h", "bus_b", "bus_h", "police_car_h", "police_car_b",
                       "engineering_car_h", "engineering_car_b", "ambulance_h", "ambulance_b", "motorcycle_h",
                       "motorcycle_b"],
        "tfc_element": ["lane1", "lane2", "stop_area", "arrow_l", "arrow_s", "arrow_r", "stop_line"],
        "all_phone": ["phone", "phone_s"]
    }  # TODO:超类配置


weights_root = "../libs/weights"  # 模型权重文件根目录


# 车辆行人检测模型
class VehPersonModelConf(ModelBaseConf):
    weight = f'{weights_root}/yolov7-e6e'
    prepare_shape = (832, 1472)
    label_dict = COCO_DICT
    det_class = ["car", "truck", "bus", "motorcycle", "bicycle", "person"]
    model_thresh = 0.15
    stride = 64


# 车牌检测模型
class PlateDetModelConf(ModelBaseConf):
    weight = f'{weights_root}/plate_detect'
    prepare_shape = (640, 640)
    model_thresh = 0.1
    label_dict = PLATE_TYPE_DICT


# 车牌识别模型
class PlateRecModelConf(ModelBaseConf):
    model_type = "ocr"
    weight = f'{weights_root}/plate_rec'
    prepare_shape = (48, 168)
    is_rec_plate = True


# 实例分割模型
class SegModelConf(ModelBaseConf):
    model_type = "seg"
    weight = f'{weights_root}/yolov5_epoch137'
    prepare_shape = (768, 1280)
    label_dict = TRAFFIC_DICT
    # det_class = ModelBaseConf.superclass["vehicle2"]
    model_thresh = 0.1


# 相似度匹配模型
class SimilarModelConf(ModelBaseConf):
    weight = f'{weights_root}/sim_model'
    prepare_shape = (224, 224)


# 红绿灯识别模型
class LightModelConf(ModelBaseConf):
    weight = f'{weights_root}/light'
    prepare_shape = (832, 1472)
    label_dict = LIGHT_DICT
    model_thresh = 0.1
    stride = 64


class PhoneHandConf(ModelBaseConf):
    weight = f'{weights_root}/phone_best'
    prepare_shape = (640, 640)
    label_dict = Phone_Hand
    model_thresh = 0.1
    iou_thresh = 0.3
    stride = 64


# 压线模型
class CoverLineModelConf(ModelBaseConf):
    model_type = "class"
    weight = f'{weights_root}/cnn_class_model'
    prepare_shape = (224, 224)
    label_dict = COVER_LINE_DICT


# 安全带/打电话分类模型
class BeltPhoneModelConf(ModelBaseConf):
    model_type = "class"
    weight = f'{weights_root}/EfficientNet_B7_n_hc'
    prepare_shape = (224, 224)
    label_dict = BELT_PHONE_DICT
    model_thresh = 0.25


# 配置审片服务需使用的功能模型
FuncModel_SET = {
    # "CoverLineModel": ('ClassModel', CoverLineModelConf),

    "VehPersonModel": ('DetModel', VehPersonModelConf),
    "PlateDetModel": ('PlateDetModel', PlateDetModelConf),
    "PlateRecModel": ('PlateRecModel', PlateRecModelConf),
    "LightModel": ('DetModel', LightModelConf),
    "SegModel": ('SegModel', SegModelConf),
    "BeltPhoneModel": ('ClassModel', BeltPhoneModelConf),
    "SimilarModel": ('SimilarModel', SimilarModelConf),
    "PhoneHandModel": ('DetModel', PhoneHandConf)
}


class VioJudgerConf:
    act_dict = {
        'request': ('RequestJudAct', 'RequestJudActConf'),
        'image': ('ImageJudAct', 'ImageJudActConf'),
        'light': ('LightJudAct', 'LightJudActConf'),  # 信号灯灯态判定
        'element': ('ElementDetAct', 'ElementDetActConf'),  # 交通元素检测
        'vehicle': ('VehicleJudAct', 'VehicleJudActConf'),  # 目标车辆匹配
        'drive_dir': ('DriveDirJudAct', 'DriveDirJudActConf'),  # 行驶方向判定
        'driver_vio': ('DriverVioJudAct', 'DriverVioJudActConf'),  # 车内人员违法识别
        'bkg_fusion': ('BkgFusionJudAct', 'BkgFusionJudActConf'),  # 无车背景图融合
        'vio_rule': ('VioRuleJudAct', 'VioRuleJudActConf'),  # 违法规则判定
    }
    rule_dict = {
        'merge_veh': 'MergeVehRule',  # 融合两个模型的车框
        'crop_veh': 'CropVehRule',  # 抠出车辆图片
        'plate_match': 'PlateMatchRule',  # 根据车牌匹配目标车辆
        'filter_veh': 'FilterVehRule',  # 通过车辆特征过滤特殊车辆
        'similar_match': 'SimilarMatchRule',  # 车辆相似度匹配
        'no_veh': 'NoVehRule',  # 车辆匹配判定
        'veh_markline': 'VehMarklineRule',  # 目标车与地标线判定
        'veh_move': 'VehMoveRule',  # 目标车明显位移判定
        'drvdir_light': 'DrvdirLightRule',  # 行驶方向和灯态关系判定
        'span_lane': 'SpanLaneRule',  # 目标车跨两车道判定
        'no_arrow': 'NoArrowRule',  # 有无导向箭头判定
        'drvdir_arrow': 'DrvdirArrowRule',  # 行驶方向和导向箭头关系判定
        'driver_vio': 'DriverVioRule',  # 车内人员违法判定
        'yield_walkman': 'YieldWalkmanRule',  # 不礼让行人判定
        'cover_line': 'CoverLineRule',  # 压线判定
        'wrong_drvdir': 'WrongDrvDirRule',  # 逆行判定
    }


# TODO:将配置参数移动到相应的动作配置里
class RequestJudActConf(VioJudgerConf):
    First_Plate_Judge = True  # 是否启用车牌首字符异常过滤


class ImageJudActConf(VioJudgerConf):
    pass


class LightJudActConf(VioJudgerConf):
    pass


class ElementDetActConf(VioJudgerConf):
    pass


class VehicleJudActConf(VioJudgerConf):
    pass


class DriveDirJudActConf(VioJudgerConf):
    pass


class DriverVioJudActConf(VioJudgerConf):
    # 开车副驾未系安全带是否违法(强判定) 为None时根据违法代码进行判定
    Copilot_Belt_Is_Vio = False
    # 副驾驶未系安全带违法的代码
    Copilot_Belt_Vio_Code = ["3019", "1240"]
    # 未识别到人是否判定废片
    No_Person_Is_Waste = True


class BkgFusionJudActConf(VioJudgerConf):
    pass


class VioRuleJudActConf(VioJudgerConf):
    pass


# 闯红灯场景配置类
class LightJudgerConf(VioJudgerConf):
    veh_rules = [
        {'rule': 'merge_veh', 'param': {'iou_thresh': 0.75, 'del_head_veh': True, 'del_head_veh_thresh': 0.42}},
        {'rule': 'crop_veh', 'param': {}},
        {'rule': 'plate_match',
         'param': {'det_thresh': 0.15, 'del_thresh': 0.95, 'del_thresh2': 0.93, 'small_plate_thresh': 380,
                   'plate_thresh': [3, 2]}},
        {'rule': 'filter_veh', 'param': {}},
        {'rule': 'similar_match', 'param': {'feature_match': True, 'match_thresh': 0.4, 'match_range': [0.32, 0.13]}},
        {'rule': 'no_veh', 'param': {'match_index': [], 'plate_match_num': 1}},
        {'rule': 'veh_move', 'param': {'iou_thresh': 0.95}},
        {'rule': 'veh_markline', 'param': {'stop_line_rule': True, 'cover_line_rule': False}},
    ]
    vio_rules = [
        {'rule': 'drvdir_light', 'param': {}},
    ]
    acts = [
        {'act': 'light', 'param': {'det_thresh': 0.1, 'judge_feature': False}},
        {'act': 'element',
         'param': {'add_det_model': True, 'yolo_det_thresh': 0.1, 'seg_det_thresh': 0.2, 'img_num': 3}},
        {'act': 'bkg_fusion', 'param': {'sort_merge': False, 'bkg_det': False,
                                        'det_cls': ['lane1', 'lane2', 'stop_line', 'arrow_s', 'arrow_r', 'arrow_l']}},
        {'act': 'vehicle', 'param': {'rule': veh_rules}},
        {'act': 'drive_dir', 'param': {}},
        {'act': 'vio_rule', 'param': {'rule': vio_rules}},
    ]


# 不按导向行驶场景配置类
class DrvdirArrowJudgerConf(VioJudgerConf):
    veh_rules = [
        {'rule': 'merge_veh', 'param': {'iou_thresh': 0.75, 'del_head_veh': True, 'del_head_veh_thresh': 0.42}},
        {'rule': 'crop_veh', 'param': {}},
        {'rule': 'plate_match',
         'param': {'det_thresh': 0.15, 'del_thresh': 0.95, 'del_thresh2': 0.93, 'small_plate_thresh': 380,
                   'plate_thresh': [3, 2]}},
        {'rule': 'filter_veh', 'param': {}},
        {'rule': 'similar_match', 'param': {'feature_match': True, 'match_thresh': 0.4, 'match_range': [0.32, 0.13]}},
        {'rule': 'no_veh', 'param': {'match_index': [], 'plate_match_num': 1}},
        {'rule': 'veh_move', 'param': {'iou_thresh': 0.95}},
        {'rule': 'veh_markline', 'param': {'stop_line_rule': False, 'cover_line_rule': True}},
    ]
    vio_rules = [
        {'rule': 'drvdir_arrow', 'param': {}},
    ]
    acts = [
        {'act': 'element',
         'param': {'add_det_model': True, 'yolo_det_thresh': 0.1, 'seg_det_thresh': 0.1, 'img_num': 3}},
        {'act': 'bkg_fusion', 'param': {'sort_merge': False, 'bkg_det': True,
                                        'det_cls': ['lane1', 'lane2', 'stop_line', 'arrow_s', 'arrow_r', 'arrow_l']}},
        {'act': 'vehicle', 'param': {'rule': veh_rules}},
        {'act': 'drive_dir', 'param': {}},
        {'act': 'vio_rule', 'param': {'rule': vio_rules}},
    ]


# 安全带打电话场景配置类
class BeltPhoneJudgerConf(VioJudgerConf):
    veh_rules = [
        {'rule': 'merge_veh', 'param': {'iou_thresh': 0.75, 'del_head_veh': True, 'del_head_veh_thresh': 0.42}},
        {'rule': 'crop_veh', 'param': {}},
        {'rule': 'plate_match',
         'param': {'det_thresh': 0.15, 'del_thresh': 0.95, 'del_thresh2': 0.93, 'small_plate_thresh': 380}},
        {'rule': 'filter_veh', 'param': {}},
        {'rule': 'similar_match', 'param': {'feature_match': True, 'match_thresh': 0.4, 'match_range': [0.32, 0.13]}},
    ]
    acts = [
        {'act': 'element',
         'param': {'add_det_model': True, 'yolo_det_thresh': 0.15, 'seg_det_thresh': 0.2}},
        {'act': 'vehicle', 'param': {'rule': veh_rules, 'param1': 0.4}},
        {'act': 'driver_vio', 'param': {'cls_thresh': 0.35}},
    ]


# 压线场景
class CoverJudgerConf(VioJudgerConf):
    veh_rules = [
        {'rule': 'merge_veh', 'param': {'iou_thresh': 0.75, 'del_head_veh': True, 'del_head_veh_thresh': 0.42}},
        {'rule': 'crop_veh', 'param': {}},
        {'rule': 'plate_match',
         'param': {'det_thresh': 0.15, 'del_thresh': 0.95, 'del_thresh2': 0.93, 'small_plate_thresh': 380}},
        {'rule': 'filter_veh', 'param': {}},
        {'rule': 'similar_match', 'param': {'feature_match': False, 'match_thresh': 0.4, 'match_range': [0.32, 0.13]}},
    ]
    vio_rules = [
        {'rule': 'cover_line', 'param': {'dist_thresh': 3, 'bias_rate': 1.5, 'min_pixel': 50}},
    ]
    # 动作
    acts = [
        {'act': 'element',
         'param': {'add_det_model': False, 'yolo_det_thresh': 0.15, 'seg_det_thresh': 0.2}},
        {'act': 'vehicle', 'param': {'rule': veh_rules}},
        {'act': 'bkg_fusion', 'param': {'det_thresh': 0.1, "det_cls": ["lane1", "lane2", "stop_area"]}},
        {'act': 'vio_rule', 'param': {'rule': vio_rules}}
    ]


# 逆行场景
class WrongDirJudgerConf(VioJudgerConf):
    veh_rules = [
        {'rule': 'merge_veh', 'param': {'iou_thresh': 0.75, 'del_head_veh': True, 'del_head_veh_thresh': 0.42}},
        {'rule': 'crop_veh', 'param': {}},
        {'rule': 'plate_match',
         'param': {'det_thresh': 0.15, 'del_thresh': 0.95, 'del_thresh2': 0.93, 'small_plate_thresh': 380}},
        {'rule': 'filter_veh', 'param': {}},
        {'rule': 'similar_match', 'param': {'feature_match': False, 'match_thresh': 0.4, 'match_range': [0.32, 0.13]}},
    ]
    vio_rules = [
        {'rule': 'wrong_drvdir', 'param': {'dist_rate': 0.45}},
    ]
    # 动作
    acts = [
        {'act': 'element',
         'param': {'add_det_model': False, 'yolo_det_thresh': 0.15, 'seg_det_thresh': 0.2, 'filter_feature': True}},
        {'act': 'vehicle', 'param': {'rule': veh_rules}},
        {'act': 'bkg_fusion', 'param': {'det_thresh': 0.1, 'sort_merge': True,
                                        'det_cls': ["lane1", 'lane2', "arrow_l", "arrow_s", "arrow_r", 'arrow_s_t',
                                                    'arrow_l_t', 'arrow_r_t']}},
        {'act': 'vio_rule', 'param': {'rule': vio_rules}}
    ]


def create_conf(conf_name):
    return eval(conf_name)
