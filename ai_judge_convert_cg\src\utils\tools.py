import shutil
import sys

import cv2
import numpy as np

sys.path.append("../..")
from conf.config import DeployConf
import logging
import logging.config
import os
import time
import traceback
from datetime import datetime
import requests
from enum import Enum
from typing import Callable
from src.utils.time_util import TimeUtil
import pickle


# class Factory: TODO:合并后需导入所有类,不值得合并
#     @staticmethod
#     def create_model(model_name, model_arch, device, conf):
#         return eval(model_name)(model_arch, device, conf)
#
#     @staticmethod
#     def create_module(module_name, act_name, conf, param, model_dict):
#         return eval(module_name)(act_name, conf, param, model_dict)


def split_img(img, n, black_height=None, black_pos=None):
    h, w, _ = img.shape
    if black_pos is None:
        black_h = 0
    elif black_height is None:
        black_h = int(DeployConf.BLACK_HEIGHT * h) if DeployConf.BLACK_HEIGHT else 0
    else:
        black_h = int(black_height * h)
    img_list = []
    # 单图无拼接
    if n == 111:
        img_list = [img.copy()]
    # 2图横向拼接
    elif n == 211:
        cx = w // 2
        img1 = img[:, :cx]
        img2 = img[:, cx:]
        img_list = [img1, img2]
    # 2图竖向拼接
    elif n == 221:
        split_h = h // 2
        img1 = img[:split_h]
        img2 = img[split_h:]
        img_list = [img1, img2]
    # 3图横向拼接
    elif n == 311:
        split_w = w // 3
        img1 = img[:, :split_w]
        img2 = img[:, split_w:2 * split_w]
        img3 = img[:, 2 * split_w:]
        img_list = [img1, img2, img3]
    # 倒品字型
    elif n == 312:
        cx = w // 2
        cy = h // 2
        img1 = img[:cy, :cx]
        img2 = img[:cy, cx:]
        img3 = img[cy:]
        img_list = [img1, img2, img3]
    # 品字型
    elif n == 313:
        cx = w // 2
        cy = h // 2
        img1 = img[:cy]
        img2 = img[cy:, :cx]
        img3 = img[cy:, cx:]
        img_list = [img1, img2, img3]
    # 品字型上2下1
    elif n == 314:
        cx = w // 2
        cy = h // 3
        img1 = img[:cy * 2]
        img2 = img[cy * 2:, :cx]
        img3 = img[cy * 2:, cx:]
        img_list = [img1, img2, img3]
    # 3图竖向拼接
    elif n == 321:
        split_h = h // 3
        img1 = img[:split_h]
        img2 = img[split_h:2 * split_h]
        img3 = img[2 * split_h:]
        img_list = [img1, img2, img3]
    # 品字型顺时针旋转90度
    elif n == 322:
        cx = w // 2
        cy = h // 2
        img1 = img[:cy, :cx]
        img2 = img[cy:, :cx]
        img3 = img[:, cx:]
        img_list = [img1, img2, img3]
    # 品字型逆时针旋转90度
    elif n == 323:
        cx = w // 2
        cy = h // 2
        img1 = img[:, :cx]
        img2 = img[:cy, cx:]
        img3 = img[cy:, cx:]
        img_list = [img1, img2, img3]
    # 4图横向拼接
    if n == 411:
        cx = w // 2
        cy = (h - black_h) // 2
        if black_pos == "up":
            img1 = img[black_h:cy + black_h, :cx]
            img2 = img[black_h:cy + black_h, cx:]
            img3 = img[cy + black_h:, :cx]
            img4 = img[cy + black_h:, cx:]
        else:
            img1 = img[:cy, :cx]
            img2 = img[:cy, cx:]
            img3 = img[cy:2 * cy, :cx]
            img4 = img[cy:2 * cy, cx:]
        img_list = [img1, img2, img3, img4]
    # 6图横向拼接
    elif n == 611:
        cx = w // 3
        cy = (h - black_h) // 2
        if black_pos == "up":
            img1 = img[black_h:cy + black_h, :cx]
            img2 = img[black_h:cy + black_h, cx:2 * cx]
            img3 = img[cy + black_h:, :cx]
            img4 = img[cy + black_h:, cx:2 * cx]
        else:
            img1 = img[:cy, :cx]
            img2 = img[:cy, cx:2 * cx]
            img3 = img[cy:2 * cy, :cx]
            img4 = img[cy:2 * cy, cx:2 * cx]
        img_list = [img1, img2, img3, img4]
    # 6图竖向拼接
    elif n == 621:
        cx = w // 2
        cy = (h - black_h) // 3
        if black_pos == "up":
            img1 = img[black_h:cy + black_h, :cx]
            img2 = img[black_h:cy + black_h, cx:]
            img3 = img[cy + black_h:cy * 2 + black_h, :cx]
            img4 = img[cy + black_h:cy * 2 + black_h, cx:]
            img5 = img[cy * 2 + black_h:, :cx]
            img6 = img[cy * 2 + black_h:, cx:]
        else:
            img1 = img[:cy, :cx]
            img2 = img[:cy, cx:]
            img3 = img[cy:2 * cy, :cx]
            img4 = img[cy:2 * cy, cx:]
            img5 = img[2 * cy:3 * cy, :cx]
            img6 = img[2 * cy:3 * cy, cx:]
        img_list = [img1, img2, img3, img4, img5, img6]

    return img_list


def cv_imread(path):  # 可以读取中文路径的图片
    img = cv2.imdecode(np.fromfile(path, dtype=np.uint8), -1)
    return img


def catch_error():
    exc_type, exc_value, exc_traceback = sys.exc_info()
    tb_info = traceback.extract_tb(exc_traceback)
    filename, line, func, text = tb_info[-1]
    error_str = f"error file:{filename}\nerror func:{func} line {line}\nerror text:{text} —— {exc_value}\n"
    return error_str


def log_error(logger, msg):
    error_msg = catch_error()
    logger.exception(f"{msg}\n{error_msg}")


log_root = "../logs"
bk_log_root = "../logs_bk"


def get_logging(name):
    log_path = f"{log_root}/{name}.txt"
    os.makedirs(log_root, exist_ok=True)
    LOGGING_CONFIG = {
        "version": 1,
        "formatters": {
            "default": {
                'format': '%(asctime)s %(filename)s %(lineno)s %(levelname)s %(message)s',
            },
            "plain": {
                "format": "%(message)s",
            },
        },
        "handlers": {
            "console": {
                "class": "logging.StreamHandler",
                "level": "DEBUG",
                "formatter": "default",
            },
            "console_plain": {
                "class": "logging.StreamHandler",
                "level": "DEBUG",
                "formatter": "plain"
            },
            "file": {
                "class": "logging.handlers.RotatingFileHandler",
                "level": "DEBUG",
                "filename": log_path,
                "formatter": "default",
                "mode": "a",
                "maxBytes": 10 * 1024 * 1024,
                "backupCount": 10
            }
        },
        "loggers": {
            "console_logger": {
                "handlers": ["console"],
                "level": "DEBUG",
                "propagate": False,
            },
            "console_plain_file_logger": {
                "handlers": ["console_plain", "file"],
                "level": "DEBUG",
                "propagate": False,
            },
            "file_logger": {
                "handlers": ["file"],
                "level": "DEBUG",
                "propagate": False,
            }
        },
        "disable_existing_loggers": False,
    }
    return LOGGING_CONFIG

def backup_logs():
    time_format = '%Y-%m-%d_%H-%M-%S'
    # 备份日志
    if os.path.exists(bk_log_root):
        bk_list = os.listdir(bk_log_root)
        while len(bk_list) >= 3:
            stamp_list = [TimeUtil.str2timestamp(_, time_format) for _ in bk_list]
            min_idx = np.argmin(stamp_list)
            shutil.rmtree(f"{bk_log_root}/{bk_list[min_idx]}")
            bk_list.pop(min_idx)
    if os.path.exists(log_root):
        os.makedirs(bk_log_root, exist_ok=True)
        shutil.copytree(log_root, f"{bk_log_root}/{TimeUtil.now_str(time_format)}")
        shutil.rmtree(log_root)
def init_logger(process_type=None, show_in_console=True):
    PID = os.getpid()
    if process_type == "main":
        name = f"main_{PID}"
        backup_logs()
    elif process_type is not None:
        name = f"{process_type}_{PID}"
    else:
        name = f"sub_{PID}"
    logging_config = get_logging(name)
    # 使用唯一的 logger 名称
    unique_name = f"{name}_logger"
    logging_config["loggers"][unique_name] = {
        "handlers": ["console_plain", "file"] if show_in_console else ["file"],
        "level": "DEBUG",
        "propagate": False,
    }

    logging.config.dictConfig(logging_config)

    return logging.getLogger(unique_name)


import functools


def exception_handler(return_value=""):
    """
    创建一个装饰器工厂，用于为方法添加异常处理，
    在发生异常时返回指定的字符串。

    参数:
        return_value (str): 发生异常时返回的字符串，默认为空字符串
    """

    def decorator(func):
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            try:
                return func(*args, **kwargs)
            except Exception as e:
                logger = init_logger(process_type="data", show_in_console=False)
                # 记录错误信息
                logger.error(f"Exception in {func.__name__}: {e}")
                # 返回指定的字符串
                return return_value

        return wrapper

    return decorator


class ExceptCode(Enum):
    """定义API请求错误码"""
    REQUEST_PARSE_DATA = "1001"
    REQUEST_PARSE_PARAM = "1002"


class CustomExcept(Exception):
    """自定义错误类型"""

    def __init__(self, code: ExceptCode, detail: str, caller: str = None):
        super().__init__(detail)
        self.code = code
        self.detail = detail
        self.callers = [caller] if caller else []

    def __str__(self):
        callers_str = ','.join(self.callers)
        return str({'code': self.code, 'detail': self.detail, 'callers': callers_str})

    def add_caller(self, caller: str):
        self.callers.insert(0, caller)

    def to_dict(self):
        """返回错误信息的字典表示"""
        return {'code': self.code.value, 'detail': self.detail}


def throw_exception(except_code: ExceptCode) -> Callable:
    """捕获异常并抛出指定编码的异常，意在统一异常类型，可用于创建装饰器
    Args:
        except_code: 异常编码，枚举类型
    Returns:
        decorator: 装饰器函数
    Raises:
        CustomExcept: 自定义异常类
    """

    def decorator(func):
        def wrapper(*args, **kwargs):
            try:
                return func(*args, **kwargs)
            except Exception as e:
                if isinstance(e, CustomExcept):
                    e.add_caller(func.__name__)
                    raise e
                else:
                    raise CustomExcept(code=except_code, detail=str(e), caller=func.__name__)

        return wrapper

    return decorator


def load_image_from_url(url):
    logger = init_logger(process_type="url", show_in_console=False)
    img = None
    for i in range(5):
        try:
            # 发送HTTP请求获取图片数据
            response = requests.get(url)
            # 将图片数据转换为numpy数组
            img_array = np.array(bytearray(response.content), dtype=np.uint8)
            # 使用OpenCV解码图片
            img = cv2.imdecode(img_array, cv2.IMREAD_COLOR)
        except:
            log_error(logger, "url图片获取失败！")
        if img is not None:
            break
        logger.error(f"图片读取失败！重试第{i + 1}次")
        time.sleep(0.5)

    return img


class AutoSplit:
    @staticmethod
    def find_bounding(img, row_or_col):  # img为灰度图；row_or_col 值为0或1；取值为0代表计算水平拼接图像数,取值为1代表计算竖直拼接图像数
        if row_or_col == 0:
            sub = np.sum(np.abs(img[:, :-1] / 255 - img[:, 1:] / 255), row_or_col)
            length = img.shape[1]
        else:
            sub = np.sum(np.abs(img[:-1, :] / 255 - img[1:, :] / 255), row_or_col)
            length = img.shape[0]
        sub_norm = (sub - np.mean(sub)) / np.std(sub)
        peak_max = np.max(sub_norm)
        thresh = 5.5
        if peak_max < thresh:
            return [], 0
        else:
            peak_loc = np.where(sub_norm >= thresh)[0]
            peak_arr = np.array(peak_loc + 1)
            return peak_arr, length

    @staticmethod
    def cut_loc(p_l, length, mode=0):
        idx = np.zeros(len(p_l))
        p_l_norm = [x / length for x in p_l]  # /length

        for i in range(len(p_l)):
            if 0.03 < p_l_norm[i] <= 0.12:
                if mode == 1:
                    idx[i] = -1
            elif 0.97 > p_l_norm[i] >= 0.88:
                if mode == 1:
                    idx[i] = -2
            elif (p_l_norm[i] > 0.2) and (p_l_norm[i] < 0.4167):
                idx[i] = 1
            elif (p_l_norm[i] >= 0.4167) and (p_l_norm[i] < 0.583):
                idx[i] = 2
            elif (p_l_norm[i] >= 0.583) and (p_l_norm[i] < 0.8):
                idx[i] = 3
        cut_loc_l = []
        idx_list = [np.where(idx == 1)[0], np.where(idx == 2)[0], np.where(idx == 3)[0]]
        num_list = [len(i) for i in idx_list]
        idx[idx_list[np.argmin(num_list)]] = 0

        cut_1 = np.where(idx == -1)[0]
        cut_2 = np.where(idx == -2)[0]
        cut_loc1 = 0
        cut_loc2 = length
        if len(cut_1) > 0:
            cut_loc1 = np.max(p_l[cut_1])
        if len(cut_2) > 0:
            cut_loc2 = np.min(p_l[cut_2])
        if cut_loc1 > length - cut_loc2:
            cut_loc_l.append([cut_loc1, -1])
        if cut_loc1 < length - cut_loc2 and cut_loc2 > 0:
            cut_loc_l.append([cut_loc2, -2])

        for i in [2, 1, 3]:
            cut = np.where(idx == i)[0]
            if len(cut) > 0:
                status = i
                cut_loc_ = np.max(p_l[cut])
                cut_loc_l.append([cut_loc_, status])
                if i == 2:
                    break

        return cut_loc_l

    @staticmethod
    def run(img_np, vio_code=None, show=False):
        img = img_np.copy()  # 使用 copy 防止原图被修改
        img_gray = cv2.cvtColor(img_np, cv2.COLOR_BGR2GRAY)

        # 检测水平拼接数目
        p_l_h, w = AutoSplit.find_bounding(img_gray, 0)
        cut_loc_hor = np.array(AutoSplit.cut_loc(p_l_h, w))

        # 检测竖直拼接数目
        p_l_v, h = AutoSplit.find_bounding(img_gray, 1)
        cut_loc_ver = np.array(AutoSplit.cut_loc(p_l_v, h, 1))

        if len(cut_loc_hor) == 0:
            cut_loc_hor = np.array([[0, 0]])
        if len(cut_loc_ver) == 0:
            cut_loc_ver = np.array([[0, 0]])

        H, W, _ = img.shape
        if show:
            for hor in cut_loc_hor:
                cv2.line(img, (hor[0], 0), (hor[0], H), (0, 0, 255), 2)
                cv2.putText(img, str(hor[1]), (hor[0] + 10, H // 2), cv2.FONT_HERSHEY_SIMPLEX, 2, (0, 255, 0, 2), 2)

            for ver in cut_loc_ver:
                cv2.line(img, (0, ver[0]), (W, ver[0]), (0, 0, 255), 2)
                cv2.putText(img, str(ver[1]), (W // 2, ver[0]), cv2.FONT_HERSHEY_SIMPLEX, 2, (0, 255, 0, 2), 2)

        ver_status = cut_loc_ver[:, 1]
        ver_value = cut_loc_ver[:, 0]

        hor_status = cut_loc_hor[:, 1]

        top_black = ver_value[np.where(ver_status == -1)[0]].tolist()
        bottom_black = ver_value[np.where(ver_status == -2)[0]].tolist()
        split_height = ver_value[np.where(ver_status == 2)[0]].tolist()
        res_black_height = 0
        split_mode = 111
        error_judge = False
        black_pos = None

        if 2 in ver_status:
            split_mode = 221
            if top_black and bottom_black:
                res_black_height = 0
            elif top_black:
                black_pos = "up"
                split_mode = 411
                single_img_height = (H - top_black[0]) / 2
                single_up_img_height = split_height[0] - top_black[0]
                single_down_img_height = H - split_height[0]
                black_height = top_black[0]
                split_black_height = 2 * split_height[0] - H
                if split_black_height < 0:
                    black_pos = None
                    error_judge = True
                black_dis_rate = (black_height - split_black_height) / H

                res_black_height = black_height
                print(black_height, split_black_height, round(black_dis_rate, 4))
                if 0 < split_black_height < 0.01 * H:
                    print("上黑框已修正：", black_height, "->", split_black_height)
                    res_black_height = split_black_height
                else:
                    if split_height[0] > 0.5 * H and 1.1 < black_height / split_black_height < 1.9:
                        print("上黑框高度:", black_height)
                        res_black_height = split_black_height
                        print("修正上黑框高度", split_black_height)
            elif bottom_black:
                black_pos = "down"
                split_mode = 411
                black_height = H - bottom_black[0]
                split_black_height = H - 2 * split_height[0]
                if split_black_height < 0:
                    black_pos = None
                    error_judge = True
                black_dis_rate = (black_height - split_black_height) / H
                res_black_height = black_height
                print(black_height, split_black_height, round(black_dis_rate, 4))
                if black_dis_rate > 0.008 and 0 < split_black_height < 0.01 * H:
                    print("下黑框已修正：", black_height, "->", split_black_height)
                    res_black_height = split_black_height
                else:
                    if split_height[0] < 0.5 * H and 1.1 < black_height / split_black_height < 1.9:
                        print("下黑框高度:", black_height)
                        res_black_height = split_black_height
                        print("修正下黑框高度:", split_black_height)
            if error_judge:
                pass
            elif 1 in hor_status and 3 in hor_status:
                split_mode = 611
                if top_black:
                    black_pos = "up"
                else:
                    black_pos = "down"

        elif 1 in ver_status:
            split_mode = 321
            if 2 in hor_status:
                split_mode = 312
            if 1 in hor_status or 3 in hor_status:
                split_mode = 111

        elif 3 in ver_status:
            split_mode = 321
            if 1 in hor_status or 3 in hor_status:
                split_mode = 111
        elif 2 in hor_status:
            split_mode = 211
        elif 1 in hor_status and 3 in hor_status:
            split_mode = 311
        elif W > H * 4:
            split_mode = 211

        if vio_code in ["1625", "1208"] and split_mode not in [411, 611]:
            split_mode = 411

        return split_mode, res_black_height / H if H != 0 else 0, black_pos


class IOU:
    @staticmethod
    def get_xiou(box1, box2):
        x1_min, _, x1_max, _ = box1
        x2_min, _, x2_max, _ = box2

        intersection_width = max(0, min(x1_max, x2_max) - max(x1_min, x2_min))
        union_width = max(x1_max, x2_max) - min(x1_min, x2_min)

        return intersection_width / union_width if union_width != 0 else 0.0

    @staticmethod
    def compute_iou(boxes1, boxes2):
        """[Compute pairwise IOU matrix for given two sets of boxes]

        Args:
            boxes1 ([numpy ndarray with shape N,4]): [representing bounding boxes with format (xmin,ymin,xmax,ymax)]
            boxes2 ([numpy ndarray with shape M,4]): [representing bounding boxes with format (xmin,ymin,xmax,ymax)]
        Returns:
            pairwise IOU maxtrix with shape (N,M)，where the value at ith row jth column hold the iou between ith
            box and jth box from box1 and box2 respectively.
        """
        boxes1 = np.asarray(boxes1)
        boxes2 = np.asarray(boxes2)

        lu = np.maximum(boxes1[:, None, :2],
                        boxes2[:,
                        :2])  # lu with shape N,M,2 ; boxes1[:,None,:2] with shape (N,1,2) boxes2 with shape(M,2)
        rd = np.minimum(boxes1[:, None, 2:4], boxes2[:, 2:4])  # rd same to lu
        intersection_wh = np.maximum(0.0, rd - lu)
        intersection_area = intersection_wh[:, :, 0] * intersection_wh[:, :, 1]  # with shape (N,M)
        boxes1_wh = np.maximum(0.0, boxes1[:, 2:4] - boxes1[:, :2])
        boxes1_area = boxes1_wh[:, 0] * boxes1_wh[:, 1]  # with shape (N,)
        boxes2_wh = np.maximum(0.0, boxes2[:, 2:4] - boxes2[:, :2])
        boxes2_area = boxes2_wh[:, 0] * boxes2_wh[:, 1]  # with shape (M,)
        union_area = np.maximum(boxes1_area[:, None] + boxes2_area - intersection_area, 1e-8)  # with shape (N,M)
        ious = np.clip(intersection_area / union_area, 0.0, 1.0)
        return ious

    @staticmethod
    def single_iou(box1, box2):
        """
        计算两个边界框的IoU。

        参数:
        box1, box2: list or tuple, 四个元素的列表或元组 [x1, y1, x2, y2]
                    其中 (x1, y1) 是左上角坐标，(x2, y2) 是右下角坐标。

        返回:
        iou: float, 两个边界框的交并比(IoU)。
        """
        try:
            # 解析边界框
            b1_x1, b1_y1, b1_x2, b1_y2 = box1
            b2_x1, b2_y1, b2_x2, b2_y2 = box2

            # 计算交集的左上角和右下角坐标
            inter_x1 = max(b1_x1, b2_x1)
            inter_y1 = max(b1_y1, b2_y1)
            inter_x2 = min(b1_x2, b2_x2)
            inter_y2 = min(b1_y2, b2_y2)

            # 计算交集面积
            inter_area = max(0, inter_x2 - inter_x1 + 1) * max(0, inter_y2 - inter_y1 + 1)

            # 计算每个框的面积
            b1_area = (b1_x2 - b1_x1 + 1) * (b1_y2 - b1_y1 + 1)
            b2_area = (b2_x2 - b2_x1 + 1) * (b2_y2 - b2_y1 + 1)

            # 计算并集面积
            union_area = b1_area + b2_area - inter_area

            # 计算IoU
            iou = round(inter_area / union_area, 2)
        except:
            iou = 0
        return iou

    @staticmethod
    def filter_boxes_by_iou(boxA, boxesB, iou_threshold=0.75) -> list:
        # boxA: [x1, y1, x2, y2] 单个边界框
        # boxesB: shape (n, 4) 多个边界框，格式为 [x1, y1, x2, y2]
        # 扩展 boxA 以匹配 boxesB 的形状
        boxA = np.asarray(boxA)
        boxesB = np.asarray(boxesB)

        # 扩展 boxA 以匹配 boxesB 的形状
        boxA = np.expand_dims(boxA, axis=0)

        # 计算交集的坐标
        interSec = np.maximum(np.minimum(boxA[:, 2:], boxesB[:, 2:]) - np.maximum(boxA[:, :2], boxesB[:, :2]) + 1, 0)
        interArea = interSec[:, 0] * interSec[:, 1]

        # 计算两个边界框的面积
        areaA = (boxA[:, 2] - boxA[:, 0] + 1) * (boxA[:, 3] - boxA[:, 1] + 1)
        areaB = (boxesB[:, 2] - boxesB[:, 0] + 1) * (boxesB[:, 3] - boxesB[:, 1] + 1)

        # 计算并集的面积
        unionArea = areaA + areaB - interArea

        # 避免除以零的情况
        unionArea = np.where(unionArea == 0, 1e-9, unionArea)

        # 计算IoU
        ious = interArea / unionArea

        # 找到IoU大于阈值的索引
        valid_indices = np.where(ious > iou_threshold)[0]

        if len(valid_indices) == 0:
            return []

        # 获取满足条件的边界框及其对应的IoU值
        valid_ious = ious[valid_indices]

        # 找到最大IoU值的索引
        max_iou_index = valid_indices[np.argmax(valid_ious)]

        # 返回具有最大IoU的边界框
        return boxesB[max_iou_index].tolist()


class CustomUnpickler(pickle.Unpickler):
    def find_class(self, module, name):
        # 将旧的模块路径映射到新的模块路径
        if module == "src.model_interface":
            module = "src.module.image_model"
        elif module == "src.modules":
            module = "src.module.judge_rule"
        elif module == "data.vio_data_parser":
            module = "src.module.request_parser"
        return super().find_class(module, name)

class ObjectProcess:
    # 保存变量
    @staticmethod
    def save_object(any_object, save_path):
        with open(save_path, 'wb') as f:
            pickle.dump(any_object, f)

    @staticmethod
    def load_object(object_path):
        # 从文件加载字典
        with open(object_path, 'rb') as f:
            # loaded_object = pickle.load(f)
            loaded_object = CustomUnpickler(f).load()
        return loaded_object


def crop_driver_area(img, veh_box):
    w = veh_box[2] - veh_box[0]
    h = veh_box[3] - veh_box[1]
    x1 = int(veh_box[0] + 0.45 * w)
    y1 = int(veh_box[1] + 0.2 * h)
    x2 = int(veh_box[2] - 0.15 * w)
    y2 = int(veh_box[3] - 0.35 * h)
    return img[y1:y2, x1:x2], x1, y1


import inspect

# def get_func_name():  # TODO:tianjia leiming
#     st = time.time()
#
#     # 获取调用栈
#     stack = inspect.stack()
#     # 获取上一层的记录，即调用此函数的函数
#     caller_record = stack[1]
#     # 从记录中获取函数名
#     function_name = caller_record.function
#     print(f"stack use time:{time.time() - st:.2f}")
#
#     return function_name

# def get_func_name():
#     st = time.time()
#     # 获取调用栈
#     stack = inspect.stack()
#     # 获取上一层的记录，即调用此函数的函数
#     caller_frame = stack[1].frame
#     caller_info = stack[1]
#
#     # 从记录中获取函数名
#     function_name = caller_info.function
#
#     # 尝试获取类名
#     class_name = None
#     if 'self' in caller_frame.f_locals:
#         # 如果有 'self'，那么这是一个实例方法
#         self_obj = caller_frame.f_locals['self']
#         class_name = self_obj.__class__.__name__
#     elif 'cls' in caller_frame.f_locals:
#         # 如果有 'cls'，那么这是一个类方法
#         cls_obj = caller_frame.f_locals['cls']
#         class_name = cls_obj.__name__
#     print(f"stack use time:{time.time() - st:.2f}")
#     if class_name:
#         return f"{class_name}.{function_name}"
#     else:
#         return function_name
