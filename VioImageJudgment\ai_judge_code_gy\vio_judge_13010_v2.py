# 逆行场景
import numpy as np

from init_models import *


# 逆行场景主函数
def judge_vio(src_plate_num, file_list, weights_list, resize_scale=0.7, merge=True, save=False,
              save_path='', highway=False, extra_msg=None):
    error_path = f"{save_path}/no_vio"
    vio_path = f"{save_path}/vio"
    if save:
        os.makedirs(error_path, exist_ok=True)
        os.makedirs(vio_path, exist_ok=True)
    # 读取模型
    plate_det_model = weights_list[0][0]
    plate_rec_model = weights_list[0][1]
    demo = weights_list[1]
    lp_model = weights_list[2][0]
    lp_meta = weights_list[2][1]
    yolov7_model = weights_list[3][0]
    yolov7_meta = weights_list[3][1]
    model_cnn = weights_list[-2]
    vio_model = weights_list[-1]

    vio_judge = 'no_judge'
    try:
        if extra_msg is None:
            split_mode = black_height = 0
        else:
            split_mode = extra_msg[0]
            black_height = extra_msg[1]
    except:
        split_mode = black_height = 0
    judge_infos = {'plate_num': src_plate_num,
                   'vio_code': 1301,
                   'vio_judge': vio_judge,
                   'zebra_line': {},
                   'police': 0,
                   'has_crossline': 0,
                   'vehicle_location': {},
                   'drv_direction': 0,
                   'drive_direction_light': [],
                   'lamp': [],
                   'stop_line': 0,
                   'lane': [],
                   'target_car': [],
                   'judgeConf': 1.0,
                   'modelDetectResults': [], 'split_mode': split_mode, "black_height": black_height}
    # 特殊车牌判定
    if not src_plate_num:
        vio_judge = "no_vio_009"
        judge_infos['vio_judge'] = vio_judge
        return vio_judge, judge_infos
    if src_plate_num[0] not in chars:
        vio_judge = 'no_vio_002'
        judge_infos['vio_judge'] = vio_judge
        return vio_judge, judge_infos
    vio_judge = VIO_JUDGE_TOOL().spe_no_vio_judge(src_plate_num)
    if vio_judge.startswith('no_vio'):
        judge_infos['vio_judge'] = vio_judge
        return vio_judge, judge_infos
    vio_judge = 'vio'

    # try:
    #     img1, img2, img3 = Tools().resize_imgs(img1_np, img2_np, img3_np)
    # except Exception as e:
    #     import traceback
    #     traceback.print_exc()
    #     vio_judge = 'no_vio_012'
    #     judge_infos['vio_judge'] = vio_judge
    #     return vio_judge, judge_infos

    for i, img in enumerate(file_list):
        judge_infos['img_id'] = i + 1
        # 找目标车
        main_module = BlendMaskModule(img, weights_list, src_plate_num, save, save_path)
        main_module.match_target_car(vio_judge, judge_infos)
    for i, value in enumerate(plate_list):
        temp_tar_car = {'plate_num': '', 'car_ps': ''}
        try:
            temp_tar_car['plate_num'] = plate_list[i]
        except:
            pass
        try:
            temp_tar_car['car_ps'] = cars_ps[i]
        except:
            pass
        judge_infos['target_car'].append(temp_tar_car)

    res_list = []
    print("plate_list:", plate_list)
    for plate in plate_list:
        if plate == "NO_DETECT":
            res_list.append(0)
        else:
            res_list.append(1)
    if np.sum(res_list) == 0:
        vio_judge = 'no_vio_006'
        judge_infos['vio_judge'] = vio_judge
        judge_infos['judgeConf'] = 0.9
        return vio_judge, judge_infos

    if db_img_label:
        img1_car_ps, img2_car_ps = cars_ps
    else:
        img1_car_ps, img2_car_ps, img3_car_ps = cars_ps
    try:
        x_ratio = ((img1_car_ps[0] + img1_car_ps[2]) / 2) / img1.shape[1]
    except:
        x_ratio = 0
    if x_ratio <= 0.5:
        judge_infos['vehicle_location'] = 0
    else:
        judge_infos['vehicle_location'] = 1

    #### 高速路判罚逻辑（直接加入与城市并行
    if highway:
        cor_y_start = (cars_ps[0][1] + cars_ps[0][3]) / 2
        cor_y_end = min([(i[1] + i[3]) / 2 for i in cars_ps if len(i) > 0])

        if cor_y_start - cor_y_end > 10:
            vio_judge = 'vio'
        else:
            vio_judge = 'no_vio'

        print(cor_y_start - cor_y_end)

        judge_infos['plate_num'] = src_plate_num
        judge_infos['img1_car_ps'] = img1_car_ps
        judge_infos['img2_car_ps'] = img2_car_ps
        judge_infos['judgeConf'] = 0.9

        if not db_img_label: judge_infos['img3_car_ps'] = img3_car_ps

        if save:
            if not db_img_label:
                show_plate(img1, img2, img3, img1_car_ps, img2_car_ps, img3_car_ps)
            else:
                show_plate(img1, img2, None, img1_car_ps, img2_car_ps)

            if vio_judge.startswith("no_vio"):
                cv2.imwrite(error_path + f'/{vio_judge}_{src_plate_num}_img1.jpg', img1)
                cv2.imwrite(error_path + f'/{vio_judge}_{src_plate_num}_img2.jpg', img2)
                if not db_img_label: cv2.imwrite(error_path + f'/{vio_judge}_{src_plate_num}_img3.jpg', img3)
            else:
                cv2.imwrite(vio_path + f'/{vio_judge}_{src_plate_num}_img1.jpg', img1)
                cv2.imwrite(vio_path + f'/{vio_judge}_{src_plate_num}_img2.jpg', img2)
                if not db_img_label: cv2.imwrite(vio_path + f'/{vio_judge}_{src_plate_num}_img3.jpg', img3)

        return vio_judge, judge_infos

    if len(cars_masks_list):
        if db_img_label:
            img1_car_mask, img2_car_mask = cars_masks_list
            if len(img1_car_mask) == len(img2_car_mask) == 0:
                vio_judge = 'no_vio_005'
                judge_infos['vio_judge'] = vio_judge
                judge_infos['judgeConf'] = 0.85

                return vio_judge, judge_infos
            predictions1, predictions2 = predictions
            car1_ps, car2_ps = cars_ps
            car1_mask, car2_mask = cars_masks_list
            kp_masks1, kp_masks2 = kp_masks_list
        else:
            img1_car_mask, img2_car_mask, img3_car_mask = cars_masks_list
            if len(img1_car_mask) == len(img2_car_mask) == len(img3_car_mask) == 0:
                vio_judge = 'no_vio_005'
                judge_infos['vio_judge'] = vio_judge
                judge_infos['judgeConf'] = 0.85

                return vio_judge, judge_infos
            predictions1, predictions2, predictions3 = predictions
            car1_ps, car2_ps, car3_ps = cars_ps
            car1_mask, car2_mask, car3_mask = cars_masks_list
            kp_masks1, kp_masks2, kp_masks3 = kp_masks_list

        class_info = ["lane1", "lane2", "stop_area", "arrow_l", "arrow_s", "arrow_r", "arrow_t", "stop_line",
                      "zebra_line", 'no_entry', 'zebra_line', 'arrow_s_t', 'arrow_l_t', 'arrow_r_t']
        car1_road_infos = \
            BlendMaskDetect(img1).save_infos(src_plate_num, demo, class_info, predictions1, kp_masks1, car1_ps,
                                             car1_mask, height_restrict=True, height_bond=0.6)

        car2_road_infos = \
            BlendMaskDetect(img2).save_infos(src_plate_num, demo, class_info, predictions2, kp_masks2, car2_ps,
                                             car2_mask, height_restrict=True, height_bond=0.6)

        data1, data2 = car1_road_infos, car2_road_infos

        if not db_img_label:
            car3_road_infos = \
                BlendMaskDetect(img3).save_infos(src_plate_num, demo, class_info, predictions3, kp_masks3, car3_ps,
                                                 car3_mask, height_restrict=True, height_bond=0.6)
            data3 = car3_road_infos
        data = dict()
        if db_img_label:
            data['shapes'] = data1['shapes'] + data2['shapes']
        else:
            data['shapes'] = data1['shapes'] + data2['shapes'] + data3['shapes']
        data['imageHeight'] = data1['imageHeight']
        data['imageWidth'] = data1['imageWidth']
        data['dev_desc'] = "dev_desc"

        # 从读取的数据取出目标车辆信息
        points = [x['points'] for x in data1['shapes'] if x["label"] == "car"]
        car1 = points[0]
        points = [x['points'] for x in data2['shapes'] if x["label"] == "car"]
        car2 = points[0]
        if not db_img_label:
            points = [x['points'] for x in data3['shapes'] if x["label"] == "car"]
            car3 = points[0]
        st = time.time()
        try:
            road = Road(data)  # 初始化道路信息（其实同一个镜头的初始化是可以复用的）
            print("road 耗时#######", time.time() - st)
            if db_img_label:
                try:
                    vio_judge_temp = road.retrograde([car1, car2])  # 输出当前道路信息下的车辆逆行判定
                except:
                    print("异常置为违法")
                    vio_judge_temp = True
            else:
                try:
                    vio_judge_temp = road.retrograde([car1, car2, car3])
                except:
                    print("异常置为违法")
                    vio_judge_temp = True
        except:
            vio_judge_temp = True
        if vio_judge_temp:
            vio_judge = 'vio'
        else:
            vio_judge = 'no_vio'

    judge_infos['plate_num'] = src_plate_num
    judge_infos['img1_car_ps'] = img1_car_ps
    judge_infos['img2_car_ps'] = img2_car_ps
    if not db_img_label: judge_infos['img3_car_ps'] = img3_car_ps

    # if vio_judge.startswith('no'):
    #     img1_car_ps, plate1_num, car1_type = get_car_box(file_list[0], src_plate_num,
    #                                                      [demo, plate_det_model, plate_rec_model])
    #     img2_car_ps, plate2_num, car2_type = get_car_box(file_list[1], src_plate_num,
    #                                                      [demo, plate_det_model, plate_rec_model])
    #     # 识别车辆前车头辅助判定违法（存在局限性）
    #     if car1_type in ['car_h', 'truck_h'] and car2_type in ['car_h', 'truck_h']:
    #         vio_judge = 'vio'

    judge_infos['vio_judge'] = vio_judge
    if vio_judge.startswith('vio'):
        judge_infos['judgeConf'] = 0.85
    else:
        judge_infos['judgeConf'] = 0.7
    ####     judge_infos['lane'] = road.area_points
    try:
        if save:
            if db_img_label and all_car_ps:
                car1_bbox_ps, car2_bbox_ps = all_car_ps
                show_cars(img1, img2, None, car1_bbox_ps, car2_bbox_ps, None)

            elif all_car_ps:
                car1_bbox_ps, car2_bbox_ps, car3_bbox_ps = all_car_ps
                show_cars(img1, img2, img3, car1_bbox_ps, car2_bbox_ps, car3_bbox_ps)
            show_plate(img1, img2, img3, img1_car_ps, img2_car_ps, img3_car_ps)
            try:
                show_judge_area(img1, img2, img3, road.area_points)
            except:
                print("show error")
            if lane_info_list:
                try:
                    show_lanes(img1, img2, img3, lane_info_list[0], lane_info_list[1], lane_info_list[2])
                except Exception as e:
                    print(e.__traceback__.tb_lineno, e)

            if vio_judge.startswith("no_vio"):
                cv2.imwrite(error_path + f'/{vio_judge}_{src_plate_num}_img1.jpg', img1)
                cv2.imwrite(error_path + f'/{vio_judge}_{src_plate_num}_img2.jpg', img2)
                if not db_img_label:
                    cv2.imwrite(error_path + f'/{vio_judge}_{src_plate_num}_img3.jpg', img3)
            else:
                cv2.imwrite(vio_path + f'/{vio_judge}_{src_plate_num}_img1.jpg', img1)
                cv2.imwrite(vio_path + f'/{vio_judge}_{src_plate_num}_img2.jpg', img2)
                if not db_img_label:
                    cv2.imwrite(vio_path + f'/{vio_judge}_{src_plate_num}_img3.jpg', img3)
    except:
        print("Error!逆行可视化模块异常！")
    return vio_judge, judge_infos


class BlendMaskModule:
    def __init__(self, img, model_list, src_plate, save, save_path=""):
        self.img = img
        self.model_list = model_list
        self.src_plate = src_plate
        self.device = model_list[3][1][2]
        self.save = save
        self.save_path = save_path

    def match_target_car(self, vio_judge, judge_infos):
        demo = self.model_list[1]
        plate_det_model = self.model_list[0][0]
        plate_rec_model = self.model_list[0][1]
        det1 = BlendMaskDetect(self.img)
        predictions1, kp_mask1 = det1.mask_predict(demo)
        nms_ls1 = det1.nms_class_distinct(predictions1["instances"].pred_boxes.tensor,
                                          predictions1["instances"].scores,
                                          predictions1["instances"].pred_classes,
                                          threshold=0.8,
                                          class_ditinct_threshold=0.85)
        predictions1, kp_mask1 = det1.prediction_nms_filter(predictions1, nms_ls1, kp_mask1)
        car_bbox_ps, mask_list, labels_list, score_list = det1.class_info2(predictions1, Blend_Judge_Vehicle,
                                                                           conf_thresh=0.1,
                                                                           need_mask=True)
        # 抠出车辆区域
        self.get_no_cars_img(mask_list)

        img_car_ps, plate_num, img_plate_dict, new_car_bbox_ps = CarJudge().confirm_img1_car_ps(self.img,
                                                                                                self.src_plate,
                                                                                                plate_det_model,
                                                                                                plate_rec_model,
                                                                                                car_bbox_ps,
                                                                                                self.device,
                                                                                                label=0,
                                                                                                show=self.save,
                                                                                                save_path=self.save_path)
        # 返回检测框和置信度信息
        img_id = judge_infos["img_id"]
        if img_car_ps:
            target_idx = car_bbox_ps.index(img_car_ps)
            percent_coord = convert_src_coord(img_car_ps, self.img, img_id,
                                              judge_infos["split_mode"], judge_infos["black_height"])
            tmp_res = {
                "objId": img_id,
                "objType": "vehicle",
                "objConf": float(score_list[target_idx]),
                "objCoord": percent_coord
            }
            judge_infos["modelDetectResults"].append(tmp_res)
        # 车牌位置信息
        plate_cor = img_plate_dict[str(img_car_ps)] if img_car_ps else []
        plate_conf = 0
        if plate_cor:
            target_idx = str(img_car_ps) + "conf"
            plate_conf = float(img_plate_dict[target_idx])
            percent_coord = convert_src_coord(plate_cor, self.img, img_id,
                                              judge_infos["split_mode"], judge_infos["black_height"])
            tmp_res = {
                "objId": img_id,
                "objType": "plate",
                "objConf": plate_conf,
                "objCoord": percent_coord
            }
            judge_infos["modelDetectResults"].append(tmp_res)

        # 警车判定
        if plate_num.endswith("警"):
            vio_judge = 'no_vio_010'
            judge_infos['judgeConf'] = 0.9
        elif img_car_ps:
            # 前端相机识别车牌有误报废片
            if len(plate_num) - len(self.src_plate) >= 1 and len(plate_num) <= 8:
                vio_judge = "no_vio_003"
                judge_infos['judgeConf'] = plate_conf
                return img_car_ps, plate_num, vio_judge
            target_idx2 = car_bbox_ps.index(img_car_ps)
            target_label = labels_list[target_idx2]
            blend_score = score_list[target_idx2]
            judge_infos['judgeConf'] = float(blend_score)

            # 特殊车辆判定
            if target_label in Special_Type:
                if target_label == "ambulance_h":
                    if blend_score >= Ambulance_Head_Thresh:
                        vio_judge = "no_vio_010"
                elif blend_score >= Special_Car_Thresh:
                    vio_judge = "no_vio_010"
        return img_car_ps, plate_num, vio_judge

    def get_no_cars_img(self,mask_list):
        no_car_img = self.img.copy()
        for mask in mask_list:
            no_car_img[mask] = 0
        cv2.imwrite("test.jpg",no_car_img)
        cv2.imwrite("src.jpg",self.img)
        exit()

    def merge_no_cars_img(self):
        pass
