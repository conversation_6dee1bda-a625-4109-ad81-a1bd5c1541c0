from kafka import KafkaConsumer
import sys
import json
sys.path.append('..')
from main_config.config import *

consumer = KafkaConsumer(VIO_DATA_TOPIC,
                         bootstrap_servers=SERVER,
                         group_id=GROUP_ID,
                         auto_offset_reset=AUTO_OFFSET_RESET,client_id=CLIENT_ID)
for msg in consumer:
    data = json.loads(msg.value)
    print(data)
    exit("初始化成功！")
consumer.close()
