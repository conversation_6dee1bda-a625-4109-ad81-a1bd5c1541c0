<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="Encoding">
    <file url="file://$PROJECT_DIR$/_local_tools/AI审片模型评估.py" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/_local_tools/iiea导出表格分类审核素材.py" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/_local_tools/依据审核结果生成效果评估.py" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/_local_tools/合成图配置.py" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/_local_tools/应用导出表格分类数据.py" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/_local_tools/整合文件夹下所有图片.py" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/_local_tools/通过可视化图片找到原图.py" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/src/module/judge_rule.py" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/src/service/data_class.py" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/src/service/vio_judge.py" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/test/kafka_test.py" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/test/kafka_test2.py" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/test/modules_test.py" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/tmp.py" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/tmp2.py" charset="UTF-8" />
    <file url="PROJECT" charset="UTF-8" />
  </component>
</project>