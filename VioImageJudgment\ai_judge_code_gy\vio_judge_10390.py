# 违法停车场景v2
import os
import time

# os.environ["CUDA_VISIBLE_DEVICES"] = "2"
from init_models import *


def match_target_car(img_np, car_ps_list, src_plate_num, plate_det_model, plate_rec_model, device=None, save=False,
                     save_path=".",
                     save_name="", judge_infos=None):
    iiea_cfg = judge_infos["iieaFilterVehicle"]
    # iiea审核模式
    iieaAuditMode = judge_infos["iieaAuditMode"]
    if iieaAuditMode is None:
        judge_mode = Precision_Judge_Mode[str(judge_infos['vio_code'])]
    else:
        judge_mode = iieaAuditMode
    judge_thresh = Judge_Thresh[str(judge_infos['vio_code'])][int(judge_mode)]
    if judge_thresh is None:
        judge_thresh = [4,1] if judge_mode else [3,2]

    save_dir = f"{save_path}/plate_num_show"
    if save:
        os.makedirs(save_dir, exist_ok=True)
    if device is None:
        device = "cuda:0" if torch.cuda.is_available() else "cpu"
    # iiea配置
    try:
        iiea_filter_police = iiea_cfg[0]
    except:
        iiea_filter_police = None
    show_img = img_np.copy()
    img_car_ps = []
    plate_num = "NO_DETECT"
    img_plate_dict = {}
    vio_score = 0
    temp = sorted(car_ps_list, key=lambda x: x[1], reverse=True)
    for car_ps in temp:
        # 车牌坐标初始化
        img_plate_dict[str(car_ps)] = []
        # 车牌识别去除车辆上三分之一部分
        h_bias = (car_ps[3] - car_ps[1]) // 3
        temp_img = img_np[car_ps[1] + h_bias:car_ps[3], car_ps[0]:car_ps[2]]
        # 车牌检测+识别
        dict_list = detect_Recognition_plate(plate_det_model, temp_img, device, plate_rec_model, 640)
        if save:
            temp_car_ps = [car_ps[0], car_ps[1] + h_bias, car_ps[2], car_ps[3]]
            show_img = draw_result2(show_img, dict_list, temp_car_ps)

        for idx, res in enumerate(dict_list):
            plate_num = res["plate_no"]
            tmp_cor = res["rect"]
            if len(plate_num) > 2:
                confidence = np.min(res["score"][2:])
            elif len(plate_num):
                confidence = np.min(res["score"])
            else:
                confidence = 0

            if plate_num and plate_num[-1] == "警" and (res["score"][
                                                            -1] < Police_Plate_Thresh or iiea_filter_police is False):
                plate_num = plate_num[:-1]
            plate_cor = [tmp_cor[0] + car_ps[0], tmp_cor[1] + car_ps[1] + h_bias, tmp_cor[2] + car_ps[0],
                         tmp_cor[3] + car_ps[1] + h_bias]

            img_plate_dict[str(car_ps)] = plate_cor
            img_plate_dict[str(car_ps) + "conf"] = confidence

            # 车牌全匹配
            if plate_num == src_plate_num:
                img_car_ps.extend(car_ps)
                vio_score = 2
                if save:
                    cv2.imwrite(f"{save_dir}/{src_plate_num}_{save_name}_2.jpg", show_img)
                return img_car_ps, plate_num, img_plate_dict, vio_score
            # 车牌模糊匹配
            elif Tools().random_choice(plate_num, src_plate_num, random_list=judge_thresh):
                img_car_ps.extend(car_ps)
                vio_score = 1
                if save:
                    cv2.imwrite(f"{save_dir}/{src_plate_num}_{save_name}_1.jpg", show_img)
                return img_car_ps, plate_num, img_plate_dict, vio_score

    if save:
        cv2.imwrite(f"{save_dir}/{src_plate_num}_{save_name}_car.jpg", show_img)
    # 直接检测识别车牌
    if len(car_ps_list) == 0:
        dict_list = detect_Recognition_plate(plate_det_model, img_np, device, plate_rec_model, 640)
        if save:
            show_img = draw_result2(show_img, dict_list, [0, 0, 0, 0])
        for res in dict_list:
            plate_num = res["plate_no"]
            plate_cor = res["rect"]
            if plate_num and plate_num[-1] == "警" and (
                    res["score"][-1] < Police_Plate_Thresh or iiea_filter_police is False):
                plate_num = plate_num[:-1]
            img_plate_dict["img"] = plate_cor
            if plate_num == src_plate_num:
                vio_score = 2
                if save:
                    cv2.imwrite(f"{save_dir}/{src_plate_num}_{save_name}_2.jpg", show_img)
                return img_car_ps, plate_num, img_plate_dict, vio_score
            elif Tools().random_choice(plate_num, src_plate_num, random_list=judge_thresh):
                vio_score = 1
                if save:
                    cv2.imwrite(f"{save_dir}/{src_plate_num}_{save_name}_1.jpg", show_img)
                return img_car_ps, plate_num, img_plate_dict, vio_score
        if save:
            cv2.imwrite(f"{save_dir}/{src_plate_num}_{save_name}_nocar.jpg", show_img)
    return img_car_ps, "NO_DETECT", img_plate_dict, vio_score


# 违法停车场景主函数
def judge_vio(src_plate_num, file_list, weights_list, save=False, save_path=".", extra_msg=None):
    # 读取模型
    plate_det_model = weights_list[0][0]
    plate_rec_model = weights_list[0][1]
    demo = weights_list[1]
    lp_model = weights_list[2][0]
    lp_meta = weights_list[2][1]
    yolov7_model = weights_list[3][0]
    yolov7_meta = weights_list[3][1]
    model_cnn = weights_list[-2]
    vio_model = weights_list[-1]

    img_np_list = file_list
    vio_judge = 'no_judge'

    try:
        split_mode = extra_msg[0]
        black_height = extra_msg[1]
        black_pos = extra_msg[2]
        iieaAuditMode = extra_msg[3]
        iieaFilterVehicle = extra_msg[4]
    except:
        split_mode = 111
        black_height = 0
        black_pos = None
        iieaAuditMode = None
        iieaFilterVehicle = None
    judge_infos = {'plate_num': src_plate_num,
                   'vio_code': 1039,
                   'vio_judge': vio_judge,
                   'drv_direction': 0,
                   'drive_direction_light': [],
                   'lamp': [],
                   'target_car': [],
                   'stop_line': 0,
                   'police': 0,
                   'has_crossline': 0,
                   'vehicle_location': 0,
                   'lane': [],
                   'zebra_line': [],
                   'judgeConf': 1.0,
                   'modelDetectResults': [], 'split_mode': split_mode, "black_height": black_height,
                   "black_pos": black_pos, "iieaAuditMode": iieaAuditMode, "iieaFilterVehicle": iieaFilterVehicle}
    score_list = []
    # 特殊车牌废片判定
    if not src_plate_num:
        vio_judge = "no_vio_009"
        judge_infos['vio_judge'] = vio_judge
        return vio_judge, judge_infos
    if src_plate_num[0] not in chars:
        vio_judge = 'no_vio_002'
        judge_infos['vio_judge'] = vio_judge
        return vio_judge, judge_infos, score_list
    vio_judge = VIO_JUDGE_TOOL().spe_no_vio_judge(src_plate_num, iieaFilterVehicle)
    if vio_judge.startswith('no_vio'):
        judge_infos['vio_judge'] = vio_judge
        return vio_judge, judge_infos, score_list
    vio_judge = "no_vio"
    for idx, img_np in enumerate(img_np_list):
        if idx == 0:
            vio_judge += "-"
        # 找目标车辆
        crop_img_list, cars_bbox_ps, _, scores_list = yolov7_detect(img_np, yolov7_model, yolov7_meta[0],
                                                                    yolov7_meta[1],
                                                                    yolov7_meta[2], need_conf=True, conf_thres=0.15)
        img_car_ps, plate_num, img_plate_dict, vio_score = match_target_car(img_np, cars_bbox_ps, src_plate_num,
                                                                            plate_det_model, plate_rec_model,
                                                                            yolov7_meta[2], save, save_path,
                                                                            f"img{idx + 1}",
                                                                            judge_infos)

        # 返回检测框和置信度信息
        if img_car_ps:
            target_idx = cars_bbox_ps.index(img_car_ps)
            img_id = idx + 1
            percent_coord = convert_src_coord(img_car_ps, img_np, img_id,
                                              judge_infos["split_mode"], judge_infos["black_height"],
                                              judge_infos["black_pos"])
            tmp_res = {
                "objId": img_id,
                "objType": "vehicle",
                "objConf": float(scores_list[target_idx]),
                "objCoord": percent_coord
            }
            judge_infos["modelDetectResults"].append(tmp_res)
        # 车牌位置信息
        plate_cor = img_plate_dict[str(img_car_ps)] if img_car_ps else []
        if plate_cor:
            target_idx = str(img_car_ps) + "conf"
            img_id = idx + 1
            percent_coord = convert_src_coord(plate_cor, img_np, img_id,
                                              judge_infos["split_mode"], judge_infos["black_height"],
                                              judge_infos["black_pos"])
            tmp_res = {
                "objId": img_id,
                "objType": "plate",
                "objConf": float(img_plate_dict[target_idx]),
                "objCoord": percent_coord
            }
            judge_infos["modelDetectResults"].append(tmp_res)
        try:
            police_filter = judge_infos["iieaFilterVehicle"][0]
        except:
            police_filter = None
        if plate_num.endswith("警") and police_filter is not False:
            vio_judge = 'no_vio_010'
            judge_infos["judgeConf"] = 0.8
            break
        if filter_special_vehicle(demo, img_np, img_car_ps, vio_judge, save, judge_infos) == 'no_vio_010':
            vio_judge = 'no_vio_010'
            break
        vio_judge += str(vio_score)
        score_list.append(vio_score)

    score_sum = np.sum(score_list)
    if vio_judge == "no_vio_010":
        pass
    elif score_sum >= len(img_np_list) + 1 and not score_list.count(0):
        vio_judge = "vio_001"
        judge_infos["judgeConf"] = 0.99
    elif score_sum >= len(img_np_list) and not score_list.count(0):
        vio_judge = "vio_002"
        judge_infos["judgeConf"] = 0.95
    elif len(score_list) >= 2 and (score_list[0] != 0 or score_list[1] != 0) and score_list[-1] != 0:
        vio_judge = "vio_003"
        judge_infos["judgeConf"] = 0.9
    elif len(score_list) >= 2 and np.sum(score_list[:-1]) > 0:
        vio_judge = "vio_004"
        judge_infos["judgeConf"] = 0.85
    elif Vio_Park_High_Recall:
        vio_judge = "vio_005"
        judge_infos["judgeConf"] = 0.8

    judge_infos['vio_judge'] = vio_judge
    return vio_judge, judge_infos, score_list
