# 开车打电话和未系安全带场景
# 1 获取condinst的目标车辆
# 2 获取yolov5的行人信息
# 3 提取目标车辆里面的主副驾驶信息
# 4 判别违法信息
import os

import cv2
import numpy as np

# os.environ["CUDA_VISIBLE_DEVICES"] = "1"
from init_models import *


# 1 获取condinst的目标车辆
class PersonCarInfo(object):
    def __init__(self, img_np, car_plate, weights_list):
        self.img_np = img_np
        self.car_plate = car_plate
        self.weights_list = weights_list

    def get_person_car_info(self, vio_judge, judge_infos, save=False, save_path="."):
        demo, yolov7_model, yolov7_meta, plate_det_model, plate_rec_model = self.weights_list
        img_size, stride, device = yolov7_meta
        # yolov7车辆行人检测
        crop_person, res_dict, tmp_box, tmp_conf = yolov7_detect(self.img_np, yolov7_model, img_size, stride, device,
                                                                 classes=['car', 'truck', 'bus', 'person'],
                                                                 is_split=True, need_conf=True, conf_thres=0.1)
        car_bbox_ps = res_dict["vehicle"]
        # 车牌识别匹配模块
        img_car_ps, plate_num, img_plate_dict, new_car_bbox_ps, first_plate_conf = \
            CarJudge(iiea_cfg=judge_infos["iieaFilterVehicle"], need_first_plate=True).confirm_img1_car_ps(self.img_np,
                                                                                                           self.car_plate,
                                                                                                           plate_det_model,
                                                                                                           plate_rec_model,
                                                                                                           car_bbox_ps,
                                                                                                           yolov7_meta[
                                                                                                               2],
                                                                                                           label=1,
                                                                                                           show=save,
                                                                                                           save_path=save_path)

        # 返回检测框和置信度信息
        img_id = judge_infos["img_id"]

        if img_car_ps:
            target_idx = tmp_box.index(img_car_ps)
            percent_coord = convert_src_coord(img_car_ps, self.img_np, img_id,
                                              judge_infos["split_mode"], judge_infos["black_height"],
                                              judge_infos["black_pos"])
            tmp_res = {
                "objId": img_id,
                "objType": "vehicle",
                "objConf": float(tmp_conf[target_idx]),
                "objCoord": percent_coord
            }
            judge_infos["modelDetectResults"].append(tmp_res)
        # 车牌位置信息
        plate_cor = img_plate_dict[str(img_car_ps)] if img_car_ps else []
        if plate_cor:
            target_idx = str(img_car_ps) + "conf"
            percent_coord = convert_src_coord(plate_cor, self.img_np, img_id,
                                              judge_infos["split_mode"], judge_infos["black_height"],
                                              judge_infos["black_pos"])
            tmp_res = {
                "objId": img_id,
                "objType": "plate",
                "objConf": float(img_plate_dict[target_idx]),
                "objCoord": percent_coord
            }
            judge_infos["modelDetectResults"].append(tmp_res)
        # 警车判定
        if plate_num.endswith("警"):
            vio_judge = 'no_vio_010'
            judge_infos['judgeConf'] = 0.9
            return img_car_ps, plate_num, car_bbox_ps, new_car_bbox_ps, crop_person, res_dict, vio_judge
        # 过滤特殊车辆
        vio_judge = filter_special_vehicle(demo, self.img_np, img_car_ps, vio_judge, save, judge_infos)
        if vio_judge == "no_vio_010":
            judge_infos['vio_judge'] = vio_judge
            judge_infos['judgeConf'] = 0.85
            return img_car_ps, plate_num, car_bbox_ps, new_car_bbox_ps, crop_person, res_dict, vio_judge
        # 过滤老头乐
        vio_judge = filter_low_speed_veh(img_car_ps, None, None,None, self.car_plate, plate_num,
                                         first_plate_conf, feat_judge=False)
        if vio_judge == "vio_100":
            vio_judge = "no_vio"

        return img_car_ps, plate_num, car_bbox_ps, new_car_bbox_ps, crop_person, res_dict, vio_judge


# 3 提取目标车辆里面的主副驾驶信息`
class GetDriveInfo(object):
    def __init__(self):
        pass

    def if_point_inpoly(self, polygon, Point):
        line = geometry.LineString(polygon)
        point = geometry.Point(Point)
        pg = geometry.Polygon(line)
        return pg.contains(point)

    def generate_poly_point(self, box):
        # point1 = (box[0], box[1])
        # point2 = (box[2], box[1])
        # point3 = (box[2], box[3])
        # point4 = (box[0], box[3])
        mid_point = (box[0] + box[2]) / 2
        return box, mid_point

    def if_box_in_inpoly(self, polygon, box):  # 判断box是否在多边形内部
        center_point = ((box[0] + box[2]) // 2, (box[1] + box[3]) // 2)
        if polygon[0] < center_point[0] < polygon[2] and polygon[1] < center_point[1] < polygon[3]:
            return True
        return False

    def get_goal_box_list(self, polygon, polygon_x_mid_point, box_list):
        select_list = []
        chief_copilot = {}  # 主副驾驶字典
        # 挑选出在目标车辆范围内的驾驶人图片
        for i, box in enumerate(box_list):
            if_box = self.if_box_in_inpoly(polygon, box)
            if if_box:
                person_mid_point = (box[0] + box[2]) / 2

                if person_mid_point < polygon_x_mid_point:
                    chief_copilot[str(i + 1)] = "副驾驶"
                else:
                    chief_copilot[str(i + 1)] = "主驾驶"
                select_list.append(str(i + 1))
        return select_list, chief_copilot

    def get_cnn_pic(self, box_list, car_pos):
        polygon, polygon_x_mid_point = self.generate_poly_point(car_pos)
        select_list, chief_copilot = self.get_goal_box_list(polygon, polygon_x_mid_point, box_list)
        # result = self.copy_suit_pics(select_list, self.source_path, self.cnn_path)
        return select_list, chief_copilot


class VioJudge(object):
    def __init__(self, device=None):
        self.transform = transforms.Compose([
            transforms.Resize([224, 224]),
            transforms.ToTensor(),
        ])
        if device is None:
            self.device = "cuda:0" if torch.cuda.is_available() else "cpu"
        else:
            self.device = device

    # 模型判定违法类型
    def cnn_eval(self, vio_judge_model, choose_imgs_np, select_img_index, chief_copilot, judge_infos):
        # iiea审核模式
        iieaAuditMode = judge_infos["iieaAuditMode"]
        if iieaAuditMode is None:
            judge_mode = Precision_Judge_Mode[str(judge_infos['vio_code'])]
        else:
            judge_mode = iieaAuditMode
        judge_thresh = Judge_Thresh[str(judge_infos['vio_code'])][int(judge_mode)]
        if judge_thresh is None:
            judge_thresh = 0.75 if judge_mode else 0.3
        class_dic = {"0": "打电话", "1": "未系安全带", "2": "其他"}
        # vio_judge_model.eval()
        res_dict = {}
        with torch.no_grad():
            for i, img_np in enumerate(choose_imgs_np):
                _id = select_img_index[i]
                if (Copilot_Belt_Is_Vio is False or str(judge_infos['vio_code']) == "1223") and chief_copilot[
                    str(_id)] == "副驾驶":
                    continue
                img = Image.fromarray(cv2.cvtColor(img_np, cv2.COLOR_BGR2RGB))
                img = self.transform(img)
                img = img.unsqueeze(0)
                img = img.to(self.device)
                with torch.no_grad():
                    out = vio_judge_model(img)
                prediction = F.softmax(out, dim=1)[:, :].tolist()
                _predict = np.array(prediction)
                # print("AAAAAAAAAAAAA",_predict)
                _predict = np.where(_predict[0] >= judge_thresh, 1, 0)
                if _predict.max() == 1:
                    res_dict[chief_copilot[str(_id)]] = class_dic[str(list(_predict).index(1))]
                    judge_infos['judgeConf'] = judge_thresh
                else:
                    res_dict[chief_copilot[str(_id)]] = class_dic["2"]
                    judge_infos['judgeConf'] = 0.7

                # 新版
                # prediction = np.asarray(F.softmax(out, dim=1)[0])
                # waste_idx = 2
                # new_pred = np.delete(prediction, waste_idx)  # 测试：不考虑废片的概率
                # idx = np.where(new_pred >= judge_thresh)[0].tolist()
                # print("idx:",idx)
                # if str(judge_infos['vio_code']) == "1223" and 0 in idx:
                #     res_dict[chief_copilot[str(_id)]] = class_dic["0"]
                #     judge_infos['judgeConf'] = float(new_pred[0])
                # elif str(judge_infos['vio_code']) == "1101" and 1 in idx:
                #     res_dict[chief_copilot[str(_id)]] = class_dic["1"]
                #     judge_infos['judgeConf'] = float(new_pred[1])
                # else:
                #     res_dict[chief_copilot[str(_id)]] = class_dic["2"]
                #     judge_infos['judgeConf'] = 1-judge_thresh

        return res_dict


def phone_hand_pos_judge(phone_boxes, hand_boxes, ph_boxes, ph_labels, ph_scores):
    if phone_boxes and hand_boxes:
        iou_res = IOU.compute_iou(phone_boxes, hand_boxes)
        idx1, idx2 = np.where(iou_res > 0)
        if Phone_Logic_Mode == 1 and len(idx1):
            print("检出模式判定违法")
            return True
        for i, j in zip(idx1, idx2):
            phone_box = phone_boxes[i]
            # 阈值筛选
            target_idx = ph_boxes.index(phone_box)
            phone_label = ph_labels[target_idx]
            phone_score = ph_scores[target_idx]
            print(phone_label,phone_score)
            if phone_label == "phone" and phone_score < Phone_Thresh:
                continue
            elif phone_label == "phone_s" and phone_score < Phone_Side_Thresh:
                continue
            hand_box = hand_boxes[j]
            phone_w = max(phone_box[2] - phone_box[0], 1e-3)
            phone_h = phone_box[3] - phone_box[1]
            phone_cy = (phone_box[1] + phone_box[3]) // 2
            hand_cy = (hand_box[1] + hand_box[3]) // 2
            judge_res = phone_cy <= hand_cy + 0.1 * phone_h
            if Phone_Logic_Mode == 2 and judge_res:
                print("均衡模式判定违法")
                return True
            inter_w = min(phone_box[2], hand_box[2]) - max(phone_box[0], hand_box[0])
            inter_rate = inter_w / phone_w
            if Phone_Logic_Mode == 3 and judge_res and inter_rate > Phone_Mix_Rate:
                print("检准模式判定违法")
                return True

    return False


# 不系安全带和开车打电话场景主函数
def judge_vio(violation_list, src_plate_num, file_list, weights_list, resize_scale=0.7, save=False,
              save_path=".", extra_msg=None):
    violation_type, src_code = violation_list
    error_path = f"{save_path}/no_vio"
    vio_path = f"{save_path}/vio"
    if save:
        os.makedirs(error_path, exist_ok=True)
        os.makedirs(vio_path, exist_ok=True)
    # 读取模型plate_module, demo, lp_module, yolov7_module, model_cnn, vio_judge_model
    plate_det_model = weights_list[0][0]
    plate_rec_model = weights_list[0][1]
    demo = weights_list[1]
    lp_model = weights_list[2][0]
    lp_meta = weights_list[2][1]
    yolov7_model = weights_list[3][0]
    yolov7_meta = weights_list[3][1]
    phone_model = weights_list[4][0]
    phone_meta = weights_list[4][1]
    model_cnn = weights_list[-2]
    vio_model = weights_list[-1]

    try:
        split_mode = extra_msg[0]
        black_height = extra_msg[1]
        black_pos = extra_msg[2]
        iieaAuditMode = extra_msg[3]
        iieaFilterVehicle = extra_msg[4]
    except:
        split_mode = 111
        black_height = 0
        black_pos = None
        iieaAuditMode = None
        iieaFilterVehicle = None
    judge_infos = {'plate_num': src_plate_num,
                   'vio_code': int(violation_type),
                   'vio_judge': 'no_vio',
                   'zebra_line': {},
                   'police': 0,
                   'has_crossline': 0,
                   'vehicle_location': 0,
                   'drv_direction': 0,
                   'drive_direction_light': [],
                   'lamp': [],
                   'stop_line': 0,
                   'lane': [],
                   'target_car': [],
                   'judgeConf': 1.0,
                   'modelDetectResults': [], 'split_mode': split_mode, "black_height": black_height,
                   "black_pos": black_pos, "iieaAuditMode": iieaAuditMode, "iieaFilterVehicle": iieaFilterVehicle}
    # 特殊车牌判定
    if not src_plate_num:
        vio_judge = "no_vio_009"
        judge_infos['vio_judge'] = vio_judge
        return vio_judge, judge_infos
    if src_plate_num[0] not in chars:
        vio_judge = 'no_vio_002'
        judge_infos['vio_judge'] = vio_judge
        return vio_judge, judge_infos
    vio_judge = VIO_JUDGE_TOOL().spe_no_vio_judge(src_plate_num, iieaFilterVehicle)
    if vio_judge.startswith('no_vio'):
        judge_infos['vio_judge'] = vio_judge
        return vio_judge, judge_infos
    vio_judge = 'no_vio'
    # 判定是否存在交警与辅警指挥
    # p_res = []
    # p_num = 0
    # judge_p_num = np.min((np.ceil(len(file_list) / 3 * 2), len(file_list)))
    # for i in file_list:
    #     _, p = detect_lp(i, lp_net, imgsz, stride, device, conf_thres=0.25, iou_thres=0.45)
    #     if p:
    #         p_num += 1
    #     p_res.append(p)
    #     
    # print("p_num:", p_num)
    # if p_num >= judge_p_num:
    #     vio_judge = 'no_vio_004'
    #     judge_infos['police'] = 1
    #     judge_infos['vio_judge'] = vio_judge
    #     code = vio_judge.split("_")[-1]
    #     if save:
    #         show_police(file_list, p_res)
    #         for idx, i in enumerate(file_list):
    #             cv2.imwrite(f"{error_path}/{code}_{src_plate_num}_img{idx + 1}.jpg", i)
    #     return vio_judge, judge_infos
    # else:
    #     judge_infos['police'] = 0

    judge_infos['target_car'] = []
    plate_list = []
    all_car_list = []
    target_car_list = []
    peron_list = []
    ph_box_list = []
    ph_label_list = []
    ph_score_list = []
    new_car_list = []
    resize_img_list = []
    main_person = False
    for idx, img in enumerate(file_list):
        temp_car = {}
        if (img.shape[0] > 4000 or img.shape[1] > 4000) and float(resize_scale) < 1:
            img = cv2.resize(img, None, fx=resize_scale, fy=resize_scale)
        resize_img_list.append(img)
        judge_weights_list = [demo, yolov7_model, yolov7_meta, plate_det_model, plate_rec_model]
        # 找目标车辆
        pc = PersonCarInfo(img, src_plate_num, judge_weights_list)
        judge_infos["img_id"] = idx + 1
        car_ps, plate_num, all_car, new_car_ps, crop_person, res_dict, vio_judge = pc.get_person_car_info(vio_judge,
                                                                                                          judge_infos,
                                                                                                          save=save,
                                                                                                          save_path=save_path)
        all_car_list.append(all_car)
        target_car_list.append(car_ps)
        plate_list.append(plate_num)
        new_car_list.append(new_car_ps)

        box_list = res_dict["person"]
        peron_list.append(box_list)
        if vio_judge == "no_vio_010":
            main_person = True
            break
        if len(car_ps) > 0:
            temp_car['plate_num'] = plate_num
            temp_car['car_ps'] = car_ps
            judge_infos['target_car'].append(temp_car)
            driveinfo = GetDriveInfo()
            select_img_index, chief_copilot = driveinfo.get_cnn_pic(box_list, car_ps)
            # choose_per_box = [box_list[int(int(a) - 1)] for a in select_img_index]
            choose_imgs_np = [crop_person[int(int(a) - 1)] for a in select_img_index]

            res_dict = VioJudge(yolov7_meta[2]).cnn_eval(vio_model, choose_imgs_np, select_img_index, chief_copilot,
                                                         judge_infos)
            if '主驾驶' in res_dict.keys():
                main_person = True
            # print(res_dict)
            if idx == 0:
                img1_car_ps = car_ps
                try:
                    x_ratio = ((img1_car_ps[0] + img1_car_ps[2]) / 2) / img.shape[1]
                except:
                    x_ratio = 0
                # 0 代表左边， 1代表右边
                if x_ratio <= 0.5:
                    judge_infos['vehicle_location'] = 0
                else:
                    judge_infos['vehicle_location'] = 1
            if len(res_dict.values()) > 0:
                # print(res_dict)
                if int(violation_type) == 1223 and ('主驾驶' in res_dict.keys()):
                    if res_dict['主驾驶'] == '打电话':
                        vio_judge = 'vio'
                        if not save:
                            break
                    else:
                        judge_infos['judgeConf'] = 0.5

                if int(violation_type) == 1101 and ('未系安全带' in res_dict.values()):
                    try:
                        vio_loc = [k for k, v in res_dict.items() if v == '未系安全带']
                        if '主驾驶' in vio_loc and '副驾驶' in vio_loc:
                            vio_judge = 'vio'
                            if not save:
                                break
                        elif '主驾驶' in vio_loc:
                            vio_judge = 'vio_003'
                            if not save:
                                break
                        elif '副驾驶' in vio_loc and Copilot_Belt_Is_Vio:
                            vio_judge = 'vio_004'
                        elif '副驾驶' in vio_loc and Copilot_Belt_Is_Vio is None and str(src_code) in ["3019", "1240"]:
                            vio_judge = 'vio_005'

                    except:
                        traceback.print_exc()
                        pass
            # 打电话场景：目标检测+判罚逻辑补充
            if int(violation_type) == 1223 and vio_judge == "no_vio":
                judge_img, x1, y1 = crop_driver_area(img, car_ps)
                if save:
                    judge_img_path = f"{save_path}/judge_img"
                    os.makedirs(judge_img_path, exist_ok=True)
                    cv2.imwrite(f"{judge_img_path}/{src_plate_num}_{x1}_{y1}.jpg", judge_img)
                ph_box_dict, ph_boxes, ph_labels, ph_scores = phone_detect(judge_img, phone_model, x1, y1,
                                                                           phone_meta[0],
                                                                           phone_meta[1], phone_meta[2],
                                                                           conf_thres=Det_Phone_Thresh, iou_thres=0.3)
                ph_box_list.append(ph_boxes)
                ph_label_list.append(ph_labels)
                ph_score_list.append(ph_scores)
                phone_boxes = ph_box_dict.get("phone", []) + ph_box_dict.get("phone_s",
                                                                             []) if Add_Phone_Side else ph_box_dict.get(
                    "phone", [])
                hand_boxes = ph_box_dict.get("hand", [])
                print(f"手机数量：{len(phone_boxes)} 手数量:{len(hand_boxes)}")
                print("ph_box_dict：",ph_box_dict)
                print(ph_scores)
                judge_res = phone_hand_pos_judge(phone_boxes, hand_boxes, ph_boxes, ph_labels, ph_scores)
                if judge_res:
                    vio_judge = f"vio_mode{Phone_Logic_Mode}"
                    main_person = True
                    if not save:
                        break

    res_list = []
    # print("plate_list:", plate_list, "target_car_list:", target_car_list)
    for plate in plate_list:
        if plate == "NO_DETECT":
            res_list.append(0)
        else:
            res_list.append(1)
    if np.sum(res_list) == 0:
        vio_judge = 'no_vio_006'
        judge_infos['vio_judge'] = vio_judge
        judge_infos['judgeConf'] = 0.9


    elif not main_person:
        judge_infos['judgeConf'] = 0.75
        if No_Person_Is_Waste:
            vio_judge = 'no_vio_001'
        else:
            vio_judge = 'vio_001'

    judge_infos['vio_judge'] = vio_judge
    try:
        if save:
            show_cars2(resize_img_list, new_car_list)
            show_person(resize_img_list, peron_list)
            show_plate2(resize_img_list, target_car_list)
            show_phone_hand(resize_img_list, ph_box_list, ph_label_list, ph_score_list)
            result_path = vio_path if vio_judge.startswith("vio") else error_path
            for idx, i in enumerate(resize_img_list):
                cv2.imwrite(result_path + f'/{vio_judge}_{src_plate_num}_img{idx + 1}.jpg', i)
    except:
        error_msg = catch_error()
        print(f"Error!安全带打电话场景可视化模块异常！{error_msg}")
    return vio_judge, judge_infos
