# 逆行场景
import time

from init_models import *


# 逆行场景主函数
def judge_vio(src_plate_num, file_list, weights_list, resize_scale=0.7, merge=True, save=False,
              save_path='', highway=False, extra_msg=None):
    error_path = f"{save_path}/no_vio"
    vio_path = f"{save_path}/vio"
    if save:
        os.makedirs(error_path, exist_ok=True)
        os.makedirs(vio_path, exist_ok=True)
    # 读取模型
    plate_det_model = weights_list[0][0]
    plate_rec_model = weights_list[0][1]
    demo = weights_list[1]
    lp_model = weights_list[2][0]
    lp_meta = weights_list[2][1]
    yolov7_model = weights_list[3][0]
    yolov7_meta = weights_list[3][1]
    model_cnn = weights_list[-2]
    vio_model = weights_list[-1]

    vio_judge = 'no_judge'
    try:
        split_mode = extra_msg[0]
        black_height = extra_msg[1]
        black_pos = extra_msg[2]
        iieaAuditMode = extra_msg[3]
        iieaFilterVehicle = extra_msg[4]
    except:
        split_mode = 111
        black_height = 0
        black_pos = None
        iieaAuditMode = None
        iieaFilterVehicle = None
    judge_infos = {'plate_num': src_plate_num,
                   'vio_code': 1301,
                   'vio_judge': vio_judge,
                   'zebra_line': {},
                   'police': 0,
                   'has_crossline': 0,
                   'vehicle_location': {},
                   'drv_direction': 0,
                   'drive_direction_light': [],
                   'lamp': [],
                   'stop_line': 0,
                   'lane': [],
                   'target_car': [],
                   'judgeConf': 1.0,
                   'modelDetectResults': [], 'split_mode': split_mode, "black_height": black_height, "black_pos":black_pos,"iieaAuditMode":iieaAuditMode,"iieaFilterVehicle":iieaFilterVehicle}
    # 特殊车牌判定
    if not src_plate_num:
        vio_judge = "no_vio_009"
        judge_infos['vio_judge'] = vio_judge
        return vio_judge, judge_infos
    if src_plate_num[0] not in chars:
        vio_judge = 'no_vio_002'
        judge_infos['vio_judge'] = vio_judge
        return vio_judge, judge_infos
    vio_judge = VIO_JUDGE_TOOL().spe_no_vio_judge(src_plate_num,iieaFilterVehicle)
    if vio_judge.startswith('no_vio'):
        judge_infos['vio_judge'] = vio_judge
        return vio_judge, judge_infos
    vio_judge = 'no_vio'
    img3_car_ps = None
    try:
        if len(file_list) >= 3:
            img1, img2, img3 = file_list[:3]
            db_img_label = 0
        elif len(file_list) == 2:
            img1, img2 = file_list
            db_img_label = 1
            img3 = None
        else:
            vio_judge = 'vio_003'
            judge_infos['vio_judge'] = vio_judge
            judge_infos['judgeConf'] = 0.5
            return vio_judge, judge_infos
    except:
        error_msg = catch_error()
        print(error_msg)
        vio_judge = 'vio_003'
        judge_infos['vio_judge'] = vio_judge
        judge_infos['judgeConf'] = 0.1

        return vio_judge, judge_infos
    # try:
    #     img1, img2, img3 = Tools().resize_imgs(img1_np, img2_np, img3_np)
    # except Exception as e:
    #     import traceback
    #     traceback.print_exc()
    #     vio_judge = 'no_vio_012'
    #     judge_infos['vio_judge'] = vio_judge
    #     return vio_judge, judge_infos

    if img1.shape[0] > 4000 or img1.shape[1] > 4000:
        resize_scale = resize_scale
        if float(resize_scale) < 1:
            img1 = cv2.resize(img1, None, fx=resize_scale, fy=resize_scale)
            img2 = cv2.resize(img2, None, fx=resize_scale, fy=resize_scale)
            if not db_img_label:
                img3 = cv2.resize(img3, None, fx=resize_scale, fy=resize_scale)

    if db_img_label:
        file_list = [img1, img2]
    else:
        file_list = [img1, img2, img3]

    # 找目标车辆，得到位置信息、车牌、道路基本元素信息等
    predictions, cars_ps, all_car_ps, plate_list, cars_masks_list, kp_masks_list, lane_mask, stop_mask, rejudge_label, lane_info_list = \
        mask_gen(src_plate_num, file_list, plate_det_model, plate_rec_model, demo, device=yolov7_meta[2], merge=True,
                 save=save, save_path=f"{save_path}/mask_gen", get_lane_info=True, judge_infos=judge_infos)
    for i, value in enumerate(plate_list):
        temp_tar_car = {'plate_num': '', 'car_ps': ''}
        try:
            temp_tar_car['plate_num'] = plate_list[i]
        except:
            pass
        try:
            temp_tar_car['car_ps'] = cars_ps[i]
        except:
            pass
        judge_infos['target_car'].append(temp_tar_car)

    res_list = []
    print("plate_list:", plate_list)
    for plate in plate_list:
        if plate == "NO_DETECT":
            res_list.append(0)
        else:
            res_list.append(1)
    if np.sum(res_list) == 0:
        vio_judge = 'no_vio_006'
        judge_infos['vio_judge'] = vio_judge
        judge_infos['judgeConf'] = 0.9
        return vio_judge, judge_infos

    if db_img_label:
        img1_car_ps, img2_car_ps = cars_ps
    else:
        img1_car_ps, img2_car_ps, img3_car_ps = cars_ps
    try:
        x_ratio = ((img1_car_ps[0] + img1_car_ps[2]) / 2) / img1.shape[1]
    except:
        x_ratio = 0
    if x_ratio <= 0.5:
        judge_infos['vehicle_location'] = 0
    else:
        judge_infos['vehicle_location'] = 1
    # 白名单车辆
    if judge_infos['vio_judge'] == "no_vio_010":
        vio_judge = 'no_vio_010'
        return vio_judge, judge_infos
    #### 高速路判罚逻辑（直接加入与城市并行
    if highway:
        cor_y_start = (cars_ps[0][1] + cars_ps[0][3]) / 2
        cor_y_end = min([(i[1] + i[3]) / 2 for i in cars_ps if len(i) > 0])

        if cor_y_start - cor_y_end > 10:
            vio_judge = 'vio'
        else:
            vio_judge = 'no_vio'

        print(cor_y_start - cor_y_end)

        judge_infos['plate_num'] = src_plate_num
        judge_infos['img1_car_ps'] = img1_car_ps
        judge_infos['img2_car_ps'] = img2_car_ps
        judge_infos['judgeConf'] = 0.9

        if not db_img_label: judge_infos['img3_car_ps'] = img3_car_ps

        if save:
            if not db_img_label:
                show_plate(img1, img2, img3, img1_car_ps, img2_car_ps, img3_car_ps)
            else:
                show_plate(img1, img2, None, img1_car_ps, img2_car_ps)

            if vio_judge.startswith("no_vio"):
                cv2.imwrite(error_path + f'/{vio_judge}_{src_plate_num}_img1.jpg', img1)
                cv2.imwrite(error_path + f'/{vio_judge}_{src_plate_num}_img2.jpg', img2)
                if not db_img_label: cv2.imwrite(error_path + f'/{vio_judge}_{src_plate_num}_img3.jpg', img3)
            else:
                cv2.imwrite(vio_path + f'/{vio_judge}_{src_plate_num}_img1.jpg', img1)
                cv2.imwrite(vio_path + f'/{vio_judge}_{src_plate_num}_img2.jpg', img2)
                if not db_img_label: cv2.imwrite(vio_path + f'/{vio_judge}_{src_plate_num}_img3.jpg', img3)

        return vio_judge, judge_infos

    if len(cars_masks_list):
        if db_img_label:
            img1_car_mask, img2_car_mask = cars_masks_list
            if len(img1_car_mask) == len(img2_car_mask) == 0:
                vio_judge = 'no_vio_005'
                judge_infos['vio_judge'] = vio_judge
                judge_infos['judgeConf'] = 0.85

                return vio_judge, judge_infos
            predictions1, predictions2 = predictions
            car1_ps, car2_ps = cars_ps
            car1_mask, car2_mask = cars_masks_list
            kp_masks1, kp_masks2 = kp_masks_list
        else:
            img1_car_mask, img2_car_mask, img3_car_mask = cars_masks_list
            if len(img1_car_mask) == len(img2_car_mask) == len(img3_car_mask) == 0:
                vio_judge = 'no_vio_005'
                judge_infos['vio_judge'] = vio_judge
                judge_infos['judgeConf'] = 0.85

                return vio_judge, judge_infos
            predictions1, predictions2, predictions3 = predictions
            car1_ps, car2_ps, car3_ps = cars_ps
            car1_mask, car2_mask, car3_mask = cars_masks_list
            kp_masks1, kp_masks2, kp_masks3 = kp_masks_list

        class_info = ["lane1", "lane2", "stop_area", "arrow_l", "arrow_s", "arrow_r", "arrow_t", "stop_line",
                      "zebra_line", 'no_entry', 'zebra_line', 'arrow_s_t', 'arrow_l_t', 'arrow_r_t']
        car1_road_infos = \
            BlendMaskDetect(img1).save_infos(src_plate_num, demo, class_info, predictions1, kp_masks1, car1_ps,
                                             car1_mask, height_restrict=True, height_bond=0.6)

        car2_road_infos = \
            BlendMaskDetect(img2).save_infos(src_plate_num, demo, class_info, predictions2, kp_masks2, car2_ps,
                                             car2_mask, height_restrict=True, height_bond=0.6)

        data1, data2 = car1_road_infos, car2_road_infos

        if not db_img_label:
            car3_road_infos = \
                BlendMaskDetect(img3).save_infos(src_plate_num, demo, class_info, predictions3, kp_masks3, car3_ps,
                                                 car3_mask, height_restrict=True, height_bond=0.6)
            data3 = car3_road_infos
        data = dict()
        if db_img_label:
            data['shapes'] = data1['shapes'] + data2['shapes']
        else:
            data['shapes'] = data1['shapes'] + data2['shapes'] + data3['shapes']
        data['imageHeight'] = data1['imageHeight']
        data['imageWidth'] = data1['imageWidth']
        data['dev_desc'] = "dev_desc"

        # 从读取的数据取出目标车辆信息
        points = [x['points'] for x in data1['shapes'] if x["label"] == "car"]
        car1 = points[0]
        points = [x['points'] for x in data2['shapes'] if x["label"] == "car"]
        car2 = points[0]
        if not db_img_label:
            points = [x['points'] for x in data3['shapes'] if x["label"] == "car"]
            car3 = points[0]
        st = time.time()
        try:
            road = Road(data)  # 初始化道路信息（其实同一个镜头的初始化是可以复用的）
            print("road 耗时#######", time.time() - st)
            if db_img_label:
                try:
                    vio_judge_temp = road.retrograde([car1, car2])  # 输出当前道路信息下的车辆逆行判定
                except:
                    print("异常置为违法")
                    vio_judge_temp = True
            else:
                try:
                    vio_judge_temp = road.retrograde([car1, car2, car3])
                except:
                    print("异常置为违法")
                    vio_judge_temp = True
        except:
            vio_judge_temp = True
        if vio_judge_temp:
            vio_judge = 'vio'
        else:
            vio_judge = 'no_vio'

    judge_infos['plate_num'] = src_plate_num
    judge_infos['img1_car_ps'] = img1_car_ps
    judge_infos['img2_car_ps'] = img2_car_ps
    if not db_img_label: judge_infos['img3_car_ps'] = img3_car_ps

    # if vio_judge.startswith('no'):
    #     img1_car_ps, plate1_num, car1_type = get_car_box(file_list[0], src_plate_num,
    #                                                      [demo, plate_det_model, plate_rec_model])
    #     img2_car_ps, plate2_num, car2_type = get_car_box(file_list[1], src_plate_num,
    #                                                      [demo, plate_det_model, plate_rec_model])
    #     # 识别车辆前车头辅助判定违法（存在局限性）
    #     if car1_type in ['car_h', 'truck_h'] and car2_type in ['car_h', 'truck_h']:
    #         vio_judge = 'vio'

    judge_infos['vio_judge'] = vio_judge
    if vio_judge.startswith('vio'):
        judge_infos['judgeConf'] = 0.85
    else:
        judge_infos['judgeConf'] = 0.7
    ####     judge_infos['lane'] = road.area_points
    try:
        if save:
            if db_img_label and all_car_ps:
                car1_bbox_ps, car2_bbox_ps = all_car_ps
                show_cars(img1, img2, None, car1_bbox_ps, car2_bbox_ps, None)

            elif all_car_ps:
                car1_bbox_ps, car2_bbox_ps, car3_bbox_ps = all_car_ps
                show_cars(img1, img2, img3, car1_bbox_ps, car2_bbox_ps, car3_bbox_ps)
            show_plate(img1, img2, img3, img1_car_ps, img2_car_ps, img3_car_ps)
            try:
                show_judge_area(img1, img2, img3, road.area_points)
            except:
                print("show error")
            if lane_info_list:
                try:
                    show_lanes(img1, img2, img3, lane_info_list[0], lane_info_list[1], lane_info_list[2])
                except Exception as e:
                    print(e.__traceback__.tb_lineno, e)

            if vio_judge.startswith("no_vio"):
                cv2.imwrite(error_path + f'/{vio_judge}_{src_plate_num}_img1.jpg', img1)
                cv2.imwrite(error_path + f'/{vio_judge}_{src_plate_num}_img2.jpg', img2)
                if not db_img_label:
                    cv2.imwrite(error_path + f'/{vio_judge}_{src_plate_num}_img3.jpg', img3)
            else:
                cv2.imwrite(vio_path + f'/{vio_judge}_{src_plate_num}_img1.jpg', img1)
                cv2.imwrite(vio_path + f'/{vio_judge}_{src_plate_num}_img2.jpg', img2)
                if not db_img_label:
                    cv2.imwrite(vio_path + f'/{vio_judge}_{src_plate_num}_img3.jpg', img3)
    except:
        print("Error!逆行可视化模块异常！")
    return vio_judge, judge_infos


