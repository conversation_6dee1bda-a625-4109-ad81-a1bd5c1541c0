from datetime import datetime


class TimeUtil:
    @staticmethod
    def str2timestamp(timestr, format_) -> float:
        return datetime.strptime(timestr, format_).timestamp()

    @staticmethod
    def timestamp2str(timestamp, format_) -> str:
        return datetime.fromtimestamp(timestamp).strftime(format_)

    @staticmethod
    def str2timestamp_ms(timestr, format_) -> int:
        return int(TimeUtil.str2timestamp(timestr, format_) * 1000)

    @staticmethod
    def timestamp_ms2str(timestamp, format_) -> str:
        return TimeUtil.timestamp2str(timestamp / 1000, format_)

    @staticmethod
    def now_str(format_) -> str:
        return datetime.now().strftime(format_)

    @staticmethod
    def now_hikstr() -> str:
        return datetime.now().strftime("%Y-%m-%dT%H:%M:%S.%f")[:-3] + "+08:00"

    @staticmethod
    def hikstr2timestamp(timestr):
        return datetime.strptime(timestr, "%Y-%m-%dT%H:%M:%S.%f+08:00").timestamp()


if __name__ == '__main__':
    now = TimeUtil.now_hikstr()
    print(now)
    print(TimeUtil.hikstr2timestamp(now))
