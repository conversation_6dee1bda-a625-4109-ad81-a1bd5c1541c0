import torch
import torch.nn as nn


def init_weight(m):
    if isinstance(m, nn.Conv2d):
        nn.init.kaiming_normal_(m.weight)
        # nn.init.xavier_normal_(m.weight)
        if m.bias is not None:
            nn.init.zeros_(m.bias)
    elif isinstance(m, nn.Linear):
        # nn.init.normal_(m.weight)
        nn.init.kaiming_normal_(m.weight)
        if m.bias is not None:
            nn.init.zeros_(m.bias)


class Net1(nn.Module):
    def __init__(self):
        super(Net1, self).__init__()
        self.cnn = nn.Sequential(
            nn.Conv2d(3, 16, 3, 1, bias=False),
            # nn.Dropout2d(0.5),  # GPU
            nn.ReLU(),
            nn.BatchNorm2d(16),

            nn.MaxPool2d(2),
            nn.Conv2d(16, 64, 3, 1),
            # nn.Dropout2d(0.5),  # GPU
            nn.ReLU(),
            nn.BatchNorm2d(64),

            nn.<PERSON>ool2d(2),
            nn.Conv2d(64, 128, 3, 1),
            # nn.Dropout2d(0.5),  # GPU
            nn.ReLU(),
            nn.BatchNorm2d(128),

            nn.MaxPool2d(2),
            nn.Conv2d(128, 512, 3, 1),
            # nn.Dropout2d(0.5),  # GPU
            nn.ReLU(),
            nn.BatchNorm2d(512),

            nn.MaxPool2d(2),
            nn.Conv2d(512, 256, 3, 1),
            # nn.Dropout2d(0.5),  # GPU
            nn.ReLU(),
            nn.BatchNorm2d(256),

            nn.Conv2d(256, 64, 3, 1),
            # nn.Dropout2d(0.5),  # GPU
            nn.ReLU(),
            nn.AdaptiveAvgPool2d(2),
        )

        self.linear = nn.Sequential(
            nn.Linear(64 * 2 * 2, 10),
            # nn.BatchNorm1d(10),
        )

        self.apply(init_weight)

    def forward(self, x):
        x = self.cnn(x)
        x = x.reshape(x.shape[0], -1)
        x = self.linear(x)
        return x


if __name__ == '__main__':
    device = "cuda:0" if torch.cuda.is_available() else "cpu"
    print(device)
    for i in range(5):
        inputs = torch.randn((30, 3, 500, 500))
        net = Net1().to(device)
        out = net(inputs.to(device))
        print(out.shape, f"{i + 1}/5")
        torch.cuda.empty_cache()
    print("test finished!")