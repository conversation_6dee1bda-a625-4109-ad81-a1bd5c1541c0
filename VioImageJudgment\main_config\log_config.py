import logging
import os


def get_logging(name=None):
    if name is None:
        name = "log"
    log_path = f"./logs/{name}.txt"
    os.makedirs("./logs", exist_ok=True)
    LOGGING_CONFIG = {
        "version": 1,
        "formatters": {
            "default": {
                'format': '%(asctime)s %(filename)s %(lineno)s %(levelname)s %(message)s',
            },
            "plain": {
                "format": "%(message)s",
            },
        },
        "handlers": {
            "console": {
                "class": "logging.StreamHandler",
                "level": "DEBUG",
                "formatter": "default",
            },
            "console_plain": {
                "class": "logging.StreamHandler",
                "level": "DEBUG",
                "formatter": "plain"
            },
            "file": {
                "class": "logging.handlers.RotatingFileHandler",
                "level": "DEBUG",
                "filename": log_path,
                "formatter": "default",
                "mode": "a",
                "maxBytes": 100 * 1024 * 1024,
                "backupCount": 10
            }
        },
        "loggers": {
            "console_logger": {
                "handlers": ["console"],
                "level": "DEBUG",
                "propagate": False,
            },
            "console_plain_file_logger": {
                "handlers": ["console_plain", "file"],
                "level": "DEBUG",
                "propagate": False,
            },
            "file_logger": {
                "handlers": ["file"],
                "level": "DEBUG",
                "propagate": False,
            }
        },
        "disable_existing_loggers": True,
    }
    return LOGGING_CONFIG


if __name__ == '__main__':
    # 运行测试
    import logging.config

    logging.config.dictConfig(get_logging())
    logger = logging.getLogger("console_plain_file_logger")
    logger.debug('debug message')
    logger.info('info message')
    logger.warning('warning message')
    logger.error('error message')
    logger.critical('critical message')
